{"version": "0.2.0", "configurations": [{"type": "java", "name": "CFW API Application", "request": "launch", "mainClass": "com.ksyun.cfwapi.CfwApiApplication", "projectName": "cfw-api", "args": "--spring.profiles.active=dev", "vmArgs": "-Dserver.port=9900", "env": {"JAVA_HOME": "C:\\Program Files\\Eclipse Adoptium\\jdk-8.0.462.8-hotspot"}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"type": "java", "name": "CFW API (Maven)", "request": "launch", "mainClass": "com.ksyun.cfwapi.CfwApiApplication", "projectName": "cfw-api", "preLaunchTask": "maven: compile", "args": "--spring.profiles.active=dev --server.port=9900", "console": "internalConsole"}]}