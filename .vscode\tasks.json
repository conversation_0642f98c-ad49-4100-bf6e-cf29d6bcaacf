{"version": "2.0.0", "tasks": [{"label": "maven: compile", "type": "shell", "command": "mvn", "args": ["compile"], "group": "build", "options": {"env": {"JAVA_HOME": "C:\\Program Files\\Eclipse Adoptium\\jdk-8.0.462.8-hotspot", "MAVEN_HOME": "D:\\apache-maven-3.6.3", "PATH": "${env:PATH};C:\\Program Files\\Eclipse Adoptium\\jdk-8.0.462.8-hotspot\\bin;D:\\apache-maven-3.6.3\\bin"}}, "problemMatcher": "$maven-compiler-java"}, {"label": "maven: install", "type": "shell", "command": "mvn", "args": ["install", "-DskipTests"], "group": "build", "options": {"env": {"JAVA_HOME": "C:\\Program Files\\Eclipse Adoptium\\jdk-8.0.462.8-hotspot", "MAVEN_HOME": "D:\\apache-maven-3.6.3", "PATH": "${env:PATH};C:\\Program Files\\Eclipse Adoptium\\jdk-8.0.462.8-hotspot\\bin;D:\\apache-maven-3.6.3\\bin"}}, "problemMatcher": "$maven-compiler-java"}]}