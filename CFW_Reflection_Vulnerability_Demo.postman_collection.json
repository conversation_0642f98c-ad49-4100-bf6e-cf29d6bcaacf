{"info": {"_postman_id": "cfw-reflection-demo-2024", "name": "CFW反射漏洞直观演示集合", "description": "🚨 CFW云防火墙反射漏洞直观演示测试集合\n\n📍 漏洞位置: CommonUtils.getOtherFieldAllNullExceptSpecial方法\n🔴 风险等级: CRITICAL\n\n✨ 新方案特点:\n✅ 直接展示私有字段被访问的过程\n✅ 不依赖数据库连接或异常信息推断\n✅ 提供详细的字段访问日志和漏洞分析\n✅ 包含完整的漏洞证据链\n\n🎯 使用说明:\n1. 确保CFW项目在localhost:9900运行\n2. 按顺序执行测试用例\n3. 观察响应中的详细字段访问日志\n4. 查看服务器日志中的漏洞触发记录\n\n⚠️ 警告: 此测试集合会故意触发安全漏洞，仅用于安全演示和测试", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 快速漏洞验证", "event": [{"listen": "test", "script": {"exec": ["pm.test('快速漏洞验证 - 请求成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('漏洞存在性验证', function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('vulnerabilityExists');", "    ", "    if (jsonData.vulnerabilityExists) {", "        console.log('🚨 漏洞确认存在！');", "        console.log('🔴 风险等级: ' + jsonData.riskLevel);", "        console.log('🔍 私有字段访问数: ' + jsonData.privateFieldsAccessed);", "        console.log('⚠️ 敏感字段访问数: ' + jsonData.sensitiveFieldsAccessed);", "        pm.expect(jsonData.vulnerabilityExists).to.be.true;", "    } else {", "        console.log('❌ 未检测到漏洞');", "        pm.expect(false).to.be.true;", "    }", "});", "", "pm.test('私有字段访问验证', function () {", "    var jsonData = pm.response.json();", "    if (jsonData.privateFieldsAccessed > 0) {", "        console.log('✅ 成功访问 ' + jsonData.privateFieldsAccessed + ' 个私有字段');", "        pm.expect(jsonData.privateFieldsAccessed).to.be.above(0);", "    }", "});", "", "pm.test('敏感信息泄露验证', function () {", "    var jsonData = pm.response.json();", "    if (jsonData.keyEvidence && jsonData.keyEvidence.length > 0) {", "        console.log('🚨 发现敏感信息泄露证据:');", "        jsonData.keyEvidence.forEach(function(evidence) {", "            console.log('  - ' + evidence.fieldName + ': ' + evidence.fieldValue);", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "X-KSC-REQUEST-ID", "value": "quick-test-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "url": {"raw": "{{base_url}}/api/test/reflection-vulnerability/quick-test", "host": ["{{base_url}}"], "path": ["api", "test", "reflection-vulnerability", "quick-test"]}, "description": "快速验证反射漏洞是否存在\n\n🎯 测试目标:\n- 验证setAccessible(true)漏洞存在\n- 确认私有字段可被访问\n- 检测敏感信息泄露\n\n✅ 预期结果:\n- vulnerabilityExists: true\n- privateFieldsAccessed > 0\n- sensitiveFieldsAccessed > 0\n- 响应中包含敏感字段的具体值"}, "response": []}, {"name": "2. 完整漏洞演示", "event": [{"listen": "test", "script": {"exec": ["pm.test('完整漏洞演示 - 请求成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('详细漏洞分析验证', function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('testResult');", "    pm.expect(jsonData.testResult).to.have.property('vulnerabilityAnalysis');", "    ", "    var analysis = jsonData.testResult.vulnerabilityAnalysis;", "    console.log('🔍 漏洞分析结果:');", "    console.log('  漏洞存在: ' + analysis.vulnerabilityExists);", "    console.log('  风险等级: ' + analysis.overallRiskLevel);", "    console.log('  漏洞描述: ' + analysis.vulnerabilityDescription);", "});", "", "pm.test('字段访问日志验证', function () {", "    var jsonData = pm.response.json();", "    var fieldLogs = jsonData.testResult.fieldAccessLogs;", "    ", "    if (fieldLogs && fieldLogs.length > 0) {", "        console.log('📋 字段访问详情 (共 ' + fieldLogs.length + ' 个字段):');", "        ", "        var privateCount = 0;", "        var sensitiveCount = 0;", "        ", "        fieldLogs.forEach(function(log) {", "            if (log.isPrivate) privateCount++;", "            if (log.isSensitive) sensitiveCount++;", "            ", "            if (log.isPrivate && log.isSensitive) {", "                console.log('  🔴 CRITICAL: ' + log.fieldName + ' = \"' + log.fieldValue + '\"');", "            } else if (log.isPrivate) {", "                console.log('  🟡 HIGH: ' + log.fieldName + ' = \"' + log.fieldValue + '\"');", "            } else if (log.isSensitive) {", "                console.log('  🟠 MEDIUM: ' + log.fieldName + ' = \"' + log.fieldValue + '\"');", "            }", "        });", "        ", "        console.log('📊 统计: 私有字段 ' + privateCount + ' 个, 敏感字段 ' + sensitiveCount + ' 个');", "        pm.expect(privateCount).to.be.above(0);", "    }", "});", "", "pm.test('漏洞演示报告验证', function () {", "    var jsonData = pm.response.json();", "    if (jsonData.vulnerabilityDemoReport) {", "        console.log('📄 漏洞演示报告:');", "        console.log(jsonData.vulnerabilityDemoReport);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "demo-test-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "body": {"mode": "raw", "raw": "{\n  \"testName\": \"CFW反射漏洞完整演示\",\n  \"testDescription\": \"演示CommonUtils.getOtherFieldAllNullExceptSpecial方法中setAccessible(true)的滥用漏洞，展示私有敏感字段被非授权访问的完整过程\",\n  \"sensitiveData\": {\n    \"publicInfo\": \"public_information_accessible\",\n    \"version\": \"1.0.0\",\n    \"description\": \"Reflection vulnerability test data\"\n  },\n  \"testMode\": \"FULL\",\n  \"enableRecursiveTest\": true,\n  \"attackerInfo\": {\n    \"attackerId\": \"demo-attacker-001\",\n    \"sourceIp\": \"*************\",\n    \"attackTime\": \"2024-08-01T10:00:00Z\",\n    \"target\": \"CFW云防火墙系统敏感数据\",\n    \"attackMethod\": \"Java Reflection setAccessible Abuse\",\n    \"payload\": \"SensitiveTestData with private sensitive fields\",\n    \"expectedSensitiveData\": [\n      \"adminPassword\",\n      \"databaseCredentials\", \n      \"apiSecretKey\",\n      \"encryptionMasterKey\",\n      \"jwtSigningSecret\",\n      \"internalServiceToken\",\n      \"systemConfigPassword\",\n      \"backupEncryptionKey\"\n    ]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/test/reflection-vulnerability/demo", "host": ["{{base_url}}"], "path": ["api", "test", "reflection-vulnerability", "demo"]}, "description": "完整的反射漏洞演示测试\n\n🎯 测试目标:\n- 展示完整的字段访问过程\n- 记录每个字段的访问详情\n- 生成详细的漏洞分析报告\n- 提供完整的证据链\n\n🔍 关键验证点:\n- 私有字段被setAccessible(true)强制访问\n- 敏感信息（密码、密钥等）被成功读取\n- 嵌套对象的私有字段也被递归访问\n- 生成详细的漏洞风险评估报告"}, "response": []}, {"name": "3. 权限提升攻击演示", "event": [{"listen": "test", "script": {"exec": ["pm.test('权限提升攻击 - 请求成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('管理员权限信息泄露验证', function () {", "    var jsonData = pm.response.json();", "    var fieldLogs = jsonData.testResult.fieldAccessLogs;", "    ", "    var adminFields = fieldLogs.filter(log => ", "        log.fieldName.toLowerCase().includes('admin') || ", "        log.fieldName.toLowerCase().includes('password') ||", "        log.fieldName.toLowerCase().includes('token')", "    );", "    ", "    if (adminFields.length > 0) {", "        console.log('🚨 权限提升攻击成功！发现管理员相关敏感信息:');", "        adminFields.forEach(function(field) {", "            console.log('  🔴 ' + field.fieldName + ': ' + field.fieldValue);", "            console.log('    风险等级: ' + field.riskLevel);", "            console.log('    漏洞描述: ' + field.vulnerabilityDescription);", "        });", "        pm.expect(adminFields.length).to.be.above(0);", "    }", "});", "", "pm.test('系统配置信息泄露验证', function () {", "    var jsonData = pm.response.json();", "    var systemFields = jsonData.testResult.fieldAccessLogs.filter(log => ", "        log.fieldName.toLowerCase().includes('system') || ", "        log.fieldName.toLowerCase().includes('config')", "    );", "    ", "    if (systemFields.length > 0) {", "        console.log('⚠️ 系统配置信息泄露:');", "        systemFields.forEach(function(field) {", "            console.log('  - ' + field.fieldName + ': ' + field.fieldValue);", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "privilege-escalation-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/api/test/reflection-vulnerability/privilege-escalation", "host": ["{{base_url}}"], "path": ["api", "test", "reflection-vulnerability", "privilege-escalation"]}, "description": "权限提升攻击演示\n\n🎯 攻击目标:\n- 获取管理员密码和权限信息\n- 访问系统配置敏感数据\n- 提升普通用户到管理员权限\n\n🔍 关键验证:\n- adminPassword字段被成功访问\n- systemConfigPassword被泄露\n- internalServiceToken被获取\n- 所有权限相关的私有字段被暴露"}, "response": []}, {"name": "4. 数据渗透攻击演示", "event": [{"listen": "test", "script": {"exec": ["pm.test('数据渗透攻击 - 请求成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('数据库凭据泄露验证', function () {", "    var jsonData = pm.response.json();", "    var fieldLogs = jsonData.testResult.fieldAccessLogs;", "    ", "    var dbFields = fieldLogs.filter(log => ", "        log.fieldName.toLowerCase().includes('database') || ", "        log.fieldName.toLowerCase().includes('db') ||", "        log.fieldName.toLowerCase().includes('credential')", "    );", "    ", "    if (dbFields.length > 0) {", "        console.log('🚨 数据库凭据泄露成功！');", "        dbFields.forEach(function(field) {", "            console.log('  🔴 ' + field.fieldName + ': ' + field.fieldValue);", "        });", "        pm.expect(dbFields.length).to.be.above(0);", "    }", "});", "", "pm.test('API密钥泄露验证', function () {", "    var jsonData = pm.response.json();", "    var apiFields = jsonData.testResult.fieldAccessLogs.filter(log => ", "        log.fieldName.toLowerCase().includes('api') || ", "        log.fieldName.toLowerCase().includes('key') ||", "        log.fieldName.toLowerCase().includes('secret')", "    );", "    ", "    if (apiFields.length > 0) {", "        console.log('🚨 API密钥泄露成功！');", "        apiFields.forEach(function(field) {", "            console.log('  🔴 ' + field.fieldName + ': ' + field.fieldValue);", "        });", "    }", "});", "", "pm.test('嵌套对象渗透验证', function () {", "    var jsonData = pm.response.json();", "    var nestedFields = jsonData.testResult.fieldAccessLogs.filter(log => ", "        log.fieldName.includes('Config') || log.fieldName.includes('.')", "    );", "    ", "    if (nestedFields.length > 0) {", "        console.log('🔄 嵌套对象渗透成功！');", "        nestedFields.forEach(function(field) {", "            console.log('  📦 ' + field.fieldName + ': ' + field.fieldValue);", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-KSC-REQUEST-ID", "value": "data-exfiltration-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/api/test/reflection-vulnerability/data-exfiltration", "host": ["{{base_url}}"], "path": ["api", "test", "reflection-vulnerability", "data-exfiltration"]}, "description": "数据渗透攻击演示\n\n🎯 攻击目标:\n- 获取数据库连接信息和凭据\n- 窃取API密钥和访问令牌\n- 渗透嵌套对象中的敏感数据\n- 获取加密密钥和系统机密\n\n🔍 关键验证:\n- databaseCredentials字段泄露\n- apiSecretKey被获取\n- encryptionMasterKey被访问\n- 嵌套对象DatabaseConfig和ApiConfig中的私有字段被递归访问"}, "response": []}, {"name": "5. 对比测试 - 正常vs漏洞", "event": [{"listen": "test", "script": {"exec": ["pm.test('对比测试 - 帮助信息获取成功', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('测试端点信息验证', function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('endpoints');", "    pm.expect(jsonData).to.have.property('warnings');", "    ", "    console.log('📋 可用的测试端点:');", "    Object.keys(jsonData.endpoints).forEach(function(endpoint) {", "        console.log('  ' + endpoint + ': ' + jsonData.endpoints[endpoint]);", "    });", "    ", "    console.log('⚠️ 安全警告:');", "    Object.keys(jsonData.warnings).forEach(function(warning) {", "        console.log('  ' + jsonData.warnings[warning]);", "    });", "});", "", "pm.test('漏洞测试环境确认', function () {", "    var jsonData = pm.response.json();", "    if (jsonData.description.includes('setAccessible')) {", "        console.log('✅ 漏洞测试环境已确认');", "        console.log('🎯 目标漏洞: setAccessible滥用');", "        pm.expect(true).to.be.true;", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "X-KSC-REQUEST-ID", "value": "help-test-{{$timestamp}}", "description": "必填：请求ID"}, {"key": "X-KSC-ACCOUNT-ID", "value": "*********", "description": "必填：账号ID"}, {"key": "X-KSC-REGION", "value": "cn-beijing-6", "description": "必填：区域代码"}], "url": {"raw": "{{base_url}}/api/test/reflection-vulnerability/help", "host": ["{{base_url}}"], "path": ["api", "test", "reflection-vulnerability", "help"]}, "description": "获取测试帮助信息和端点说明\n\n🎯 测试目标:\n- 确认测试环境可用性\n- 获取所有可用测试端点\n- 了解测试安全警告\n- 验证漏洞测试框架正常工作\n\n📋 对比分析:\n- 正常业务端点：需要复杂的业务逻辑和数据库\n- 漏洞测试端点：直接展示反射访问过程\n- 证据获取：从异常推断 vs 直接日志展示"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 设置全局变量", "pm.globals.set('timestamp', Date.now());", "", "// 输出测试信息", "console.log('🚨 CFW反射漏洞直观演示开始');", "console.log('🎯 目标: ' + pm.variables.get('base_url'));", "console.log('⏰ 时间: ' + new Date().toISOString());", "console.log('📋 测试类型: 直观漏洞演示 (不依赖数据库)');", "console.log('🔍 验证方法: 直接展示私有字段访问过程');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局响应分析", "var responseTime = pm.response.responseTime;", "var statusCode = pm.response.code;", "var responseSize = pm.response.responseSize;", "", "console.log('📊 响应统计:');", "console.log('  状态码: ' + statusCode);", "console.log('  响应时间: ' + responseTime + 'ms');", "console.log('  响应大小: ' + responseSize + ' bytes');", "", "// 检查是否有漏洞相关的响应内容", "var responseText = pm.response.text().toLowerCase();", "var vulnerabilityKeywords = ['setaccessible', 'private', 'sensitive', 'password', 'secret', 'key'];", "var foundKeywords = [];", "", "vulnerabilityKeywords.forEach(keyword => {", "    if (responseText.includes(keyword)) {", "        foundKeywords.push(keyword);", "    }", "});", "", "if (foundKeywords.length > 0) {", "    console.log('🚨 检测到漏洞相关内容: ' + foundKeywords.join(', '));", "} else {", "    console.log('ℹ️ 未在响应中检测到明显的漏洞痕迹');", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:9900", "description": "CFW API基础URL"}]}