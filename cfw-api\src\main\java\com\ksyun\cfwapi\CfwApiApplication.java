package com.ksyun.cfwapi;

import lombok.extern.log4j.Log4j2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@Log4j2
@SpringBootApplication(scanBasePackages = {"com.ksyun"})
@MapperScan("com.ksyun.cfwapi.dao.mapper")
public class CfwApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(CfwApiApplication.class, args);
        log.info("cfw-Api服务组件启动成功");
    }

}
