package com.ksyun.cfwapi.aspect;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Description: 控制层日志切面
 */
@Slf4j
@Aspect
@Component
public class ControllerLogAspect {


    /**
     * 创建切入点
     */
    @Pointcut("execution(public * com.ksyun.cfwapi.controller.cfw..*.*(..))")
    public void controllerPointCut() {
    }

    /**
     * 前置处理
     *
     * @param point
     */
    @Around("controllerPointCut()")
    public Object beforeAspect(ProceedingJoinPoint point) throws Throwable {
        String methodName = point.getSignature().getName();
        Object[] args = point.getArgs();
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        StringBuffer param = new StringBuffer();
        if (args != null && args.length >= 1) {
            for (Object arg : args) {
                param.append(JSONUtil.toJsonStr(arg)).append(CommonConstant.COMMA);
            }
            param.deleteCharAt(param.length() - 1);
        }
        log.debug("入参：Method:{},param:{},auth:{}", methodName, param, JSONUtil.toJsonStr(auth));

        Object result = point.proceed();
        if(Objects.nonNull(result)&&JSONUtil.toJsonStr(result).length()>1000){
            log.debug("回参：Method:{} result:{},auth:{} ", methodName, JSONUtil.toJsonStr(result).substring(0,1000), JSONUtil.toJsonStr(auth));
        }else{
            log.debug("回参：Method:{} result:{},auth:{} ", methodName, JSONUtil.toJsonStr(result), JSONUtil.toJsonStr(auth));
        }
        return result;
    }
}
