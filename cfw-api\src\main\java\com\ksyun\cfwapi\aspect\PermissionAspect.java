package com.ksyun.cfwapi.aspect;

import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @Description: 控制层日志切面
 */
@Slf4j
@Aspect
@Component
public class PermissionAspect {
    @Autowired
    private CfwInstanceService instanceService;

    /**
     * 创建切入点
     */
    @Pointcut("execution(public * com.ksyun.cfwapi.controller.cfw..*.*(..))")
    public void controllerPointCut() {
    }

    /**
     * 前置处理
     *
     * @param point
     */
    @Around("controllerPointCut()")
    public Object beforeAspect(ProceedingJoinPoint point) throws Throwable {
        Object[] args = point.getArgs();
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        //无参不拦截
        if (null != args && args.length != 0) {
            //获取参数对象的userId
            Object param=args[0];
            Class<?> paramClazz = param.getClass();
            Field[] fields = paramClazz.getDeclaredFields();
            for (Field field:fields) {
                if(!CommonConstant.CFW_INSTANCE_ID.equals(field.getName())){
                    continue;
                }
                checkParam(param,field,auth.getAccount_id());
            }
        }
        Object result = point.proceed();
        return result;
    }

    /**
     *  校验具体入参数据和用户参数比较
     * @param target 实际方法入参
     * @param field  要校验的属性
     */
    private void checkParam(Object target,Field field,String accountId) throws CfwException {
        if(StringUtils.isBlank(accountId)){
            throw new CfwException("账号信息错误");
        }
        try {
            ReflectionUtils.makeAccessible(field);
            Object targetValue = field.get(target);
            int count = instanceService.countCfwInstanceByFwAccountId(targetValue.toString(),accountId);
            if(count == 0){
                throw new CfwException("云防火墙id信息错误");
            }
        } catch (IllegalAccessException e) {
            log.error("数据越权校验出错",e);
        }
    }
}
