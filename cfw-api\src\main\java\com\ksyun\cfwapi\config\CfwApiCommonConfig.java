package com.ksyun.cfwapi.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.cfwcore.config.LoadConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshMethod;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Component
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-api-common","cfw-core-common"})
public class CfwApiCommonConfig extends LoadConfig {

    @Value("${vpc.api.host}")
    @RefreshField("vpc.api.host")
    private String vpcApiHost;

    @Value("${vpc.api.port}")
    @RefreshField("vpc.api.port")
    private String vpcApiPort;

    @Value("${http.time.out}")
    @RefreshField("http.time.out")
    private Integer httpTimeOut;

    @Value("${charge.type.mapping:}")
    @RefreshField("charge.type.mapping")
    private String chargeTypeMapping;

    @Value("${cfw.cloudserver.userdata}")
    @RefreshField("cfw.cloudserver.userdata")
    private String userdata;

    @Value("${cfw.security.group.id}")
    @RefreshField("cfw.security.group.id")
    private String securityGroupId;

    @Value("${cfw.chargeType}")
    @RefreshField("cfw.chargeType")
    private String chargeType;

    @Value("${cfw.systemdisk.type}")
    @RefreshField("cfw.systemdisk.type")
    private String systemdiskType;

    @Value("${cfw.systemdisk.size}")
    @RefreshField("cfw.systemdisk.size")
    private Integer systemdiskSize;

    @Value("${cfw.instance.type}")
    @RefreshField("cfw.instance.type")
    private String instanceType;

    @Value("${cfw.default.name}")
    @RefreshField("cfw.default.name")
    private String defaultName;

    @Value("${cfw.hs.type}")
    @RefreshField("cfw.hs.type")
    private String hsType;

    @Value("${cfw.create.job.interval:10}")
    @RefreshField("cfw.create.job.interval")
    private Integer createJobInterval;

    @Value("${kafka.url}")
    @RefreshField("kafka.url")
    private String kafkaUrl;

    @Value("${down.file.url}")
    @RefreshField("down.file.url")
    private String downFileUrl;

    @Value("${cfw.total.acl.num}")
    @RefreshField("cfw.total.acl.num")
    private Integer totalAclNum;

    @Value("${current.region}")
    @RefreshField("current.region")
    private String currentRegion;

    @Value("${cfw.project.id}")
    @RefreshField("cfw.project.id")
    private Integer cfwProjectId;

    @Value("${kafka.user}")
    @RefreshField("kafka.user")
    private String kafkaUser;

    @Value("${kafka.password}")
    @RefreshField("kafka.password")
    private String kafkPassword;

    @Value("${kafka.saslEnable}")
    @RefreshField("kafka.saslEnable")
    private String kafkSaslEnable;

    @Value("${kafka.certPath}")
    @RefreshField("kafka.certPath")
    private String kafkaCertPath;

    @Value("${kafka.cert.md5}")
    @RefreshField("kafka.cert.md5")
    private String kafkaCertMd5;



    private Map<String,Integer> cfwEipNumMappingCache = new HashMap<>();

    private Map<String, String> chargeTypeMappingCache = new HashMap<>();

    @RefreshMethod(field = {"charge.type.mapping"})
    private void initChargeTypeMappingCache() {
        if (StringUtils.isEmpty(chargeTypeMapping)) return;
        synchronized (chargeTypeMappingCache) {
            Map<String, List<String>> loadMap = loadConfig(chargeTypeMapping, "charge.type.mapping", chargeTypeMappingCache, 2);
            loadMap.forEach((k, v) -> {
                chargeTypeMappingCache.put(StringUtils.trimWhitespace(v.get(0)), StringUtils.trimWhitespace(v.get(1)));
            });
            log.info("新老计费方式的映射关系 [{}]", chargeTypeMappingCache);
        }
    }

    @PostConstruct
    private void commonConfigInit() throws Exception {
        this.initChargeTypeMappingCache();
    }
}
