package com.ksyun.cfwapi.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Component
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-api-common"})
public class SuperConfig {
    @Value("${admin.uid}")
    @RefreshField("admin.uid")
    private String superAccountId;
}
