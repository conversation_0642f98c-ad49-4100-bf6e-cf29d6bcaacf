package com.ksyun.cfwapi.config;

import com.ksyun.cfwapi.interceptor.HeaderInterceptor;
import com.ksyun.cfwapi.interceptor.MDCInterceptor;
import com.ksyun.cfwapi.interceptor.ProxyAuthInterceptor;
import com.ksyun.cfwapi.interceptor.SubOrderIdCleanIntercrptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {
    private HeaderInterceptor headerInterceptor;
    private ProxyAuthInterceptor proxyAuthInterceptor;

    private SubOrderIdCleanIntercrptor subOrderIdCleanIntercrptor;

    private MDCInterceptor mdcInterceptor;

    @Autowired
    public void setMdcInterceptor(MDCInterceptor mdcInterceptor) {
        this.mdcInterceptor = mdcInterceptor;
    }

    @Autowired
    public void setSubOrderIdCleanIntercrptor(SubOrderIdCleanIntercrptor subOrderIdCleanIntercrptor) {
        this.subOrderIdCleanIntercrptor = subOrderIdCleanIntercrptor;
    }

    @Autowired
    public void setHeaderInterceptor(HeaderInterceptor headerInterceptor) {
        this.headerInterceptor = headerInterceptor;
    }

    @Autowired
    public void setProxyAuthInterceptor(ProxyAuthInterceptor proxyAuthInterceptor) {
        this.proxyAuthInterceptor = proxyAuthInterceptor;
    }
    /**
     * 请求路径返回多种数据格式
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        //是否开启后缀模式
        configurer.favorPathExtension(false)
                //默认是xml模式
                .defaultContentType(MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("json", MediaType.APPLICATION_JSON);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(headerInterceptor).addPathPatterns("/**").excludePathPatterns("/heart/CheckHealth");
        registry.addInterceptor(subOrderIdCleanIntercrptor).addPathPatterns("/**").excludePathPatterns("/heart/CheckHealth", "/queryParentResource");
        registry.addInterceptor(mdcInterceptor).addPathPatterns("/**").excludePathPatterns("/heart/CheckHealth");
        registry.addInterceptor(proxyAuthInterceptor).addPathPatterns("/**").excludePathPatterns("/heart/CheckHealth");
    }
}
