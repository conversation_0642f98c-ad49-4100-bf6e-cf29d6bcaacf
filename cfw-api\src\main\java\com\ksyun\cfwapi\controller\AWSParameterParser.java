package com.ksyun.cfwapi.controller;

import com.google.common.base.Splitter;
import com.ksyun.cfwapi.enums.AwsParamKindEnum;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import com.ksyun.common.http.OpenAPIException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Pattern;

@Slf4j
public class AWSParameterParser {

	public static final String _PARAMETER_NETWORKINTERFACEID = "Filter";

	private static final Pattern PATTERN_RESOURCE_ID = Pattern
			.compile(Validation.REGEX_ID);

	private static final int MAX_INSTANCE_IDS = 1000;

	// TODO 如果没有按照这种格式，要校验
	@SuppressWarnings("unchecked")
	public static List<String> parseInstanceId(HttpServletRequest request,
                                               String prefix, boolean ... checkValues ) {
		boolean checkValue=false;
		if(checkValues==null||checkValues.length!=1){
			checkValue=true;
		}else{
			checkValue=checkValues[0];
		}
		List<String> t = new ArrayList<String>();
		Map<String, String[]> parameters = request.getParameterMap();
		Map<String, String> check_parameter = new HashMap<String, String>();
		Iterator<Entry<String, String[]>> it = parameters.entrySet().iterator();
		while (it.hasNext()) {
			Entry<String, String[]> e = it.next();
			String key = e.getKey();
			String[] values = e.getValue();
			if (StringUtils.isEmpty(key) || !key.startsWith(prefix)) {
				continue;
			}
			// 判断是否含有相同的key
			if (values.length > 1) {
				throw new OpenAPIException(
						ErrorCode.InvalidParameterInstanceIdSameKey,
						String.format("request parameter %s can not same", key),
						HttpStatus.BAD_REQUEST);
			}

			List<String> parts = Splitter.on('.').splitToList(key);
			// 判断是否含有".",和是否"."后面还有数据
			if (parts.size() != 2 || StringUtils.isBlank(parts.get(1))) {
				throw new OpenAPIException(
						ErrorCode.InvalidParameterInstanceIdMalformedPoint,
						String.format("request parameter is invalid,"
								+ "refer %s.1", prefix), HttpStatus.BAD_REQUEST);
			}
			String count_str = parts.get(1);
			if (!NumberUtils.isNumber(count_str)) {
				throw new OpenAPIException(
						ErrorCode.InvalidParameterInstanceIdMalformedIndex,
						String.format("Id %s value is invalid. must be number"
								+ " ", key), HttpStatus.BAD_REQUEST);
			}

//			log.debug("parts.get(1) {}, Max_instance_ids {}", parts.get(1), MAX_INSTANCE_IDS);

			if (String.valueOf(count_str).length() > 4) {
				throw new OpenAPIException(
						ErrorCode.InvalidParameterInstanceIdMaxlengthIndex,
						String.format("Id %s value is invalid. .N length <= 4",
								key), HttpStatus.BAD_REQUEST);
			}
			int count_number = Integer.parseInt(count_str);
			if (count_number > MAX_INSTANCE_IDS || count_number < 1) {
				throw new OpenAPIException(
						ErrorCode.InvalidParameterInstanceIdRangeIndex,
						String.format("Id %s value is invalid. .N must <= 1000"
								+ " and >=1", key), HttpStatus.BAD_REQUEST);
			}
			String check_key = parts.get(0) + count_number;
			if (check_parameter.containsKey(check_key)) {
				String code = ErrorCode.InvalidParameterInstanceIdSameKey;
				String message = "the Parameter is invalid,"
						+ "request parmeter .N can not same";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			} else {
				check_parameter.put(check_key, null);
			}
			for (String value : values) {
				//特殊处理了ProjectId
				if (checkValue&&!PATTERN_RESOURCE_ID.matcher(value).find()) {
					throw new OpenAPIException(
							ErrorCode.InvalidParameterValueInstanceId,
							String.format("Id %s value is invalid.", value),
							HttpStatus.BAD_REQUEST);
				}
				if (StringUtils.isBlank(value)){
					String code = ErrorCode.InvalidParameterValueFilterValueNotEmptyValue;
					String message = String.format("the Parameter %s is "
							+ "invalid,value can not be empty", key);
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				t.add(value);
			}
		}

		if (t.size() > MAX_INSTANCE_IDS) {
			throw new OpenAPIException(
					ErrorCode.InvalidParameterInstanceIdRangeIndex,
					String.format("Id %s value length must <= 1000", prefix),
					HttpStatus.BAD_REQUEST);
		}

		return t;
	}

	@SuppressWarnings("unchecked")
	public static Map<String, List<String>> parseResouse(
            HttpServletRequest request, String prefix, String middle_name) {
		Map<String, List<String>> value_map = new HashMap<String, List<String>>();

		/*
		 * &RemoteCidrBockSet.CidrBock.1=10.0.0.0/16
		 * &RemoteCidrBockSet.CidrBock.2=11.0.0.0/16
		 */
		Map<String, String[]> parameters = request.getParameterMap();
		Iterator<Entry<String, String[]>> it = parameters.entrySet().iterator();
		while (it.hasNext()) {
			Entry<String, String[]> e = it.next();
			String key = e.getKey();
			String[] valueset = e.getValue();
			String value = valueset[0];
			if (StringUtils.isEmpty(key) || !key.startsWith(prefix)) {
				continue;
			}
			if (!StringUtils.isEmpty(key) || key.startsWith(prefix)) {
				List<String> parts = Splitter.on('.').splitToList(key);
				String first = parts.get(0);
				if (StringUtils.isEmpty(first) || !prefix.equals(first)) {
					String code = ErrorCode.InvalidParameterFilterPrefix;
					String message = "the Parameter is invalid,the first word must be "
							+ prefix;
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				String num = parts.get(2);
				if (StringUtils.isEmpty(num) || !StringUtils.isNumeric(num)) {
					String code = ErrorCode.InvalidParameter;
					String message = "the Parameter is invalid,.N can not be empty and be number";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				String key_value = parts.get(1);
				if (!middle_name.equals(key_value)) {
					String code = ErrorCode.InvalidParameter;
					String message = "the Parameter is invalid,the middle word must be "
							+ middle_name;
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				if (value_map.containsKey(key_value)) {
					List<String> value_list = value_map.get(key_value);
					value_list.add(value);
				} else {
					List<String> list = new ArrayList<String>();
					list.add(value);
					value_map.put(key_value, list);
				}
			}

		}
		return value_map;
	}

	public static Map<String, List<String>> parseFilterParamter(HttpServletRequest request, String prefix){
		return parseFilterParamter(request, prefix, false);
	}

	@SuppressWarnings("unchecked")
	public static Map<String, List<String>> parseFilterParamter(
            HttpServletRequest request, String prefix, boolean valueCanNull) {

		Map<String, List<String>> value_map = new HashMap<String, List<String>>();
		Map<String, String> key_map = new HashMap<String, String>();
		Map<String, List<String>> key_value_map = new HashMap<String, List<String>>();
		// key : num.Name
		// value: num.Value
		// map:<Name,List<Value>>

		// &Filter.1.Name=instance-type
		// &Filter.1.Value.1=m1.small
		// &Filter.1.Value.2=m1.large
		// &Filter.2.Name=block-device-mapping.status
		// &Filter.2.Value.1=attached
		// &Filter.3.Name=block-device-mapping.delete-on-termination
		// &Filter.3.Value.1=true
		// 存储名称的值集合，用于检查名字是否存在
		Map<String, String> name_value_map = new HashMap<String, String>();
		// 存储对应的Value中的名字集合，用于检查对应的Value的名字是值存在
		Map<String, String> value_name_map = new HashMap<String, String>();
		Map<String, String[]> parameters = request.getParameterMap();
		Iterator<Entry<String, String[]>> it = parameters.entrySet().iterator();
		while (it.hasNext()) {
			Entry<String, String[]> e = it.next();
			String key = e.getKey();
			if (StringUtils.isEmpty(key) || !key.startsWith(prefix)) {
				continue;
			}
			List<String> s = new ArrayList<String>();
			s = Splitter.on('.').splitToList(key);
			int size = s.size();
			if (size < 3) {
				String code = ErrorCode.InvalidParameterFilterMalformedNameParts;
				String message = "the Parameter is invalid,refer to the example";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);

			}

			String f = s.get(0);
			if (StringUtils.isEmpty(f) || !f.equals(prefix)) {
				String code = ErrorCode.InvalidParameterFilterPrefix;
				String message = "the Parameter is invalid,refer to the example";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
			String num = s.get(1);
			checkFilterNum(num);
			String name_value = s.get(2);
			if (name_value.equals("Name")) {
				if (key_map.containsKey(num)) {
					String code = ErrorCode.InvalidParameterFilterNameSameKey;
					String message = "the Parameter is invalid,"
							+ "request parmeter .N can not same";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				if (s.size() != 3) {
					String code = ErrorCode.InvalidParameterFilterMalformedNameParts;
					String message = "the Parameter is invalid,"
							+ "must be Filter.N.Name";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				checkKey(e);
				String value_key = e.getValue()[0];
				if (name_value_map.containsKey(value_key)) {
					String code = ErrorCode.InvalidParameterValueFilterNameSameValue;
					String message = String.format("the Parameter is "
							+ "invalid,request value %s can not same", key,
							value_key);
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				name_value_map.put(value_key, null);
				key_map.put(num, value_key);
			} else if (name_value.equals("Value")) {
				if (s.size() != 4) {
					String code = ErrorCode.InvalidParameterFilterMalformedValueParts;
					String message = "the Parameter is invalid,"
							+ "must be Filter.N.Value.M";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				String value_num = s.get(3);
				if (StringUtils.isEmpty(value_num)
						|| !StringUtils.isNumeric(value_num)) {
					String code = ErrorCode.InvalidParameterFilterValueEndPartsMalformed;
					String message = "the Parameter is invalid,refer to the example";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				if (value_num.length() > 4) {
					String code = ErrorCode.InvalidParameterFilterValueEndPartsDigits;
					String message = "the Parameter is invalid,"
							+ "in Value.N, .N length must be <= 4";
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				int value_num_count = Integer.parseInt(value_num);
				value_num = String.valueOf(value_num_count);
				if (value_num_count > MAX_INSTANCE_IDS || value_num_count < 1) {
					throw new OpenAPIException(
							ErrorCode.InvalidParameterFilterValueEndPartsRange,
							String.format(
									"Value.%s  is invalid,the number must be "
											+ "<=1000 and >=1", value_num),
							HttpStatus.BAD_REQUEST);
				}
				if (e.getValue().length > 1) {
					String code = ErrorCode.InvalidParameterValueFilterValueSameValue;
					String message = String.format("the Parameter %s is "
							+ "invalid,request parmeter can not same", key);
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				String value_value = e.getValue()[0];
				if (StringUtils.isBlank(value_value)) {
					String code = ErrorCode.InvalidParameterValueFilterValueNotEmptyValue;
					String message = String.format("the Parameter %s is "
							+ "invalid,value can not be empty", key);
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				}
				String check_value_name = "Filter." + num + "Value."
						+ value_num;
				if (value_name_map.containsKey(check_value_name)) {
					String code = ErrorCode.InvalidParameterFilterValueSameKey;
					String message = String.format("the Parameter %s is "
							+ "invalid,in Value.N, .N can not same", key);
					throw new OpenAPIException(code, message,
							HttpStatus.BAD_REQUEST);
				} else {
					value_name_map.put(check_value_name, null);
				}
				// validateId(value_value);
				if (value_map.containsKey(num)) {
					List<String> list = value_map.get(num);
					list.add(value_value);
				} else {
					List<String> value_list = new ArrayList<String>();
					value_list.add(value_value);
					value_map.put(num, value_list);
				}

			} else {
				String code = ErrorCode.InvalidParameter;
				String message = "the Parameter is invalid,refer to the example";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		// 分别从value_map key_map取出对应一样的key,
		Iterator<Entry<String, String>> it_key = key_map.entrySet().iterator();
		while (it_key.hasNext()) {
			Entry<String, String> next = it_key.next();
			if (value_map.keySet().contains(next.getKey())) {
				List<String> value_list = value_map.get(next.getKey());
				key_value_map.put(next.getValue(), value_list);
			}else if (valueCanNull) {
				key_value_map.put(next.getValue(), Collections.emptyList());
			}else {
				String code = ErrorCode.InvalidParameterFilterNameMatch;
				String message = String.format("the Parameter is invalid,"
						+ "request parameter %s have no value to match",
						"Filter." + next.getKey() + ".Name");
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		// 判断是否key,value对应
		for (String value_key : value_map.keySet()) {
			if (!key_map.containsKey(value_key)) {
				String code = ErrorCode.InvalidParameterFilterValueMatch;
				String message = String.format("the Parameter is invalid,"
						+ "request parameter Filter.%s.Value have no "
						+ "name to match Filter.%s.Name", value_key, value_key);
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		return key_value_map;
	}

	@SuppressWarnings("unchecked")
	public static Map<String, String> parseKeyValueParamter(
            HttpServletRequest request, String prefix) {
		return parseKeyValueParamter(request, prefix, "Key", "Value");
	}

	/**
	 * 组装过滤参数，request包含入参
	 * prefix.N.key
	 * prefix.N.value
	 *
	 * 例如：
	 * Attributes.member.1.Key=deletion_protection.enabled
	 * Attributes.member.1.Value=true
	 *
	 * prefix、key和value支持自定义
	 *
	 * @param request
	 * @param prefix  前缀
	 * @param key     若传入为null，默认为Key
	 * @param value   若传入为null，默认为Value
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, String> parseKeyValueParamter(
            HttpServletRequest request, String prefix, String key, String value) {
		if (StringUtils.isEmpty(key)) {
			key = "Key";
		}
		if (StringUtils.isEmpty(value)) {
			value = "Value";
		}

		// 存储根据"."分割后的key或value后两部分，及N与key或value的value;  Map<N(num) , keyOrValue_value>
		Map<String, String> keyNumAndValueMap = new HashMap<>();
		Map<String, String> valueNumAndValueMap = new HashMap<>();

		Map<String, String> key_value_map = new HashMap<>();
		// 存储名称的值集合，用于检查名字是否存在
		List<String> key_value_list = new ArrayList<>();
		List<String> value_value_list = new ArrayList<>();
		Map<String, String[]> parameters = request.getParameterMap();
		Iterator<Entry<String, String[]>> it = parameters.entrySet().iterator();
		while (it.hasNext()) {
			Entry<String, String[]> e = it.next();
			// key或value的键
			String keyOrValueKey = e.getKey();
			if (StringUtils.isEmpty(keyOrValueKey) || !keyOrValueKey.startsWith(prefix)) {
				continue;
			}
			// 去掉前缀后再根据"."分割；前缀中可能包含"."
			String keyDeletePrefix = keyOrValueKey.substring(prefix.length());
			// 分割后的三段：""，N，key/value
			List<String> s = Splitter.on('.').splitToList(keyDeletePrefix);
			if (CollectionUtils.isEmpty(s) || s.size() != 3) {
				String code = ErrorCode.InvalidParameterFilterMalformedNameParts;
				String message = "the Parameter is invalid,must be " + prefix + ".N." + key + " or " + prefix + ".N." + value;
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}

			String pre = s.get(0);
			if (StringUtils.isNotEmpty(pre)) {
				String code = ErrorCode.InvalidParameterFilterPrefix;
				String message = "the Parameter is invalid,refer to the example";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
			String num = s.get(1);
			// 校验数字N合法性
			checkFilterNum(num);
			// 校验key和value公共参数
			checkKey(e);

			String key_or_value = s.get(2);
			if (key.equals(key_or_value) || value.equals(key_or_value)) {
				String key_or_value_value = e.getValue()[0];
				if (key.equals(key_or_value)) {
					if (keyNumAndValueMap.containsKey(num)) {
						String code = ErrorCode.InvalidParameterFilterNameSameKey;
						String message = "the Parameter is invalid,request parmeter .N can not same";
						throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST);
					}
					if (key_value_list.contains(key_or_value_value)) {
						String code = ErrorCode.InvalidParameterValueKeySameValue;
						String message = String.format("the Parameter is invalid,request value %s can not same", keyOrValueKey, key_or_value_value);
						throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST);
					}
					key_value_list.add(key_or_value_value);
					keyNumAndValueMap.put(num, key_or_value_value);
				} else {
					if (valueNumAndValueMap.containsKey(num)) {
						String code = ErrorCode.InvalidParameterFilterNameSameKey;
						String message = "the Parameter is invalid,request parmeter .N can not same";
						throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST);
					}
					if (value_value_list.contains(key_or_value_value)) {
						String code = ErrorCode.InvalidParameterValueValueSameValue;
						String message = String.format("the Parameter is invalid,request value %s can not same", keyOrValueKey, key_or_value_value);
						throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST);
					}
					value_value_list.add(key_or_value_value);
					valueNumAndValueMap.put(num, key_or_value_value);
				}
			} else {
				String code = ErrorCode.InvalidParameter;
				String message = "the Parameter is invalid,refer to the example";
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		// 分别从value_map key_map取出对应一样的key,
		for (Entry<String, String> entry : keyNumAndValueMap.entrySet()) {
			if (valueNumAndValueMap.containsKey(entry.getKey())) {
				key_value_map.put(entry.getValue(), valueNumAndValueMap.get(entry.getKey()));
			} else {
				String code = ErrorCode.InvalidParameterFilterNameMatch;
				String message = String.format("the Parameter is invalid,"
						+ "request parameter %s have no value to match", prefix + "." + entry.getKey() + ".Name");
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		// 判断key与value的N数量是否一致
		for (String valueNum : valueNumAndValueMap.keySet()) {
			if (!keyNumAndValueMap.containsKey(valueNum)) {
				String code = ErrorCode.InvalidParameterFilterValueMatch;
				String message = String.format("the Parameter is invalid,request parameter " + prefix + ".%s." + value + " have no "
						+ "name to match " + prefix + ".%s." + key, valueNum, valueNum);
				throw new OpenAPIException(code, message,
						HttpStatus.BAD_REQUEST);
			}
		}
		return key_value_map;
	}

	/**
	 * 校验数字N合法性
	 * */
	private static void checkFilterNum(String num) {
		if (StringUtils.isEmpty(num)) {
			String code = ErrorCode.InvalidParameterFilterMalformedIndex;
			String message = "the Parameter is invalid,.N can not be empty";
			throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST);
		}
		if (!StringUtils.isNumeric(num)) {
			String code = ErrorCode.InvalidParameterFilterMalformedIndex;
			String message = "the Parameter is invalid,"
					+ ".N must be number";
			throw new OpenAPIException(code, message,
					HttpStatus.BAD_REQUEST);
		}
		if (num.length() > 4) {
			String code = ErrorCode.InvalidParameterFilterMaxIndexDigits;
			String message = "the Parameter is invalid,"
					+ ".N must be =<1000";
			throw new OpenAPIException(code, message,
					HttpStatus.BAD_REQUEST);
		}
		int count_num = Integer.parseInt(num);
		if (count_num > MAX_INSTANCE_IDS || count_num < 1) {
			String code = ErrorCode.InvalidParameterFilterIndexRange;
			String message = "the Parameter is invalid," + ""
					+ ".N must be <=1000 and >=1";
			throw new OpenAPIException(code, message,
					HttpStatus.BAD_REQUEST);
		}
	}

	private static void checkKey(Entry<String, String[]> e) {
		if (StringUtils.isBlank(e.getValue()[0])) {
			String code = ErrorCode.InvalidParameterValueNameNotEmptyValue;
			String message = "the Parameter is invalid,"
					+ "value can not be empty";
			throw new OpenAPIException(code, message,
					HttpStatus.BAD_REQUEST);
		}
		if (e.getValue().length > 1) {
			String code = ErrorCode.InvalidParameterFilterNameSameKey;
			String message = String.format("the Parameter %s is "
					+ "invalid,request parmeter can not same", e.getKey());
			throw new OpenAPIException(code, message,
					HttpStatus.BAD_REQUEST);
		}
	}

	public static void validateId(String id) {
		if (!PATTERN_RESOURCE_ID.matcher(id).find()) {
			throw new OpenAPIException(ErrorCode.InvalidId, String.format(
					"Id %s value is invalid.", id), HttpStatus.BAD_REQUEST);

		}
	}

	public static void buildAwsParam(HttpServletRequest request, AwsParamKindEnum awsParamKindEnum, String filterField, String instanceIdField) {
		Optional.ofNullable(awsParamKindEnum).orElseThrow(
				() -> new OpenAPIException(ErrorCode.InvalidParameter, "awsParamKindEnum not empty", HttpStatus.BAD_REQUEST)
		);

		List<String> list = new ArrayList<>();
		if (AwsParamKindEnum.FILTER.equals(awsParamKindEnum)) {
			Optional.ofNullable(filterField).orElseThrow(
					() -> new OpenAPIException(ErrorCode.InvalidParameter,
							"filterField not empty when awsParamKindEnum is FILTER", HttpStatus.BAD_REQUEST)
			);
			list = parseFilterParamter(request, AWSParameterParser._PARAMETER_NETWORKINTERFACEID).get(filterField);
		} else {
			Optional.ofNullable(instanceIdField).orElseThrow(
					() -> new OpenAPIException(ErrorCode.InvalidParameter,
							"instanceIdField not empty when awsParamKindEnum is INSTANCE_ID", HttpStatus.BAD_REQUEST)
			);
			list = parseInstanceId(request, instanceIdField);
		}
	}
}
