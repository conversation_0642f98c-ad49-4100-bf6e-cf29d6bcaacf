package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.acl.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.service.cfwService.AclService;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class AclController {
    @Autowired
    private AclService aclService;

    @Autowired
    private RedissonTemplate redissonTemplate;

    /**
     * 查询地域信息
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeZone"})
    @ResponseBody
    public DescribeZoneResponse describeZone() {
        return aclService.describeZone();
    }

    /**
     * 创建规则
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=CreateCfwAcl"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse createCfwAcl(@RequestBody @Valid CreateCfwAclParam param) throws Exception {
        return aclService.createCfwAcl(param);
    }

    /**
     * 删除规则
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DeleteCfwAcl"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse deleteCfwAcl(@RequestBody @Valid DeleteAclParam param) throws CfwException {
        String key = String.format(RedisConstants.ACL_FWID, param.getCfwInstanceId());
        RLock lock = redissonTemplate.getRedissonClient().getLock(key);
        boolean lockResult = lock.tryLock();
        if (lockResult) {
            try {
                return aclService.deleteCfwAcl(param.getAclIds());
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.forceUnlock();
                }
            }
        }else{
            throw new CfwException("不能同时操作一个Acl");
        }
    }

    /**
     * 修改规则
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwAcl"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwAcl(@RequestBody @Valid ModifyCfwAclParam param) throws Exception {
        return aclService.modifyCfwAcl(param);
    }

    /**
     * 查询规则
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCfwAcl"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeCfwAclResponse describeCfwAcl(@RequestBody @Valid DescribeCfwAclParam param) throws Exception {
        return aclService.describeCfwAcl(param);
    }

    /**
     * 修改优先级
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=AlterAclPriority"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse alterAclPriority(@RequestBody @Valid AlterAclPriorityParam param) throws Exception {
        String key = String.format(RedisConstants.ACL_FWID,param.getCfwInstanceId());
        RLock lock = redissonTemplate.getRedissonClient().getLock(key);
        boolean lockResult = lock.tryLock();
        if(lockResult){
            try {
                return aclService.alterAclPriority(param);
            }finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.forceUnlock();
                }
            }
        }else {
            throw new CfwException("不能同时操作一个Acl");
        }
    }

    /**
     * ACL重置命中数
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ResetCfwAclHitCount"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse resetCfwAclHitCount(@RequestBody @Valid AclIdParam param) {
        return aclService.resetCfwAclHitCount(param);
    }

    /**
     * ACL开启和关闭
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=AlterCfwAclStatus"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse alterCfwAclStatus(@RequestBody @Valid AlterCfwAclStatusParam param) throws Exception {
        return aclService.alterCfwAclStatus(param);
    }

}
