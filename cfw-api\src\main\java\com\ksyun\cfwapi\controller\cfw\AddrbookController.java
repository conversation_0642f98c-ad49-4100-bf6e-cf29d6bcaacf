package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.addrbook.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.service.cfwService.AddrbookService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class AddrbookController {
    @Autowired
    private AddrbookService addrbookService;

    /**
     * 创建地址簿
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=CreateCfwAddrbook"}, method = RequestMethod.POST)
    @ResponseBody
    public CreateAddrbookResponse createCfwAddrbook(@RequestBody @Valid CreateCfwAddrbookParam param) throws Exception {
        return addrbookService.createCfwAddrbook(param);
    }

    /**
     * 创建地址簿
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwAddrbook"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwAddrbook(@RequestBody @Valid ModifyCfwAddrbookParam param) throws CfwException {
        return addrbookService.modifyCfwAddrbook(param);
    }

    /**
     * 删除地址簿
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DeleteCfwAddrbook"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse deleteCfwAddrbook(@RequestBody @Valid DeleteCfwAddrbookParam param) throws CfwException {
        return addrbookService.deleteCfwAddrbook(param);
    }

    /**
     * 修改服务组
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCfwAddrbook"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeAddrbookResponse describeCfwAddrbook(@RequestBody @Valid DescribeAddrbookParam param) {
        return addrbookService.describeCfwAddrbook(param);
    }

}
