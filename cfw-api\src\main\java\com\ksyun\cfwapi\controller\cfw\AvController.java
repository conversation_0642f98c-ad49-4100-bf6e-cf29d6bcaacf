package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.av.DescribeCfwAvParam;
import com.ksyun.cfwapi.domain.av.DescribeCfwAvResponse;
import com.ksyun.cfwapi.domain.av.ModifyCfwAvParam;
import com.ksyun.cfwapi.service.cfwService.AvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class AvController {
    @Autowired
    private AvService avService;

    /**
     * 查询防病毒
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCfwAv"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeCfwAvResponse describeCfwAv(@RequestBody @Valid DescribeCfwAvParam param) {
        return avService.describeCfwAv(param);
    }

    /**
     * 修改防病毒
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwAv"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwAv(@RequestBody @Valid ModifyCfwAvParam param) throws Exception {
        return avService.modifyCfwAv(param);
    }

  /*
    @RequestMapping(params = {"Action=CreateCfwAv"})
    public void createCfwAv(String fwId) {
        avService.createCfwAv(fwId);
    }*/
}