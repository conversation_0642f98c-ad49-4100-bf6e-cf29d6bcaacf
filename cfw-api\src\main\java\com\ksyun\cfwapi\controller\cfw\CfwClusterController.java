package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.cluster.ClusterOperationParam;
import com.ksyun.cfwapi.domain.cluster.UpdateStartParam;
import com.ksyun.cfwapi.service.cfwService.CfwClusterService;
import com.ksyun.cfwcore.rabbitmq.SyncMessageSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class CfwClusterController {
    @Autowired
    private CfwClusterService cfwClusterService;


    @Autowired
    private SyncMessageSendService syncMessageSendService;
    /**
     * 上传文件后，发送更新
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ClusterOperation"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse clusterOperation(@RequestBody @Valid ClusterOperationParam param) throws Exception {
        return cfwClusterService.uploadFileUrl(param);
    }

    /**
     * 修改启动参数
     * @return
     */
    @RequestMapping(params = {"Action=UpdateStartConfig"}, method = RequestMethod.POST)
    public void updateStartConfig(@RequestBody UpdateStartParam param) {
        cfwClusterService.updateStartParam(param);
    }

}
