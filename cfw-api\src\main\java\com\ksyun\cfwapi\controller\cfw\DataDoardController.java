package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.dataDoard.*;
import com.ksyun.cfwapi.domain.fw.DescribeFireWallLBParam;
import com.ksyun.cfwapi.domain.fw.DescribeFireWallResponse;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.service.cfwService.DataDoardService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class DataDoardController {
    @Autowired
    private DataDoardService dataDoardService;
    /**
     * 查询访问控制日志
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeAclLog"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeLogResponse<CfwAclLog> describeAclLog(@RequestBody @Valid DescribeLogParam param) throws CfwException {
        return dataDoardService.describeAclLog(param);
    }

    /**
     * 查询攻击日志
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeAttackLog"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeLogResponse<CfwAttackLog> describeAttackLog(@RequestBody @Valid DescribeLogParam param) throws CfwException {
        return dataDoardService.describeAttackLog(param);
    }

    /**
     * 查询流量日志
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeTrafficLog"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeLogResponse<CfwTrafficLog> describeTrafficLog(@RequestBody @Valid DescribeLogParam param) throws CfwException {
        return dataDoardService.describeFlowLog(param);
    }

}
