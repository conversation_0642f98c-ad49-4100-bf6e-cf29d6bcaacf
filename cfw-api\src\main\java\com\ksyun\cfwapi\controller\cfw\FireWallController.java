package com.ksyun.cfwapi.controller.cfw;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.TradeConvert;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.fw.*;
import com.ksyun.cfwapi.service.cfwService.FireWallLBService;
import com.ksyun.cfwcore.config.RegionConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwapi.deadletter.DeadLetterSendService;
import com.ksyun.cfwapi.deadletter.domain.ProcessingOrderParam;
import com.ksyun.cfwapi.deadletter.enums.DeadLetterQueueInfo;
import com.ksyun.cfwcore.enums.*;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.trade.wapper.FwNotifyService;
import com.ksyun.cfwcore.trade.wapper.TradeWapper;
import com.ksyun.cfwcore.trade.wapper.domain.CreateOrderParam;
import com.ksyun.cfwcore.trade.wapper.domain.CreateProductParam;
import com.ksyun.cfwcore.trade.wapper.domain.KfwProductInfo;
import com.ksyun.comm.thirdpart.trade.api.domain.ProductItem;
import com.ksyun.comm.thirdpart.trade.enums.OrderType;
import com.ksyun.comm.thirdpart.trade.enums.OrderUse;
import com.ksyun.comm.thirdpart.trade.enums.ProductUse;
import com.ksyun.comm.thirdpart.trade.enums.ProductWhat;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Locale;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class FireWallController {
    @Autowired
    private FireWallLBService fireWallLBService;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private TradeWapper tradeWapper;

    @Autowired
    private RegionConfig regionConfig;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private FwNotifyService fwNotifyService;
    @Autowired
    private DeadLetterSendService deadLetterSendService;
    /**
     * 创建防火墙
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=CreateCloudFireWallInstance"})
    @ResponseBody
    public OperateResponse createCloudFireWallInstance(@Valid CreateFireWallLBParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        String subOrderId = null;
        String instanceId = null;
        boolean isSuccess = true;
        try {
            // 如果子订单号为空或者订单来源非控制台并且计费方式为空
            if (StringUtil.isBlank(param.getChargeType()) && (StringUtil.isBlank(param.getSubOrderId())
                    || !Constants.CONSOLE_SOURCE.equals(param.getSource()))) {
                String code = ErrorCode.ChargeTypeEmpty;
                String message = messageSource.getMessage(code, new Object[]{}, Locale.CHINESE);
                String arg = "ChargeType";
                throw new OpenAPIException(code, message, HttpStatus.BAD_REQUEST, arg);
            }

            if (Constants.CONSOLE_SOURCE.equals(param.getSource())) {
                subOrderId = param.getSubOrderId();
                CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceBySubOrderId(param.getSubOrderId(),auth.getAccount_id());
                if (Objects.nonNull(cfwInstanceDO)) {
                    instanceId = cfwInstanceDO.getFwId();
                    if (FirewallStatusEnum.RUNNING.getStatus().equals(cfwInstanceDO.getStatus())) {
                        fwNotifyService.notifySubOrder(subOrderId, NotifySubOrderResult.SUCCESS.getValue(), instanceId, auth, "");
                        return new OperateResponse().setResult(true).setId(instanceId).setRequestId(auth.getRequest_id());
                    }
                    if (FirewallStatusEnum.ERROR.getStatus().equals(cfwInstanceDO.getStatus())) {
                        fwNotifyService.notifySubOrder(subOrderId, NotifySubOrderResult.FAIL.getValue(), instanceId, auth, "");
                        return new OperateResponse().setResult(false).setId(instanceId).setRequestId(auth.getRequest_id());
                    }
                    return new OperateResponse().setResult(true).setId(instanceId).setRequestId(auth.getRequest_id());
                }
            } else {
                subOrderId = this.createProductAndOrder(auth, param);
            }
            OperateResponse result = fireWallLBService.createFireWall(param, subOrderId);
            if (Objects.nonNull(result) && Objects.nonNull(result.getId())) {
                instanceId = result.getId();
            }

            if (Objects.isNull(result) || !result.isResult()) {
                isSuccess = false;
            }
            return result;
        } catch (Exception e) {
            isSuccess = false;
            log.error("CreateKfw error, subOrderId:{}, instanceId:{}, auth:{}, error:{}", subOrderId, instanceId, auth, e.getStackTrace());
            throw e;
        } finally {
            if (subOrderId != null) {
                // 创建云防火墙为异步操作，此处创建成功通知订单为创建中，
                int status = isSuccess ? NotifySubOrderResult.UPDATE.getValue() : NotifySubOrderResult.FAIL.getValue();
                fwNotifyService.notifySubOrder(subOrderId, status, instanceId, auth, "");
                if(StringUtil.isNotBlank(instanceId)){
                    ProcessingOrderParam message = new ProcessingOrderParam(instanceId,subOrderId,OperateTypeEnum.CREATE.getType(), auth);
                    deadLetterSendService.sendMq(DeadLetterQueueInfo.ORDER_NOTIFY_DELAY.getRoutingKey(), message);
                    log.info("发送死信队列成功，RoutingKey:{}, message:{}", DeadLetterQueueInfo.ORDER_NOTIFY_DELAY.getRoutingKey(), message);
                }
            }
        }
    }

    /**
     * 查询防火墙
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCloudFireWallInstance"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeFireWallResponse describeCloudFireWallInstance(@RequestBody @Valid DescribeFireWallLBParam param) throws Exception {
        return fireWallLBService.queryFireWall(param.getCfwInstanceIds(),param.getRegion());
    }

    /**
     * 修改防火墙
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCloudFireWallInstance"})
    @ResponseBody
    public OperateResponse modifyCloudFireWallInstance(@Valid ModifyFireWallLBParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        InnerAPIHolder.setInstanceId(param.getInstanceId());
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceBySubOrderId(param.getSubOrderId(),auth.getAccount_id());
        if (Objects.nonNull(cfwInstanceDO)) {
            if (FirewallStatusEnum.RUNNING.getStatus().equals(cfwInstanceDO.getStatus())) {
                fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.SUCCESS.getValue(), param.getInstanceId(), auth, "");
                return new OperateResponse().setResult(true).setId(cfwInstanceDO.getFwId()).setRequestId(auth.getRequest_id());
            }
            if (FirewallStatusEnum.ERROR.getStatus().equals(cfwInstanceDO.getStatus())) {
                log.info("防火墙实例状态异常，auth：{}", auth);
                fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.FAIL.getValue(), param.getInstanceId(), auth, "");
                return new OperateResponse().setResult(false).setId(cfwInstanceDO.getFwId()).setRequestId(auth.getRequest_id());
            }
            return new OperateResponse().setResult(true).setId(cfwInstanceDO.getFwId()).setRequestId(auth.getRequest_id());
        }
        try {
            OrderOperteInfo orderOperteInfo = fireWallLBService.modifyFireWall(param);
            log.info("修改结果:{}", JSONUtil.toJsonStr(orderOperteInfo));
            if (orderOperteInfo.isSendFlag()) {
                fwNotifyService.notifySubOrder(param.getSubOrderId(), orderOperteInfo.getStatus(), param.getInstanceId(), auth, "");
            }
            return new OperateResponse().setResult(true).setId(param.getInstanceId()).setRequestId(auth.getRequest_id());
        } catch (Exception e) {
            log.error("修改防火墙失败,requestId:{},param:{},错误原因{}", auth.getRequest_id(), param, e.getStackTrace());
            fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.FAIL.getValue(), param.getInstanceId(), auth, "");
            return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(false);
        }finally {
            ProcessingOrderParam message = new ProcessingOrderParam(param.getInstanceId(), param.getSubOrderId(), OperateTypeEnum.UPDATE.getType(), auth);
            deadLetterSendService.sendMq(DeadLetterQueueInfo.ORDER_NOTIFY_DELAY.getRoutingKey(), message);
            log.info("发送死信队列成功，RoutingKey:{}, message:{}", DeadLetterQueueInfo.ORDER_NOTIFY_DELAY.getRoutingKey(), message);
        }
    }


    /**
     * 修改防火墙
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCloudFireWallFeature"})
    @ResponseBody
    public OperateResponse modifyCloudFireWallFeature(@Valid ModifyFireWallFeatureParam param) {
        return fireWallLBService.modifyCloudFireWallFeature(param);
    }

    /**
     * 删除防火墙
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DeleteCloudFireWallInstance"})
    @ResponseBody
    public OperateResponse deleteCloudFireWallInstance(@Valid DeleteFireWallParam param) {
        return fireWallLBService.deleteFireWall(param);
    }

    /**
     * 关停防火墙
     *
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=UpdateCloudFireWallInstanceState"})
    @ResponseBody
    public OperateResponse alterCloudFireWallInstanceState(@Valid AlterFireWallLBParam param) throws Exception {
        return fireWallLBService.alterCloudFireWallInstanceState(param);
    }

    /**
     * 项目分配制
     *
     * @param param
     * @return
     */
    @RequestMapping(params = {"Action=AllocatedProject"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse allocatedProject(@RequestBody @Valid AllocatedProjectParam param) {
        return fireWallLBService.allocatedProject(param);
    }

    private String createProductAndOrder(ProxyAuth auth, CreateFireWallLBParam param) {
        KfwProductInfo kfwProductInfo = TradeConvert.INSTANCE.convert2KfwProductInfo(param);
        kfwProductInfo.setBand_width(param.getBandwidth());

        List<ProductItem> items = tradeWapper.buildProductItemList(kfwProductInfo, auth);
        items.add(new ProductItem("类型", "type", "kfw"));
        // 计算bean id
        String className = this.getClass().getName();
        int lastPointIndex = className.lastIndexOf(".") + 1;
        String beanId = className.substring(lastPointIndex, lastPointIndex + 1).toLowerCase()
                + className.substring(lastPointIndex + 1);
        items.add(new ProductItem("Bean ID", "beanId", beanId));
        items.add(new ProductItem("防护带宽", "BandWidth", param.getBandwidth()));
        items.add(new ProductItem("受保护的EIP个数", "protected_EIP_count", param.getTotalEipNum()));
        items.add(new ProductItem("项目ID", "iamProjectId", param.getProjectId()));
        CreateProductParam createProductParam = new CreateProductParam(
                auth.getAccount_id(),
                regionConfig.convertRegion(auth),
                ProductType.KFW.getValue(),
                ChargeType.getValue(param.getChargeType()),
                ProductWhat.BUY.getValue(),
                ProductUse.BUY.getValue(),
                param.getPurchaseTime(),
                items,
                OrderSource.getValue(param.getSource())
        );
        String productId = tradeWapper.createProduct(createProductParam);
        log.info("***productId:{}***", productId);
        CreateOrderParam createOrderParam = new CreateOrderParam(
                auth.getAccount_id(),
                productId,
                OrderUse.BUY.getValue(),
                OrderType.BUY.getValue(),
                OrderSource.getValue(param.getSource()),
                1,
                OrderAppId.KFW.getValue()
        );
        return tradeWapper.createOrder(createOrderParam);
    }

    /**
     * 查询防火墙
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=QueryCfwInstance"})
    @ResponseBody
    public CloudFireWallInstance queryCfwInstance(@Valid QueryInstanceParam param) throws Exception {
        return fireWallLBService.queryCfwInstance(param.getInstanceId());
    }

    /**
     * 总览数据
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=QueryOverviewDetail"})
    @ResponseBody
    public OverviewDetailResponse queryOverviewDetail(@Valid OverviewDetailParam param) throws Exception {
        return fireWallLBService.queryOverviewDetail(param);
    }

    /**
     * 总览数据
     *
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=QueryCfwInstanceDetail"})
    @ResponseBody
    public InstanceDetailResponse queryCfwInstanceDetail(@Valid QueryInstanceDetailParam param) {
        return fireWallLBService.queryCfwInstanceDetail(param);
    }
}
