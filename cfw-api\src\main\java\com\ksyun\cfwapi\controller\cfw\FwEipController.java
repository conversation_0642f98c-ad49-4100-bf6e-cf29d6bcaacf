package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.eip.QueryEipsResponse;
import com.ksyun.cfwapi.domain.eip.QueryEipsResult;
import com.ksyun.cfwapi.domain.fw.DescribeCfwEipsParam;
import com.ksyun.cfwapi.domain.fw.DescribeCfwEipsResponse;
import com.ksyun.cfwapi.domain.fw.ModifyCfwEipParam;
import com.ksyun.cfwapi.service.cfwService.FireWallEipService;
import com.ksyun.cfwcore.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class FwEipController {
    @Autowired
    private FireWallEipService fireWallEipService;

    /**
     * Eip开启/关闭防护
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwEipProtect"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwEipProtect(@RequestBody @Valid ModifyCfwEipParam param) throws Exception {
        return fireWallEipService.modifyCfwEip(param);
    }

    /**
     * 查询Eip信息
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCfwEips"})
    @ResponseBody
    public QueryEipsResponse describeCfwEips(@Valid DescribeCfwEipsParam param,
                                             @RequestHeader(value = "X-KSC-SOURCE", defaultValue = Constants.SDK_SOURCE, required = false) String source) throws Exception {
        return fireWallEipService.describeCfwEips(param,source);
    }
}
