package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.ips.DescribeCfwIpsParam;
import com.ksyun.cfwapi.domain.ips.DescribeCfwIpsResponse;
import com.ksyun.cfwapi.domain.ips.IpsDictionaryResponse;
import com.ksyun.cfwapi.domain.ips.ModifyCfwIpsParam;
import com.ksyun.cfwapi.service.cfwService.IpsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class IpsController {
    @Autowired
    private IpsService ipsService;

    /**
     * 查询Ips
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeCfwIps"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeCfwIpsResponse describeCfwAv(@RequestBody @Valid DescribeCfwIpsParam param) throws Exception {
        return ipsService.describeCfwIps(param);
    }

    /**
     * 修改Ips
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwIps"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwIps(@RequestBody @Valid ModifyCfwIpsParam param) throws Exception {
        return ipsService.modifyCfwIps(param);
    }

    /**
     * 查询Ips
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=QueryIpsDictionary"})
    @ResponseBody
    public IpsDictionaryResponse queryIpsDictionary() {
        return ipsService.queryIpsDictionary();
    }


   /* @RequestMapping(params = {"Action=CreateCfwIps"})
    public void createCfwIps(String fwId) {
        ipsService.createCfwIps(fwId);
    }*/
}
