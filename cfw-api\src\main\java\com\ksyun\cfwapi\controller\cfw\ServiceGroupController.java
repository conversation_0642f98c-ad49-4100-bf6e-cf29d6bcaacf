package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.controller.AWSParameterParser;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.serviceGroup.*;
import com.ksyun.cfwapi.service.cfwService.ServiceGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class ServiceGroupController {

    @Autowired
    private ServiceGroupService serviceGroupService;
    /**
     * 创建服务组
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=CreateCfwServiceGroup"},method = RequestMethod.POST)
    @ResponseBody
    public CreateServiceGroupResponse createCfwServiceGroup(@Valid @RequestBody CreateServiceGroupParam param) throws Exception {
        return  serviceGroupService.createCfwServiceGroup(param);
    }

    /**
     * 删除服务组
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DeleteCfwServiceGroup"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse deleteCfwServiceGroup(@RequestBody @Valid DeleteServiceGroupParam param) throws Exception {
        return serviceGroupService.deleteCfwServiceGroup(param);
    }

    /**
     * 修改服务组
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ModifyCfwServiceGroup"}, method = RequestMethod.POST)
    @ResponseBody
    public OperateResponse modifyCfwServiceGroup(@RequestBody @Valid ModifyServiceGroupParam param) {
        return serviceGroupService.modifyCfwServiceGroup(param);
    }

    /**
     * 查询服务组
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=DescribeServiceGroup"}, method = RequestMethod.POST)
    @ResponseBody
    public DescribeServiceGroupResponse describeServiceGroup(@RequestBody @Valid DescribeServiceGroupParam param) {
        return serviceGroupService.describeServiceGroup(param);
    }

}
