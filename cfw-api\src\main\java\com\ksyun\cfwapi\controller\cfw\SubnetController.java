package com.ksyun.cfwapi.controller.cfw;

import com.ksyun.cfwapi.domain.fw.CreateSubnetParam;
import com.ksyun.cfwapi.dao.service.SubnetService;
import com.ksyun.cfwapi.domain.fw.CreateSubnetResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = { "Action" }, produces = {MediaType.APPLICATION_JSON_VALUE})
public class SubnetController {
    @Autowired
    private SubnetService subnetService;

    @RequestMapping(params = { "Action=CreateSubNetCfw" },method = RequestMethod.POST)
    @ResponseBody
    public List<CreateSubnetResponse> createSubNetCfw(@RequestBody @Valid CreateSubnetParam param, HttpServletRequest request) throws Exception {
        return subnetService.createSubNetCfw(param);
    }

}
