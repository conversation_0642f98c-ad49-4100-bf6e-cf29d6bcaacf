package com.ksyun.cfwapi.controller.test;

import com.ksyun.cfwapi.security.test.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 反射漏洞测试控制器
 * 专门用于演示和验证CommonUtils.getOtherFieldAllNullExceptSpecial方法中的反射漏洞
 * 
 * ⚠️ 警告：此控制器仅用于安全测试和漏洞演示，不应在生产环境中使用
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/test/reflection-vulnerability")
public class ReflectionVulnerabilityTestController {
    
    /**
     * 主要漏洞演示端点
     * 直接展示setAccessible(true)漏洞的危害
     * 
     * @param request 测试请求参数
     * @return 详细的漏洞测试结果
     */
    @PostMapping("/demo")
    public ResponseEntity<Map<String, Object>> demonstrateReflectionVulnerability(
            @RequestBody ReflectionTestRequest request) {
        
        log.warn("🚨 开始执行反射漏洞演示测试: {}", request.getTestName());
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 记录攻击者信息
            if (request.getAttackerInfo() != null) {
                log.warn("🔍 模拟攻击者信息 - ID: {}, IP: {}, 目标: {}", 
                        request.getAttackerInfo().getAttackerId(),
                        request.getAttackerInfo().getSourceIp(),
                        request.getAttackerInfo().getTarget());
            }
            
            // 执行反射漏洞测试
            ReflectionTestResult testResult = ReflectionTestUtils.getOtherFieldAllNullExceptSpecialWithLogging(
                    request.getSensitiveData(), 
                    request.getSpecialFields()
            );
            
            // 生成演示报告
            String demoReport = ReflectionTestUtils.generateVulnerabilityDemoReport(testResult);
            log.warn("📋 漏洞演示报告:\n{}", demoReport);
            
            // 构建响应
            response.put("success", true);
            response.put("testName", request.getTestName());
            response.put("testDescription", request.getTestDescription());
            response.put("testResult", testResult);
            response.put("vulnerabilityDemoReport", demoReport);
            response.put("timestamp", System.currentTimeMillis());
            
            // 漏洞确认信息
            Map<String, Object> vulnerabilityConfirmation = new HashMap<>();
            vulnerabilityConfirmation.put("vulnerabilityExists", testResult.getVulnerabilityAnalysis().isVulnerabilityExists());
            vulnerabilityConfirmation.put("riskLevel", testResult.getVulnerabilityAnalysis().getOverallRiskLevel());
            vulnerabilityConfirmation.put("privateFieldsAccessed", testResult.getTestSummary().getPrivateFieldsAccessed());
            vulnerabilityConfirmation.put("sensitiveFieldsAccessed", testResult.getTestSummary().getSensitiveFieldsAccessed());
            vulnerabilityConfirmation.put("criticalVulnerabilities", testResult.getTestSummary().getCriticalVulnerabilities());
            
            response.put("vulnerabilityConfirmation", vulnerabilityConfirmation);
            
            log.error("🚨 反射漏洞演示完成 - 漏洞存在: {}, 风险等级: {}", 
                    testResult.getVulnerabilityAnalysis().isVulnerabilityExists(),
                    testResult.getVulnerabilityAnalysis().getOverallRiskLevel());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ 反射漏洞测试执行失败", e);
            
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("errorType", e.getClass().getSimpleName());
            response.put("testName", request.getTestName());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 快速漏洞验证端点
     * 使用默认参数快速验证漏洞存在
     * 
     * @return 简化的测试结果
     */
    @GetMapping("/quick-test")
    public ResponseEntity<Map<String, Object>> quickVulnerabilityTest() {
        
        log.warn("🚨 执行快速反射漏洞验证");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 创建测试数据
            SensitiveTestData testData = new SensitiveTestData();
            
            // 执行快速测试
            ReflectionTestResult testResult = ReflectionTestUtils.quickReflectionTest(testData);
            
            // 构建响应
            response.put("success", true);
            response.put("testType", "quick-vulnerability-verification");
            response.put("vulnerabilityExists", testResult.getVulnerabilityAnalysis().isVulnerabilityExists());
            response.put("riskLevel", testResult.getVulnerabilityAnalysis().getOverallRiskLevel());
            response.put("privateFieldsAccessed", testResult.getTestSummary().getPrivateFieldsAccessed());
            response.put("sensitiveFieldsAccessed", testResult.getTestSummary().getSensitiveFieldsAccessed());
            response.put("totalFieldsAccessed", testResult.getTestSummary().getTotalFieldsAccessed());
            response.put("timestamp", System.currentTimeMillis());
            
            // 添加关键证据
            response.put("keyEvidence", testResult.getSensitiveFieldLogs());
            
            log.error("🚨 快速验证完成 - 漏洞确认: {}, 私有字段访问: {}, 敏感字段访问: {}", 
                    testResult.getVulnerabilityAnalysis().isVulnerabilityExists(),
                    testResult.getTestSummary().getPrivateFieldsAccessed(),
                    testResult.getTestSummary().getSensitiveFieldsAccessed());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("❌ 快速漏洞验证失败", e);
            
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.status(500).body(response);
        }
    }
    
    /**
     * 权限提升攻击演示端点
     * 
     * @return 权限提升攻击测试结果
     */
    @PostMapping("/privilege-escalation")
    public ResponseEntity<Map<String, Object>> demonstratePrivilegeEscalation() {
        
        log.warn("🚨 执行权限提升攻击演示");
        
        ReflectionTestRequest request = ReflectionTestRequest.createPrivilegeEscalationRequest();
        return demonstrateReflectionVulnerability(request);
    }
    
    /**
     * 数据渗透攻击演示端点
     * 
     * @return 数据渗透攻击测试结果
     */
    @PostMapping("/data-exfiltration")
    public ResponseEntity<Map<String, Object>> demonstrateDataExfiltration() {
        
        log.warn("🚨 执行数据渗透攻击演示");
        
        ReflectionTestRequest request = ReflectionTestRequest.createDataExfiltrationRequest();
        return demonstrateReflectionVulnerability(request);
    }
    
    /**
     * 获取测试帮助信息
     * 
     * @return 测试端点使用说明
     */
    @GetMapping("/help")
    public ResponseEntity<Map<String, Object>> getTestHelp() {
        
        Map<String, Object> help = new HashMap<>();
        help.put("title", "CFW反射漏洞测试端点说明");
        help.put("description", "用于演示和验证CommonUtils.getOtherFieldAllNullExceptSpecial方法中的setAccessible滥用漏洞");
        
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("POST /api/test/reflection-vulnerability/demo", "主要漏洞演示端点，需要提供ReflectionTestRequest参数");
        endpoints.put("GET /api/test/reflection-vulnerability/quick-test", "快速漏洞验证，使用默认参数");
        endpoints.put("POST /api/test/reflection-vulnerability/privilege-escalation", "权限提升攻击演示");
        endpoints.put("POST /api/test/reflection-vulnerability/data-exfiltration", "数据渗透攻击演示");
        endpoints.put("GET /api/test/reflection-vulnerability/help", "获取帮助信息");
        
        help.put("endpoints", endpoints);
        
        Map<String, String> warnings = new HashMap<>();
        warnings.put("security", "⚠️ 这些端点仅用于安全测试，不应在生产环境中使用");
        warnings.put("vulnerability", "🚨 这些端点会故意触发反射漏洞，用于演示安全风险");
        warnings.put("logging", "📝 所有测试活动都会被详细记录在日志中");
        
        help.put("warnings", warnings);
        help.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(help);
    }
}
