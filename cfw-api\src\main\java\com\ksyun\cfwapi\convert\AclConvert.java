package com.ksyun.cfwapi.convert;

import com.ksyun.cfwapi.dao.entity.*;
import com.ksyun.cfwapi.domain.acl.*;

import com.ksyun.cfwapi.domain.addrbook.CreateCfwAddrbookParam;
import com.ksyun.cfwapi.domain.etcd.*;
import com.ksyun.cfwapi.domain.serviceGroup.CfwServiceGroup;
import com.ksyun.cfwapi.domain.serviceGroup.CreateServiceGroupParam;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(imports = {GUIDGeneratorUtil.class, Date.class})
public interface AclConvert {
    AclConvert INSTANCE = Mappers.getMapper(AclConvert.class);

    @Mappings({
            @Mapping(target = "serviceGroupId", expression = "java(GUIDGeneratorUtil.generateGUID())"),
            @Mapping(target = "fwId", source = "param.cfwInstanceId"),
            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "updateTime", expression = "java(new Date())"),
            @Mapping(target = "deleteStatus", constant = "0")
    })
    CfwServicegroupDO convert2CfwServicegroupDO(CreateServiceGroupParam param, String accountId, String service);

    CfwServiceGroup convert2CfwServiceGroup(CfwServicegroupDO servicegroup, List<String> serviceInfo);

    @Mappings({
            @Mapping(target = "aclId", expression = "java(GUIDGeneratorUtil.generateGUID())"),
            @Mapping(target = "fwId", source = "param.cfwInstanceId"),
            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "updateTime", expression = "java(new Date())"),
            @Mapping(target = "hitCount", constant = "0L"),
            @Mapping(target = "deleteStatus", constant = "0")
    })
    CfwAclDO convert2CfwAclDO(CreateCfwAclParam param, String accountId, String srcIp, String destIp, String srcAddrbook, String destAddrbook, String srcZone, String serviceInfo, String serviceGroup, Integer priority, String appValue, String destZone);

   /* CfwAcl convertParam2CfwAclDO(CreateCfwAclParam param, String aclId, Integer priority);*/

    @Mappings({
            @Mapping(target = "updateTime", expression = "java(new Date())"),
            @Mapping(target = "deleteStatus", constant = "0")
    })
    CfwAclDO convert2CfwAclDOForUpdate(ModifyCfwAclParam param, String accountId, String srcIp, String destIp, String srcAddrbook, String destAddrbook, String srcZone, String serviceInfo, String serviceGroup,String fwId,Long id,Date createTime,Long hitCount,Integer priority,String appValue,String destZone);

    CfwAclRelateDO convert2CfwAclRelateDO(String aclId, String relateId, String relateType, Date createTime);

    @Mappings({
            @Mapping(target = "addrbookId", expression = "java(GUIDGeneratorUtil.generateGUID())"),
            @Mapping(target = "fwId", source = "param.cfwInstanceId"),
            @Mapping(target = "ipAddress", source = "ipAddressStr"),
            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "updateTime", expression = "java(new Date())"),
            @Mapping(target = "deleteStatus", constant = "0")
    })
    CfwAddrbookDO convert2CfwAddrbookDO(CreateCfwAddrbookParam param, String ipAddressStr, String accountId);

    @Mappings({
            @Mapping(target = "cfwInstanceId", source = "fwId"),
    })
    CfwAcl convert2CfwAcl(CfwAclDO cfwAclDO);

    @Mappings({
            @Mapping(target = "id", source = "addrbook.addrbookId"),
            @Mapping(target = "name", source = "addrbook.addrbookId"),
            @Mapping(target = "description", constant = ""),
            @Mapping(target = "addr_value", source = "addrbook.ipAddress")
    })
    AddrbookEtcd convert2AddrbookEtcd(CfwAddrbookDO addrbook,String traceId);

    @Mappings({
            @Mapping(target = "operationId",expression = "java(GUIDGeneratorUtil.generateGUID())")
    })
    WallChangeOperationEtcd convert2WallChangeEtcd(String traceId, String type, String action, List<String> instanceids, String timestamp);

    @Mappings({
            @Mapping(target = "id", source = "serviceGroupDO.serviceGroupId"),
            @Mapping(target = "name", source = "serviceGroupDO.serviceGroupId"),
            @Mapping(target = "description", constant = ""),
            @Mapping(target = "service", source = "serviceInfo")
    })
    ServiceGroupEtcd convert2ServiceGroupEtcd(CfwServicegroupDO serviceGroupDO, String traceId,List<String> serviceInfo);

    @Mappings({
            @Mapping(target = "id", source = "aclDO.ruleId"),
            @Mapping(target = "name", source = "aclDO.aclId"),
            @Mapping(target = "src_type", source = "aclDO.srcType"),
            @Mapping(target = "dest_type", source = "aclDO.destType"),
            @Mapping(target = "service_type", source = "aclDO.serviceType"),
            @Mapping(target = "app_type", source = "aclDO.appType"),
            @Mapping(target = "ip_version", constant = "4"),
            @Mapping(target = "description", constant = ""),
            @Mapping(target = "priority", source = "priority")
    })
    AclEtcd convert2AclEtcd(CfwAclDO aclDO, String traceId,String priority);

    PriorityEtcd convert2PriorityEtcd(String id, String name, String traceId, String priority);
}
