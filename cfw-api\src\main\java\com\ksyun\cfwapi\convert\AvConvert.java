package com.ksyun.cfwapi.convert;

import com.ksyun.cfwapi.dao.entity.CfwAvDO;
import com.ksyun.cfwapi.domain.av.CfwAv;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

@Mapper(imports = {GUIDGeneratorUtil.class, Date.class})
public interface AvConvert {
    AvConvert INSTANCE = Mappers.getMapper(AvConvert.class);

    List<CfwAv> convert2CfwAvList(List<CfwAvDO> cfwAvDOList);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "updateTime", expression = "java(new Date())")
    })
    CfwAvDO convert2CfwAvDO(String avId, String fwId, String protocol, String protectType,String status,String accountId);
}
