package com.ksyun.cfwapi.convert;

import com.ksyun.cfwapi.dao.entity.CfwEtcdDO;
import com.ksyun.cfwapi.domain.cluster.ClusterOperationParam;
import com.ksyun.cfwapi.domain.etcd.FirewallCheckEtcd;
import com.ksyun.cfwapi.domain.etcd.WallclusterEtcd;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;

@Mapper(imports = {GUIDGeneratorUtil.class, Date.class})
public interface EtcdConvert {
    EtcdConvert INSTANCE = Mappers.getMapper(EtcdConvert.class);

    @Mappings({
            @Mapping(target = "operationId", expression = "java(GUIDGeneratorUtil.generateGUID())"),
            @Mapping(target = "traceId", source = "requestId"),
            @Mapping(target = "fileName", source = "param.fileName"),
            @Mapping(target = "action", source = "param.action")
    })
    WallclusterEtcd convert2WallclusterEtcd(String requestId,String md5, ClusterOperationParam param,String type);

    @Mappings({
            @Mapping(target = "createTime", expression = "java(new Date())")
    })
    CfwEtcdDO convert2CfwEtcdDO(String etcdKey, String etcdValue, String accountId, Integer status);

    @Mappings({
            @Mapping(target = "repair_type", source = "repairType")
    })
    FirewallCheckEtcd convert2FirewallCheckEtcd(String timestamp, String traceId, String fwId, String type, String action,String repairType);
}
