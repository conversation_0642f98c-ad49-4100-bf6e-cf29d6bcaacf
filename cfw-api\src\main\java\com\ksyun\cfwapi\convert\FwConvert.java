package com.ksyun.cfwapi.convert;

import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.domain.fw.CloudFireWallInstance;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FwConvert {
    FwConvert INSTANCE = Mappers.getMapper(FwConvert.class);
    @Mappings({
            @Mapping(target = "instanceId", source = "fwId")
    })
    CloudFireWallInstance toCloudFireWallInstance(CfwInstanceDO cfwInstanceDO);
}
