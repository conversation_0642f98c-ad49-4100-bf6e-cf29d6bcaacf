package com.ksyun.cfwapi.convert;

import com.ksyun.cfwapi.dao.entity.CfwDictionaryDO;
import com.ksyun.cfwapi.dao.entity.CfwIpsDO;
import com.ksyun.cfwapi.domain.CfwDictionary;
import com.ksyun.cfwapi.domain.etcd.IpsEtcd;
import com.ksyun.cfwapi.domain.ips.CfwIps;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(imports = {GUIDGeneratorUtil.class, Date.class})
public interface IpsConvert {
    IpsConvert INSTANCE = Mappers.getMapper(IpsConvert.class);

    CfwIps convert2CfwIps(CfwIpsDO cfwIpsDO);

    IpsEtcd convert2IpsEtcd(CfwIpsDO cfwIpsDO);

    @Mappings({
            @Mapping(target = "ipsId", expression = "java(GUIDGeneratorUtil.generateGUID())"),
            @Mapping(target = "createTime", expression = "java(new Date())"),
            @Mapping(target = "updateTime", expression = "java(new Date())"),
    })
    CfwIpsDO convert2CfwIpsDO(String fwId, String mode, String status, String accountId);

    List<CfwDictionary> convert2CfwDictionaryList(List<CfwDictionaryDO> ipsDicDOList);

    @Mappings({
            @Mapping(target = "desc", source = "description")
    })
    CfwDictionary convert2CfwDictionary(CfwDictionaryDO ipsDicDO);
}
