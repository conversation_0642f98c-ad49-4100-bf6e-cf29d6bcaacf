package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 
 * @TableName cfw_acl_relate
 */
@TableName(value ="cfw_acl_relate")
@Data
@Accessors(chain = true)
public class CfwAclRelateDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "acl_id")
    private String aclId;

    /**
     * 
     */
    @TableField(value = "relate_id")
    private String relateId;

    /**
     * servicegroup,addrbook
     */
    @TableField(value = "relate_type")
    private String relateType;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}