package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 地址簿
 * @TableName cfw_addrbook
 */
@TableName(value ="cfw_addrbook")
@Data
public class CfwAddrbookDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String addrbookId;

    /**
     * 墙Id
     */
    private String fwId;

    /**
     * 
     */
    private String accountId;

    /**
     * 
     */
    private String addrbookName;

    /**
     * 
     */
    private String ipVersion;

    /**
     * IP地址值
     */
    private String ipAddress;

    /**
     * 描述
     */
    private String description;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 被引用次数
     */
    private Integer deleteStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}