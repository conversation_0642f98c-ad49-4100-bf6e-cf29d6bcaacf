package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 防病毒
 * @TableName cfw_av
 */
@TableName(value ="cfw_av")
@Data
public class CfwAvDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "av_id")
    private String avId;

    /**
     * 墙Id
     */
    @TableField(value = "fw_id")
    private String fwId;

    /**
     * 
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 协议(HTTP,SMTP,FTP,POP3,SMB,IMAP)
     */
    @TableField(value = "protocol")
    private String protocol;


    /**
     * 监听：monitor/拦截：block
     */
    @TableField(value = "protect_type")
    private String protectType;


    /**
     * 状态(start|stop)
     */
    @TableField(value = "status")
    private String status;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}