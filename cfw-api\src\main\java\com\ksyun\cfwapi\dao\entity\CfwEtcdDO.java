package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * <AUTHOR>
 * @TableName cfw_etcd
 */
@TableName(value ="cfw_etcd")
@Data
public class CfwEtcdDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "etcd_key")
    private String etcdKey;

    /**
     * 
     */
    @TableField(value = "etcd_value")
    private String etcdValue;

    /**
     * 1：已执行，0：未执行
     */
    @TableField(value = "status")
    private Integer status;

    /**
     *
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}