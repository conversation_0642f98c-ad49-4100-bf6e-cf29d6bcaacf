package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName cfw_ips
 */
@TableName(value ="cfw_ips")
@Data
public class CfwIpsDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    @TableField(value = "ips_id")
    private String ipsId;

    /**
     * 墙Id
     */
    @TableField(value = "fw_id")
    private String fwId;

    /**
     * 
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 模式(观察observer|普通normal|严格strict|宽松loose)
     */
    @TableField(value = "mode")
    private String mode;

    /**
     * 状态(start|stop)
     */
    @TableField(value = "status")
    private String status;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}