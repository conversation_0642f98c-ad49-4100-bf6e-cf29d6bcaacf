package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@TableName(value = "cfw_region_config")
@Data
public class CfwRegionConfigDO implements Serializable {
    private static final long serialVersionUID = -436335288645439063L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "region")
    private String region;

    @TableField(value = "ks3_endpoint")
    private String ks3Endpoint;

    @TableField(value = "ks3_bucket")
    private String ks3Bucket;

    @TableField(value = "image_id")
    private String imageId;

    @TableField(value = "esgw_in_ip")
    private String esgwInIp;

    @TableField(value = "esgw_out_ip")
    private String esgwOutIp;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "delete_status")
    private Integer deleteStatus;

    @TableField(value = "ecs_type")
    private String ecsType;

    @TableField(value = "kafka_cert_path")
    private String kafkaCertPath;

    @TableField(value = "kafka_cert_md5")
    private String kafkaCertMd5;


}
