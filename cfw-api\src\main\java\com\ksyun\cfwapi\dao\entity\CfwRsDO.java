package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 
* @TableName cfw_rs
*/
@Data
@TableName("cfw_rs")
public class CfwRsDO implements Serializable {

    /**
    * 
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *
     */
    private String fwInstanceId;
    /**
    * fwId
    */
    private String fwId;
    /**
    * 
    */
    private String lbRsId;
    /**
     *
     */
    private String region;

    /**
    * 
    */
    private String accountId;
    /**
    * 
    */
    private String fwLbId;
    /**
    * 云主机ID
    */
    private String kecId;
    /**
    * 子网ID
    */
    private String subnetId;
    /**
    * 安全组ID
    */
    private String sgId;
    /**
    * 镜像ID
    */
    private String imageId;
    /**
    * 山石实例规格如VM01
    */
    private String hsType;
    /**
    * rs状态 1创建中 2创建成功 3创建失败
    */
    private Integer rsStatus;
    /**
    * 是否删除 0-未删，1-已删
    */
    private Integer deleteStatus;
    /**
    * 不健康次数
    */
    private Integer unhealthTimes;
    /**
    * 
    */
    private Date createTime;
    /**
    * 
    */
    private Date updateTime;
}
