package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* rs网卡
* @TableName cfw_rs_vif
*/
@TableName("cfw_rs_vif")
@Data
public class CfwRsVifDO implements Serializable {

    /**
    * 
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 
    */
    private String rsId;
    /**
    * 
    */
    private String kecId;
    /**
    * 
    */
    private String novaVifId;
    /**
    * 
    */
    private String vifMac;
    /**
    * primary, extension
    */
    private String vifType;
    /**
    * etcd，log，in，out
    */
    private String useTo;
    /**
    * 
    */
    private String ip;
    /**
    * 
    */
    private Date createTime;
    /**
    * 
    */
    private Date updateTime;

}
