package com.ksyun.cfwapi.dao.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName cfw_servicegroup
 */
@Data
@TableName("cfw.cfw_servicegroup")
public class CfwServicegroupDO implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 
     */
    private String serviceGroupId;

    /**
     * 
     */
    private String accountId;

    /**
     * 
     */
    private String serviceGroupName;

    /**
     * 
     */
    private String service;

    /**
     * 
     */
    private String description;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 墙Id
     */
    private String fwId;

    /**
     * 删除标识
     */
    private Integer deleteStatus;

    private static final long serialVersionUID = 1L;
}