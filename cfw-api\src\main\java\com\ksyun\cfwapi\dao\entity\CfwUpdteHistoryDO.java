package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName cfw_updte_history
 */
@TableName(value ="cfw_updte_history")
@Data
public class CfwUpdteHistoryDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 实例ID
     */
    @TableField(value = "fw_id")
    private String fwId;

    /**
     * 
     */
    @TableField(value = "sub_order_id")
    private String subOrderId;

    /**
     * 
     */
    @TableField(value = "old_sub_order_id")
    private String oldSubOrderId;

    /**
     * 套餐
     */
    @TableField(value = "old_instance_type")
    private String oldInstanceType;

    /**
     * 带宽值
     */
    @TableField(value = "old_bandwidth")
    private Integer oldBandwidth;

    /**
     * 可防护ip总数
     */
    @TableField(value = "old_total_eip_num")
    private Integer oldTotalEipNum;

    /**
     * 1-执行、2-复原
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     *
     */
    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}