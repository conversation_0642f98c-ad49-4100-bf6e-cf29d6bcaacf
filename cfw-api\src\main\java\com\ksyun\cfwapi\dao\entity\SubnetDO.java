package com.ksyun.cfwapi.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 
* <AUTHOR>
 * @TableName cfw_subnet
*/
@Data
@TableName("cfw_subnet")
public class SubnetDO implements Serializable {

    private static final long serialVersionUID = -1237690215134046301L;
    /**
    * 主键Id
    */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
    * 
    */
    private String subnetId;

    /**
    * 
    */
    private String vpcId;

    /**
    * 编码长度不能超过20
    */
    private String cidr;

    /**
    * 状态 1未使用 2已使用
    */
    private Integer status;
    /**
    * 
    */
    private Date createTime;
    /**
    * 
    */
    private Date updateTime;

    /**
     *
     */
    private String subnetIp;


    /**
     * 安全组id
     */
    private String securityGroupId;

    /**
     * 机房
     */
    private String region;
}
