package com.ksyun.cfwapi.dao.mapper;

import com.ksyun.cfwapi.dao.entity.CfwAclDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.cfwapi.dao.entity.StatisticsDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface CfwAclMapper extends BaseMapper<CfwAclDO> {

    List<StatisticsDO> getAclCountByFwIds(@Param("list") List<String> fwIds);

    void batchUpdateHitCount(@Param("list") List<CfwAclDO> aclList);

    long countAclDeny(String fwId);
}




