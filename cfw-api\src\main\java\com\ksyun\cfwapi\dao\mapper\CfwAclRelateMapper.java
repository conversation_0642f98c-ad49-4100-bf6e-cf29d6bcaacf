package com.ksyun.cfwapi.dao.mapper;

import com.ksyun.cfwapi.dao.entity.CfwAclRelateDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ksyun.cfwapi.dao.entity.CitationCountDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_acl_relate】的数据库操作Mapper
* @createDate 2024-12-25 11:00:54
* @Entity com.ksyun.cfwapi.dao.CfwAclRelate
*/
public interface CfwAclRelateMapper extends BaseMapper<CfwAclRelateDO> {

    List<CitationCountDO> getCitationCountByRelateIds(@Param("relateIds") List<String> relateIds);
}




