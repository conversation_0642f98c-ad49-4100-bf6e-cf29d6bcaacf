package com.ksyun.cfwapi.dao.service;

import com.ksyun.cfwapi.dao.entity.CfwAclRelateDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.dao.entity.CitationCountDO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_acl_relate】的数据库操作Service
* @createDate 2024-12-25 11:00:54
*/
public interface CfwAclRelateService extends IService<CfwAclRelateDO> {

    void deleteAclrelate(List<String> cfwAclIdRelates);

    List<CitationCountDO> getCitationCountByRelateIds(List<String> addrbookIds);

    boolean existByRelateId(String relateId);
}
