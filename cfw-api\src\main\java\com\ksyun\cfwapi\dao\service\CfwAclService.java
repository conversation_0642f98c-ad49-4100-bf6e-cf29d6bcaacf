package com.ksyun.cfwapi.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.dao.entity.CfwAclDO;
import com.ksyun.cfwapi.dao.entity.CfwAclRelateDO;
import com.ksyun.cfwapi.dao.entity.StatisticsDO;
import com.ksyun.cfwapi.domain.acl.DescribeCfwAclParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【cfw_acl】的数据库操作Service
 * @createDate 2024-12-20 15:24:00
 */
public interface CfwAclService extends IService<CfwAclDO> {

    void saveAcl(CfwAclDO aclDO, int priority,  Map<String, CfwAclRelateDO> cfwAclRelateDOMap);

    int getMaxPriority(String fwId);

    void resetHitCount(List<String> aclIds);

    List<CfwAclDO> getByAclIds(List<String> aclIds,String accountId);

    void deleteAcl(List<CfwAclDO> cfwAclDOs);

    void handleAllPriority(String fwId);

    CfwAclDO getByAclId(String aclId,String accountId);

    void priorityMoveBack(String fwId, Integer min, Integer max);

    void priorityMoveForward(String fwId, Integer min, Integer max);

    void updatePriority(String aclId, int priority);

    List<CfwAclDO> queryByParam(DescribeCfwAclParam param, int offset,String accountId);

    Integer countCfwAclDOByParam(DescribeCfwAclParam param);

    List<CfwAclDO> countCfwAclDOByFwId(String fwId);

    CfwAclDO getCfwAclByFwIdAndPriority(String fwId, Integer integer);

    void alterCfwAclStatus(List<String> aclIds, String status);

    boolean checkAclNameDuplicate(String fwId, String aclId, String aclName);

    List<StatisticsDO> getAclCountByFwIds(List<String> fwIds);

    List<CfwAclDO> getHitCountByFwId(String fwId);

    int countAclByFwId(String fwId);

    void batchUpdateHitCount(List<CfwAclDO> aclList);

    long getAclDenyCount(String instanceId);

    List<CfwAclDO>  getAclDeny(String instanceId);

    List<CfwAclDO> queryNameByIds(List<String> aclIdList);
}
