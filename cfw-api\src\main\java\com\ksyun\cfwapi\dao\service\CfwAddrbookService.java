package com.ksyun.cfwapi.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.dao.entity.CfwAddrbookDO;
import com.ksyun.cfwapi.domain.addrbook.DescribeAddrbookParam;
import com.ksyun.cfwapi.domain.addrbook.ModifyCfwAddrbookParam;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_addrbook(地址簿)】的数据库操作Service
* @createDate 2024-12-20 15:13:48
*/
public interface CfwAddrbookService extends IService<CfwAddrbookDO> {

    void updateAddrbook(ModifyCfwAddrbookParam param);

    void deleteAddrbook(String addrbookId);

    CfwAddrbookDO getByAddrbookId(String addrbookId,String accountId);

    List<CfwAddrbookDO> getByAddrbookIdList(List<String> addrbookIds);

    List<CfwAddrbookDO> queryCfwAddrbook(DescribeAddrbookParam param,int from);

    Integer countCfwAddrbook(DescribeAddrbookParam param);

    Integer countCfwAddrbookByFwId(String fwId);

    List<CfwAddrbookDO> listCfwAddrbookByFwId(String fwId);
}
