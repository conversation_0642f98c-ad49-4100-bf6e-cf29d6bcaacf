package com.ksyun.cfwapi.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.domain.fw.ModifyFireWallFeatureParam;

import java.util.List;

public interface CfwInstanceService extends IService<CfwInstanceDO> {
    List<CfwInstanceDO> getCfwInstanceByFwIds(List<String> fwIds,String accountId);

    List<CfwInstanceDO> getCfwInstanceByRegion(List<String> fwIds, String accountId, String region);

    CfwInstanceDO getCfwInstanceByFwId(String fwId);

    CfwInstanceDO getCfwInstanceByFwAccountId(String fwId, String accountId);

    CfwInstanceDO getCfwInstanceBySubOrderId(String subOrderId,String accountId);

    void removeFw(Long id);

    void updateStatus(Long id, Integer status);

    List<CfwInstanceDO> getCfwInstancesByAccountId(String accountId);

    int countCfwInstancesByAccountId(String accountId);

    List<CfwInstanceDO> pageFwDO(Integer offset, Integer pageSize);

    List<CfwInstanceDO> pageFwDOByFwId(Integer offset,Integer pageSize,List<String> fwIds,String instanceType);

    void updateProjectByFwIds(List<String> fwIds, String projectId,String accountId);

    void modifyCloudFireWallFeature(ModifyFireWallFeatureParam param,String accountId);

    void updateStatusByFwId(String fwId,Integer status);

    void updateStatusByFwIds(List<String> fwId, Integer status);

    List<CfwInstanceDO> listByFwIdsStatus(List<String> fwIds,List<Integer> status);

    int countCfwInstanceByFwAccountId(String fwId, String accountId);
}
