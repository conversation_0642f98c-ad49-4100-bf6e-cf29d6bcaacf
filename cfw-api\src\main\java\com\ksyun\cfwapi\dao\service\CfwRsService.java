package com.ksyun.cfwapi.dao.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;

import java.util.Date;
import java.util.List;

public interface CfwRsService extends IService<CfwRsDO> {
    List<CfwRsDO> getUnCreatedRsByTime(Date time);

    List<CfwRsDO> getCfwRsByFwId(String cfwInstanceId);

    void removeBatchByFwId(String fwId);

    void removeRsByIds(List<String> fwInstanceIdList);

    List<CfwRsDO> queryInstanceIdByFwId(String fwId);

    int countRsByFwId(String fwId);

    List<CfwRsDO> pageInstanceIdByFwId(String fwId, int offset, int size);

    List<CfwRsDO> queryByUpdateTime(Date queryTime);

    void updateStatusByRsIds(List<String> fwRsIds, Integer status);

    List<CfwRsDO> getCfwRsByFwIdRegion(List<String> cfwInstanceIds, String region);
}
