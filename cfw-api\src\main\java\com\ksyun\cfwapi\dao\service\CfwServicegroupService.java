package com.ksyun.cfwapi.dao.service;

import com.ksyun.cfwapi.dao.entity.CfwServicegroupDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.domain.serviceGroup.DescribeServiceGroupParam;
import com.ksyun.cfwapi.domain.serviceGroup.ModifyServiceGroupParam;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_servicegroup】的数据库操作Service
* @createDate 2024-12-20 15:07:48
*/
public interface CfwServicegroupService extends IService<CfwServicegroupDO> {

    CfwServicegroupDO getByServiceGroupId(String serviceGroupId,String accountId);

    List<CfwServicegroupDO> getByServiceGroupIdList(List<String> serviceGroupIds);

    void deleteServicegroup(String serviceGroupId);

    void updateServiceGroup(ModifyServiceGroupParam param,String accountId);

    List<CfwServicegroupDO> getByServiceGroup(DescribeServiceGroupParam param,String accountId);

    int countByFwId(String cfwInstanceId);

    List<CfwServicegroupDO>  listByFwId(String cfwInstanceId);
}
