package com.ksyun.cfwapi.dao.service;

import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwUpdteHistoryDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.domain.fw.ModifyFireWallLBParam;

/**
* <AUTHOR>
* @description 针对表【cfw_updte_history】的数据库操作Service
* @createDate 2025-03-17 16:35:31
*/
public interface CfwUpdteHistoryService extends IService<CfwUpdteHistoryDO> {

    CfwUpdteHistoryDO queryUpdteHistory(String subOrderId, String fwId);

    void saveInfo(ModifyFireWallLBParam param, CfwInstanceDO cfwInstanceDO);

    void updateStatus(String subOrderId, String fwId, Integer type);
}
