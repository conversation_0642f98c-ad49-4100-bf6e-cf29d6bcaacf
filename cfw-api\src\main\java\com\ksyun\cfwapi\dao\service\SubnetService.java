package com.ksyun.cfwapi.dao.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ksyun.cfwapi.domain.fw.CreateSubnetParam;
import com.ksyun.cfwapi.dao.entity.SubnetDO;
import com.ksyun.cfwapi.domain.fw.CreateSubnetResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SubnetService extends IService<SubnetDO> {
    List<CreateSubnetResponse> createSubNetCfw(CreateSubnetParam param) throws Exception;

    List<SubnetDO> getAvailableSubNet(Integer count,String region) throws Exception;

    List<SubnetDO> getSubnetBySubnetIds(List<String> subnetIds);

    void releaseSubnet(List<String> subnetId);

    SubnetDO getSubnetBySubnetId(String subnetId);

}
