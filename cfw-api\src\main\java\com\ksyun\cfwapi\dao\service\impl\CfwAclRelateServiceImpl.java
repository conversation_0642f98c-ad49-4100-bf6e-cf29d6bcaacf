package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwAclRelateDO;
import com.ksyun.cfwapi.dao.entity.CitationCountDO;
import com.ksyun.cfwapi.dao.service.CfwAclRelateService;
import com.ksyun.cfwapi.dao.mapper.CfwAclRelateMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_acl_relate】的数据库操作Service实现
* @createDate 2024-12-25 11:00:54
*/
@Service
public class CfwAclRelateServiceImpl extends ServiceImpl<CfwAclRelateMapper, CfwAclRelateDO>implements CfwAclRelateService{
    @Autowired
    private CfwAclRelateMapper cfwAclRelateMapper;
    @Override
    public void deleteAclrelate(List<String> cfwAclIdRelates) {
        LambdaQueryWrapper<CfwAclRelateDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CfwAclRelateDO::getAclId,cfwAclIdRelates);
        this.remove(wrapper);
    }

    @Override
    public List<CitationCountDO> getCitationCountByRelateIds(List<String> relateIds) {
        return cfwAclRelateMapper.getCitationCountByRelateIds(relateIds);
    }

    @Override
    public boolean existByRelateId(String relateId) {
        LambdaQueryWrapper<CfwAclRelateDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CfwAclRelateDO::getRelateId,relateId);
        return cfwAclRelateMapper.exists(wrapper);
    }
}




