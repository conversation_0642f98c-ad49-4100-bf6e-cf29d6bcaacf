package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.*;
import com.ksyun.cfwapi.dao.service.CfwAclService;
import com.ksyun.cfwapi.dao.mapper.CfwAclMapper;
import com.ksyun.cfwapi.domain.acl.DescribeCfwAclParam;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cfw_acl】的数据库操作Service实现
 * @createDate 2024-12-20 15:24:00
 */
@Service
public class CfwAclServiceImpl extends ServiceImpl<CfwAclMapper, CfwAclDO> implements CfwAclService {
    @Autowired
    private CfwAclMapper cfwAclMapper;

    @Override
    public void saveAcl(CfwAclDO aclDO, int priority, Map<String, CfwAclRelateDO> cfwAclRelateDOMap) {
        //查询优先级最高的记录
        if (priority == 0) {
            aclDO.setPriority(getMaxPriority(aclDO.getFwId())+1);
        }

        //处理优先级
        if (priority != 0) {
            handlePriority(aclDO.getFwId(), priority, "priority = priority + 1");
        }

        //保存
        this.save(aclDO);
    }

    @Override
    public int getMaxPriority(String fwId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getFwId, fwId)
                .eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus())
                .orderByDesc(CfwAclDO::getPriority).last("limit 1");
        CfwAclDO maxCfwAclDO = this.getOne(queryWrapper);
        if (Objects.isNull(maxCfwAclDO)) {
            return 1;
        } else {
            return maxCfwAclDO.getPriority();
        }
    }


    @Override
    public void resetHitCount(List<String> aclIds) {
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwAclDO::getAclId, aclIds);
        updateWrapper.set(CfwAclDO::getHitCount, 0);
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public List<CfwAclDO> getByAclIds(List<String> aclIds,String accountId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwAclDO::getAclId, aclIds);
        queryWrapper.eq(CfwAclDO::getAccountId,accountId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.list(queryWrapper);
    }

    @Override
    public void deleteAcl(List<CfwAclDO> cfwAclDOs) {
        List<String> aclIds = cfwAclDOs.stream().map(CfwAclDO::getAclId).collect(Collectors.toList());
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwAclDO::getAclId, aclIds);
        updateWrapper.set(CfwAclDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public void handleAllPriority(String fwId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.orderByAsc(CfwAclDO::getPriority);
        List<CfwAclDO> cfwAclDOExist = this.list(queryWrapper);

        if (CollectionUtil.isNotEmpty(cfwAclDOExist)) {
            Date nowTime = new Date();
            for (int i = 1; i <= cfwAclDOExist.size(); i++) {
                cfwAclDOExist.get(i-1).setPriority(i);
                cfwAclDOExist.get(i-1).setUpdateTime(nowTime);
            }
            this.updateBatchById(cfwAclDOExist);
        }
    }

    @Override
    public CfwAclDO getByAclId(String aclId,String accountId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getAclId, aclId);
        queryWrapper.eq(CfwAclDO::getAccountId,accountId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    private void handlePriority(String fwId, int priority, String sqlStr) {
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwAclDO::getFwId, fwId);
        updateWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        updateWrapper.ge(CfwAclDO::getPriority, priority);
        updateWrapper.setSql(sqlStr);
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public void priorityMoveBack(String fwId, Integer min, Integer max) {
        handlePriorityRange(fwId, min, max, "priority = priority + 1");
    }

    @Override
    public void priorityMoveForward(String fwId, Integer min, Integer max) {
        handlePriorityRange(fwId, min, max, "priority = priority - 1");
    }

    @Override
    public void updatePriority(String aclId, int priority) {
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwAclDO::getAclId, aclId);
        updateWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        updateWrapper.set(CfwAclDO::getPriority, priority);
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    private void handlePriorityRange(String fwId, Integer min, Integer max, String sqlStr) {
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwAclDO::getFwId, fwId);
        updateWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        updateWrapper.ge(CfwAclDO::getPriority, min);
        if (Objects.nonNull(max)) {
            updateWrapper.le(CfwAclDO::getPriority, max);
        }
        updateWrapper.setSql(sqlStr);
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }


    @Override
    public List<CfwAclDO> queryByParam(DescribeCfwAclParam param, int offset,String accountId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getAclIds())) {
            queryWrapper.in(CfwAclDO::getAclId, param.getAclIds());
        }
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwAclDO::getFwId, param.getCfwInstanceId());
        queryWrapper.eq(CfwAclDO::getAccountId,accountId);
        queryWrapper.orderByAsc(CfwAclDO::getPriority);
        queryWrapper.last("LIMIT " + offset + CommonConstant.COMMA + param.getMaxResults());
        return this.list(queryWrapper);
    }

    @Override
    public Integer countCfwAclDOByParam(DescribeCfwAclParam param) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getAclIds())) {
            queryWrapper.in(CfwAclDO::getAclId, param.getAclIds());
        }
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwAclDO::getFwId, param.getCfwInstanceId());
        queryWrapper.orderByDesc(CfwAclDO::getPriority);
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public List<CfwAclDO> countCfwAclDOByFwId(String fwId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.select(CfwAclDO::getRuleId);
        queryWrapper.orderByAsc(CfwAclDO::getRuleId);
        return list(queryWrapper);
    }

    @Override
    public CfwAclDO getCfwAclByFwIdAndPriority(String fwId, Integer priority) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getPriority, priority);
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    @Override
    public void alterCfwAclStatus(List<String> aclIds, String status) {
        LambdaUpdateWrapper<CfwAclDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwAclDO::getAclId, aclIds);
        updateWrapper.set(CfwAclDO::getStatus, status);
        updateWrapper.set(CfwAclDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public boolean checkAclNameDuplicate(String fwId, String aclId, String aclName) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getAclName, aclName);
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        if(StringUtils.isNotBlank(aclId)){
            queryWrapper.ne(CfwAclDO::getAclId, aclId);
        }
        return cfwAclMapper.exists(queryWrapper);
    }

    @Override
    public List<StatisticsDO> getAclCountByFwIds(List<String> fwIds) {
        return cfwAclMapper.getAclCountByFwIds(fwIds);
    }

    @Override
    public List<CfwAclDO> getHitCountByFwId(String fwId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwAclDO::getId,CfwAclDO::getAclId, CfwAclDO::getFwId,CfwAclDO::getHitCount,CfwAclDO::getAclName);
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.list(queryWrapper);
    }

    @Override
    public int countAclByFwId(String fwId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAclDO::getFwId, fwId);
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public void batchUpdateHitCount(List<CfwAclDO> aclList) {
        cfwAclMapper.batchUpdateHitCount(aclList);
    }

    @Override
    public long getAclDenyCount(String instanceId) {
        return cfwAclMapper.countAclDeny(instanceId);
    }

    @Override
    public List<CfwAclDO>  getAclDeny(String instanceId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwAclDO::getId,CfwAclDO::getAclId, CfwAclDO::getFwId,CfwAclDO::getHitCount,CfwAclDO::getAclName);
        queryWrapper.eq(CfwAclDO::getFwId, instanceId);
        queryWrapper.eq(CfwAclDO::getPolicy,"deny");
        return this.list(queryWrapper);
    }

    @Override
    public List<CfwAclDO> queryNameByIds(List<String> aclIdList) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwAclDO::getAclId,CfwAclDO::getAclName);
        queryWrapper.in(CfwAclDO::getAclId, aclIdList);
        return this.list(queryWrapper);
    }

}




