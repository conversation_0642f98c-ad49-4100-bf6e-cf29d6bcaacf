package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwAddrbookDO;
import com.ksyun.cfwapi.dao.mapper.CfwAddrbookMapper;
import com.ksyun.cfwapi.dao.service.CfwAddrbookService;
import com.ksyun.cfwapi.domain.addrbook.DescribeAddrbookParam;
import com.ksyun.cfwapi.domain.addrbook.ModifyCfwAddrbookParam;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cfw_addrbook(地址簿)】的数据库操作Service实现
 * @createDate 2024-12-20 15:13:48
 */
@Service
public class CfwAddrbookServiceImpl extends ServiceImpl<CfwAddrbookMapper, CfwAddrbookDO> implements CfwAddrbookService {

    @Override
    public void updateAddrbook(ModifyCfwAddrbookParam param) {
        LambdaUpdateWrapper<CfwAddrbookDO> updateWrapper = new LambdaUpdateWrapper<>();
        if (StringUtils.isNotBlank(param.getAddrbookName())) {
            updateWrapper.set(CfwAddrbookDO::getAddrbookName, param.getAddrbookName());
        }
        if (StringUtils.isNotBlank(param.getIpVersion())) {
            updateWrapper.set(CfwAddrbookDO::getIpVersion, param.getIpVersion());
        }
        if (CollectionUtil.isNotEmpty(param.getIpAddress())) {
            updateWrapper.set(CfwAddrbookDO::getIpAddress, String.join(CommonConstant.COMMA,param.getIpAddress()));
        }
        if (StringUtils.isNotBlank(param.getDescription())) {
            updateWrapper.set(CfwAddrbookDO::getDescription, param.getDescription());
        }
        updateWrapper.set(CfwAddrbookDO::getUpdateTime, new Date());
        updateWrapper.eq(CfwAddrbookDO::getAddrbookId, param.getAddrbookId());
        this.update(updateWrapper);
    }

    @Override
    public void deleteAddrbook(String addrbookId) {
        LambdaUpdateWrapper<CfwAddrbookDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        updateWrapper.set(CfwAddrbookDO::getUpdateTime, new Date());
        updateWrapper.eq(CfwAddrbookDO::getAddrbookId, addrbookId);
        this.update(updateWrapper);
    }

    @Override
    public CfwAddrbookDO getByAddrbookId(String addrbookId,String accountId) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAddrbookDO::getAddrbookId, addrbookId);
        queryWrapper.eq(CfwAddrbookDO::getAccountId, accountId);
        queryWrapper.eq(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<CfwAddrbookDO> getByAddrbookIdList(List<String> addrbookIds) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwAddrbookDO::getAddrbookId, addrbookIds);
        queryWrapper.select(CfwAddrbookDO::getAddrbookId,CfwAddrbookDO::getAddrbookName);
        return this.list(queryWrapper);
    }

    @Override
    public List<CfwAddrbookDO> queryCfwAddrbook(DescribeAddrbookParam param,int offset) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getAddrbookIds())) {
            queryWrapper.in(CfwAddrbookDO::getAddrbookId, param.getAddrbookIds());
        }
        queryWrapper.eq(CfwAddrbookDO::getFwId, param.getCfwInstanceId());
        queryWrapper.eq(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.last("LIMIT " + offset + CommonConstant.COMMA + param.getMaxResults());
        return this.list(queryWrapper);
    }

    @Override
    public Integer countCfwAddrbook(DescribeAddrbookParam param) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getAddrbookIds())) {
            queryWrapper.in(CfwAddrbookDO::getAddrbookId, param.getAddrbookIds());
        }
        queryWrapper.eq(CfwAddrbookDO::getFwId, param.getCfwInstanceId());
        queryWrapper.eq(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public Integer countCfwAddrbookByFwId(String fwId) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAddrbookDO::getFwId, fwId);
        queryWrapper.eq(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public List<CfwAddrbookDO> listCfwAddrbookByFwId(String fwId) {
        LambdaQueryWrapper<CfwAddrbookDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAddrbookDO::getFwId, fwId);
        queryWrapper.eq(CfwAddrbookDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.select(CfwAddrbookDO::getAddrbookId,CfwAddrbookDO::getIpAddress);
        return list(queryWrapper);
    }
}




