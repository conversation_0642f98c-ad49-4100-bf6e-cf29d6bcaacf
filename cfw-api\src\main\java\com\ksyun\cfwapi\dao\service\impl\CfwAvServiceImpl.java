package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwAvDO;
import com.ksyun.cfwapi.dao.service.CfwAvService;
import com.ksyun.cfwapi.dao.mapper.CfwAvMapper;
import com.ksyun.cfwapi.domain.av.ModifyCfwAvParam;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_av(防病毒)】的数据库操作Service实现
* @createDate 2024-12-30 19:38:44
*/
@Service
public class CfwAvServiceImpl extends ServiceImpl<CfwAvMapper, CfwAvDO> implements CfwAvService{

    @Override
    public List<CfwAvDO> queryByFwId(String fwId,String accountId) {
        LambdaQueryWrapper<CfwAvDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwAvDO::getFwId, fwId);
        queryWrapper.eq(CfwAvDO::getAccountId,accountId);
        return list(queryWrapper);
    }

    @Override
    public void modifyCfwAv(ModifyCfwAvParam param,String accountId) {
        LambdaUpdateWrapper<CfwAvDO> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.eq(CfwAvDO::getFwId, param.getCfwInstanceId());
        updateWrapper.eq(CfwAvDO::getProtocol, param.getProtocol());
        updateWrapper.eq(CfwAvDO::getAccountId, accountId);
        updateWrapper.set(CfwAvDO::getProtectType, param.getProtectType());
        updateWrapper.set(CfwAvDO::getStatus, param.getStatus());
        updateWrapper.set(CfwAvDO::getUpdateTime, new Date());
        update(updateWrapper);
    }
}




