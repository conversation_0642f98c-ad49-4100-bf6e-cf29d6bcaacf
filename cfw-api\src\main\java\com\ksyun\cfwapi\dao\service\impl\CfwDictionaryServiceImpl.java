package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwDictionaryDO;
import com.ksyun.cfwapi.dao.service.CfwDictionaryService;
import com.ksyun.cfwapi.dao.mapper.CfwDictionaryMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_dictionary】的数据库操作Service实现
* @createDate 2025-01-13 15:30:35
*/
@Service
public class CfwDictionaryServiceImpl extends ServiceImpl<CfwDictionaryMapper, CfwDictionaryDO>  implements CfwDictionaryService{

    @Override
    public List<CfwDictionaryDO> queryDictionaryByType(String type) {
        LambdaQueryWrapper<CfwDictionaryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwDictionaryDO::getDicType, type);
        queryWrapper.orderByAsc(CfwDictionaryDO::getOrderNum);
        return list(queryWrapper);
    }
}




