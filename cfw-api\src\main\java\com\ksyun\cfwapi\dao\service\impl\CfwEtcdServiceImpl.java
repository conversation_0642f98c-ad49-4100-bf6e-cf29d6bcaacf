package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwEtcdDO;
import com.ksyun.cfwapi.dao.mapper.CfwEtcdMapper;
import com.ksyun.cfwapi.dao.service.CfwEtcdService;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.ExecutedStatusEnum;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cfw_etcd_cluster】的数据库操作Service实现
 * @createDate 2024-12-25 23:09:01
 */
@Service
public class CfwEtcdServiceImpl extends ServiceImpl<CfwEtcdMapper, CfwEtcdDO>
        implements CfwEtcdService {

    @Override
    public List<CfwEtcdDO> queryChangeWallOperation(Date yesterday, Integer offset, Integer pagesize) {
        LambdaQueryWrapper<CfwEtcdDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(CfwEtcdDO::getCreateTime, yesterday);
        queryWrapper.eq(CfwEtcdDO::getStatus, ExecutedStatusEnum.UNEXECUTED.getCode());
        queryWrapper.last("limit " + offset + CommonConstant.COMMA + pagesize);
        return list(queryWrapper);
    }

    @Override
    public void updateStatusByIds(List<Long> ids) {
        LambdaUpdateWrapper<CfwEtcdDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwEtcdDO::getId, ids);
        updateWrapper.set(CfwEtcdDO::getStatus, ExecutedStatusEnum.EXECUTED.getCode());
        this.update(updateWrapper);
    }
}




