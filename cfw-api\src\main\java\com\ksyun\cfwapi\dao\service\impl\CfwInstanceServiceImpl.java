package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.mapper.CfwInstanceMapper;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.domain.fw.ModifyFireWallFeatureParam;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CfwInstanceServiceImpl extends ServiceImpl<CfwInstanceMapper, CfwInstanceDO> implements CfwInstanceService {

    @Override
    public List<CfwInstanceDO> getCfwInstanceByFwIds(List<String> fwIds, String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isEmpty(fwIds) && StringUtils.isBlank(accountId)) {
            return CollectionUtil.newArrayList();
        }
        if (CollectionUtil.isNotEmpty(fwIds)) {
            queryWrapper.in(CfwInstanceDO::getFwId, fwIds);
        }
        if (StringUtils.isNotBlank(accountId)) {
            queryWrapper.in(CfwInstanceDO::getAccountId, accountId);
        }
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.orderByDesc(CfwInstanceDO::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public List<CfwInstanceDO> getCfwInstanceByRegion(List<String> fwIds, String accountId,String region) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isEmpty(fwIds) && StringUtils.isBlank(accountId)) {
            return CollectionUtil.newArrayList();
        }
        if (CollectionUtil.isNotEmpty(fwIds)) {
            queryWrapper.in(CfwInstanceDO::getFwId, fwIds);
        }
        if (StringUtils.isNotBlank(accountId)) {
            queryWrapper.in(CfwInstanceDO::getAccountId, accountId);
        }
        queryWrapper.eq(CfwInstanceDO::getRegion,region);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.orderByDesc(CfwInstanceDO::getCreateTime);
        return list(queryWrapper);
    }

    @Override
    public CfwInstanceDO getCfwInstanceByFwId(String fwId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwInstanceDO::getFwId, fwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    @Override
    public CfwInstanceDO getCfwInstanceByFwAccountId(String fwId,String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwInstanceDO::getFwId, fwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwInstanceDO::getAccountId,accountId);
        return this.getOne(queryWrapper);
    }

    @Override
    public CfwInstanceDO getCfwInstanceBySubOrderId(String subOrderId,String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwInstanceDO::getSubOrderId, subOrderId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwInstanceDO::getAccountId,accountId);
        return this.getOne(queryWrapper);
    }

    @Override
    public void removeFw(Long id) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwInstanceDO::getId, id);
        updateWrapper.set(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        this.update(updateWrapper);
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        updateWrapper.set(CfwInstanceDO::getStatus, status);
        updateWrapper.eq(CfwInstanceDO::getId, id);
        this.update(updateWrapper);
    }

    @Override
    public List<CfwInstanceDO> getCfwInstancesByAccountId(String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwInstanceDO::getAccountId, accountId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return list(queryWrapper);
    }

    @Override
    public int countCfwInstancesByAccountId(String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwInstanceDO::getAccountId, accountId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return Math.toIntExact(this.count(queryWrapper));
     }

    @Override
    public List<CfwInstanceDO> pageFwDO(Integer offset, Integer pageSize) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwInstanceDO::getFwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.last("LIMIT " + offset + CommonConstant.COMMA + pageSize);
        return list(queryWrapper);
    }

    @Override
    public List<CfwInstanceDO> pageFwDOByFwId(Integer offset, Integer pageSize,List<String> fwIds,String instanceType) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwInstanceDO::getFwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        if(CollectionUtil.isNotEmpty(fwIds)){
            queryWrapper.in(CfwInstanceDO::getFwId,fwIds);
        }
        if(StringUtils.isNotBlank(instanceType)){
            queryWrapper.eq(CfwInstanceDO::getInstanceType, instanceType);
        }
        queryWrapper.orderByDesc(CfwInstanceDO::getCreateTime);
        queryWrapper.last("LIMIT " + offset + CommonConstant.COMMA + pageSize);
        return list(queryWrapper);
    }

    @Override
    public void updateProjectByFwIds(List<String> fwIds, String projectId,String accountId) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        updateWrapper.set(CfwInstanceDO::getProjectId, projectId);
        updateWrapper.in(CfwInstanceDO::getFwId, fwIds);
        updateWrapper.eq(CfwInstanceDO::getAccountId,accountId);
        this.update(updateWrapper);
    }

    @Override
    public void modifyCloudFireWallFeature(ModifyFireWallFeatureParam param,String accountId) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        updateWrapper.set(CfwInstanceDO::getName, param.getInstanceName());
        updateWrapper.eq(CfwInstanceDO::getFwId, param.getCfwInstanceId());
        updateWrapper.eq(CfwInstanceDO::getAccountId,accountId);
        this.update(updateWrapper);
    }

    @Override
    public void updateStatusByFwId(String fwId,Integer status) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        updateWrapper.set(CfwInstanceDO::getStatus, status);
        updateWrapper.eq(CfwInstanceDO::getFwId, fwId);
        this.update(updateWrapper);
    }

    @Override
    public void updateStatusByFwIds(List<String> fwId, Integer status) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        updateWrapper.set(CfwInstanceDO::getStatus, status);
        updateWrapper.in(CfwInstanceDO::getFwId, fwId);
        this.update(updateWrapper);
    }

    @Override
    public List<CfwInstanceDO> listByFwIdsStatus(List<String> fwIds,List<Integer> statusList) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwInstanceDO::getFwId, fwIds);
        queryWrapper.in(CfwInstanceDO::getStatus, statusList);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return list(queryWrapper);
    }

    @Override
    public int countCfwInstanceByFwAccountId(String fwId, String accountId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwInstanceDO::getFwId, fwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwInstanceDO::getAccountId,accountId);
        return Math.toIntExact(this.count(queryWrapper));
    }

}
