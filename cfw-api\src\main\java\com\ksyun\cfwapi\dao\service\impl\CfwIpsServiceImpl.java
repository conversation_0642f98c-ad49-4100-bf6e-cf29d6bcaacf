package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwIpsDO;
import com.ksyun.cfwapi.dao.service.CfwIpsService;
import com.ksyun.cfwapi.dao.mapper.CfwIpsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cfw_ips】的数据库操作Service实现
* @createDate 2024-12-30 19:38:54
*/
@Service
public class CfwIpsServiceImpl extends ServiceImpl<CfwIpsMapper, CfwIpsDO> implements CfwIpsService{

    @Override
    public CfwIpsDO queryByFwId(String cfwInstanceId,String accountId) {
        LambdaQueryWrapper<CfwIpsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwIpsDO::getFwId, cfwInstanceId);
        queryWrapper.eq(CfwIpsDO::getAccountId,accountId);
        return this.getOne(queryWrapper);
    }

    @Override
    public CfwIpsDO queryByIpsId(String ipsId,String accountId) {
        LambdaQueryWrapper<CfwIpsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwIpsDO::getIpsId, ipsId);
        queryWrapper.eq(CfwIpsDO::getAccountId,accountId);
        return this.getOne(queryWrapper);
    }
}




