package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ksyun.cfwapi.dao.entity.CfwRegionConfigDO;
import com.ksyun.cfwapi.dao.mapper.CfwRegionConfigMapper;
import com.ksyun.cfwapi.dao.service.CfwRegionConfigService;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @date 2025/6/24
 */
@Service
public class CfwRegionConfigServiceImpl  extends ServiceImpl<CfwRegionConfigMapper, CfwRegionConfigDO> implements CfwRegionConfigService{
    @Autowired
    private JedisTemplate jedisTemplate;
    @Override
    public CfwRegionConfigDO queryRegionConfig(String region) {
        String key = String.format(RedisConstants.CFW_REGION_CONFIG,region);
        String regionConfigDOStr = jedisTemplate.getKey(key);
        CfwRegionConfigDO regionConfigDO;
        if (StringUtils.isBlank(regionConfigDOStr)) {
            LambdaQueryWrapper<CfwRegionConfigDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CfwRegionConfigDO::getRegion, region);
            queryWrapper.eq(CfwRegionConfigDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
            regionConfigDO = this.getOne(queryWrapper);
            jedisTemplate.setExpireKey(RedisConstants.CFW_REGION_CONFIG, JSONUtil.toJsonStr(regionConfigDO), 1800);
        } else {
            ObjectMapper objectMapper = new ObjectMapper();
            try{
                regionConfigDO = objectMapper.readValue(regionConfigDOStr, new TypeReference<CfwRegionConfigDO>() {});
            }catch(Exception e){
                regionConfigDO = null;
                log.error("查询机房配置信息错误,region:"+region,e);
            }
        }
        return regionConfigDO;
    }
}
