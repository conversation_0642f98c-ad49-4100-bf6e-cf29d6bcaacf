package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.mapper.CfwRsMapper;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import com.ksyun.cfwcore.enums.FirewallRsStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CfwRsServiceImpl  extends ServiceImpl<CfwRsMapper, CfwRsDO> implements CfwRsService {
    @Override
    public List<CfwRsDO> getUnCreatedRsByTime(Date time) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(CfwRsDO::getCreateTime, time);
        queryWrapper.eq(CfwRsDO::getRsStatus, FirewallRsStatusEnum.UN_CREATE.getStatus());
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.list(queryWrapper);
    }

    @Override
    public List<CfwRsDO> getCfwRsByFwId(String fwId) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwRsDO::getFwId, fwId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.orderByDesc(CfwRsDO::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public void removeBatchByFwId(String fwId) {
        LambdaUpdateWrapper<CfwRsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwRsDO::getFwId, fwId);
        updateWrapper.set(CfwRsDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        this.update(updateWrapper);
    }

    @Override
    public void removeRsByIds(List<String> fwInstanceIdList) {
        LambdaUpdateWrapper<CfwRsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwRsDO::getFwInstanceId, fwInstanceIdList);
        updateWrapper.set(CfwRsDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        this.update(updateWrapper);
    }

    @Override
    public List<CfwRsDO> queryInstanceIdByFwId(String fwId) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwRsDO::getFwInstanceId);
        queryWrapper.eq(CfwRsDO::getFwId, fwId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.list(queryWrapper);
    }

    @Override
    public int countRsByFwId(String fwId) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwRsDO::getFwId, fwId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public List<CfwRsDO> pageInstanceIdByFwId(String fwId, int offset, int size) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwRsDO::getFwId, fwId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.select(CfwRsDO::getFwInstanceId);
        queryWrapper.last("limit " + offset + CommonConstant.COMMA + size);
        return list(queryWrapper);
    }

    @Override
    public List<CfwRsDO> queryByUpdateTime(Date queryTime) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(CfwRsDO::getUpdateTime, queryTime);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return list(queryWrapper);
    }

    @Override
    public void updateStatusByRsIds(List<String> fwRsIds, Integer status) {
        LambdaUpdateWrapper<CfwRsDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwRsDO::getFwInstanceId, fwRsIds);
        updateWrapper.set(CfwRsDO::getRsStatus, status);
        updateWrapper.set(CfwRsDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public List<CfwRsDO> getCfwRsByFwIdRegion(List<String> cfwInstanceIds, String region) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwRsDO::getRegion, region);
        if(CollectionUtil.isNotEmpty(cfwInstanceIds)){
            queryWrapper.in(CfwRsDO::getFwId, cfwInstanceIds);
        }
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return list(queryWrapper);
    }
}
