package com.ksyun.cfwapi.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwServicegroupDO;
import com.ksyun.cfwapi.dao.mapper.CfwServicegroupMapper;
import com.ksyun.cfwapi.dao.service.CfwServicegroupService;
import com.ksyun.cfwapi.domain.serviceGroup.DescribeServiceGroupParam;
import com.ksyun.cfwapi.domain.serviceGroup.ModifyServiceGroupParam;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【cfw_servicegroup】的数据库操作Service实现
 * @createDate 2024-12-20 15:07:48
 */
@Service
public class CfwServicegroupServiceImpl extends ServiceImpl<CfwServicegroupMapper, CfwServicegroupDO> implements CfwServicegroupService {

    @Override
    public CfwServicegroupDO getByServiceGroupId(String serviceGroupId,String accountId) {
        LambdaQueryWrapper<CfwServicegroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwServicegroupDO::getServiceGroupId, serviceGroupId);
        queryWrapper.eq(CfwServicegroupDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwServicegroupDO::getAccountId,accountId);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<CfwServicegroupDO> getByServiceGroupIdList(List<String> serviceGroupIds) {
        LambdaQueryWrapper<CfwServicegroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CfwServicegroupDO::getServiceGroupId, serviceGroupIds);
        queryWrapper.select(CfwServicegroupDO::getServiceGroupId,CfwServicegroupDO::getServiceGroupName);
        return this.list(queryWrapper);
    }
    @Override
    public void deleteServicegroup(String serviceGroupId) {
        LambdaUpdateWrapper<CfwServicegroupDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwServicegroupDO::getDeleteStatus, DeleteFlagEnum.DELETE.getStatus());
        updateWrapper.set(CfwServicegroupDO::getUpdateTime, new Date());
        updateWrapper.eq(CfwServicegroupDO::getServiceGroupId, serviceGroupId);
        this.update(updateWrapper);
    }

    @Override
    public void updateServiceGroup(ModifyServiceGroupParam param,String accountId) {
        LambdaUpdateWrapper<CfwServicegroupDO> updateWrapper = new LambdaUpdateWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getServiceInfo())) {
            updateWrapper.set(CfwServicegroupDO::getService, String.join(CommonConstant.COMMA, param.getServiceInfo()));
        }
        if (StringUtils.isNotBlank(param.getServiceGroupName())) {
            updateWrapper.set(CfwServicegroupDO::getServiceGroupName, param.getServiceGroupName());
        }
        if (StringUtils.isNotBlank(param.getDescription())) {
            updateWrapper.set(CfwServicegroupDO::getDescription, param.getDescription());
        }
        updateWrapper.eq(CfwServicegroupDO::getServiceGroupId, param.getServiceGroupId());
        updateWrapper.eq(CfwServicegroupDO::getAccountId,accountId);
        this.update(updateWrapper);
    }

    @Override
    public List<CfwServicegroupDO> getByServiceGroup(DescribeServiceGroupParam param,String accountId) {
        LambdaQueryWrapper<CfwServicegroupDO> queryWrapper = new LambdaQueryWrapper<>();
        if (CollectionUtil.isNotEmpty(param.getServiceGroupIds())) {
            queryWrapper.in(CfwServicegroupDO::getServiceGroupId, param.getServiceGroupIds());
        }
        queryWrapper.eq(CfwServicegroupDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwServicegroupDO::getFwId, param.getCfwInstanceId());
        queryWrapper.eq(CfwServicegroupDO::getAccountId,accountId);
        return this.list(queryWrapper);
    }

    @Override
    public int countByFwId(String fwId) {
        LambdaQueryWrapper<CfwServicegroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwServicegroupDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwServicegroupDO::getFwId, fwId);
        return  Math.toIntExact(this.count(queryWrapper));
    }

    @Override
    public List<CfwServicegroupDO> listByFwId(String fwId) {
        LambdaQueryWrapper<CfwServicegroupDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwServicegroupDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.eq(CfwServicegroupDO::getFwId, fwId);
        queryWrapper.select(CfwServicegroupDO::getServiceGroupId,CfwServicegroupDO::getService);
        return  list(queryWrapper);
    }
}




