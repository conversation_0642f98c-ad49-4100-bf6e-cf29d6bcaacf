package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwServicegroupDO;
import com.ksyun.cfwapi.dao.entity.CfwUpdteHistoryDO;
import com.ksyun.cfwapi.dao.service.CfwUpdteHistoryService;
import com.ksyun.cfwapi.dao.mapper.CfwUpdteHistoryMapper;
import com.ksyun.cfwapi.domain.fw.ModifyFireWallLBParam;
import com.ksyun.cfwapi.enums.UpdateStatusEnum;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【cfw_updte_history】的数据库操作Service实现
* @createDate 2025-03-17 16:35:31
*/
@Service
public class CfwUpdteHistoryServiceImpl extends ServiceImpl<CfwUpdteHistoryMapper, CfwUpdteHistoryDO> implements CfwUpdteHistoryService{

    @Override
    public CfwUpdteHistoryDO queryUpdteHistory(String subOrderId, String fwId) {
        LambdaQueryWrapper<CfwUpdteHistoryDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwUpdteHistoryDO::getFwId, fwId);
        queryWrapper.eq(CfwUpdteHistoryDO::getSubOrderId, subOrderId);
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void saveInfo(ModifyFireWallLBParam param, CfwInstanceDO cfwInstanceDO) {
        CfwUpdteHistoryDO updteHistoryDO = new CfwUpdteHistoryDO();
        updteHistoryDO.setFwId(cfwInstanceDO.getFwId());
        updteHistoryDO.setOldBandwidth(cfwInstanceDO.getBandwidth());
        updteHistoryDO.setOldInstanceType(cfwInstanceDO.getInstanceType());
        updteHistoryDO.setOldTotalEipNum(cfwInstanceDO.getTotalEipNum());
        updteHistoryDO.setOldSubOrderId(cfwInstanceDO.getSubOrderId());
        updteHistoryDO.setStatus(UpdateStatusEnum.UNEXECUTED.getType());
        updteHistoryDO.setSubOrderId(param.getSubOrderId());
        updteHistoryDO.setCreateTime(new Date());
        updteHistoryDO.setUpdateTime(new Date());
        this.save(updteHistoryDO);
    }

    @Override
    public void updateStatus(String subOrderId, String fwId, Integer status) {
        LambdaUpdateWrapper<CfwUpdteHistoryDO> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.set(CfwUpdteHistoryDO::getStatus,status);
        updateWrapper.set(CfwUpdteHistoryDO::getUpdateTime,new Date());
        updateWrapper.eq(CfwUpdteHistoryDO::getSubOrderId,subOrderId);
        updateWrapper.eq(CfwUpdteHistoryDO::getFwId,fwId);
        this.update(updateWrapper);
    }
}




