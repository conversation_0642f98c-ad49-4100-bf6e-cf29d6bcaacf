package com.ksyun.cfwapi.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwapi.dao.entity.SubnetDO;
import com.ksyun.cfwapi.dao.mapper.SubnetMapper;
import com.ksyun.cfwapi.enums.SubNetUseStatusEnum;
import com.ksyun.cfwapi.dao.service.SubnetService;
import com.ksyun.cfwapi.domain.fw.CreateSubnetParam;
import com.ksyun.cfwapi.domain.fw.CreateSubnetResponse;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.vpcapi.domain.QueryDomainsResponse;
import com.ksyun.cfwapi.vpcapi.service.SubNetApiService;
import com.ksyun.cfwapi.utils.SubnetUtil;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SubnetServiceImpl extends ServiceImpl<SubnetMapper, SubnetDO> implements SubnetService {

    private final ExecutorService executorService = new ThreadPoolExecutor(10, 10,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

    @Autowired
    private SubNetApiService subNetApiService;

    @Autowired
    private RedissonTemplate redissonTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public List<SubnetDO> getAvailableSubNet(Integer count,String region) throws CfwException {
        List<SubnetDO> subnetDOs;
        String key = "CFW:availableSubNet";
        RLock lock = redissonTemplate.getRedissonClient().getLock(key);
        lock.lock(60, TimeUnit.SECONDS);
        try {
            LambdaQueryWrapper<SubnetDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SubnetDO::getStatus, SubNetUseStatusEnum.UNUSED.getStatus());
            queryWrapper.eq(SubnetDO::getRegion,region);
            queryWrapper.orderByAsc(SubnetDO::getCreateTime);
            queryWrapper.last("LIMIT "+ count);
            subnetDOs = this.list(queryWrapper);
            if(CollectionUtils.isEmpty(subnetDOs)||subnetDOs.size()<count){
                throw new CfwException("子网段不足");
            }

            if (CollectionUtils.isNotEmpty(subnetDOs)) {
                List<Long> ids = subnetDOs.stream().map(SubnetDO::getId).collect(Collectors.toList());
                LambdaUpdateWrapper<SubnetDO> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.set(SubnetDO::getStatus, SubNetUseStatusEnum.USED.getStatus());
                updateWrapper.set(SubnetDO::getUpdateTime, new Date());
                updateWrapper.in(SubnetDO::getId, ids);
                this.update(updateWrapper);
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
        return subnetDOs;
    }

    @Override
    public List<CreateSubnetResponse> createSubNetCfw(CreateSubnetParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        if(StringUtils.isBlank(param.getRegion())){
            param.setRegion(auth.getRegion());
        }
        //查询vpc信息
        QueryDomainsResponse vpc = subNetApiService.describeVpc(param.getVpcId(), auth);
        if (Objects.isNull(vpc) || CollectionUtils.isEmpty(vpc.getDomains()) || StringUtils.isBlank(vpc.getDomains().get(0).getCidr())) {
            throw new CfwException("查询vpc错误");
        }

        String parentNet = vpc.getDomains().get(0).getCidr();

        //加vpcId 锁
        String key = "createSubNetCfw:" + param.getVpcId();
        RLock lock = redissonTemplate.getRedissonClient().getLock(key);
        boolean resultLock = lock.tryLock();
        if (!resultLock) {
            throw new CfwException("正在添加子网段，请稍后尝试");
        }

        try {
            String subNetLastUse = getMaxSubNetUse(param);

            List<CreateSubnetResponse> result = new ArrayList<>(param.getSubnetsNum());
            //首次使用，数据库没有数据
            if (StringUtils.isBlank(subNetLastUse)) {
                String[] parentNetIpArr = parentNet.split("/");
                String parentNetIp = parentNetIpArr[0];
                // 拼接第一个可用网段
                subNetLastUse = parentNetIp +"/" + param.getSubNetMaskLength();
                //创建第一个使用子网
                List<CreateSubnetResponse> firstSubnet = createSubNets(Collections.singletonList(subNetLastUse), param.getVpcId(), auth);
                if (CollectionUtils.isEmpty(firstSubnet)) {
                    throw new CfwException("创建第一个子网段失败");
                }
                result.addAll(firstSubnet);
                saveSubNet(param, firstSubnet);
                param.setSubnetsNum(param.getSubnetsNum() - 1);
            }

            //创建子网段
            for (int i = 0; i <= param.getSubnetsNum() / CommonConstant.BATCH_HANDLE_SIZE_ONE_THOUSAND; i++) {
                int subNetNum = Math.min(param.getSubnetsNum() - i * CommonConstant.BATCH_HANDLE_SIZE_ONE_THOUSAND, CommonConstant.BATCH_HANDLE_SIZE_ONE_THOUSAND);
                //获取子网字符串集合
                List<String> netSegs = getSubNetStrs(param, subNetLastUse, subNetNum, parentNet);
                if (CollectionUtils.isEmpty(netSegs)) {
                    continue;
                }
                //重新设置最后一个创建的子网
                subNetLastUse = netSegs.get(netSegs.size() - 1);
                //open api创建子网
                List<CreateSubnetResponse> saveSubresults = createSubNets(netSegs, param.getVpcId(), auth);
                if (CollectionUtils.isEmpty(saveSubresults)) {
                    continue;
                }

                result.addAll(saveSubresults);
                //保存mysql子网信息
                saveSubNet(param, saveSubresults);
            }
            return result;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    /**
     * 保存mysql子网信息
     * @param param
     * @param saveSubresults
     * @throws Exception
     */
    private void saveSubNet(CreateSubnetParam param, List<CreateSubnetResponse> saveSubresults) throws Exception {
        List<SubnetDO> addSubNet = new ArrayList<>(saveSubresults.size());
        for (CreateSubnetResponse saveSubresult : saveSubresults) {
            String cidr = saveSubresult.getSubCidr();
            Date nowDate = new Date();
            SubnetDO subnetDO = new SubnetDO();
            subnetDO.setSubnetId(saveSubresult.getSubNetId());
            subnetDO.setCidr(cidr);
            subnetDO.setVpcId(param.getVpcId());
            subnetDO.setSubnetIp(SubnetUtil.ipToBinaryString(cidr.substring(0, cidr.length() - 3)));
            subnetDO.setStatus(SubNetUseStatusEnum.UNUSED.getStatus());
            subnetDO.setCreateTime(nowDate);
            subnetDO.setUpdateTime(nowDate);
            subnetDO.setRegion(param.getRegion());
            subnetDO.setSecurityGroupId(param.getSecurityGroupId());
            addSubNet.add(subnetDO);
        }
        this.saveBatch(addSubNet);
    }

    /**
     * 调用open api创建子网
     *
     * @param netSegs
     * @throws InterruptedException
     */
    private List<CreateSubnetResponse> createSubNets(List<String> netSegs, String vpcId,  ProxyAuth auth) throws Exception {
        List<List<String>> lists = Lists.partition(netSegs, CommonConstant.BATCH_HANDLE_SIZE_FIVE);
        List<CreateSubnetResponse> result = new ArrayList<>();
        for (List<String> list : lists) {
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (String cidr : list) {
                executorService.execute(() -> {
                    try {
                        CreateSubnetResponse subNetResult = subNetApiService.createSubNet(cidr, vpcId, auth);
                        if(Objects.nonNull(subNetResult)&&subNetResult.isResult()){
                            result.add(subNetResult);
                        }
                    } catch (Exception e) {
                        log.error("创建子网:{}失败 error:{}", cidr, e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return result;
    }


    /**
     * 使用工具创建子网段IP，并校验创建的子网段是否超出父网段
     *
     * @param param
     * @param subNetLastUse
     * @param subNetNum
     * @param parentNet
     * @return
     * @throws Exception
     */
    private List<String> getSubNetStrs(CreateSubnetParam param, String subNetLastUse, int subNetNum, String parentNet) throws Exception {

        List<String> netSegs = SubnetUtil.getSubNetSeg(param.getSubNetMaskLength(), subNetNum, subNetLastUse);

        if (CollectionUtils.isEmpty(netSegs)) {
            return Collections.EMPTY_LIST;
        }

        netSegs.removeIf(subNet -> !SubnetUtil.judgeSubNetSeg(subNet, parentNet));
        return netSegs;
    }


    private String getMaxSubNetUse(CreateSubnetParam param) {
        LambdaQueryWrapper<SubnetDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubnetDO::getVpcId, param.getVpcId());
        queryWrapper.eq(SubnetDO::getRegion, param.getRegion());
        queryWrapper.orderByDesc(SubnetDO::getSubnetIp);
        queryWrapper.last("LIMIT 1");
        queryWrapper.select(SubnetDO::getCidr);

        SubnetDO subnetDO = this.getOne(queryWrapper);
        if (Objects.nonNull(subnetDO)) {
            return subnetDO.getCidr();
        }
        return null;
    }

    @Override
    public List<SubnetDO> getSubnetBySubnetIds(List<String> subnetIds) {
        LambdaQueryWrapper<SubnetDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubnetDO::getSubnetId, subnetIds);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
    public void releaseSubnet(List<String> subnetIds) {
        LambdaUpdateWrapper<SubnetDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SubnetDO::getStatus,SubNetUseStatusEnum.UNUSED.getStatus());
        updateWrapper.in(SubnetDO::getSubnetId, subnetIds);
        this.update(updateWrapper);
    }

    @Override
    public SubnetDO getSubnetBySubnetId(String subnetId) {
        LambdaQueryWrapper<SubnetDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubnetDO::getSubnetId, subnetId);
        return this.getOne(queryWrapper);
    }
}
