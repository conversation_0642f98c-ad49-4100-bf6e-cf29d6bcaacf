package com.ksyun.cfwapi.deadletter;

import com.ksyun.cfwapi.deadletter.enums.DeadLetterQueueInfo;
import com.ksyun.cfwcore.config.RabbitMqConfig;
import com.ksyun.cfwcore.context.SpringContext;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.Connection;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;

@Slf4j
@Component
public class DeadLetterConfigure {

    private List<SimpleMessageListenerContainer> listenerContainers = new ArrayList<>();

    @Autowired
    private SpringContext springContext;

    @Autowired
    @Qualifier("apiScurityConnectionFactory")
    private  CachingConnectionFactory factory;

    @PostConstruct
    public void init() throws Exception {
        Connection conn = factory.createConnection();
        Channel c = conn.createChannel(true);

        Map<String, DeadLetterMessageListener> beans = springContext.getContext().getBeansOfType(DeadLetterMessageListener.class);
        for (Map.Entry<String, DeadLetterMessageListener> entry : beans.entrySet()) {
            DeadLetterListener annotation = entry.getValue().getClass().getAnnotation(DeadLetterListener.class);
            DeadLetterQueueInfo queueInfo = annotation.queueInfo();

            //死信队列
            c.queueDeclare(queueInfo.getDeadLetterQueueName(), true, false, false, null);
            c.queueBind(queueInfo.getDeadLetterQueueName(), RabbitMQConfiguration.KSYUN_DELAY_EXCHANGE, queueInfo.getDeadLetterRoutingKey());

            //retry队列
            Map<String, Object> params = new HashMap<>();
            params.put("x-dead-letter-exchange", RabbitMQConfiguration.KSYUN_DELAY_EXCHANGE);
            params.put("x-dead-letter-routing-key", queueInfo.getDeadLetterRoutingKey());
            params.put("x-message-ttl", queueInfo.getTtl());

            c.queueDeclare(queueInfo.getQueueName(), true, false, false, params);
            c.queueBind(queueInfo.getQueueName(), RabbitMQConfiguration.KSYUN_DELAY_EXCHANGE, queueInfo.getRoutingKey());

            //监听死信队列
            SimpleMessageListenerContainer listenerContainer = new SimpleMessageListenerContainer();
            listenerContainer.setConnectionFactory(factory);
            listenerContainer.setQueueNames(queueInfo.getDeadLetterQueueName());
            listenerContainer.setMessageListener(entry.getValue());
            listenerContainer.start();
            listenerContainers.add(listenerContainer);
        }
    }

    @PreDestroy
    public void destroy() {
        if (CollectionUtils.isNotEmpty(listenerContainers)) {
            listenerContainers.forEach(SimpleMessageListenerContainer::stop);
        }
    }
}
