package com.ksyun.cfwapi.deadletter;

import com.ksyun.cfwcore.config.RabbitMqConfig;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import com.ksyun.comm.rabbitmq.RabbitMQConstants;
import com.ksyun.comm.rabbitmq.RabbitMQUtils;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DeadLetterSendService {

    @Autowired
    private RabbitMqConfig rabbitMqConfig;

    public void sendMq(String routeKey, Object message) {
        try {
            RabbitMQUtils.sendAck(
                    "DeadLetter",
                    rabbitMqConfig.getRabbitmqAddress(),
                    null,
                    0,
                    rabbitMqConfig.getRabbitmqUsername(),
                    rabbitMqConfig.getRabbitmqPassword(),
                    RabbitMQConfiguration.KSYUN_DELAY_EXCHANGE,
                    routeKey,
                    message,
                    false,
                    false,
                    RabbitMQConstants.ExChangeType.Topic.getValue(),
                    rabbitMqConfig.getRabbitmqVhost()
            );
        } catch (Exception e) {
            log.error("DeadLetterSendService send to routingKey {} , message {} error", routeKey, message, e);
            ScheduleWarnLog.getLog().error(
                    new NetworkLogMsg().putMsg("DeadLetterSendService 死信队列发送异常，" + routeKey + " [" + e.getMessage() + "]"), e);
        }
        log.info("DeadLetterSendService send to routingKey {} , message {}", routeKey, message);
    }
}