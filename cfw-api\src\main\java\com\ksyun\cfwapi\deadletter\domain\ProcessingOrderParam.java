package com.ksyun.cfwapi.deadletter.domain;

import com.ksyun.common.proxy.ProxyAuth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProcessingOrderParam implements Serializable {
    private static final long serialVersionUID = 2029115307527033500L;
    private String fwId;
    private String subOrderId;
    private String operationType;
    private ProxyAuth proxyAuth;
    
    public ProcessingOrderParam() {
    }
    public ProcessingOrderParam(String fwId, String subOrderId, String operationType,ProxyAuth proxyAuth) {
        this.fwId = fwId;
        this.subOrderId = subOrderId;
        this.operationType = operationType;
        this.proxyAuth = proxyAuth;
        }
}
