package com.ksyun.cfwapi.deadletter.enums;

import lombok.Getter;

/**
 * queueName 是原队列名称
 * routingKey 是原队列路由键
 * deadLetterQueueName 是死信队列名称
 * deadLetterRoutingKey 是死信队列路由键
 * ttl 是消息过期时间
 * 使用routingKey发送到原队列 原队列不设消费者 消息等ttl时间后会发送到死信队列 由死信队列消费者消费
 */
@Getter
public enum DeadLetterQueueInfo {

    ORDER_NOTIFY_DELAY("security_order_notify_delay_queue", "order_notify_delay", "security_order_notify_delay_dl_queue", "order_notify_delay_dl", 1200000),

    ;

    private String queueName;
    private String routingKey;
    private String deadLetterQueueName;
    private String deadLetterRoutingKey;
    private Integer ttl;

    DeadLetterQueueInfo(String queueName, String routingKey, String deadLetterQueueName, String deadLetterRoutingKey, Integer ttl) {
        this.queueName = queueName;
        this.routingKey = routingKey;
        this.deadLetterQueueName = deadLetterQueueName;
        this.deadLetterRoutingKey = deadLetterRoutingKey;
        this.ttl = ttl;
    }
}
