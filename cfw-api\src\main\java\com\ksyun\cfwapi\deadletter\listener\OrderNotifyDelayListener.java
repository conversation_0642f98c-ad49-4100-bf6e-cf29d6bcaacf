package com.ksyun.cfwapi.deadletter.listener;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.deadletter.DeadLetterListener;
import com.ksyun.cfwapi.deadletter.DeadLetterMessageListener;
import com.ksyun.cfwapi.deadletter.domain.ProcessingOrderParam;
import com.ksyun.cfwapi.deadletter.enums.DeadLetterQueueInfo;
import com.ksyun.cfwapi.service.cfwService.RollbackFwService;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@DeadLetterListener(queueInfo = DeadLetterQueueInfo.ORDER_NOTIFY_DELAY)
public class OrderNotifyDelayListener implements DeadLetterMessageListener {
    @Autowired
    private RollbackFwService rollbackFwService;
    @Override
    public void onMessage(Message msg) {
        if (msg == null) {
            log.warn("receive empty message from rabbitmq.queue：{}",DeadLetterQueueInfo.ORDER_NOTIFY_DELAY);
            return;
        }
        byte[] bs = msg.getBody();
        String messageJson = new String(bs, StandardCharsets.UTF_8);
        log.info("延时订单执行 receive message from queue {}, {}", DeadLetterQueueInfo.ORDER_NOTIFY_DELAY.getDeadLetterQueueName(), messageJson);
        try {
            log.info("[CancelProcessingOrderHander]取消处理中订单开始 get message from queue,param:[{}]",JSONUtil.toJsonStr(messageJson));
            ProcessingOrderParam rollbackFwParam = jsonBinder.fromJson(messageJson, ProcessingOrderParam.class);
            ProxyAuth auth = rollbackFwParam.getProxyAuth();
            rollbackFwService.cancelProcessingOrder(auth, rollbackFwParam);
            log.info("[CancelProcessingOrderHander]取消处理中订单开始结束 auth: [{}], param: [{}]", JSONUtil.toJsonStr(auth), rollbackFwParam);
        }catch (Exception e){
            log.error("延时任务处理失败，{}",JSONUtil.toJsonStr(msg));
        }
    }
}
