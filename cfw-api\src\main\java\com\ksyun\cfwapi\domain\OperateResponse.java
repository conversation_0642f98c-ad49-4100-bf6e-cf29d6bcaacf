package com.ksyun.cfwapi.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OperateResponse implements Serializable {
    private static final long serialVersionUID = 3397548515615613121L;
    
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("Result")
    private boolean result;

    @JsonProperty("Id")
    private String id;
}
