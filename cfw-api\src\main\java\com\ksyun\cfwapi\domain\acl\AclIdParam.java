package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AclIdParam implements Serializable {
    private static final long serialVersionUID = -468613902850680123L;
    @JsonProperty("AclIds")
    @NotEmpty(message = "AclId不能为空")
    private List<String> aclIds;
}
