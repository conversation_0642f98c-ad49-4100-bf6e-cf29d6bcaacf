package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class AlterAclPriorityParam implements Serializable {
    private static final long serialVersionUID = -567010910772997053L;
    @JsonProperty("PriorityPosition")
    @NotBlank(message = "PriorityPosition 不能为空")
    private String priorityPosition;

    @JsonProperty("AclId")
    @NotBlank(message = "AclId 不能为空")
    private String aclId;

    /**
     *防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "防火墙实例ID不能为空")
    private String cfwInstanceId;
    
}
