package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AlterCfwAclStatusParam implements Serializable {
    private static final long serialVersionUID = 1125229495307612793L;

    @JsonProperty("AclIds")
    @NotEmpty(message = "AclId不能为空")
    private List<String> aclIds;

    @JsonProperty("Status")
    @NotBlank(message = "Status不能为空")
    @Pattern(regexp = Validation.REGEX_STATUS, message = ErrorCode.StateInvalid)
    private String status;
}
