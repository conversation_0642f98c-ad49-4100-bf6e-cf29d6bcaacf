package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AreaInfo implements Serializable {
    private static final long serialVersionUID = 6038228245607737467L;
    @JsonProperty("AreaCode")
    private String areaCode;
    @JsonProperty("AreaName")
    private String areaName;
    @JsonProperty("SubAreas")
    private List<AreaInfo> subAreas;
}
