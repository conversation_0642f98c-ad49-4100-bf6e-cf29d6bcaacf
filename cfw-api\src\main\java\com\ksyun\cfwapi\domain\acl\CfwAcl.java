package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CfwAcl implements Serializable {
    private static final long serialVersionUID = 2604586558442627420L;
    /**
     *防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    /**
     *aclId
     */
    @JsonProperty("AclId")
    private String aclId;

    /**
     *acl名称
     */
    @JsonProperty("AclName")
    private String aclName;

    /**
     *出入向(in入向, out出向)
     */
    @JsonProperty("Direction")
    private String direction;

    /**
     *源地址类型(ip|addrbook|zone|any)
     */
    @JsonProperty("SrcType")
    private String srcType;

    /**
     *源IP
     */
    @JsonProperty("SrcIps")
    private List<String> srcIps;

    /**
     *源地址簿Name
     */
    @JsonProperty("SrcAddrbooks")
    private List<KeyInfo> srcAddrbooks;

    /**
     *地域
     */
    @JsonProperty("SrcZones")
    private List<AreaInfo> srcZones;

    /**
     *目的地址类型(ip|addrbook|any)
     */
    @JsonProperty("DestType")
    private String destType;

    /**
     *目的IP
     */
    @JsonProperty("DestIps")
    private List<String> destIps;

    /**
     *目的地址簿Name
     */
    @JsonProperty("DestAddrbooks")
    private List<KeyInfo> destAddrbooks;

    /**
     *目的地域
     */
    @JsonProperty("DestZones")
    private List<AreaInfo> destZones;


    /**
     *服务类型(service|servicegroup|any)
     */
    @JsonProperty("ServiceType")
    private String serviceType;

    /**
     *服务信息（协议:源端口最小-源端口最大/目的最小-目的最大 ）
     * 例：TCP:1-100/2-200,UDP:22/33,ICMP
     */
    @JsonProperty("ServiceInfos")
    private List<String> serviceInfos;

    /**
     *服务组Name
     */
    @JsonProperty("ServiceGroups")
    private List<KeyInfo> serviceGroups;

    /**
     *应用类型(app|any)
     */
    @JsonProperty("AppType")
    private String appType;

    /**
     *应用值
     */
    @JsonProperty("AppValue")
    private List<String> appValues;

    /**
     *动作(accept|deny)
     */
    @JsonProperty("Policy")
    private String policy;

    /**
     *状态(start|stop)
     */
    @JsonProperty("Status")
    private String status;

    /**
     *优先级
     */
    @JsonProperty("Priority")
    private Integer priority;

    /**
     *描述
     */
    @JsonProperty("Description")
    private String description;

    /**
     *命中数
     */
    @JsonProperty("HitCount")
    private Long hitCount;

    /**
     *创建时间 2024-11-06 14:39:00
     */
    @JsonProperty("CreateTime")
    private String createTime;

}
