package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CreateCfwAclParam implements Serializable {
    private static final long serialVersionUID = -2702469170356942259L;
    /**
     *防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "防火墙实例ID不能为空")
    private String cfwInstanceId;

    /**
     *acl名称
     */
    @JsonProperty("AclName")
    @NotBlank(message = "acl名称不能为空")
    @Length(min = 1, max = 64)
    private String aclName;

    /**
     *出入向(in入向, out出向)
     */
    @JsonProperty("Direction")
    @NotBlank(message = "出入向不能为空")
    private String direction;

    /**
     *源地址类型(ip|addrbook|zone|any)
     */
    @JsonProperty("SrcType")
    @NotBlank(message = "源地址类型不能为空")
    private String srcType;

    /**
     *源IP
     */
    @JsonProperty("SrcIps")
    @Size(max = 20,message = "ip个数不能超过20")
    private List<String> srcIps;

    /**
     *源地址簿Name
     */
    @JsonProperty("SrcAddrbooks")
    @Size(max = 2,message = "地址簿个数不能超过2")
    private List<String> srcAddrbooks;

    /**
     *地域
     */
    @JsonProperty("SrcZones")
    private List<AreaInfo> srcZones;

    /**
     *目的地址类型(ip|addrbook|any)
     */
    @JsonProperty("DestType")
    @NotBlank(message = "目的地址类型不能为空")
    private String destType;

    /**
     *目的IP
     */
    @JsonProperty("DestIps")
    @Size(max = 20,message = "ip个数不能超过20")
    private List<String> destIps;

    /**
     *目的地址簿
     */
    @JsonProperty("DestAddrbooks")
    @Size(max = 2,message = "地址簿个数不能超过2")
    private List<String> destAddrbooks;

    /**
     *目的地域
     */
    @JsonProperty("DestZones")
    private List<AreaInfo> destZones;

    /**
     *服务类型(service|servicegroup|any)
     */
    @JsonProperty("ServiceType")
    @NotBlank(message = "服务类型不能为空")
    private String serviceType;

    /**
     *服务信息（协议:源端口最小-源端口最大/目的最小-目的最大 ）
     * 例：TCP:1-100/2-200,UDP:22/33,ICMP
     */
    @JsonProperty("ServiceInfos")
    @Size(max = 5,message = "服务个数不能超过5")
    private List<String> serviceInfos;

    /**
     *服务组Name
     */
    @JsonProperty("ServiceGroups")
    @Size(max = 5,message = "服务组个数不能超过5")
    private List<String> serviceGroups;

    /**
     *应用类型(app|any)
     */
    @JsonProperty("AppType")
    @NotBlank(message = "应用类型不能为空")
    private String appType;

    /**
     *应用值
     */
    @JsonProperty("AppValue")
    private List<String> appValues;

    /**
     *动作(accept|deny)
     */
    @JsonProperty("Policy")
    @NotBlank(message = "动作不能为空")
    private String policy;

    /**
     *状态(start|stop)
     */
    @JsonProperty("Status")
    @NotBlank(message = "状态不能为空")
    private String status;

    /**
     *优先级
     */
    @JsonProperty("PriorityPosition")
    @NotBlank(message = "优先级不能为空")
    private String priorityPosition;

    /**
     *描述
     */
    @JsonProperty("Description")
    @Size(max = 225,message = "描述长度不能超过225")
    private String description;
}
