package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateCfwAclResponse implements Serializable {
    private static final long serialVersionUID = 8599072107054088903L;
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("CfwAcl")
    private CfwAcl cfwAcl;
}
