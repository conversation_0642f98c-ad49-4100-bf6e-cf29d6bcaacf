package com.ksyun.cfwapi.domain.acl;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DeleteAclParam implements Serializable {
    private static final long serialVersionUID = -919019814993238269L;
    @JsonProperty("AclIds")
    @NotEmpty(message = "AclId不能为空")
    private List<String> aclIds;

    /**
     *防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "防火墙实例ID不能为空")
    private String cfwInstanceId;
}