package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DescribeCfwAclParam implements Serializable {
    private static final long serialVersionUID = -3562579942035288924L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "CfwInstanceId不能为空")
    private String cfwInstanceId;

    @JsonProperty("AclIds")
    private List<String> aclIds;

    @Max(value = 1000, message = "最大查询1000条")
    @Min(value = 5, message = "最小查询5条")
    @NotNull
    @JsonProperty("MaxResults")
    private Integer maxResults;

    @JsonProperty("NextToken")
    private String nextToken;
}
