package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeCfwAclResponse implements Serializable {
    private static final long serialVersionUID = -3562579942035288924L;
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("CfwAcls")
    private List<CfwAcl> cfwAcls;

    @JsonProperty("NextToken")
    private String nextToken;

    @JsonProperty("TotalCount")
    private Integer totalCount;
}
