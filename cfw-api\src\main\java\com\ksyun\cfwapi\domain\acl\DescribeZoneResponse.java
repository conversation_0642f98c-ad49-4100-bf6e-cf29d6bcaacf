package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeZoneResponse implements Serializable {
    private static final long serialVersionUID = 6806846245401350829L;

    /**
     * 地域
     */
    @JsonProperty("Zones")
    private List<AreaInfo> zones;

}
