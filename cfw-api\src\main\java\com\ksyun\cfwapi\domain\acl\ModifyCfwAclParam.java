package com.ksyun.cfwapi.domain.acl;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ModifyCfwAclParam implements Serializable {

    private static final long serialVersionUID = -8042170300972658382L;
    /**
     *aclId
     */
    @JsonProperty("AclId")
    private String aclId;

    /**
     *acl名称
     */
    @JsonProperty("AclName")
    private String aclName;

    /**
     *出入向(in入向, out出向)
     */
    @JsonProperty("Direction")
    private String direction;

    /**
     *源地址类型(ip|addrbook|zone|any)
     */
    @JsonProperty("SrcType")
    private String srcType;

    /**
     *源IP
     */
    @JsonProperty("SrcIps")
    @Size(max = 20,message = "ip个数不能超过20")
    private List<String> srcIps;

    /**
     *源地址簿Name
     */
    @JsonProperty("SrcAddrbooks")
    @Size(max = 2,message = "地址簿个数不能超过2")
    private List<String> srcAddrbooks;

    /**
     *地域
     */
    @JsonProperty("SrcZones")
    private List<AreaInfo> srcZones;

    /**
     *目的地址类型(ip|addrbook|any)
     */
    @JsonProperty("DestType")
    private String destType;

    /**
     *目的IP
     */
    @JsonProperty("DestIps")
    @Size(max = 20,message = "ip个数不能超过20")
    private List<String> destIps;

    /**
     *目的地址簿Name
     */
    @JsonProperty("DestAddrbooks")
    @Size(max = 2,message = "地址簿个数不能超过2")
    private List<String> destAddrbooks;

    /**
     *目的地域
     */
    @JsonProperty("DestZones")
    private List<AreaInfo> destZones;

    /**
     *服务类型(service|servicegroup|any)
     */
    @JsonProperty("ServiceType")
    private String serviceType;

    /**
     *服务信息（协议:源端口最小-源端口最大/目的最小-目的最大 ）
     * 例：TCP:1-100/2-200,UDP:22/33,ICMP
     */
    @JsonProperty("ServiceInfos")
    @Size(max = 5,message = "服务个数不能超过5")
    private List<String> serviceInfos;

    /**
     *服务组Name
     */
    @JsonProperty("ServiceGroups")
    @Size(max = 5,message = "服务组个数不能超过5")
    private List<String> serviceGroups;

    /**
     *应用类型(app|any)
     */
    @JsonProperty("AppType")
    private String appType;

    /**
     *应用值
     */
    @JsonProperty("AppValue")
    private List<String> appValues;

    /**
     *动作(accept|deny)
     */
    @JsonProperty("Policy")
    private String policy;

    /**
     *状态(start|stop)
     */
    @JsonProperty("Status")
    private String status;

    /**
     *优先级
     */
    @JsonProperty("PriorityPosition")
    private String priorityPosition;

    /**
     *描述
     */
    @JsonProperty("Description")
    @Size(max = 225,message = "描述长度不能超过225")
    private String description;
}
