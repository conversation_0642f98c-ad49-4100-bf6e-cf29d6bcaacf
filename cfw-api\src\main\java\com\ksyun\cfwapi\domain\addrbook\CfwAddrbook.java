package com.ksyun.cfwapi.domain.addrbook;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ksyun.cfwapi.dao.entity.CfwAddrbookDO;
import com.ksyun.cfwcore.constants.CommonConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CfwAddrbook implements Serializable {
    private static final long serialVersionUID = -4409120024552412303L;
    /**
     *地址簿ID
     */
    @JsonProperty("AddrbookId")
    private String addrbookId;
    /**
     *名称
     */
    @JsonProperty("AddrbookName")
    private String addrbookName;
    /**
     *ip地址
     */
    @JsonProperty("IpAddress")
    private List<String> ipAddress;
    /**
     *描述
     */
    @JsonProperty("Description")
    private String description;
    /**
     *被引用次数
     */
    @JsonProperty("CitationCount")
    private Integer citationCount = 0;

    /**
     * 创建时间 2024-11-06 14:39:00
     */
    @JsonProperty("CreateTime")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date createTime;
    
    public CfwAddrbook(CfwAddrbookDO addrbook){
        this.addrbookId = addrbook.getAddrbookId();
        this.addrbookName = addrbook.getAddrbookName();
        this.ipAddress =  Arrays.asList(addrbook.getIpAddress().split(CommonConstant.COMMA));
        this.description = addrbook.getDescription();
        this.createTime = addrbook.getCreateTime();
    }
}
