package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CreateAddrbookResponse implements Serializable {
    private static final long serialVersionUID = 7073644206341483758L;
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("CfwAddrbook")
    private CfwAddrbook cfwAddrbook;
}
