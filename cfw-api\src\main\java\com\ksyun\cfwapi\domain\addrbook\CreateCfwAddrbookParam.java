package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateCfwAddrbookParam implements Serializable {
    private static final long serialVersionUID = 7764149330099859119L;

    /**
     * 防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;
    /**
     * 名称
     */
    @JsonProperty("AddrbookName")
    @Length(max = 95,message = "名称不能超过95个字符")
    private String addrbookName;
    /**
     * ip地址
     */
    @JsonProperty("IpAddress")
    @NotEmpty(message = "ip地址不能为空")
    @Size(max = 640,message = "每个IP地址簿中最多添加640个IP地址成员")
    private List<String> ipAddress;
    /**
     * 描述
     */
    @JsonProperty("Description")
    @Length(max = 500,message = "ip类型不能超过500个字符")
    private String description;

    /**
     * IpType
     */
    @JsonProperty("IpVersion")
    private String ipVersion;

}
