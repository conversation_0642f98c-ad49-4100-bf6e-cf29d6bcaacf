package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeleteCfwAddrbookParam implements Serializable {
    private static final long serialVersionUID = 1675765839535637091L;
    @JsonProperty("AddrbookId")
    private String addrbookId;
}
