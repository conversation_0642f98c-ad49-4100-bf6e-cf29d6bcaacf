package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DescribeAddrbookParam implements Serializable {
    private static final long serialVersionUID = 7203986084932911685L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "cfwInstanceId不能为空")
    private String cfwInstanceId;

    @JsonProperty("AddrbookIds")
    private List<String> addrbookIds;

    @Max(value = 1000, message = "最大查询1000条")
    @Min(value = 5, message = "最小查询5条")
    @JsonProperty("MaxResults")
    private Integer maxResults = 1000;

    @JsonProperty("NextToken")
    private String nextToken ;
}
