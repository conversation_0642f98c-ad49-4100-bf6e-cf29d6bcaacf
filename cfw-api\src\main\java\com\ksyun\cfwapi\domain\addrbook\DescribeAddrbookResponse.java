package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeAddrbookResponse implements Serializable {
    private static final long serialVersionUID = -3437412879979150744L;
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("CfwAddrbooks")
    private List<CfwAddrbook> cfwAddrbooks;

    @JsonProperty("NextToken")
    private String nextToken;

    @JsonProperty("TotalCount")
    private Integer totalCount;
}
