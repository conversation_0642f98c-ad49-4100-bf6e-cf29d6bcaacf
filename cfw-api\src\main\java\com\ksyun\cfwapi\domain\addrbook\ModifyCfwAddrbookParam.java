package com.ksyun.cfwapi.domain.addrbook;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModifyCfwAddrbookParam implements Serializable {

    private static final long serialVersionUID = -2397214870240890724L;

    /**
     * 防火墙实例ID
     */
    @JsonProperty("AddrbookId")
    @NotBlank(message = "地址簿id不能为空" )
    private String addrbookId;
    /**
     * 名称
     */
    @JsonProperty("AddrbookName")
    @Length(max = 95,message = "名称不能超过95个字符")
    private String addrbookName;
    /**
     * ip地址
     */
    @JsonProperty("IpAddress")
    @NotEmpty(message = "ip地址不能为空")
    @Size(max = 640,message = "每个IP地址簿中最多添加640个IP地址成员")
    private List<String> ipAddress;
    /**
     * 描述
     */
    @JsonProperty("Description")
    @Length(max = 500,message = "ip类型不能超过500个字符")
    private String description;

    /**
     * Ip版本
     */
    @JsonProperty("IpVersion")
    private String ipVersion;
}
