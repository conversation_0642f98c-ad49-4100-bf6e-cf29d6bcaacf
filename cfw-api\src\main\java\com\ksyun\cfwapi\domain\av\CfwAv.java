package com.ksyun.cfwapi.domain.av;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CfwAv implements Serializable {
    private static final long serialVersionUID = 4464906070177725102L;
    @JsonProperty("AvId")
    private String avId;
    @JsonProperty("Protocol")
    private String protocol;
    @JsonProperty("ProtectType")
    private String protectType;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
