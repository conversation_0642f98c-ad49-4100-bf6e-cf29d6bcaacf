package com.ksyun.cfwapi.domain.av;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class DescribeCfwAvParam implements Serializable {
    private static final long serialVersionUID = 7035576432650457633L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "CfwInstanceId不能为空")
    private String cfwInstanceId;
}
