package com.ksyun.cfwapi.domain.av;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeCfwAvResponse implements Serializable {
    private static final long serialVersionUID = 7035576432650457633L;
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("CfwAvs")
    private List<CfwAv> cfwAvs;
}
