package com.ksyun.cfwapi.domain.av;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ModifyCfwAvParam implements Serializable {
    private static final long serialVersionUID = -1145380756431520380L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "CfwInstanceId不能为空")
    private String cfwInstanceId;
    @JsonProperty("Protocol")
    @NotBlank(message = "Protocol不能为空")
    private String protocol;
    @JsonProperty("ProtectType")
    private String protectType;
    @JsonProperty("Status")
    private String status;
}
