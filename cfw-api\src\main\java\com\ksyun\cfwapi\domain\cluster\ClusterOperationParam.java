package com.ksyun.cfwapi.domain.cluster;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClusterOperationParam implements Serializable {
    private static final long serialVersionUID = -1761185309321420305L;
    @JsonProperty("Action")
    @NotBlank(message = "Action不能为空")
    private String action;

    @JsonProperty("FileName")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @JsonProperty("Region")
    @NotBlank(message = "Region不能为空")
    private String region;

    @JsonProperty("CfwInstanceIds")
    private List<String> cfwInstanceIds;


}
