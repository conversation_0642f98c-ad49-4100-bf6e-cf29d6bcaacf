package com.ksyun.cfwapi.domain.cluster;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class UpdateStartParam implements Serializable {
    private static final long serialVersionUID = 2427961457583905722L;
    @JsonProperty("CfwInstanceIds")
    private List<String> cfwInstanceIds;

    @JsonProperty("Region")
    @NotBlank(message = "Region不能为空")
    private String region;

}
