package com.ksyun.cfwapi.domain.dataDoard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class CfwAclLog implements Serializable {
    private static final long serialVersionUID = -8870464378446811770L;
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    @JsonProperty("AclId")
    private String aclId;

    @JsonProperty("AclName")
    private String aclName;

    @JsonProperty("StartTime")
    private String startTime;

    @JsonProperty("SrcIp")
    private String srcIp;

    @JsonProperty("SrcPort")
    private Integer srcPort;

    @JsonProperty("DestIp")
    private String destIp;

    @JsonProperty("DestPort")
    private Integer destPort;

    @JsonProperty("Protocol")
    private String protocol;

    @JsonProperty("App")
    private String app;

    @JsonProperty("Policy")
    private String policy;

    @JsonProperty("Direction")
    private String direction;

    @JsonProperty("CreateTime")
    private String createTime;
}
