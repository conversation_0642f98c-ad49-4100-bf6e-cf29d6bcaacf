package com.ksyun.cfwapi.domain.dataDoard;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
/**
 * <AUTHOR>
 */
@Data
public class CfwAttackLog implements Serializable {
    private static final long serialVersionUID = 2740405098440286422L;
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    @JsonProperty("Time")
    private Date time;

    @JsonProperty("ThreatType")
    private String threatType;

    @JsonProperty("ThreatName")
    private String threatName;

    @JsonProperty("Severity")
    private String severity;

    @JsonProperty("SrcIp")
    private String srcIp;

    @JsonProperty("SrcPort")
    private Integer srcPort;

    @JsonProperty("DestIp")
    private String destIp;

    @JsonProperty("DestPort")
    private Integer destPort;

    @JsonProperty("Protocol")
    private String protocol;

    @JsonProperty("App")
    private String app;

    @JsonProperty("Policy")
    private String policy;

    @JsonProperty("Direction")
    private String direction;

    @JsonProperty("PayLoad")
    private String payLoad;

    @JsonProperty("CreateTime")
    private String createTime;

}
