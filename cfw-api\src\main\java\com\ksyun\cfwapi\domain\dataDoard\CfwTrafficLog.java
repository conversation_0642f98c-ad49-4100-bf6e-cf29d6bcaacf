package com.ksyun.cfwapi.domain.dataDoard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class CfwTrafficLog implements Serializable {
    private static final long serialVersionUID = 577701558467849717L;
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    @JsonProperty("StartTime")
    private String startTime;

    @JsonProperty("EndTime")
    private String endTime;

    @JsonProperty("SrcIp")
    private String srcIp;

    @JsonProperty("SrcPort")
    private Integer srcPort;

    @JsonProperty("DestIp")
    private String destIp;

    @JsonProperty("DestPort")
    private Integer destPort;

    @JsonProperty("Protocol")
    private String protocol;

    @JsonProperty("PacketSize")
    private Integer packetSize;

    @JsonProperty("CreateTime")
    private String createTime;
}
