package com.ksyun.cfwapi.domain.dataDoard;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DescribeLogParam implements Serializable {
    private static final long serialVersionUID = 541092280058074527L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "cfwInstanceId不能为空")
    private String cfwInstanceId;
    @NotNull(message = "查询开始时间不能为空")
    @JsonProperty("StartTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;
    @NotNull(message = "查询结束时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @JsonProperty("EndTime")
    private Date endTime;

    @JsonProperty("QueryKeyword")
    private String queryKeyword;

    @JsonProperty("MaxResults")
    private Integer maxResults = 1000;

    @JsonProperty("NextToken")
    private String nextToken = "0";
}
