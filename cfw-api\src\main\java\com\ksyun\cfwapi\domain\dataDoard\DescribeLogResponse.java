package com.ksyun.cfwapi.domain.dataDoard;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeLogResponse<T> implements Serializable {
    private static final long serialVersionUID = 1605240816634935842L;
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("DataList")
    private List<T> dataList;

    @JsonProperty("NextToken")
    private String nextToken;

    @JsonProperty("TotalCount")
    private Long totalCount;
}
