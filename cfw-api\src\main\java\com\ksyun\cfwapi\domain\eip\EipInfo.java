package com.ksyun.cfwapi.domain.eip;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class EipInfo {

	protected String uuid;

	protected String floating_network_uuid;

	protected String floating_network_name;

	protected String state;

	protected String IpState;

	protected String type;

	protected String usage_type;

	protected String instance_id;

	protected String floating_ip_address;

	protected Integer egress;

	protected Integer ingress;

	protected String igw_uuid;

	protected String router_uuid;

	protected String lb_pool_uuid;

	protected String device_uuid;

	protected String fixed_ip_address;

	protected String port_uuid;

	protected String created_at;

	protected String bwp_id;

	protected String iamProjectId;

	protected String ipVersion;

	protected String user_tag;

	protected String vif_type;

	protected String ip;

	protected Integer billType;

	protected String chargeType;

	protected String productType;

	protected String productWhat;

	protected String serviceEndTime;

	protected String binding_type;

	protected String hostType;

	protected String natpool_id;

	protected String eip_pool_id;

	private String firewallId;

	private String projectName;
}
