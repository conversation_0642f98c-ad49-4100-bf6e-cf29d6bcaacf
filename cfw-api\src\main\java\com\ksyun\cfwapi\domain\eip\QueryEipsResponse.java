package com.ksyun.cfwapi.domain.eip;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.ksyun.cfwapi.enums.EipBindModeEnum;
import com.ksyun.cfwcore.constants.Constants;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.annotation.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@XmlRootElement(name = "DescribeAddressesResponse")
@XmlAccessorType(XmlAccessType.NONE)
@NoArgsConstructor
@Data
public class QueryEipsResponse implements Serializable {

	private static final long serialVersionUID = 1998018604934486194L;
	@Expose
	@SerializedName("RequestId")
	@JsonProperty("RequestId")
	private String request_id;

	@Expose
	@SerializedName("AddressesSet")
	@JsonProperty("AddressesSet")
	private List<AddressInfo> eips;

	@Expose
	@SerializedName("NextToken")
	@JsonProperty("NextToken")
	private String next_token;

	private String pre_token;

	@Expose
	@SerializedName("TotalCount")
	@JsonProperty("TotalCount")
	private Integer totalCount;

	public QueryEipsResponse(QueryEipsResult response,String source) {
		this.next_token=response.getNext_token();
		this.pre_token=response.getPre_token();
		this.totalCount = response.getTotalCount();
		eips = new ArrayList<AddressInfo>();
		if (response.getEips() != null && !response.getEips().isEmpty()) {
			for (EipInfo ip : response
					.getEips()) {
				AddressInfo eip = new AddressInfo();
				eip.setCreated_at(ip.getCreated_at());
				// BandWidth
				eip.setEgress(ip.getEgress());
				eip.setFixed_ip_address(ip.getFixed_ip_address());
				// PublicIp
				eip.setAddress(ip.getFloating_ip_address());
				// LineId
				eip.setNetwork_uuid(ip.getFloating_network_uuid());
				eip.setIgw_uuid(ip.getIgw_uuid());
				eip.setIngress(ip.getIngress());
				eip.setLb_pool_uuid(ip.getLb_pool_uuid());
				eip.setNatpool_id(ip.getNatpool_id());
				eip.setPort_uuid(ip.getPort_uuid());
				eip.setRouter_uuid(ip.getRouter_uuid());
				eip.setType(ip.getType());
				// InstanceType
				eip.setUsage_type(ip.getUsage_type());
				// AllocationId
				eip.setUuid(ip.getUuid());
				// State
				eip.setState(ip.getState());
				eip.setIpState(ip.getIpState());
				// InstanceId
				eip.setInstance_uuid(ip.getInstance_id());
				eip.setDevice_uuid(ip.getDevice_uuid());
				eip.setNetworkInterfaceType("eni".equals(ip.getVif_type()) ? "extension" : ip.getVif_type());
				eip.setPrivateIpAddress(ip.getFixed_ip_address());
				eip.setBwp_id(ip.getBwp_id());
				//项目制ID
				eip.setIamProjectId(ip.getIamProjectId());
				//version
				eip.setIpVersion(ip.getIpVersion());

				eip.setChargeType(ip.getChargeType());
				eip.setServiceEndTime(ip.getServiceEndTime());
				if(!source.equals(Constants.SDK_SOURCE)){
					eip.setUser_tag(ip.getUser_tag());
					eip.setProductType(ip.getProductType());
					eip.setProductWhat(ip.getProductWhat());
				}
				eip.setHostType(ip.getHostType());
				eip.setMode(EipBindModeEnum.getNameByType(ip.getBinding_type()));
				eip.setEipPoolId(ip.getEip_pool_id());
				eip.setFirewallId(ip.getFirewallId());
				if(StringUtils.isNotBlank(ip.getFirewallId())){
					eip.setProtectStatus(1);
				}else{
					eip.setProtectStatus(0);
				}
				eip.setProjectName(ip.getProjectName());
				eips.add(eip);
			}
		}
	}


	@NoArgsConstructor
	@Data
	public static class AddressInfo implements Serializable{

		private static final long serialVersionUID = -5562791145730222548L;
		@Expose
		@SerializedName("PublicIp")
		@JsonProperty("PublicIp")
		private String address;

		private String type;

		@Expose
		@SerializedName("AllocationId")
		@JsonProperty("AllocationId")
		private String uuid;

		@Expose
		@SerializedName("State")
		@JsonProperty("State")
		private String state;

		@Expose
		@SerializedName("IpState")
		@JsonProperty("IpState")
		private String IpState;

		@Expose
		@SerializedName("LineId")
		@JsonProperty("LineId")
		private String network_uuid;

		@Expose
		@SerializedName("NetworkInterfaceId")
		@JsonProperty("NetworkInterfaceId")
		private String device_uuid;

		@Expose
		@SerializedName("NetworkInterfaceType")
		@XmlElement(name = "NetworkInterfaceType")
		@JsonProperty("NetworkInterfaceType")
		private String networkInterfaceType;

		@Expose
		@SerializedName("PrivateIpAddress")
		@XmlElement(name = "PrivateIpAddress")
		@JsonProperty("PrivateIpAddress")
		private String privateIpAddress;

		@Expose
		@SerializedName("BandWidth")
		@JsonProperty("BandWidth")
		private Integer egress;

		@Expose
		@SerializedName("InstanceType")
		@JsonProperty("InstanceType")
		private String usage_type;

		@Expose
		@SerializedName("InstanceId")
		@JsonProperty("InstanceId")
		private String instance_uuid;

		private Integer ingress;

		@Expose
		@SerializedName("InternetGatewayId")
		@JsonProperty("InternetGatewayId")
		private String igw_uuid;

		@Expose
		@SerializedName("BandWidthShareId")
		@JsonProperty("BandWidthShareId")
		private String  bwp_id;

		@Expose
		@SerializedName("UserTag")
		@JsonProperty("UserTag")
		private String user_tag;

		private String router_uuid;

		private String lb_pool_uuid;

		private String natpool_id;

		private String fixed_ip_address;

		private String port_uuid;

		@Expose
		@SerializedName("ChargeType")
		@JsonProperty("ChargeType")
		private String chargeType;

		@Expose
		@SerializedName("ProductType")
		@XmlElement(name = "ProductType")
		@JsonProperty("ProductType")
		private String productType;

		@Expose
		@SerializedName("ProductWhat")
		@XmlElement(name = "ProductWhat")
		@JsonProperty("ProductWhat")
		private String productWhat;

		@Expose
		@SerializedName("ServiceEndTime")
		@XmlElement(name = "ServiceEndTime")
		@JsonProperty("ServiceEndTime")
		private String ServiceEndTime;

		@Expose
		@SerializedName("IpVersion")
		@JsonProperty("IpVersion")
		private String  ipVersion;

		@Expose
		@SerializedName("ProjectId")
		@JsonProperty("ProjectId")
		private String iamProjectId;

		@Expose
		@SerializedName("ProjectName")
		@JsonProperty("ProjectName")
		private String projectName;

		@Expose
		@SerializedName("CreateTime")
		@JsonProperty("CreateTime")
		private String created_at;

		@Expose
        @SerializedName("Mode")
		@JsonProperty("Mode")
		private String mode;

		@Expose
		@SerializedName("HostType")
		@JsonProperty("HostType")
		private String hostType;

		@Expose
		@SerializedName("EipPoolId")
		@JsonProperty("EipPoolId")
		private String eipPoolId;

		@Expose
		@SerializedName("FirewallId")
		@JsonProperty("FirewallId")
		private String firewallId;

		@Expose
		@SerializedName("ProtectStatus")
		@JsonProperty("ProtectStatus")
		private Integer protectStatus;
	}

}
