package com.ksyun.cfwapi.domain.eip;

import com.ksyun.cfwapi.enums.InstanceType;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.QueryFloatingipsResponse;
import com.ksyun.cfwcore.openstack.wapper.domain.Floatingip;
import com.ksyun.cfwcore.utils.Stringable;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import com.ksyun.cfwapi.enums.EipState;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class QueryEipsResult extends Stringable {
	private List<EipInfo> eips;
	private String next_token;
	private String pre_token;
	private Integer totalCount;

	public QueryEipsResult(QueryFloatingipsResponse response) {
		if (response.getFloatingips() != null
				&& !response.getFloatingips().isEmpty()) {
			eips = new ArrayList<EipInfo>();
			totalCount = response.getTotalCount();
			for (Floatingip ip : response
					.getFloatingips()) {
				EipInfo floatingip = new EipInfo();
				floatingip.setCreated_at(ip.getCreated_at());
                boolean isInstance = false;
                floatingip.setCreated_at(ip.getCreated_at());
                if(InstanceType.KvmType.getName().equals(ip.getUsage_type())
                        || InstanceType.EpcType.getName().equals(ip.getUsage_type())
                        || InstanceType.DockerType.getName().equals(ip.getUsage_type())
						|| InstanceType.SmartNic.getName().equals(ip.getUsage_type())){
                    isInstance = true;
                }
                floatingip.device_uuid = isInstance ? ip.getDevice_uuid() : null;
				floatingip.setEgress(ip.getEgress());
				floatingip.setFixed_ip_address(ip.getFixed_ip_address());
				floatingip.setFloating_ip_address(ip.getFloating_ip_address());
				floatingip.setFloating_network_uuid(ip
						.getFloating_network_uuid());
				floatingip.setFloating_network_name(ip
						.getFloating_network_name());
				floatingip.setIgw_uuid(ip.getIgw_uuid());
				floatingip.setIngress(ip.getIngress());
				floatingip.setLb_pool_uuid(ip.getLb_pool_uuid());
				floatingip.setNatpool_id(ip.getNatpool_id());
				floatingip.setPort_uuid(ip.getPort_uuid());
				floatingip.setRouter_uuid(ip.getRouter_uuid());
				floatingip.setType(ip.getType());
				floatingip.setUsage_type(InstanceType.getAlias(ip
						.getUsage_type()));
				floatingip.setUuid(ip.getUuid());
				floatingip.setInstance_id(ip.getInstance_id());
				floatingip.state = StringUtils.isBlank(ip.getUsage_type()) ? EipState.DETACHED
						.getEipState() : EipState.ATTACHED.getEipState();
				floatingip.setIpState(ip.getAdmin_state_up() ? "start" : "stop");
				floatingip.setBwp_id(ip.getBwp_id());
				floatingip.setIamProjectId(ip.getIamProjectId());
				floatingip.setIpVersion(ip.getIpVersion());
				floatingip.setUser_tag(ip.getUser_tag());
				floatingip.setVif_type(ip.getVif_type());
				floatingip.setIp(ip.getIp());
				floatingip.setBillType(ip.getBillType());
				floatingip.setChargeType(ip.getChargeType());
				floatingip.setProductType(ip.getProductType());
				floatingip.setProductWhat(ip.getProductWhat());
				floatingip.setServiceEndTime(ip.getServiceEndTime());
				floatingip.setBinding_type(ip.getBinding_type());
				floatingip.setHostType(ip.getHostType());
				floatingip.setEip_pool_id(ip.getEip_pool_id());
				floatingip.setFirewallId(ip.getFirewall_id());
				floatingip.setProjectName(ip.getProjectName());
				eips.add(floatingip);
			}
		}
	}

}
