package com.ksyun.cfwapi.domain.eip;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class Resource {

    @Expose
    @SerializedName("ResourceUuid")
    private String resourceUuid;
    @Expose
    @SerializedName("Tags")
    private List<Tag> tags;
    @Expose
    @SerializedName("RegionCode")
    private String regionCode;
    @Expose
    @SerializedName("RegionName")
    private String regionName;
    @Expose
    @SerializedName("RegionEnName")
    private String regionEnName;
    @Expose
    @SerializedName("BindTagTotal")
    private Integer bindTagTotal;
    @Expose
    @SerializedName("InstanceName")
    private String instanceName;
    @Expose
    @SerializedName("ResourceType")
    private String resourceType;
    @Expose
    @SerializedName("ResourceTypeCn")
    private String resourceTypeCn;

    @NoArgsConstructor
    @Data
    public static class Tag {
        @Expose
        @SerializedName("resourceUuid")
        private String resourceUuid;
        @Expose
        @SerializedName("tagId")
        private Integer tagId;
        @Expose
        @SerializedName("tagKey")
        private String tagKey;
        @Expose
        @SerializedName("tagValue")
        private String tagValue;
    }
}
