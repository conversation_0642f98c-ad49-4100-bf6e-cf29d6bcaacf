package com.ksyun.cfwapi.domain.eip;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/10/16.
 */
@NoArgsConstructor
@Data
public class TagInfo {

    @Expose
    @SerializedName("ResourceUuid")
    private String resourceUuid;

    @Expose
    @SerializedName("TagId")
    private Integer tagId;

    @Expose
    @SerializedName("TagKey")
    private String tagKey;

    @Expose
    @SerializedName("TagValue")
    private String tagValue;

    public TagInfo(String resourceUuid, Integer tagId, String tagKey, String tagValue) {
        this.resourceUuid = resourceUuid;
        this.tagId = tagId;
        this.tagKey = tagKey;
        this.tagValue = tagValue;
    }

    public TagInfo(Resource.Tag tag) {
        this.resourceUuid = tag.getResourceUuid();
        this.tagId = tag.getTagId();
        this.tagKey = tag.getTagKey();
        this.tagValue = tag.getTagValue();
    }
}
