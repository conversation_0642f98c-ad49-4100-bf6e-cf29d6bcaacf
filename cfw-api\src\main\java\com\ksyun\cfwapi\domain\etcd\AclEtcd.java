package com.ksyun.cfwapi.domain.etcd;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AclEtcd implements Serializable {
    private String traceId;
    private String id;
    private String name;
    private String direction;
    private String ip_version;
    private String src_type;
    private Object src_value;
    private String dest_type;
    private Object dest_value;
    private String service_type;
    private String service_value;
    private String app_type;
    private List<String> app_value;
    private String policy;
    private String status;
    private String priority;
    private String description;
}
