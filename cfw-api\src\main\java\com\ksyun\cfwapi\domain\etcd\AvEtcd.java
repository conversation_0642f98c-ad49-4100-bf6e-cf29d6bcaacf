package com.ksyun.cfwapi.domain.etcd;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Data
public class AvEtcd implements Serializable {
    private static final long serialVersionUID = -6796117468066543783L;


    /**
     * 请求ID
     */
    private String traceId;
    /**
     * ID
     */
    private String id;

    private String name;

    /**
     * #协议类型：HTTP、SMTP、FTP、POP3、IMAP4、SMB六种，监听/拦截两种模式，协议不传代表关闭
     * "protocol": {
     *         "HTTP": "monitor",
     *         "SMTP": "monitor",
     *         "FTP": "monitor",
     *         "POP3": "block",
     *         "SMB": "block",
     *         "IMAP": "block"
     *     }
     */
    private HashMap protocol;

    /**
     * 状态(start|stop)
     */
    private String status;
}


