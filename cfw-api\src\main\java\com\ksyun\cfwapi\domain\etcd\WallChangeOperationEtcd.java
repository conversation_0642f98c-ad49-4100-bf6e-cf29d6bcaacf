package com.ksyun.cfwapi.domain.etcd;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class WallChangeOperationEtcd implements Serializable {
    private static final long serialVersionUID = -2180008188867896529L;
    private String traceId;
    private String operationId;
    private String type;
    private String action;
    private List<String> instanceids;
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private String timestamp;
}
