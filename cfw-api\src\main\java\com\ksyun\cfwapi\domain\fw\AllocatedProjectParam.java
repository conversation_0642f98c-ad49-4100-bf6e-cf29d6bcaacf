package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class AllocatedProjectParam implements Serializable {

    private static final long serialVersionUID = 5225274563668418607L;

    @JsonProperty("CfwInstanceIds")
    @NotEmpty(message = "CfwInstanceIds不能为空")
    private List<String> cfwInstanceIds;

    @JsonProperty("ProjectId")
    @NotBlank(message = "ProjectId不能为空")
    private String projectId;

}
