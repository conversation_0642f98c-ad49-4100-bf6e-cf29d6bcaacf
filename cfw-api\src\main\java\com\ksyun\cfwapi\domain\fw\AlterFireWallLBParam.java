package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlterFireWallLBParam implements Serializable {
    private static final long serialVersionUID = -7144400085372326792L;

    @NotBlank(message = "CfwInstanceId不能为空")
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;


    @NotBlank(message = "Status不能为空")
    @JsonProperty("Status")
    @Pattern(regexp = Validation.REGEX_STATUS, message = ErrorCode.StateInvalid)
    private String status;
}
