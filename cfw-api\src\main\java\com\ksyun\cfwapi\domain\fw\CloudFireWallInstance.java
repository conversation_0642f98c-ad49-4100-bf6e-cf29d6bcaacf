package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CloudFireWallInstance implements Serializable {

    private static final long serialVersionUID = -5379601324205473181L;
    @JsonProperty("InstanceId")
    private String instanceId;

    @JsonProperty("InstanceName")
    private String instanceName;

    @JsonProperty("InstanceType")
    private String instanceType;

    @JsonProperty("Bandwidth")
    private Integer bandwidth;

    @JsonProperty("Status")
    private Integer status;

    @JsonProperty("TotalEipNum")
    private Integer totalEipNum;

    @JsonProperty("TotalAclNum")
    private Integer totalAclNum;

    @JsonProperty("UsedEipNum")
    private Integer usedEipNum;

    @JsonProperty("UsedAclNum")
    private Integer usedAclNum;

    @JsonProperty("IpsStatus")
    private Integer ipsStatus;

    @JsonProperty("AvStatus")
    private Integer avStatus;

    @JsonProperty("CreateTime")
    private String createTime;

    @JsonProperty("ProjectId")
    private String projectId;

    @JsonProperty("ChargeType")
    private String chargeType;

    @JsonProperty("ProjectName")
    private String projectName;

    @JsonProperty("Region")
    private String region;

    @JsonProperty("ServiceEndTime")
    private String serviceEndTime;

    @JsonProperty("Duration")
    private Integer duration;

    @JsonProperty("FwLbId")
    private String fwLbId;

}
