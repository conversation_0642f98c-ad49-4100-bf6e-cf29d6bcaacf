package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.ksyun.cfwapi.domain.trade.TradeParam;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateFireWallLBParam extends TradeParam implements Serializable {

    private static final long serialVersionUID = -2161256625439413287L;
    /**
     * 实例名称，不传给予默认值
     */
    @JsonProperty("InstanceName")
    private String instanceName;

    /**
     * 实例描述
     */
    @JsonProperty("InstanceDesc")
    private String instanceDesc;


    /**
     * 实例类型（高级版Advanced | 企业版Enterprise）
     */
    @JsonProperty("InstanceType")
    private String instanceType;

    /**
     * 带宽（10-15360）
     */
    @JsonProperty("Bandwidth")
    @NotNull(message = "带宽不能为空")
    private Integer bandwidth;

    /**
     * 可防护ip总数
     */
    @JsonProperty("TotalEipNum")
    @NotNull(message = "可防护ip总数不能为空")
    private Integer totalEipNum;

    /**
     * 项目制ID
     */
    @JsonProperty("ProjectId")
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    @Pattern(regexp = Validation.REGEX_KFW_CHARGETYPE, message = ErrorCode.KfwChargeTypeInvalid)
    @JsonProperty("ChargeType")
    @NotBlank(message = "计费类型不能为空")
    private String chargeType;

}
