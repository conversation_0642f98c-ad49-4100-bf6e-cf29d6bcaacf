package com.ksyun.cfwapi.domain.fw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateSubnetParam implements Serializable {
    private static final long serialVersionUID = 4895624312990942708L;
    /**
     * vpc id
     */
    @NotBlank
    private String vpcId;

    /**
     * 安全组id
     */
    @NotBlank
    private String securityGroupId;

    /**
     * 机房
     */
    @NotBlank
    private String region;


    /**
     * 子网掩码长度
     */
    @Min(16)
    @Max(29)
    private Integer subNetMaskLength;
    /**
     * 要生成的子网数量
     */
    @Min(1)
    private Integer subnetsNum;
}
