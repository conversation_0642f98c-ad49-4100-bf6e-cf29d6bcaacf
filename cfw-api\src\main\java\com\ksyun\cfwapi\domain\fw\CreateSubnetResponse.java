package com.ksyun.cfwapi.domain.fw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateSubnetResponse implements Serializable {

	private static final long serialVersionUID = -9039823167675745204L;
	/**
	 * 子网段id
	 */
	private String subNetId;
	/**
	 * 子网段
	 */
	private String subCidr;

	/**
	 * 创建结果
	 */
	private boolean result;
}
