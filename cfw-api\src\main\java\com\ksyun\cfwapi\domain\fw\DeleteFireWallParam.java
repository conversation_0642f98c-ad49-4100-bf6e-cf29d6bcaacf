package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwcore.constants.Constants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeleteFireWallParam implements Serializable {
    private static final long serialVersionUID = 4734591052339397349L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "CfwInstanceId不能为空")
    private String cfwInstanceId;

    @JsonProperty("Source")
    private String source = Constants.SDK_SOURCE;
}
