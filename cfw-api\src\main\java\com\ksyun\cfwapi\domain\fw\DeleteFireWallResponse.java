package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class DeleteFireWallResponse {
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("Result")
    private boolean result;
    
    public DeleteFireWallResponse(String requestId, boolean result){
        this.setRequestId(requestId);
        this.setResult(result);
    }
}
