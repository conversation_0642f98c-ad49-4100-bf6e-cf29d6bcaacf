package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DescribeCfwEipsParam {
    /**
     * 实例Id
     */
    @NotBlank(message = "CfwInstanceId不能为空")
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;


    /**
     * 最大返回单次调用可返回的最大条目数量. 传入返回的 NextToken 值可以获取剩余的其它条目. 这个值可以允许的范围是 5 - 1000
     */
    @JsonProperty("MaxResults")
    private Integer maxResults;

    /**
     * 获取另一页返回结果的 token
     */
    @JsonProperty("NextToken")
    private String nextToken;

    /**
     * 查询ip
     */
    @JsonProperty("EipAddress")
    private String eipAddress;


}
