package com.ksyun.cfwapi.domain.fw;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwapi.enums.EipBindModeEnum;
import com.ksyun.cfwapi.enums.EipState;
import com.ksyun.cfwapi.enums.InstanceType;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.QueryFloatingipsResponse;
import com.ksyun.cfwcore.openstack.wapper.domain.Floatingip;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

import javax.xml.bind.annotation.*;
import java.util.ArrayList;
import java.util.List;

@XmlRootElement(name = "DescribeAddressesResponse")
@XmlAccessorType(XmlAccessType.NONE)
@NoArgsConstructor
@ToString
@Data
public class DescribeCfwEipsResponse {

    @JsonProperty("RequestId")
    private String request_id;

    @JsonProperty("AddressesSet")
    private List<AddressInfo> eips;

    @JsonProperty("NextToken")
    private String next_token;

    private String pre_token;

    @JsonProperty("TotalCount")
    private Integer totalCount;

    public DescribeCfwEipsResponse(QueryFloatingipsResponse response ) {
        this.next_token=response.getNext_token();
        this.pre_token=response.getPre_token();
        this.totalCount = response.getTotalCount();
        eips = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(response.getFloatingips())) {
            for (Floatingip ip : response.getFloatingips()) {
                AddressInfo eip = new AddressInfo();
                eip.setCreated_at(ip.getCreated_at());
                // BandWidth
                eip.setEgress(ip.getEgress());
                eip.setFixed_ip_address(ip.getFixed_ip_address());
                // PublicIp
                eip.setAddress(ip.getFloating_ip_address());
                // LineId
                eip.setNetwork_uuid(ip.getFloating_network_uuid());
                eip.setIgw_uuid(ip.getIgw_uuid());
                eip.setIngress(ip.getIngress());
                eip.setLb_pool_uuid(ip.getLb_pool_uuid());
                eip.setNatpool_id(ip.getNatpool_id());
                eip.setPort_uuid(ip.getPort_uuid());
                eip.setRouter_uuid(ip.getRouter_uuid());
                eip.setType(ip.getType());
                // InstanceType
                eip.setUsage_type(ip.getUsage_type());
                // AllocationId
                eip.setUuid(ip.getUuid());
                // State
                eip.state = StringUtils.isBlank(ip.getUsage_type()) ? EipState.DETACHED
                        .getEipState() : EipState.ATTACHED.getEipState();
                eip.setIpState(ip.getAdmin_state_up() ? "start" : "stop");
                // InstanceId
                boolean isInstance = false;
                if(InstanceType.KvmType.getName().equals(ip.getUsage_type())
                        || InstanceType.EpcType.getName().equals(ip.getUsage_type())
                        || InstanceType.DockerType.getName().equals(ip.getUsage_type())
                        || InstanceType.SmartNic.getName().equals(ip.getUsage_type())){
                    isInstance = true;
                }
                eip.device_uuid = isInstance ? ip.getDevice_uuid() : null;
                eip.setInstance_uuid(ip.getInstance_id());
                eip.setNetworkInterfaceType("eni".equals(ip.getVif_type()) ? "extension" : ip.getVif_type());
                eip.setPrivateIpAddress(ip.getFixed_ip_address());
                eip.setBwp_id(ip.getBwp_id());

                //项目制ID
                eip.setIamProjectId(ip.getIamProjectId());
                //version
                eip.setIpVersion(ip.getIpVersion());

                eip.setChargeType(ip.getChargeType());
                eip.setServiceEndTime(ip.getServiceEndTime());
                eip.setHostType(ip.getHostType());
                eip.setMode(EipBindModeEnum.getNameByType(ip.getBinding_type()));
                eip.setEipPoolId(ip.getEip_pool_id());
                eip.setFirewallId(ip.getFirewall_id());
                eip.setProjectName(ip.getProjectName());
                if(StringUtils.isNotBlank(ip.getFirewall_id())){
                    eip.setProtectStatus(1);
                }else{
                    eip.setProtectStatus(0);
                }
                eips.add(eip);
            }
        }
    }

    @XmlElementWrapper(name = "AddressesSet")
    @XmlElement(name = "item")
    public List<AddressInfo> getEips() {
        return eips;
    }

    @XmlElement(name = "NextToken")
    public String getNext_token() {
        return next_token;
    }

    @XmlElement(name = "RequestId")
    public String getRequest_id() {
        return request_id;
    }

    @XmlTransient
    public String getPre_token() {
        return pre_token;
    }

    @XmlElement(name = "TotalCount")
    public Integer getTotalCount() {
        return totalCount;
    }

    @XmlAccessorType(XmlAccessType.NONE)
    @XmlType(name = "AddressesSet.item")
    @NoArgsConstructor
    @ToString
    @Setter
    public static class AddressInfo {
        @JsonProperty("PublicIp")
        private String address;

        private String type;

        @JsonProperty("AllocationId")
        private String uuid;

        @JsonProperty("State")
        private String state;

        @JsonProperty("IpState")
        private String IpState;

        @JsonProperty("LineId")
        private String network_uuid;

        @JsonProperty("NetworkInterfaceId")
        private String device_uuid;

        @JsonProperty("NetworkInterfaceType")
        private String networkInterfaceType;

        @JsonProperty("PrivateIpAddress")
        private String privateIpAddress;

        @JsonProperty("BandWidth")
        private Integer egress;

        @JsonProperty("InstanceType")
        private String usage_type;

        @JsonProperty("InstanceId")
        private String instance_uuid;

        @JsonProperty("InstanceName")
        private String instanceName;

        private Integer ingress;

        @JsonProperty("InternetGatewayId")
        private String igw_uuid;

        @JsonProperty("BandWidthShareId")
        private String  bwp_id;

        @JsonProperty("UserTag")
        private String user_tag;

        private String router_uuid;

        private String lb_pool_uuid;

        private String natpool_id;

        private String fixed_ip_address;

        private String port_uuid;

        @JsonProperty("ChargeType")
        private String chargeType;

        @JsonProperty("ProductType")
        private String productType;

        @JsonProperty("ProductWhat")
        private String productWhat;

        @JsonProperty("ServiceEndTime")
        private String serviceEndTime;

   /*     @Expose
        @SerializedName("TagSet")
        @XmlElement(name = "TagSet")
        private List<TagInfo> tagSet;*/

        @JsonProperty("IpVersion")
        private String  ipVersion;

        @JsonProperty("ProjectId")
        private String iamProjectId;

        @JsonProperty("ProjectName")
        private String projectName;

        @JsonProperty("CreateTime")
        private String created_at;

        @JsonProperty("Mode")
        private String mode;

        @JsonProperty("HostType")
        private String hostType;

        @JsonProperty("EipPoolId")
        private String eipPoolId;

        @JsonProperty("FirewallId")
        private String firewallId;

        @JsonProperty("ProtectStatus")
        private Integer protectStatus;

        @XmlTransient
        public String getType() {
            return type;
        }

        @XmlElement(name = "BandWidth")
        public Integer getEgress() {
            return egress;
        }

        @XmlElement(name = "InstanceId")
        public String getInstance_uuid() {
            return instance_uuid;
        }

        @XmlElement(name = "InstanceType")
        public String getUsage_type() {
            return usage_type;
        }

        @XmlElement(name = "AllocationId")
        public String getUuid() {
            return uuid;
        }

        @XmlElement(name = "PublicIp")
        public String getAddress() {
            return address;
        }

        @XmlElement(name = "State")
        public String getState() {
            return state;
        }

        @XmlElement(name = "LineId")
        public String getNetwork_uuid() {
            return network_uuid;
        }

        @XmlTransient
        public Integer getIngress() {
            return ingress;
        }

        @XmlElement(name = "InternetGatewayId")
        public String getIgw_uuid() {
            return igw_uuid;
        }

        @XmlTransient
        public String getRouter_uuid() {
            return router_uuid;
        }

        @XmlTransient
        public String getLb_pool_uuid() {
            return lb_pool_uuid;
        }

        @XmlTransient
        public String getNatpool_id() {
            return natpool_id;
        }



        @XmlTransient
        public String getFixed_ip_address() {
            return fixed_ip_address;
        }

        @XmlTransient
        public String getPort_uuid() {
            return port_uuid;
        }

        @XmlElement(name = "CreateTime")
        public String getCreated_at() {
            return created_at;
        }

        @XmlElement(name = "NetworkInterfaceId")
        public String getDevice_uuid() {
            return device_uuid;
        }

        @XmlElement(name = "BandWidthShareId")
        public String getBwp_id() {
            return bwp_id;
        }

        @XmlElement(name = "ProjectId")
        public String getIamProjectId() {
            return iamProjectId;
        }

        @XmlElement(name = "IpVersion")
        public String getIpVersion() {
            return ipVersion;
        }

        @XmlElement(name = "UserTag")
        public String getUser_tag() {
            return user_tag;
        }

        @XmlElement(name = "ChargeType")
        public String getChargeType() {
            return chargeType;
        }

        @XmlElement(name = "Mode")
        public String getMode() {
            return mode;
        }
        @XmlElement(name = "HostType")
        public String getHostType() {
            return hostType;
        }

        @XmlElement(name = "IpState")
        public String getIpState() {
            return IpState;
        }

        @XmlElement(name = "EipPoolId")
        public String getEipPoolId() {
            return eipPoolId;
        }
    }
}