package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class DescribeFireWallLBParam implements Serializable {
    private static final long serialVersionUID = -2075673263531466784L;
    @JsonProperty("CfwInstanceIds")
    private List<String> cfwInstanceIds;

    /**
     * 实例Id
     */
   // @NotBlank(message = "Region不能为空")
    @JsonProperty("Region")
    private String region;

}
