package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DescribeFireWallResponse implements Serializable {
    private static final long serialVersionUID = -8023243662741720342L;
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("CloudFireWallInstanceList")
    private List<CloudFireWallInstance> cloudFireWallInstanceList;
}
