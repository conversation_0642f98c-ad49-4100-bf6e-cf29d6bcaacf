package com.ksyun.cfwapi.domain.fw;

import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class EipInfo {

    protected String uuid;

    protected String floating_network_uuid;

    protected String floating_network_name;

    protected String state;

    protected String IpState;

    protected String type;

    protected String usage_type;

    protected String instance_id;

    protected String floating_ip_address;

    protected Integer egress;

    protected Integer ingress;

    protected String igw_uuid;

    protected String router_uuid;

    protected String lb_pool_uuid;

    protected String device_uuid;

    protected String fixed_ip_address;

    protected String port_uuid;

    protected String created_at;

    protected String bwp_id;

    protected String iamProjectId;

    protected String ipVersion;

    protected String user_tag;

    protected Integer billType;

    protected String chargeType;

    protected String vif_type;

    protected String ip;

    protected String productType;

    protected String serviceEndTime;

    protected String binding_type;

    protected String hostType;

    protected String natpool_id;

}
