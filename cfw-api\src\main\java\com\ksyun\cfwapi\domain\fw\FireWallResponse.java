package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FireWallResponse {
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("CloudFireWallInstance")
    private CloudFireWallInstance  cloudFireWallInstance;
}
