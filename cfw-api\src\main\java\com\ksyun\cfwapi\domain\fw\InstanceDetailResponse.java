package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class InstanceDetailResponse implements Serializable {
    private static final long serialVersionUID = -2292327267705230408L;
    @JsonProperty("CloudFireWallInstanceDetail")
    private CloudFireWallInstanceDetail fireWallInstanceDetail;

    @JsonProperty("RequestId")
    private String requestId;
}
