package com.ksyun.cfwapi.domain.fw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InstanceIdStartParam {
    private String traceId;
    private String operationId;
    private String wallId;
    private String uploadLogUseVif;
    private String uploadBusinessUseVif;
    private String subnetGatewayIpLog;
    private String subnetGatewayIpBusiness;

    private KafkaInfo kafka;

    @Data
    public static class KafkaInfo {
        private String url;
        private String username;
        private String password;
        private String saslEnable;
        private String certPath;
        private String md5;
    }

    //接口地址（拉取agent/上传日志文件等）
    private String httpUrl;

    private List<VxlanInfo> vxlan;

    @Data
    public static class VxlanInfo {
        private String direction;
        private String dest;
        private String vni;
        private String mac;
        private String ip;
    }

}
