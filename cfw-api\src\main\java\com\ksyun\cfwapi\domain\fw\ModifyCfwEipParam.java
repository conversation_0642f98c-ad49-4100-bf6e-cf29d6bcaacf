package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModifyCfwEipParam implements Serializable {
    private static final long serialVersionUID = 6641322650024405870L;
    /**
     * 实例Id
     */
    @NotBlank(message = "CfwInstanceId不能为空")
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    /**
     * EipId
     */
    @NotEmpty(message = "EipId不能为空")
    @JsonProperty("EipIds")
    private List<String> eipIds;


    /**
     * EipProtectStatus
     */
    @NotBlank(message = "EipProtectStatus不能为空")
    @JsonProperty("EipProtectStatus")
    private String eipProtectStatus;
}
