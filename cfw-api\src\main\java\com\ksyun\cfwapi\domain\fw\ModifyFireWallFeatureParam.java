package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ModifyFireWallFeatureParam implements Serializable {
    private static final long serialVersionUID = 2086563650124431575L;

    /**
     * 实例Id
     */
    @NotBlank(message = "CfwInstanceId不能为空")
    @JsonProperty("CfwInstanceId")
    private String cfwInstanceId;

    @JsonProperty("InstanceName")
    private String instanceName;
}
