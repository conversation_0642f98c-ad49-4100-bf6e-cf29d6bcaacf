package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModifyFireWallLBParam implements Serializable {

    private static final long serialVersionUID = -7848314180445204472L;
    /**
     * 实例Id
     */
    @JsonProperty("InstanceId")
    @NotBlank(message = "实例id不能为空")
    private String instanceId;

    /**
     * 实例类型（高级版Advanced | 企业版Enterprise）
     */
    @JsonProperty("InstanceType")
    @NotBlank(message = "实例类型不能为空")
    private String instanceType;

    /**
     * 带宽
     */
    @NotNull(message = "带宽不能为空")
    @JsonProperty("Bandwidth")
    private Integer bandwidth;

    /**
     * 可防护ip总数
     */
    @JsonProperty("TotalEipNum")
    @NotNull(message = "可防护ip总数不能为空")
    private Integer totalEipNum;

    /**
     * 可防护ip总数
     */
    @JsonProperty("SubOrderId")
    @NotNull(message = "子订单不能为空")
    private String subOrderId;
}
