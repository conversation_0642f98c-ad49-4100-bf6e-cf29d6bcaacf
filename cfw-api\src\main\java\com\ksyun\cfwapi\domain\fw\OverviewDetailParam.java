package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class OverviewDetailParam implements Serializable {
    private static final long serialVersionUID = 1953479877800795360L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "cfwInstanceId不能为空")
    private String cfwInstanceId;

    @NotNull(message = "查询开始时间不能为空")
    @JsonProperty("StartTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @NotNull(message = "查询结束时间不能为空")
    @JsonProperty("EndTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
