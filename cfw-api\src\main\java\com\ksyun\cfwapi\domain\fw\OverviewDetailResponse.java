package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OverviewDetailResponse implements Serializable {
    private static final long serialVersionUID = -2872590092868008825L;
    @JsonProperty("RequestId")
    private String requestId;

    @JsonProperty("AclDenyCount")
    private Long aclDenyCount;

    @JsonProperty("IpsCount")
    private Long ipsCount;

    @JsonProperty("InMax")
    private Long inMax;

    @JsonProperty("OutMax")
    private Long outMax;

    @JsonProperty("FwLbId")
    private String fwLbId;
}
