package com.ksyun.cfwapi.domain.fw;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class QueryInstanceParam implements Serializable {
    private static final long serialVersionUID = -6961695909271664186L;
    /**
     * 实例Id
     */
    @NotBlank(message = "InstanceId不能为空")
    @JsonProperty("InstanceId")
    private String instanceId;
}
