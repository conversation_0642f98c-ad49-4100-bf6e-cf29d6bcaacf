package com.ksyun.cfwapi.domain.ips;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class CfwIps implements Serializable {
    private static final long serialVersionUID = -3350586730681500382L;
    @JsonProperty("IpsId")
    private String ipsId;
    @JsonProperty("Mode")
    private String mode;
    @JsonProperty("Status")
    private String status;
    @JsonProperty("CreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
