package com.ksyun.cfwapi.domain.ips;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwapi.domain.CfwDictionary;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class IpsDictionaryResponse implements Serializable {
    private static final long serialVersionUID = -3482338920854404352L;
    @JsonProperty("IpsDictionary")
    private List<CfwDictionary> ipsDictionary;
}
