package com.ksyun.cfwapi.domain.ips;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.ksyun.cfwapi.enums.StatusEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class ModifyCfwIpsParam implements Serializable {
    private static final long serialVersionUID = 3107317188563533408L;
    @JsonProperty("IpsId")
    @NotBlank(message = "IpsId不能为空")
    private String ipsId;

    @JsonProperty("Status")
    private String status = StatusEnum.START.getStatusStr();

    @JsonProperty("Mode")
    @NotBlank(message = "模式不能为空")
    public String mode;

}
