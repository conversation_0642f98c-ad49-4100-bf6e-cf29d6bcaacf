package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CfwServiceGroup implements Serializable {
    private static final long serialVersionUID = 6202406804159402666L;
    /**
     *服务组ID
     */
    @JsonProperty("ServiceGroupId")
    private String serviceGroupId;

    /**
     *名称
     */
    @JsonProperty("ServiceGroupName")
    private String serviceGroupName;

    /**
     *服务信息（协议:源端口最小-源端口最大/目的最小-目的最大 ）
     * 例：TCP:1-100/2-200,UDP:22/33,ICMP
     */
    @JsonProperty("ServiceInfo")
    private List<String> serviceInfo;

    /**
     *描述
     */
    @JsonProperty("Description")
    private String description;

    /**
     *被引用次数
     */
    @JsonProperty("CitationCount")
    private Integer citationCount = 0;

}
