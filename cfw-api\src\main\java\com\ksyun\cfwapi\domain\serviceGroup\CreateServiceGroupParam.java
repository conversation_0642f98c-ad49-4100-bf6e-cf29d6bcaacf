package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CreateServiceGroupParam implements Serializable {
    private static final long serialVersionUID = 6165037830923875812L;
    /**
     * 防火墙实例ID
     */
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "防火墙实例ID不能为空")
    private String cfwInstanceId;

    /**
     * 服务组名称
     */
    @JsonProperty("ServiceGroupName")
    @NotBlank(message = "服务组名称不能为空")
    @Length(min = 1,max = 95, message = "服务组名称长度不能超过95")
    private String serviceGroupName;

    /**
     * 服务信息（协议:源端口最小-源端口最大/目的最小-目的最大 ）
     * 例：TCP:1-100/2-200,UDP:22/33,ICMP
     */

    @JsonProperty("ServiceInfo")
    @Size(min = 1, max = 64, message = "服务信息数量不能超过64")
    @NotEmpty(message = "服务信息不能为空")
    private List<String> serviceInfo;



    /**
     * 描述
     */
    @JsonProperty("Description")
    @Length(max=500,message = "描述长度不能超过500")
    private String description;

}
