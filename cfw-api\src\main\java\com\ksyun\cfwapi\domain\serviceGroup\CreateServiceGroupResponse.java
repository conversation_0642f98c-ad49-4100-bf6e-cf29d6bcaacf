package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CreateServiceGroupResponse implements Serializable {
    private static final long serialVersionUID = -185149210318768992L;
    @JsonProperty("RequestId")
    private String requestId;
    @JsonProperty("CfwServiceGroup")
    private CfwServiceGroup cfwServiceGroup;
}
