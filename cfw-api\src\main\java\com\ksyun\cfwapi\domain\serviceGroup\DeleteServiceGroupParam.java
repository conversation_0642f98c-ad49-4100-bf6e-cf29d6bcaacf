package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DeleteServiceGroupParam implements Serializable {

    private static final long serialVersionUID = -2403139118448681287L;
    
    @JsonProperty("ServiceGroupId")
    private String serviceGroupId;
}
