package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DescribeServiceGroupParam implements Serializable {
    private static final long serialVersionUID = 5626653441862292443L;
    @JsonProperty("CfwInstanceId")
    @NotBlank(message = "cfwInstanceId 不能为空")
    private String cfwInstanceId;
    @JsonProperty("ServiceGroupIds")
    private List<String> serviceGroupIds;

}
