package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class DescribeServiceGroupResponse implements Serializable {
    private static final long serialVersionUID = 829332878421692492L;
    @JsonProperty("CfwServiceGroups")
    private List<CfwServiceGroup> cfwServiceGroups;
    @JsonProperty("RequestId")
    private String requestId;
}
