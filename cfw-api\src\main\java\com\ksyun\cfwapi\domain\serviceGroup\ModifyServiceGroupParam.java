package com.ksyun.cfwapi.domain.serviceGroup;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModifyServiceGroupParam implements Serializable {
    private static final long serialVersionUID = -38960983351178184L;
    @JsonProperty("ServiceGroupId")
    private String serviceGroupId;

    @JsonProperty("ServiceGroupName")
    @NotBlank(message = "服务组名称不能为空")
    @Length(min = 1,max = 95, message = "服务组名称长度不能超过95")
    private String serviceGroupName;

    @JsonProperty("ServiceInfo")
    @Size(min = 1, max = 64, message = "服务信息数量不能超过64")
    @NotEmpty(message = "服务信息不能为空")
    private List<String> serviceInfo;

    @JsonProperty("Description")
    @Length(max=500,message = "描述长度不能超过500")
    private String description;
}
