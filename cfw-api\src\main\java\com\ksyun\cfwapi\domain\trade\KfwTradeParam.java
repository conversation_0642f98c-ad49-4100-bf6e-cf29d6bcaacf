package com.ksyun.cfwapi.domain.trade;

import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class KfwTradeParam extends TradeParam implements Serializable {

    private static final long serialVersionUID = -6385875806284185204L;
    @Pattern(regexp = Validation.REGEX_KFW_CHARGETYPE, message = ErrorCode.KfwChargeTypeInvalid)
    private String chargeType;
}
