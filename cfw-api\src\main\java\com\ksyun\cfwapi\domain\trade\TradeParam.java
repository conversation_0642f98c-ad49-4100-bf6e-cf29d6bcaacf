package com.ksyun.cfwapi.domain.trade;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.ksyun.cfwapi.config.CfwApiCommonConfig;
import com.ksyun.cfwapi.validators.ControllerTradeParamCase;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.Validation;
import com.ksyun.cfwcore.context.SpringContext;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.ErrorCode;
import lombok.*;
import org.apache.commons.lang.StringUtils;

import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Getter
@ToString
@ControllerTradeParamCase
@AllArgsConstructor
@NoArgsConstructor
public class TradeParam implements Serializable{
    // 控制台传递
    @Setter
    @JsonProperty("SubOrderId")
    private String subOrderId;

    @Pattern(regexp = Validation.REGEX_CHARGETYPE, message = ErrorCode.ChargeTypeInvalid)
    @JsonProperty("ChargeType")
    private String chargeType;

    @Setter
    //@Max(value = Validation.PURCHASE_TIME_MAX, message = ErrorCode.PurchaseTimeInvalid)
    @Min(value = Validation.PURCHASE_TIME_MIN, message = ErrorCode.PurchaseTimeInvalid)
    @JsonProperty("PurchaseTime")
    private Integer purchaseTime;

    @JsonProperty("Source")
    private String source = Constants.SDK_SOURCE;

    public void setSource(String source) {
        this.source = source;
        // 如果来源非user、非opcenter,则设置成控制台来源
        if (source != null && !source.equals(Constants.SDK_SOURCE)
                && !source.equals(Constants.OPCENTER_SOURCE)) {
            this.source = Constants.CONSOLE_SOURCE;
        }
    }

    public void setChargeType(String charge_type) {
        InnerAPIHolder.setChargeTypeHolder(charge_type);
        this.chargeType = charge_type;
        if (StringUtils.isNotBlank(charge_type)) {
            CfwApiCommonConfig apiConfig = SpringContext.context.getBean(CfwApiCommonConfig.class);
            String value = apiConfig.getChargeTypeMappingCache().get(this.chargeType);
            if (StringUtils.isNotBlank(value)) {
                this.chargeType = value;
            }
        }
    }
}
