package com.ksyun.cfwapi.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */

@Getter
public enum AddrTypeEnum {
    /**
     * ip
     */
    IP("ip"),
    /**
     * addrbook
     */
    ADDRBOOK("addrbook"),
    /**
     * zone
     */
    ZONE("zone"),
    /**
     * any
     */
    ANY("any"),
    ;

    private String type;

    AddrTypeEnum(String type) {
        this.type = type;
    }
    
    public static AddrTypeEnum getAddrType(String type) {

        return Arrays.stream(AddrTypeEnum.values())
                .filter(e -> e.type.equalsIgnoreCase(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("invalid addrType"));
    }
}
