package com.ksyun.cfwapi.enums;

import lombok.Getter;
import org.apache.commons.lang.StringUtils;

/**
 * @author: hueason
 * @date: 2021/8/20 14:15
 * @description:
 */
@Getter
public enum EipBindModeEnum {
    /**
     * binded：可见/直通模式 ，normal：普通模式
     */
    Binded("binded", "pass_through"),
    Normal("normal", "default");

    private String name;

    private String bindingType;

    EipBindModeEnum(String name, String bindingType) {
        this.name = name;
        this.bindingType = bindingType;
    }

    public static String getNameByType(String bindingType) {
        for (EipBindModeEnum modeEnum : EipBindModeEnum.values()) {
            if (StringUtils.equals(modeEnum.getBindingType(), bindingType)) {
                return modeEnum.getName();
            }
        }
        return null;
    }

    public static String getBindingTypeByName(String name) {
        for (EipBindModeEnum modeEnum : EipBindModeEnum.values()) {
            if (StringUtils.equals(modeEnum.getName(), name)) {
                return modeEnum.getBindingType();
            }
        }
        return null;
    }
}
