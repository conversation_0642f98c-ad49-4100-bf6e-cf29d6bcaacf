package com.ksyun.cfwapi.enums;

import com.ksyun.cfwcore.constants.CommonConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum FirewallActionEnum {

    ALL("all"),
    ACL("acl"),
    ADDRBOOK("addrbook"),
    SERVICEGROUP("servicegroup"),
    IPS("ips"),
    AV("av");

    private String type;

    FirewallActionEnum(String type) {
        this.type = type;
    }
    
    public static String getEnterpriseType() {
        return FirewallActionEnum.ALL.getType();
    }

    public static String getAdvancedType() {
        return FirewallActionEnum.ACL.getType()+ CommonConstant.COMMA+FirewallActionEnum.ADDRBOOK.getType()+CommonConstant.COMMA+FirewallActionEnum.SERVICEGROUP.getType();
    }
}
