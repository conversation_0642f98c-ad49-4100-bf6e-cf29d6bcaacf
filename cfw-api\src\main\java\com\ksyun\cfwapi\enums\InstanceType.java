package com.ksyun.cfwapi.enums;

import lombok.Getter;

@Getter
public enum InstanceType {

	SlbType("lb", "Slb"),
	AlbAdvancedType("lb_advanced", "Slb"),
	KvmType("vpc_vm", "Ipfwd"),
	PortfwdType("vpc_portfwd", "Portfwd"),
	EpcType("vpc_physical", "Ipfwd"),
	DockerType("vpc_docker", "Ipfwd"),
	SmartNic("vpc_smartnic", "Ipfwd"),
	Havip("vpc_vm_vip", "Havip"),
	Nat("nat", "Nat"),
	;

	private String name;

	private String alias;

	private InstanceType(String name, String alias) {
		this.name = name;
		this.alias = alias;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getAlias() {
		return alias;
	}

	public void setAlias(String alias) {
		this.alias = alias;
	}

	public static String getAlias(String name) {
		for (InstanceType addresses_type : InstanceType.values()) {
			if (addresses_type.getName().equals(name)) {
				return addresses_type.getAlias();
			}
		}
		return null;
	}

	public static String getName(String alias) {
		for (InstanceType addresses_type : InstanceType.values()) {
			if (addresses_type.getAlias().equals(alias)) {
				return addresses_type.getName();
			}
		}
		return null;
	}

}
