package com.ksyun.cfwapi.enums;

import lombok.Getter;

import java.io.Serializable;
import java.util.Arrays;

/**
 * <AUTHOR>
 */

@Getter
public enum ServiceTypeEnum implements Serializable {
    /**
     * service
     */
    SERVICE("service"),
    /**
     * servicegroup
     */
    SERVICEGROUP("servicegroup"),
    /**
     * any
     */
    ANY("any"),
    ;
    private String type;

    ServiceTypeEnum(String type) {
        this.type = type;
    }

    public static ServiceTypeEnum getServiceType(String type) {
        return Arrays.stream(ServiceTypeEnum.values())
                .filter(e -> e.type.equalsIgnoreCase(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("invalid addrType"));
    }
}
