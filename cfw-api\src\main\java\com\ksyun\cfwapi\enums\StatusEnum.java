package com.ksyun.cfwapi.enums;

import com.ksyun.cfwapi.exception.CfwException;
import lombok.Getter;

@Getter
public enum StatusEnum {
    /**
     * 开启
     */
    START("start",true,1),
    /**
     * 关闭
     */
    STOP("stop",false,0);
    private String statusStr;
    private boolean openStackStatus;
    private Integer status;

    StatusEnum(String statusStr,boolean openStackStatus,Integer status) {
        this.statusStr = statusStr;
        this.openStackStatus = openStackStatus;
        this.status = status;
    }
    
    public static boolean getStatusByCode(String statusStr) throws CfwException {
        for (StatusEnum statusEnum : StatusEnum.values()) {
            if (statusEnum.getStatusStr().equals(statusStr)) {
                return statusEnum.isOpenStackStatus();
            }
        }
        throw new CfwException("state invalid");
    }
}
