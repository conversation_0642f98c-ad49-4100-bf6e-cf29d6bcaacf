package com.ksyun.cfwapi.es.log;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class CfwEsUtils {
    @Qualifier("restHighLevelClient")
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private JedisTemplate jedisTemplate;
    public RestHighLevelClient getRestHighLevelClient() {
        return restHighLevelClient;
    }
    private List<String> getAllIndex() {
        List<String> indexsList= jedisTemplate.getListCache(RedisConstants.ES_ALL_INDEX);
        if (CollectionUtil.isEmpty(indexsList)) {
            try {
                indexsList = new ArrayList<>();
                GetIndexRequest allIndex = new GetIndexRequest("*");
                GetIndexResponse response = restHighLevelClient.indices().get(allIndex, RequestOptions.DEFAULT);
                String[] indexArr = response.getIndices();
                if (indexArr != null) {
                    indexsList.addAll(Arrays.asList(indexArr));
                    jedisTemplate.setListCache(RedisConstants.ES_ALL_INDEX, indexArr);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return indexsList;
    }

    public List<String> fetchLogIndex(String logType, Date dateFrom, Date dateTo) {
        //查所有index
        List<String> allIndexList = getAllIndex();

        //保留日期，时分秒清零
        dateFrom = DateUtils.setHours(DateUtils.setMinutes(DateUtils.setSeconds(DateUtils.setMilliseconds(dateFrom,
                0), 0), 0), 0);
        dateTo = DateUtils.setHours(DateUtils.setMinutes(DateUtils.setSeconds(DateUtils.setMilliseconds(dateTo,
                0), 0), 0), 0);
        List<String> reslutList = Lists.newArrayList();

        while (dateFrom.compareTo(dateTo) <= 0) {
            String currentIndex = String.join(CommonConstant.UNDERLINE, logType,
                    DateUtils.formatDate(dateFrom, DateUtils.DATE_FORMAT));
            if (allIndexList.contains(currentIndex)) {
                reslutList.add(currentIndex);
            }
            dateFrom = DateUtils.addDays(dateFrom, 1);
        }

        return reslutList;
    }
}
