package com.ksyun.cfwapi.handle;

import com.ksyun.common.http.OpenAPIException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by xuyaming on 16/7/18.
 */
@Component
public class ExceptionBuilder {
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private MessageSource i18n;
    public OpenAPIException openAPIException(String code,String arg){
        String message=null;
        if(arg!=null){
            message = i18n.getMessage(code, new Object[] { arg },
                    RequestContextUtils.getLocale(request));
        }else{
            message = i18n.getMessage(code, new Object[] {},
                    RequestContextUtils.getLocale(request));
        }
        return new OpenAPIException(code, message,
                HttpStatus.BAD_REQUEST, arg);
    }

    public OpenAPIException openAPIException(String code,String arg,String message){
        return new OpenAPIException(code, message,
                HttpStatus.BAD_REQUEST, arg);
    }

    public OpenAPIException openAPIException(String code) {
        return openAPIException(code, null);
    }

    public OpenAPIException buildCommonException(String code,String[] args){
        return new OpenAPIException(code, null,
                HttpStatus.BAD_REQUEST,args);
    }

    /**
     * 把受检异常通过泛型方式包装后抛出（Lambda内部方法无法抛出受检异常使用）
     *
     * @param e
     * @param <E>
     * @throws E
     */
    public static <E extends Exception> void throwLambdaException(Exception e) throws E {
        throw (E) e;
    }
}