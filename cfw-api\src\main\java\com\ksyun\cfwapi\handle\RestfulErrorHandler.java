package com.ksyun.cfwapi.handle;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.enums.NotifySubOrderResult;
import com.ksyun.cfwcore.exception.ThirdPartException;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.rabbitmq.SyncMessageSendService;
import com.ksyun.cfwcore.trade.wapper.FwNotifyService;
import com.ksyun.cfwcore.utils.CharUtils;
import com.ksyun.comm.exception.KsyunCommonException;
import com.ksyun.comm.thread.ThreadPool;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.http.Response;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.network.log.utils.NetWorkLogUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.Builder;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ValidationException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Log4j2
@ControllerAdvice
public class RestfulErrorHandler {

    /**
     * dns错误异常
     */
    private static final Pattern DESIGNATE_EXCEPTION_PATTERN = Pattern.compile(".+(DesignateError).+", Pattern.DOTALL);
    /**
     * nuetron错误异常
     */
    private static final Pattern NEUTRON_EXCEPTION_PATTERN = Pattern.compile(".+(NeutronError).+", Pattern.DOTALL);

    /**
     * 内网DNS错误异常
     */
    private static final Pattern INNER_DNS_EXCEPTION_PATTERN = Pattern.compile(".+(InnerDnsError).+", Pattern.DOTALL);

    private static final Pattern NENTRON_EXCEPTION_CODE = Pattern.compile("^[0-9]{1,6}$");

    private static final Pattern NEUTRON_EXCEPTION_VAR = Pattern.compile("<<<.+?>>>");

    private MessageSource messageSource;

    protected RestTemplate restTemplate;

    private FwNotifyService fwNotifyService;

    private ThreadPool threadPool;

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Autowired
    public void setSyncEventSendService(FwNotifyService fwNotifyService) {
        this.fwNotifyService = fwNotifyService;
    }

    @Autowired
    public void setThreadPool(ThreadPool threadPool) {
        this.threadPool = threadPool;
    }

    /**
     * 获取当前语言模式
     */
    private Locale getLocale(HttpServletRequest request) {
        Locale locale = RequestContextUtils.getLocale(request);
        return locale == null ? Locale.US : locale;
    }

    /**
     * 打印异常报错日志，便于统计
     */
    private ResponseEntity<Response> logResponse(Response response, HttpStatus status) {
        log.info("LogErrorResponse RequestId [{}], Code [{}], Message [{}], HttpStatus [{}]",
                response.getRequest_id(),
                (response.getError() != null) ? response.getError().getCode() : "NoErrorCode",
                (response.getError() != null) ? response.getError().getMessage() : "NoErrorMessage",
                (status != null) ? status.toString() : "NoHttpStatus");
        InnerAPIHolder.setHttpStatusHolder(status);
        return new ResponseEntity<>(response, status);
    }

    @ResponseBody
    @ExceptionHandler
    public ResponseEntity<Response> handleHttpMediaTypeException(HttpServletRequest request, HttpMediaTypeException e) {
        log.error("Method: [{}], Url: [{}], Message: [{}]", request.getMethod(),
                request.getRequestURL() + "?" + request.getQueryString(), e.getMessage(), e);
        String requestId = getRequestId(request);
        Response r = new Response(requestId, ErrorCode.UnsupportedMediaType, e.getMessage());
        notifyOrderAndClean(e.getMessage());
        return logResponse(r, HttpStatus.UNSUPPORTED_MEDIA_TYPE);
    }

    private String getRequestId(HttpServletRequest request) {
        String requestId = request.getHeader(HttpEntityWapper.HEADER_X_KSC_REQUEST_ID);
        if (StringUtils.isBlank(requestId)) {
            Enumeration<String> parameterNames = request.getParameterNames();
            while (parameterNames != null && parameterNames.hasMoreElements()) {
                String element = parameterNames.nextElement();
                if (StringUtils.equalsIgnoreCase("requestId", element)) {
                    requestId = request.getParameter(element);
                    break;
                }
            }
        }
        return requestId;
    }

    @ResponseBody
    @ExceptionHandler({KsyunCommonException.class})
    public ResponseEntity<Response> handleKsyunCommonException(HttpServletRequest request, KsyunCommonException exception) {
        Locale locale = getLocale(request);
        log.error("Method: [{}], Url: [{}], Accept-Language: [{}], Locale: [{}], Error Message: [{}] 异常",
                request.getMethod(), request.getRequestURL() + "?" + request.getQueryString(),
                request.getHeader(Constants.ACCEPT_LANGUAGE_HEADER), locale.toString(),
                exception.getMessage());
        String requestId = getRequestId(request);
        Response r = exception.getResponse();
        String message = messageSource.getMessage(r.getError().getCode(), new String[]{}, locale);
        r.getError().setMessage(message == null ? exception.getMessage() : message);
        r.setRequest_id(requestId);
        notifyOrderAndClean(r.getError().getCode());
        return logResponse(r, exception.getHttp_status());
    }

    @ResponseBody
    @ExceptionHandler({BindException.class, MethodArgumentNotValidException.class})
    public ResponseEntity<Response> handleBindException(HttpServletRequest request, Exception e) {
        log.error("Method: [{}], Url: [{}], Message: [{}]", request.getMethod(),
                request.getRequestURL() + "?" + request.getQueryString(), e.getMessage(), e);
        notifyOrderAndClean("BindException");
        Locale locale = getLocale(request);
        String requestId = getRequestId(request);
        BindingResult bindingResult = (e instanceof BindException) ? ((BindException) e).getBindingResult()
                : ((MethodArgumentNotValidException) e).getBindingResult();
        List<FieldError> fieldErrors = bindingResult.getFieldErrors();
        if (CollectionUtils.isNotEmpty(fieldErrors)) {
            List<String> codeList = Lists.newArrayList(), messageList = Lists.newArrayList();
            for (FieldError error : fieldErrors) {
                Iterable<String> it = Splitter.on(".").split(error.getField());
                StringBuilder builder = new StringBuilder("");
                for (String s : it) {
                    builder.append(StringUtils.capitalize(s));
                    builder.append(".");
                }
                String code = messageSource.getMessage(error, locale);
                // NotEmpty及NotNull校验无需写message
                boolean isBuildEnd = false;
                if ("NotEmpty".equals(error.getCode()) || "NotNull".equals(error.getCode())) {
                    code = ErrorCode.EmptyField;
                    isBuildEnd = true;
                }
                String errorFields = builder.toString().substring(0, builder.toString().lastIndexOf("."));
                String message = messageSource.getMessage(code,
                        new Object[]{errorFields, error.getRejectedValue()}, locale);
                // 参数的开头，commonErrorCode只有有限的几个,code收敛
                if (code.startsWith(ErrorCode.InvalidParameterPrefix)
                        || code.startsWith(ErrorCode.MissingParameterPrefix)
                        || code.startsWith(ErrorCode.ChargeTypeInvalidPrefix)) {

                    code = Splitter.on('.').splitToList(code).get(0);
                }
                codeList.add(isBuildEnd ? errorFields + (code.replace("Field", "")) : code);
                messageList.add(message);
            }
            Response r = new Response(requestId, codeList.toString(), messageList.toString());
            return logResponse(r, HttpStatus.BAD_REQUEST);
        }
        if (StringUtils.isNotEmpty(bindingResult.getGlobalError().getDefaultMessage())) {
            Response r = new Response(requestId, bindingResult.getGlobalError().getDefaultMessage(),
                    messageSource.getMessage(bindingResult.getGlobalError().getDefaultMessage(), new Object[]{}, locale));
            return logResponse(r, HttpStatus.BAD_REQUEST);
        }
        Response r = new Response(requestId, ErrorCode.InternalError,
                messageSource.getMessage(ErrorCode.InternalError, new Object[]{}, locale));
        return logResponse(r, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ResponseBody
    @ExceptionHandler({HttpStatusCodeException.class, ValidationException.class})
    public ResponseEntity<Response> handleDefaultHttpClientErrorException(
            HttpServletRequest request, HttpStatusCodeException e) {
        HttpStatus statusCode = e.getStatusCode();
        String responseBodyString = e.getResponseBodyAsString();
        String url = request.getRequestURL() + "?" + request.getQueryString();
        log.error("Method: [{}], Url: [{}], Response: [{}], StatusCode: [{}], Message: [{}]", request.getMethod(),
                url, responseBodyString, statusCode.value(), e.getMessage(), e);

        /**当HttpStatus > 500 告警*/
        if (statusCode != null && statusCode.value() >= 500) {
            if (NetWorkLogUtils.getCustomField() != null) {
                NetWorkLogUtils.getCustomField().put("Cause", e.getMessage());
            }
            log.error(new NetworkLogMsg().putMsg("[HttpStatusCodeException] 请求Method [" + request.getMethod()
                    + "] URL: " + url + ", Response: " + responseBodyString + ", 返回异常{" + e.getMessage() + "}"), e);
        }
        /**当I/O异常时候，不强制通知订单，需人为判断最终通知订单的状态*/
        if (responseBodyString.indexOf("I/O error on") > 0) {
            InnerAPIHolder.forceSetNeedNotifyFail(false);
        }
        String requestId = getRequestId(request);
        HttpHeaders headers = e.getResponseHeaders();

        if (headers.containsKey(HttpEntityWapper.HEADER_X_ERROR_CODE)) {
            Response r = new Response(requestId, headers.getFirst(HttpEntityWapper.HEADER_X_ERROR_CODE), responseBodyString);
            notifyOrderAndClean(r.getError().getCode());
            return logResponse(r, e.getStatusCode());
        }

        try {
            ResponseEntity<Response> responseResponseEntity = transformOpenStackErrorResponse(request, e);
            notifyOrderAndClean(responseResponseEntity.getBody().getError().getCode());
            return responseResponseEntity;
        } catch (Exception exception) {
            notifyOrderAndClean(ErrorCode.InternalError);
            Response r = new Response(requestId, ErrorCode.InternalError, e.getMessage());
            return logResponse(r, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @ExceptionHandler(OpenAPIException.class)
    @ResponseBody
    public ResponseEntity<Response> handleOpenAPIException(
            HttpServletRequest request, OpenAPIException exception) {
        Locale locale = getLocale(request);
        log.error("Method: [{}], Url: [{}], Accept-Language: [{}], Locale: [{}], Error Message: [{}] 异常",
                request.getMethod(), request.getRequestURL() + "?" + request.getQueryString(),
                request.getHeader(Constants.ACCEPT_LANGUAGE_HEADER), locale.toString(),
                exception.getMessage(), exception);

        String requestId = getRequestId(request);
        Response r = exception.getResponse();
        Object[] args = exception.getArguments();
        if (args != null) {
            int index = 0;
            //argument的国际化处理
            //需要在配置文件中 设置如Subnet=子网 就能只支持参数的国际化
            for (Object arg : args) {
                if (arg instanceof String) {
                    String message = messageSource.getMessage(arg.toString(), null, locale);
                    args[index] = message;
                }
                index++;
            }
        }
        String code = r.getError().getCode();
        String message = messageSource.getMessage(code, args, locale);

        // 参数的开头，commonErrorCode只有有限的几个,code收敛
        if (code.startsWith(ErrorCode.InvalidParameterPrefix)
                || code.startsWith(ErrorCode.MissingParameterPrefix)
                || code.startsWith(ErrorCode.ChargeTypeInvalidPrefix)) {

            code = Splitter.on('.').splitToList(code).get(0);
        }
        r.getError().setCode(code);
        r.getError().setMessage(message == null ? exception.getMessage() : message);
        r.setRequest_id(requestId);
        notifyOrderAndClean(r.getError().getCode());
        return logResponse(r, exception.getHttp_status());
    }

    @ExceptionHandler(ThirdPartException.class)
    @ResponseBody
    public ResponseEntity<Response> handleOpenAPIException(
            HttpServletRequest request, ThirdPartException e) {
        Locale locale = getLocale(request);
        log.error("Method: [{}], Url: [{}], Accept-Language: [{}], Locale: [{}], Error Message: [{}]", request.getMethod(),
                request.getRequestURL() + "?" + request.getQueryString(),
                request.getHeader(Constants.ACCEPT_LANGUAGE_HEADER), locale.toString(), e.getMessage(), e);
        Response r = e.getResponse();
        r.setRequest_id(getRequestId(request));
        notifyOrderAndClean(r.getError().getCode());
        return logResponse(r, e.getHttp_status());
    }

    @ExceptionHandler(Throwable.class)
    @ResponseBody
    public ResponseEntity<Response> handleError(HttpServletRequest request,
                                                Throwable e) {
        log.error("Method: [{}], Url: [{}], Error Message: [{}]", request.getMethod(),
                request.getRequestURL(), e.getMessage(), e);
        if (NetWorkLogUtils.getCustomField() != null) {
            NetWorkLogUtils.getCustomField().put("Cause", e.getMessage());
        }
        log.error(
                new NetworkLogMsg().putMsg("[Throwable] 请求Method [" + request.getMethod() + "] " +
                        "URL: " + request.getRequestURL() + "?" + request.getQueryString() +
                        ", 返回异常{" + e.getMessage() + "}"), e);

        String requestId = getRequestId(request);
        Response r = new Response(requestId, ErrorCode.InternalError, e.getMessage());
        //IO异常不在出发通知订单-转等通知手工处理
        if ((e instanceof ResourceAccessException)
                || (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().indexOf("I/O error on") > 0)) {
            InnerAPIHolder.forceSetNeedNotifyFail(false);
        }
        notifyOrderAndClean(r.getError().getCode());

        return logResponse(r, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 判断是否是Neutron报错
     */
    private boolean isNeutronException(HttpStatusCodeException e) {
        String responseBodyAsString = e.getResponseBodyAsString();
        return NEUTRON_EXCEPTION_PATTERN.matcher(responseBodyAsString).find();
    }


    private void notifyOrderAndClean(final String reason) {
        String subOrderId = InnerAPIHolder.getSubOderId();
        Set<String> subOrderIdSet = InnerAPIHolder.getSubOrderIdSet();
        String instanceId = InnerAPIHolder.getInstanceId();
        try {
            log.info("NeedNotifyFail [{}], subOrderId [{}], SubOrderIdSet [{}] ", InnerAPIHolder.getNeedNotifyFail(), subOrderId, subOrderIdSet);
            if (InnerAPIHolder.getNeedNotifyFail() == null || InnerAPIHolder.getNeedNotifyFail()) {
                if (!StringUtils.isEmpty(subOrderId)) {
                    fwNotifyService.notifySubOrder(subOrderId,NotifySubOrderResult.FAIL.getValue(), instanceId,InnerAPIHolder.getProxyAuth(),
                             reason);
                }
                //批量情况的处理
                if (CollectionUtils.isNotEmpty(subOrderIdSet)) {
                    List<List<String>> all = Lists.partition(new ArrayList<String>(subOrderIdSet), 5);
                    final ProxyAuth auth = InnerAPIHolder.getProxyAuth();
                    for (List<String> list : all) {
                        final CountDownLatch countDownLatch = new CountDownLatch(list.size());
                        for (final String id : list) {
                            threadPool.getThreadPool().execute(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        fwNotifyService.notifySubOrder(id,NotifySubOrderResult.FAIL.getValue(),"", auth, reason);
                                    } catch (Exception e) {
                                        log.error("通知子订单状态失败 SubOrder [{}], Reason [{}], Message [{}]", id, reason, e.getMessage(), e);
                                    } finally {
                                        countDownLatch.countDown();
                                    }
                                }
                            });
                        }
                        countDownLatch.await();
                    }
                }

            }

        } catch (Exception e) {
            log.error("通知子订单状态失败 SubOrder [{}], SubOrderIdSet [{}], Reason [{}], Message [{}]", subOrderId, subOrderIdSet, reason, e.getMessage(), e);
        } finally {
//            InnerAPIHolder.cleanAllHolder();
        }
    }

    /**
     * 判断是否为DNS报错
     */
    private boolean isDesignateException(HttpStatusCodeException e) {
        String responseBodyAsString = e.getResponseBodyAsString();
        return DESIGNATE_EXCEPTION_PATTERN.matcher(responseBodyAsString).find();
    }

    /**
     * 判断是否为内网DNS报错
     */
    private boolean isInnerDnsException(HttpStatusCodeException e) {
        String responseBodyAsString = e.getResponseBodyAsString();
        return INNER_DNS_EXCEPTION_PATTERN.matcher(responseBodyAsString).find();
    }

    /**
     * 处理内网DNS异常信息
     */
    private ApiErrorInfo transferInnerDnsError(Map<String, Map<String, String>> messageMap, Locale locale) {
        String apiErrorCode, apiErrorMessage, errorCode = null, errorMessage = null;
        for (Map.Entry<String, Map<String, String>> entry : messageMap.entrySet()) {
            if (entry.getKey().startsWith("InnerDnsError.")) {
                errorCode = entry.getKey() + "." + entry.getValue().get("exception_name");
                errorMessage = entry.getValue().get("message");
                break;
            }
        }
        try {
            apiErrorMessage = messageSource.getMessage(errorCode, new Object[]{}, locale);
            if (apiErrorMessage.equals(errorCode)) {
                // 没有在文档中列出的异常信息 直接返回给前端
                apiErrorMessage = errorMessage;
            } else {
                apiErrorMessage = this.getApiErrorMessageByNeutron(errorMessage, apiErrorMessage, "'", null, null, null);
            }
            List<String> codeList = Splitter.on('.').splitToList(errorCode);
            apiErrorCode = (codeList.size() == 3) ? codeList.get(2) : errorCode;

        } catch (NoSuchMessageException e2) {
            apiErrorCode = errorCode;
            apiErrorMessage = errorMessage;
        }
        return ApiErrorInfo.builder().apiErrorMessage(apiErrorMessage).apiErrorCode(apiErrorCode).build();
    }

    /**
     * 处理公网DNS异常信息
     */
    private ApiErrorInfo transferDesignateError(Map<String, Map<String, String>> messageMap, Locale locale) {

        Map<String, String> errorData = messageMap.get("DesignateError");
        String errorMessage = errorData.get("message");
        String errorCode = errorData.get("type");
        String apiErrorCode, apiErrorMessage;
        try {
            /** 由于目前存在返回的type不具有唯一性的问题，可能导致不同的错误显示相同的错误信息, 首先访问type+"_"+message，若不存在，再访问type */
            String middleErrorCode = messageSource.getMessage(errorCode + "_" + errorMessage, new Object[]{}, locale);
            String middleErrorCodeType = messageSource.getMessage(errorCode, new Object[]{}, locale);
            apiErrorMessage = messageSource.getMessage(middleErrorCode, new Object[]{}, locale);
            List<String> codeList = Splitter.on('.').splitToList(middleErrorCode);
            if ((errorCode + "_" + errorMessage).equals(apiErrorMessage)) {
                apiErrorMessage = messageSource.getMessage(middleErrorCodeType, new Object[]{}, locale);
                codeList = Splitter.on('.').splitToList(middleErrorCodeType);
            }
            apiErrorCode = (codeList.size() == 2) ? codeList.get(0) : middleErrorCode;
        } catch (NoSuchMessageException e2) {
            apiErrorCode = errorCode;
            apiErrorMessage = errorMessage;
        }
        return ApiErrorInfo.builder().apiErrorMessage(apiErrorMessage).apiErrorCode(apiErrorCode).build();
    }

    /**
     * 处理Neutron异常信息
     */
    private ApiErrorInfo transferNeutronError(Map<String, Map<String, String>> messageMap, Locale locale) {
        Map<String, String> errorData = messageMap.get("NeutronError");
        String errorCode = errorData.get("type");
        String errorMessage = errorData.get("message");

        String apiErrorCode = "Server." + errorCode;
        // neutron返回的错误码
        String neutronErrorCode = "neutron-" + errorCode;
        String apiErrorMessage;
        try {
            apiErrorMessage = messageSource.getMessage(neutronErrorCode, new Object[] {}, locale);
            if (apiErrorMessage.equals(neutronErrorCode)) {
                apiErrorMessage = errorMessage;
            } else {
                apiErrorMessage = this.getApiErrorMessageByNeutron(errorMessage, apiErrorMessage, null, NEUTRON_EXCEPTION_VAR, "<", ">");
            }

        } catch (Exception et) {
            apiErrorMessage = errorMessage;
        }
        return ApiErrorInfo.builder().apiErrorCode(apiErrorCode).apiErrorMessage(apiErrorMessage).build();
    }

    private ResponseEntity<Response> transformOpenStackErrorResponse(HttpServletRequest request, HttpStatusCodeException e) {
        String requestId = getRequestId(request);
        Locale locale = getLocale(request);

        String responseBodyAsString = e.getResponseBodyAsString();
        String subMessage = responseBodyAsString.substring(responseBodyAsString.indexOf("{"), responseBodyAsString.lastIndexOf("}") + 1);
        Gson gson = new GsonBuilder().enableComplexMapKeySerialization().create();
        subMessage = StringEscapeUtils.unescapeJava(subMessage);
        Map<String, Map<String, String>> messageMap = gson.fromJson(subMessage,
                new TypeToken<Map<String, Map<String, String>>>() {
                }.getType());

        ApiErrorInfo info = ApiErrorInfo.builder().apiErrorCode(ErrorCode.InternalError).apiErrorMessage(e.getMessage()).build();
        if (isNeutronException(e)) info = transferNeutronError(messageMap, locale);
        else if (isDesignateException(e)) info = transferDesignateError(messageMap, locale);
        else if (isInnerDnsException(e)) info = transferInnerDnsError(messageMap, locale);
        Response r = new Response(requestId, info.getApiErrorCode(), info.getApiErrorMessage());
        return logResponse(r, e.getStatusCode());
    }

    /**
     * 转译neutron步骤时，替换其中的变量
     *
     * @param neutronMessage      neutron返回的原始错误信息
     * @param apiMessage          翻译过的带占位错误信息，占位待替换变量，目前占位仅支持：<<<xxx>>>
     * @param singleSymbol        neutron返回的信息包含在单一的符号中，例如：'8e404aff-5e56-46d5-831b-b2e342fd37e9'，此处入参则为"'"
     * @param doubleSymbolPattern neutron返回的信息包含在起始与结束不同的符号中的正则表达式，例如：<<<8e404aff-5e56-46d5-831b-b2e342fd37e9>>>
     * @param doubleSymbolStart   起始符号，若有重复可只写一个，例如"<<<"仅可写"<"
     * @param doubleSymbolEnd     结束符号，若有重复可只写一个，例如">>>"仅可写">"
     * @return
     */
    private String getApiErrorMessageByNeutron(String neutronMessage, String apiMessage, String singleSymbol,
                                               Pattern doubleSymbolPattern, String doubleSymbolStart, String doubleSymbolEnd) {
        if (singleSymbol == null && doubleSymbolPattern == null || singleSymbol != null && doubleSymbolPattern != null) {
            log.debug("转换参数格式错误，singleSymbol和doubleSymbol必须一个为空一个不为空！！！");
            throw new RuntimeException("转换参数错误！");
        }
        List<String> varList = new ArrayList<>();// neutron异常中变量集合

        /** 当neutron返回的信息包含在单一的符号中 */
        if (singleSymbol != null) {
            int indexLength = neutronMessage.split(singleSymbol).length;
            if (indexLength > 1 && indexLength % 2 == 1) {// 分割奇数段是偶数
                // 若neutron给的报错信息中存在偶数个''，说明其含有附加信息，转译需保留，例如：Instance '8e404aff-5e56-46d5-831b-b2e342fd37e9' cannot be found.
                for (int i = 0; i < indexLength - 1; i += 2) {
                    int startNum = CharUtils.indexOf(neutronMessage, singleSymbol, i + 1);
                    int endNum = CharUtils.indexOf(neutronMessage, singleSymbol, i + 2);
                    varList.add(neutronMessage.substring(startNum + 1, endNum));
                }
            }
        }
        /** 当neutron返回的信息包含在起始与结束不同的符号中 */
        else if (doubleSymbolStart != null && doubleSymbolEnd != null) {
            Matcher m_neutron = doubleSymbolPattern.matcher(neutronMessage);
            while (m_neutron.find()) {
                varList.add(m_neutron.group().replaceAll(doubleSymbolStart, "").replaceAll(doubleSymbolEnd, ""));
            }
        }
        // 在转译code中使用<<<xxx>>>占位
        Object[] vars = varList.toArray();
        int count = 0;
        //匹配变量到信息中 替换占位符
        StringBuilder sb = new StringBuilder();
        Matcher matcher = NEUTRON_EXCEPTION_VAR.matcher(apiMessage);
        while (matcher.find()) {
            String temp = matcher.group();
            int index = apiMessage.indexOf(temp);
            sb.append(apiMessage.substring(0, index));
            sb.append(count < vars.length ? vars[count].toString() : "");
            apiMessage = apiMessage.substring(index + temp.length());
            count++;
        }
        if (sb.length() > 0) {
            sb.append(apiMessage);
            apiMessage = sb.toString();
        }
        return apiMessage;
    }

    @Data
    @Builder
    public static class ApiErrorInfo {

        private String apiErrorCode;

        private String apiErrorMessage;
    }
}