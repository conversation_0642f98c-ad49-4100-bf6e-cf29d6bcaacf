package com.ksyun.cfwapi.heartbeat;

import com.ksyun.cfwapi.heartbeat.domain.CheckHealth;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class HeartBeatController {

    /**
     * 服务心跳，用于监控服务是否异常，提供给运维
     */
    @RequestMapping(value = "/heart/CheckHealth", headers = {"proxy-check-health=1"},
            method = RequestMethod.GET, produces = {MediaType.APPLICATION_JSON_VALUE})
    public CheckHealth check() {
        return CheckHealth.builder().success(0).build();
    }
}
