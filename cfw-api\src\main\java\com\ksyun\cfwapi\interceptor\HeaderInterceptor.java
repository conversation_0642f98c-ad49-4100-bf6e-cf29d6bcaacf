package com.ksyun.cfwapi.interceptor;

import com.google.common.collect.Lists;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.OpenAPIException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


@Component
public class HeaderInterceptor implements HandlerInterceptor {

    private MessageSource messageSource;

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        List<String> headers = Lists.newArrayList(
                //RequestId
                HttpEntityWapper.HEADER_X_KSC_REQUEST_ID,
                // 账号ID
                HttpEntityWapper.HEADER_X_KSC_ACCOUNT_ID,
                // 机房Code
                HttpEntityWapper.HEADER_X_KSC_REGION
        );

        headers.forEach(headerKey -> {
            if (StringUtils.isBlank(request.getHeader(headerKey))) {
                throw new OpenAPIException(ErrorCode.HttpHeaderNotFound,
                        messageSource.getMessage(ErrorCode.HttpHeaderNotFound,
                                new Object[]{headerKey}, RequestContextUtils.getLocale(request)),
                        HttpStatus.BAD_REQUEST, headerKey);
            }
        });
        return true;
    }
}
