package com.ksyun.cfwapi.interceptor;

import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.proxy.ProxyAuth;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;
/**
 * <AUTHOR>
 */
@Component
public class MDCInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        MDC.clear();
        ProxyAuth proxyAuth = HttpEntityWapper.generateProxyAuth(request);
        MDC.put("Action", request.getParameter("Action"));
        MDC.put("Region", proxyAuth.getRegion());
        MDC.put("Account", proxyAuth.getAccount_id());
        MDC.put("RequestId", proxyAuth.getRequest_id());
        MDC.put("Source", Optional.ofNullable(request.getHeader(HttpEntityWapper.HEADER_X_KSC_SOURCE)).orElse(Constants.SDK_SOURCE));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDC.clear();
    }
}
