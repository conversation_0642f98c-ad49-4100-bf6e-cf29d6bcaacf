package com.ksyun.cfwapi.interceptor;

import com.ksyun.cfwapi.controller.AWSParameterParser;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.config.ElasticSearchConfig;
import com.ksyun.cfwcore.config.RegionConfig;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.iam.api.IamAPI;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfos;
import com.ksyun.cfwcore.iam.api.domain.IamProjectResult;
import com.ksyun.cfwcore.json.GsonUtils;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.network.log.plugin.OnePiecePlugin;
import com.ksyun.common.proxy.ProxyAuth;
import com.ksyun.common.proxy.ProxyHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;


@Slf4j
@Component
public class ProxyAuthInterceptor extends HandlerInterceptorAdapter {

    private static final String _PARAMETER_PROJECT_ID = "ProjectId.";
    private static final String HEADER_X_KSC_COUNT = "X-KSC-NETWORK-COUNT";
    private static final String HEADER_X_KSC_AGG = "X-KSC-NETWORK-AGG";
    private static final String COUNT_ACTION_SUFFIX = "Describe";

    private ElasticSearchConfig elasticSearchConfig;

    private RegionConfig regionConfig;

    private CommonConfig commonConfig;

    private MessageSource messageSource;

    private IamAPI iamAPI;

    @Override
    public boolean preHandle(HttpServletRequest request,
                             HttpServletResponse response, Object handler) throws Exception {
        ProxyHolder.clearHolder();
        ProxyAuth proxy_auth = HttpEntityWapper.generateProxyAuth(request);// 得到ProxyAuth对象
        Locale locale = RequestContextUtils.getLocale(request);
        InnerAPIHolder.setLocaleHolder(locale);

        // 判断机房是否非法
        String regionParam = proxy_auth.getRegion();
        Map<String, String> regionMap = regionConfig.getRegionCodeCache();
        Map<String, String> reRegionMap = regionConfig.getRegionEnCodeCache();
        if (MapUtils.isNotEmpty(regionMap) && !regionMap.containsKey(regionParam)
                && MapUtils.isNotEmpty(reRegionMap) && !reRegionMap.containsKey(regionParam)) {
            throw new OpenAPIException(ErrorCode.InvalidRegion, messageSource.getMessage(
                    ErrorCode.InvalidRegion, new Object[]{}, Locale.US), HttpStatus.BAD_REQUEST, regionParam);
        }

        String service = request.getHeader("X-KSC-SERVICE");
        InnerAPIHolder.setService(service);
        InnerAPIHolder.setSynchronization(true);
        InnerAPIHolder.initInstanceIdTags();

        if (!StringUtils.isEmpty(request.getHeader(Constants.NEUTRON_TAG))) {
            proxy_auth.getExtraHeaders().put(Constants.NEUTRON_TAG, request.getHeader(Constants.NEUTRON_TAG));
        }

        String action = request.getParameter("Action");
        InnerAPIHolder.setActionHolder(action);
        String region = request.getParameter("Region");
        String source = StringUtils.isEmpty(request.getHeader("X-KSC-SOURCE")) ? Constants.SDK_SOURCE : request.getHeader("X-KSC-SOURCE");
        InnerAPIHolder.setSource(source);
        OnePiecePlugin plugin = new OnePiecePlugin(commonConfig.getSendOnepieceUrl(),
                commonConfig.getSendOnepieceMark(), Constants.OPEN_API_SERVICE_NAME, commonConfig.getAlertCode());
        InnerAPIHolder.setProxyAuth(proxy_auth, action, "OpenApi", plugin);
        InnerAPIHolder.setTimestamp(System.currentTimeMillis());

        logQueryString(request);

        //Counts的统一实现
        if (!StringUtils.isEmpty(request.getHeader(HEADER_X_KSC_COUNT))
                && !StringUtils.isEmpty(action)
                && action.startsWith(COUNT_ACTION_SUFFIX)) {
            InnerAPIHolder.setCountFlag(true);
            InnerAPIHolder.setResourceCount(new AtomicInteger(0));
        } else {
            //聚合的统一实现-优先计数器
            if (!StringUtils.isEmpty(request.getHeader(HEADER_X_KSC_AGG))
                    && !StringUtils.isEmpty(action)
                    && action.startsWith(COUNT_ACTION_SUFFIX)) {
                InnerAPIHolder.setAggFlag(request.getHeader(HEADER_X_KSC_AGG));
                InnerAPIHolder.intAggList();
            }
            InnerAPIHolder.setCountFlag(false);
        }

        //跨地域查询的支持
        if (!StringUtils.isEmpty(region) && elasticSearchConfig.isSupportRegionConvert()
                && (action.startsWith(COUNT_ACTION_SUFFIX) || action.equals("CommonQuery"))) {
            proxy_auth.setRegion(region);
        }

        //统一的ProjectId处理
        String projectId = request.getParameter("ProjectId");
        if (!StringUtils.isEmpty(projectId)) {
            iamAPI.getIamProjectInfo(proxy_auth, projectId);
            InnerAPIHolder.addIamProjectId(projectId);
            proxy_auth.getExtraHeaders().put(Constants.X_IAM_PROJECT_ID, projectId);
        } else if (projectId != null) { // 当projectId = ""
            throw new OpenAPIException(ErrorCode.EmptyField, ErrorCode.EmptyField, HttpStatus.BAD_REQUEST, "ProjectId");
        }
        //只有子账号才需要判断
        if (!StringUtils.isEmpty(proxy_auth.getUser_id())) {
            //多个的情况
            List<String> project_id_list = AWSParameterParser.parseInstanceId(request,
                    _PARAMETER_PROJECT_ID, false);
            if (CollectionUtils.isNotEmpty(project_id_list)) {
                IamProjectInfos iamProjectInfos = iamAPI.getIamProjectInfos(proxy_auth);
                List<String> projectIds = new ArrayList<>();
                if (iamProjectInfos != null && iamProjectInfos.getInfos() != null
                        && CollectionUtils.isNotEmpty(iamProjectInfos.getInfos().getProjectList())) {
                    for (IamProjectResult iamProjectResult : iamProjectInfos.getInfos().getProjectList()) {
                        projectIds.add(iamProjectResult.getProjectId());
                    }
                }
                StringBuilder stringBuilder = new StringBuilder();
                for (String id : project_id_list) {
                    if (!projectIds.contains(id)) {
                        stringBuilder.append(id).append(CommonConstant.COMMA);
                    }
                }
                if (stringBuilder.length() > 0) {
                    throw new OpenAPIException(ErrorCode.ProjectMemberNotExist, ErrorCode.ProjectMemberNotExist,
                            HttpStatus.BAD_REQUEST, stringBuilder.toString().substring(0, stringBuilder.length() - 1));
                }
            }
        }

        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           ModelAndView modelAndView) throws Exception {
        ProxyHolder.clearHolder();
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        HttpStatus httpStatus = InnerAPIHolder.getHttpStatusHolder() != null ? InnerAPIHolder.getHttpStatusHolder() : HttpStatus.OK;
        long timestamp = InnerAPIHolder.getTimestamp() == null ? 0 : System.currentTimeMillis() - InnerAPIHolder.getTimestamp();
        log.info("Action:{},auth:{}, HttpStatus:{},time cost:{} ms",
                InnerAPIHolder.getActionHolder(), InnerAPIHolder.getProxyAuth(), httpStatus.value(), timestamp);
        InnerAPIHolder.cleanAllHolder();
    }

    @Autowired
    public void setElasticSearchConfig(ElasticSearchConfig elasticSearchConfig) {
        this.elasticSearchConfig = elasticSearchConfig;
    }

    @Autowired
    public void setRegionConfig(RegionConfig regionConfig) {
        this.regionConfig = regionConfig;
    }

    @Autowired
    public void setCommonConfig(CommonConfig commonConfig) {
        this.commonConfig = commonConfig;
    }

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Autowired
    public void setIamAPI(IamAPI iamAPI) {
        this.iamAPI = iamAPI;
    }



    /**
     * 打印请求日志
     */
    private void logQueryString(HttpServletRequest request) {
        Map parameterMap = request.getParameterMap();
        if (parameterMap == null) parameterMap = new HashMap();

        Map<String, String> headers = new HashMap<>();
        Enumeration<String> enumeration = request.getHeaderNames();
        String name;
        while (enumeration.hasMoreElements()){
            name = enumeration.nextElement();
            headers.put(name, request.getHeader(name));
        }

        log.info("QueryString [{}], Method [{}], ContentType [{}], User-Agent [{}], URI [{}], URL [{}], ParameterMap [{}], RemoteAddr [{}], Headers [{}]",
                request.getQueryString(), request.getMethod(), request.getContentType(),
                request.getHeader("User-Agent"), request.getRequestURI(), request.getRequestURL(),
                GsonUtils.getGson().toJson(parameterMap), request.getRemoteAddr(), GsonUtils.getGson().toJson(headers));

    }
}
