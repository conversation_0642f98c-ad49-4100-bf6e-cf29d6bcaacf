package com.ksyun.cfwapi.interceptor;

import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.context.SpringContext;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class SubOrderIdCleanIntercrptor extends HandlerInterceptorAdapter {

    private MessageSource messageSource;

    @Autowired
    public SubOrderIdCleanIntercrptor(MessageSource messageSource) {
        this.messageSource = messageSource;
    }


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        InnerAPIHolder.cleanAllHolder();
        InnerAPIHolder.setSubOderId(request.getParameter("SubOrderId"));
        ProxyAuth proxyAuth = HttpEntityWapper.generateProxyAuth(request);
        if (StringUtils.isNotBlank(InnerAPIHolder.getSubOderId())) {
            JedisTemplate jedisTemplate = SpringContext.context.getBean(JedisTemplate.class);
            if (jedisTemplate.checkKeyExist(InnerAPIHolder.getSubOderId(), "1", 60 * 60 * 24)) {
                InnerAPIHolder.cleanAllHolder();
                String message = messageSource.getMessage(ErrorCode.SubOrderIdDup,
                        new Object[]{"SubOrderId"}, RequestContextUtils.getLocale(request));
                throw new OpenAPIException(ErrorCode.SubOrderIdDup, message, HttpStatus.BAD_REQUEST);
            }
            proxyAuth.getExtraHeaders().put(Constants.X_AUTH_SUBORDER_ID, InnerAPIHolder.getSubOderId());
        }
        proxyAuth.getExtraHeaders().put(Constants.X_OPENSTACK_REQUEST_ID, proxyAuth.getRequest_id());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
//        InnerAPIHolder.cleanAllHolder();
        super.postHandle(request, response, handler, modelAndView);
    }
}
