package com.ksyun.cfwapi.job;

import cn.hutool.core.collection.CollectionUtil;
import com.ksyun.cfwapi.dao.entity.CfwAclDO;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.service.CfwAclService;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AclHitCountJob {

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private JedisTemplate jedisTemplate;

    @Autowired
    private CfwAclService cfwAclService;

    @XxlJob("AddAclHitCountJob")
    public void addAclHitCountJob() {
        log.info("***AddAclHitCountJob start***");

        int i = 0;
        while (true) {
            List<CfwInstanceDO> fwDOs = cfwInstanceService.pageFwDO(i * Constants.DEFAULT_MAX_RESULTS, Constants.DEFAULT_MAX_RESULTS);
            if (CollectionUtil.isEmpty(fwDOs)) {
                break;
            }

            for (CfwInstanceDO fwDO : fwDOs) {
                try {
                    String keyRedis = String.format(RedisConstants.ACL_HIT_COUNT, fwDO.getFwId());
                    Map<String, String> hitCountMap = jedisTemplate.getMap(keyRedis);
                    if (CollectionUtil.isEmpty(hitCountMap)) {
                        continue;
                    }
                    List<CfwAclDO> aclList = cfwAclService.getHitCountByFwId(fwDO.getFwId());
                    for (CfwAclDO aclDO : aclList) {
                        //redis缓存击中数
                        if (CollectionUtil.isNotEmpty(hitCountMap) && StringUtils.isNotBlank(hitCountMap.get(aclDO.getAclId()))) {
                            long hitCount = aclDO.getHitCount() == null ? 0L : aclDO.getHitCount();
                            aclDO.setHitCount(Long.parseLong(hitCountMap.get(aclDO.getAclId())) + hitCount);
                        }
                    }
                    cfwAclService.batchUpdateHitCount(aclList);
                    jedisTemplate.delKey(keyRedis);
                }catch (Exception e){
                    log.error("更新命中规则数失败 error,fwDO:{},errMsg:{}", fwDO, e);
                }
            }
            i++;
        }
        log.info("***FirewallCheckJob end***");

    }
}
