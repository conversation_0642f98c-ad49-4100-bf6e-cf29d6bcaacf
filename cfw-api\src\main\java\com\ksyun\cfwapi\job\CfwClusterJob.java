package com.ksyun.cfwapi.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwapi.dao.entity.CfwEtcdDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.service.CfwEtcdService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwapi.domain.etcd.WallclusterEtcd;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.utils.DateUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CfwClusterJob {
    @Autowired
    private CfwEtcdService cfwEtcdService;

    @Autowired
    private EtcdService etcdService;

    @XxlJob("UpdateFireWallFileJob")
    public void updateFireWallFileJob() {
        log.info("updateFireWallFileJob start");
        // 获取当前时间的 Date 对象
        Date now = new Date();
        System.out.println("当前时间: " + now);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date previousDay = calendar.getTime();
        int i = 0;
        while (true) {
            int offset = i * Constants.DEFAULT_MAX_RESULTS;
            List<CfwEtcdDO> list = cfwEtcdService.queryChangeWallOperation(previousDay, offset, Constants.DEFAULT_MAX_RESULTS);
            if (CollectionUtil.isEmpty(list)) {
                break;
            }
            for (CfwEtcdDO cfwEtcdDO : list) {
                String key = cfwEtcdDO.getEtcdKey();
                String etcdValue = cfwEtcdDO.getEtcdValue();
                WallclusterEtcd wallclusterEtcd;
                Gson gson = new Gson();
                Type type = new TypeToken<WallclusterEtcd>() {}.getType();
                wallclusterEtcd = gson.fromJson(etcdValue, type);
                wallclusterEtcd.setTimestamp(DateUtils.formatDate(new Date(), DateUtils.DATETIME_FORMAT));
                etcdService.putValue(key, JSONUtil.toJsonStr(wallclusterEtcd));
                log.info("集群重启,key:{},value:{}", key, JSONUtil.toJsonStr(wallclusterEtcd));
            }
            List<Long> ids = list.stream().map(CfwEtcdDO::getId).collect(Collectors.toList());
            cfwEtcdService.updateStatusByIds(ids);
            i++;
        }
        log.info("updateFireWallFileJob end");
    }

}
