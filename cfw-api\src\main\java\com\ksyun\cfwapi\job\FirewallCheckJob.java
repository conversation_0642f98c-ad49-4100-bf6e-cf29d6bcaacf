package com.ksyun.cfwapi.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.EtcdConvert;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.domain.etcd.FirewallCheckEtcd;
import com.ksyun.cfwapi.enums.FirewallActionEnum;
import com.ksyun.cfwapi.enums.FirewallTypeEnum;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwapi.enums.RepairTypeEnum;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FirewallCheckJob {

    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @XxlJob("FirewallCheckJob")
    public void firewallCheckJob() {
        String traceId = GUIDGeneratorUtil.generateGUID();
        log.info("***FirewallCheckJob start*** traceId:{}", traceId);

        int i = 0;
        while (true) {
            List<CfwInstanceDO> fwDOs = cfwInstanceService.pageFwDO(i * Constants.DEFAULT_MAX_RESULTS, Constants.DEFAULT_MAX_RESULTS);
            if (CollectionUtil.isEmpty(fwDOs)) {
                break;
            }
            String date = DateUtils.formatDate(new Date(), DateUtils.DATETIME_FORMAT);
            for (CfwInstanceDO fwDO : fwDOs) {
                String action = FirewallActionEnum.getEnterpriseType();
                if (InstanceTypeEnum.ADVANCED.getType().equals(fwDO.getInstanceType())) {
                    action = FirewallActionEnum.getAdvancedType();
                }
                FirewallCheckEtcd firewallCheckEtcd = EtcdConvert.INSTANCE.convert2FirewallCheckEtcd(date, traceId, fwDO.getFwId(), FirewallTypeEnum.RECONCILIATION.getType(), action, RepairTypeEnum.REPAIR.getType());
                String key = String.format(EtcdConstants.FIREWALL_CHECK, fwDO.getFwId());
                etcdService.putValue(key, JSONUtil.toJsonStr(firewallCheckEtcd));
            }
            i++;
        }
        log.info("***FirewallCheckJob end*** traceId:{}", traceId);
    }
}
