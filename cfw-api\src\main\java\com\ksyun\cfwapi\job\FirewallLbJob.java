package com.ksyun.cfwapi.job;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.ksyun.cfwapi.config.CfwApiCommonConfig;
import com.ksyun.cfwapi.config.SuperConfig;
import com.ksyun.cfwapi.dao.entity.*;
import com.ksyun.cfwapi.dao.service.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.service.cfwService.FireWallLBService;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.FirewallRsStatusEnum;
import com.ksyun.cfwcore.openapi.kec.RunInstancesAPI;
import com.ksyun.cfwcore.openapi.kec.domain.DescribeInstancesRequest;
import com.ksyun.cfwcore.openapi.kec.domain.DescribeInstancesResponse;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CreateCfwRsOtResponse;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import com.ksyun.common.proxy.ProxyAuth;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FirewallLbJob {
    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwRsVifService cfwRsVifService;

    @Autowired
    private RunInstancesAPI runInstancesAPI;

    @Autowired
    private FireWallLBService fireWallLBService;

    @Autowired
    private SubnetService subnetService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private CfwApiCommonConfig cfwApiCommonConfig;

    @Autowired
    private SuperConfig superConfig;

    @Autowired
    private RedissonTemplate redissonTemplate;

    @Autowired
    private CfwRegionConfigService regionConfigService;

    @XxlJob("CreateFireWallJob")
    public void createFireWallJob() {
        String key = String.format(RedisConstants.CREATE_FIREWALL_JOB);
        RLock lock = redissonTemplate.getRedissonClient().getLock(key);
        boolean lockResult = lock.tryLock();
        if (lockResult) {
            try {
                createNode();
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.forceUnlock();
                }
            }
        } else {
            log.info("创建任务还没有执行完");
        }
    }

    private void createNode() {
        log.info("定时任务：创建firewall开始...");
        // 创建 Calendar 实例并设置为当前时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, -cfwApiCommonConfig.getCreateJobInterval());
        Date queryTime = calendar.getTime();
        List<CfwRsDO> unCreateRsList = cfwRsService.getUnCreatedRsByTime(queryTime);
        if (CollectionUtil.isEmpty(unCreateRsList)) {
            log.info("没有未创建Rs的数据...");
            return;
        }

        //查询子网段信息
        Set<String> subnetIds = new HashSet<>();
        Set<String> fwIds = new HashSet<>();
        unCreateRsList.forEach(rsDO -> {
            subnetIds.addAll(Arrays.asList(rsDO.getSubnetId().split(CommonConstant.COMMA)));
            fwIds.add(rsDO.getFwId());
        });
        //查询子网信息
        List<SubnetDO> subnetDOList = subnetService.getSubnetBySubnetIds(new ArrayList<>(subnetIds));
        if (CollectionUtil.isEmpty(subnetDOList)) {
            log.error("查询子网信息为空");
            return;
        }
        //查询vni信息
        List<CfwInstanceDO> fwInstanceList = cfwInstanceService.getCfwInstanceByFwIds(new ArrayList<>(fwIds), "");
        if (CollectionUtil.isEmpty(fwInstanceList)) {
            log.error("查询防火墙实例信息为空");
            return;
        }
        //创建防火墙节点
        createFirewallNode(subnetDOList, fwInstanceList, unCreateRsList);
        log.info("定时任务：创建firewall结束...");
    }

    private void createFirewallNode(List<SubnetDO> subnetDOList, List<CfwInstanceDO> fwInstanceList, List<CfwRsDO> unCreateRsList) {
        //key:subnetId,value:cidr
        Map<String, String> subnetMap = subnetDOList.stream().collect(Collectors.toMap(SubnetDO::getSubnetId, SubnetDO::getCidr, (k1, K2) -> k1));
        Map<String, Integer> fwVniMap = fwInstanceList.stream().collect(Collectors.toMap(CfwInstanceDO::getFwId, CfwInstanceDO::getFwLbVni, (k1, K2) -> k1));
        String requestId = GUIDGeneratorUtil.generateGUID();
        log.info("requestId:{}", requestId);
        List<CfwRsVifDO> cfwRsVifDOList = new ArrayList<>(unCreateRsList.size() * 3);
        List<CfwRsDO> updateRsList = new ArrayList<>(unCreateRsList.size());
        List<List<CfwRsDO>> unCreateRsLists = Lists.partition(unCreateRsList, CommonConstant.BATCH_HANDLE_SIZE_FIVE);
        for (List<CfwRsDO> list : unCreateRsLists) {
            final CountDownLatch latch = new CountDownLatch(list.size());
            //因为每个rs的account_id和Region不相同，只能单个处理
            for (CfwRsDO rsDO : list) {
                try {
                    String rsId = rsDO.getFwInstanceId();
                    Integer fwVni = fwVniMap.get(rsDO.getFwId());
                    if (Objects.isNull(fwVni)) {
                        log.error("reId:{}未找到对应fwVni,FwId:{}}", rsId, rsDO.getFwId());
                        throw new CfwException("未找到对应fwVni");
                    }

                    ProxyAuth auth = new ProxyAuth(requestId, rsDO.getAccountId(), rsDO.getRegion());
                    ProxyAuth superAuth = new ProxyAuth(requestId, superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
                    DescribeInstancesRequest describeParam = new DescribeInstancesRequest();
                    if(StringUtils.isBlank(rsDO.getKecId())){
                        throw new CfwException("获取云服务器信息错误,云主机id为空");
                    }
                    describeParam.setInstanceId(Collections.singletonList(rsDO.getKecId()));
                    Set<DescribeInstancesResponse.InstanceResponse> describeInstancesSet = runInstancesAPI.describeInstances(describeParam, superAuth);
                    if (CollectionUtil.isEmpty(describeInstancesSet)) {
                        log.error("获取云服务器信息错误,云服务器Id值:" + rsDO.getKecId());
                        throw new CfwException("获取云服务器信息错误");
                    }
                    Iterator<DescribeInstancesResponse.InstanceResponse> iterator = describeInstancesSet.iterator();
                    DescribeInstancesResponse.InstanceResponse instanceResponse = iterator.next();
                    if (CollectionUtil.isEmpty(instanceResponse.getNetworkInterfaceSet())) {
                        log.info("云服务器网卡异常，KecId:{}，Response:{}", rsDO.getKecId(), JSONUtil.parse(instanceResponse));
                        throw new CfwException("云服务器网卡异常");
                    }
                    //创建防火墙节点
                    CreateCfwRsOtResponse.FirewallNodeResponse firewallNodeResponse = fireWallLBService.createFirewallNode(rsDO.getFwLbId(), superAuth, rsDO.getFwInstanceId(), instanceResponse.getNetworkInterfaceSet());

                    //组装rs网卡mysql数据
                    List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList = new ArrayList<>(instanceResponse.getNetworkInterfaceSet());
                    fireWallLBService.sortNetworkInterface(networkInterfaceList);
                    cfwRsVifDOList.add(fireWallLBService.getCfwRsVifDO(rsId, networkInterfaceList.get(0), "etcd", instanceResponse.getInstanceId()));
                    cfwRsVifDOList.add(fireWallLBService.getCfwRsVifDO(rsId, networkInterfaceList.get(1), "flow", instanceResponse.getInstanceId()));
                    cfwRsVifDOList.add(fireWallLBService.getCfwRsVifDO(rsId, networkInterfaceList.get(2), "log", instanceResponse.getInstanceId()));

                    //修改Rs状态
                    rsDO.setLbRsId(firewallNodeResponse.getId());
                    rsDO.setRsStatus(FirewallRsStatusEnum.CREATEING.getStatus());
                    rsDO.setUpdateTime(new Date());
                    updateRsList.add(rsDO);

                    CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(auth.getRegion());
                    if (Objects.isNull(regionConfigDO)) {
                        throw new CfwException("查询机房配置信息失败");
                    }
                    //发送etcd
                    fireWallLBService.sendEtcd(requestId, rsDO.getFwId(), String.valueOf(fwVni), rsId, networkInterfaceList, subnetMap, regionConfigDO,auth.getRegion());
                } catch (Exception e) {
                    log.error("创建rs异常：", e);
                } finally {
                    latch.countDown();
                }
            }
            try {
                latch.await();
            } catch (InterruptedException e) {
                log.error("latch.await()异常", e);
            }
        }
        if (CollectionUtil.isNotEmpty(updateRsList)) {
            cfwRsService.updateBatchById(updateRsList);
        }
        if (CollectionUtil.isNotEmpty(cfwRsVifDOList)) {
            cfwRsVifService.saveBatch(cfwRsVifDOList);
        }
    }
}
