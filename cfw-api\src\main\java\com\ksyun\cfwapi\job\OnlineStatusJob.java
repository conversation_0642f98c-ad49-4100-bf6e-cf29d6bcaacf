package com.ksyun.cfwapi.job;

import cn.hutool.core.collection.CollectionUtil;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwcore.alert.AlertTemplate;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmDeal;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmPriority;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.enums.FirewallRsStatusEnum;
import com.ksyun.cfwcore.enums.FirewallStatusEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class OnlineStatusJob {
    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private AlertTemplate alertTemplate;

    @XxlJob("OnlineStatusJob")
    public void onlineStatusJob() {
        log.info("onlineStatusJob start");
        //查询所有时间大于10分钟未更新的rs
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MINUTE, -20);
        Date queryTime = calendar.getTime();
        List<CfwRsDO>  errorCfwRsDOList= cfwRsService.queryByUpdateTime(queryTime);
        if(CollectionUtil.isEmpty(errorCfwRsDOList)){
            log.info("onlineStatusJob 都是正常状态");
            return ;
        }
        
        List<String> fwIds = errorCfwRsDOList.stream().map(CfwRsDO::getFwId).collect(Collectors.toList());
        List<String> fwRsIds = errorCfwRsDOList.stream().map(CfwRsDO::getFwInstanceId).collect(Collectors.toList());
        List<CfwInstanceDO> cfwInstanceDOList = cfwInstanceService.listByFwIdsStatus(fwIds, Arrays.asList(FirewallStatusEnum.RUNNING.getStatus()));
        if(CollectionUtil.isEmpty(cfwInstanceDOList)){
            log.info("onlineStatusJob 都是正常状态,查不到异常防火墙");
            return ;
        }
        List<String> errorFwIds = cfwInstanceDOList.stream().map(CfwInstanceDO::getFwId).collect(Collectors.toList());
        cfwRsService.updateStatusByRsIds(fwRsIds,FirewallRsStatusEnum.ABNORMAL.getStatus());
        cfwInstanceService.updateStatusByFwIds(errorFwIds,FirewallStatusEnum.ERROR.getStatus());
        //失败操作告警
        try{
            OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
            onePieceAlarm.setName(Constants.OPEN_API_SERVICE_NAME);
            onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
            onePieceAlarm.setProduct(commonConfig.getAlertCode());
            onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
            onePieceAlarm.setContent("【防火墙异常】，fwIds：" + fwIds);
            onePieceAlarm.setHtml_content("【五分钟未上报状态】，fwIds：" + fwIds + "，fwRsIds:" + "[" + fwRsIds + "]");
            alertTemplate.send(onePieceAlarm);
        }catch (Exception e){
            log.error("防火墙告警异常，有防火墙五分钟未上报墙状态：fwIds：{}，fwRsIds：{}",fwIds,fwRsIds);
        }
        log.info("onlineStatusJob stop");
    }
}
