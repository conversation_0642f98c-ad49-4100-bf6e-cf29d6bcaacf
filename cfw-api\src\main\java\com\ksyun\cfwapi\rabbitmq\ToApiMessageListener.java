package com.ksyun.cfwapi.rabbitmq;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ksyun.cfwapi.rabbitmq.processor.EventMessageProcessor;
import com.ksyun.cfwapi.thread.ApiThreadPool;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.comm.util.JsonBinder;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import org.springframework.amqp.core.Message;


/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/28 11:11
 */
@Component
@Slf4j
public class ToApiMessageListener {

    @Autowired
    private ApiThreadPool apiThreadPool;

    private JsonBinder jsonBinder = JsonBinder.buildNormalBinder(false);

    @RabbitListener(queues = RabbitMQConfiguration.TO_API_QUEUE, containerFactory = "apiRabbitListenerContainerFactory")
    public void receiveMessage(Message message, Channel channel) throws Exception {
        if (message == null) {
            log.warn("receive empty message from rabbitmq.");
            return;
        }
        byte[] body = message.getBody();
        String message_json = new String(body, StandardCharsets.UTF_8);
        log.debug("receive message from queue {}, {}", RabbitMQConfiguration.TO_API_QUEUE, message_json);
        MessageInfo<?> messageInfo = null;
        try {
            messageInfo = jsonBinder.fromJson(message_json, MessageInfo.class);

            if (messageInfo != null && messageInfo.getMessage_type() != null) {
                EventMessageProcessor eventMessageProcessor = new EventMessageProcessor(messageInfo, message, channel);
                ListenableFuture<Object> future = apiThreadPool.getListeningExecutorService().submit(eventMessageProcessor);
                Futures.addCallback(future, eventMessageProcessor, apiThreadPool.getListeningExecutorService());
            }
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}