package com.ksyun.cfwapi.rabbitmq.config;

import com.ksyun.cfwcore.config.RabbitMqConfig;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.amqp.core.*;


@EnableRabbit
@Configuration
public class RabbitMqConfiguration {

    @Autowired
    private RabbitMqConfig rabbitMqConfig;

    @Bean(name = "apiScurityConnectionFactory")
    public CachingConnectionFactory networkConnectionFactory() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setAddresses(rabbitMqConfig.getRabbitmqAddress());
        connectionFactory.setUsername(rabbitMqConfig.getRabbitmqUsername());
        connectionFactory.setPassword(rabbitMqConfig.getRabbitmqPassword());
        connectionFactory.setVirtualHost(rabbitMqConfig.getRabbitmqVhost());
        connectionFactory.setCacheMode(CachingConnectionFactory.CacheMode.CHANNEL);// 通道缓存
        return connectionFactory;
    }

    @Bean(name = "apiRabbitListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory simpleRabbitListenerContainerFactory() {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(networkConnectionFactory());
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 手动应答
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(2);// 最大消费者数量
        factory.setPrefetchCount(30);// 限流
        return factory;
    }

    @Bean
    public DirectExchange ksyunNewBackupExchange() {
        return ExchangeBuilder
                .directExchange(RabbitMQConfiguration.TO_API_EXCHANGE)
                .durable(true)
                .build();
    }

    @Bean
    public Queue apiToSchedulerNewQueue() {
        return QueueBuilder
                .durable(RabbitMQConfiguration.TO_API_QUEUE)
                .build();
    }

    @Bean
    public Binding apiToSchedulerNewBinding() {
        return BindingBuilder
                .bind(apiToSchedulerNewQueue())
                .to(ksyunNewBackupExchange())
                .with(RabbitMQConfiguration.TO_API_ROUTINGKEY);
    }

    /**
     * 消费者启动自动创建队列、交换机
     *
     * @return
     */
    @Bean
    public RabbitAdmin rabbitAdmin() {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(networkConnectionFactory());
        rabbitAdmin.setAutoStartup(true);
        rabbitAdmin.declareQueue(apiToSchedulerNewQueue());
        rabbitAdmin.declareExchange(ksyunNewBackupExchange());
        rabbitAdmin.declareBinding(apiToSchedulerNewBinding());
        return rabbitAdmin;
    }
}