package com.ksyun.cfwapi.rabbitmq.config;


import com.ksyun.cfwapi.rabbitmq.MessageCommonHandler;
import com.ksyun.cfwapi.rabbitmq.annotation.ToApiCallBack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;


@Component
public class ToApiCallbackConfig {

    @Autowired
    private ApplicationContext applicationContext;

    public static Map<Integer, MessageCommonHandler> commonHandlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, MessageCommonHandler> handlerMap = applicationContext.getBeansOfType(MessageCommonHandler.class);
        handlerMap.forEach((_key, _value) -> {
            MessageCommonHandler messageCommonHandler = handlerMap.get(_key);
            ToApiCallBack apiToSchedulerCallBack = messageCommonHandler.getClass().getAnnotation(ToApiCallBack.class);
            if (apiToSchedulerCallBack != null) {
                commonHandlerMap.put(apiToSchedulerCallBack.messageType(), messageCommonHandler);
            }
        });
    }
}
