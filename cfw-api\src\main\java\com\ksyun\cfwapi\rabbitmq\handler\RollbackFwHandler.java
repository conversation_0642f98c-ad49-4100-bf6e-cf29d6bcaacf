package com.ksyun.cfwapi.rabbitmq.handler;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.rabbitmq.MessageCommonHandler;
import com.ksyun.cfwapi.rabbitmq.annotation.ToApiCallBack;
import com.ksyun.cfwapi.service.cfwService.RollbackFwService;
import com.ksyun.cfwcore.fw.domain.RollbackFwParam;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.monitor.api.domain.MonitorResponse;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ToApiCallBack(messageType = 1)
public class RollbackFwHandler implements MessageCommonHandler {

    @Autowired
    private RollbackFwService rollbackFwService;

    @Override
    public void process(MessageInfo<?> messageInfo) {
        ProxyAuth auth = messageInfo.getAuth();
        RollbackFwParam rollbackFwParam = jsonBinder.fromJson(jsonBinder.toJson(messageInfo.getMessage()), RollbackFwParam.class);
        log.info("[RollbackFwHandler]回滚开始 get message from queue,param:[{}],auth:[{}]", rollbackFwParam, JSONUtil.toJsonStr(auth));
        rollbackFwService.rollbackFwService(auth, rollbackFwParam);
        log.info("[rollbackFwParam]回滚结束 auth: [{}], param: [{}]", JSONUtil.toJsonStr(auth), rollbackFwParam);
    }
}
