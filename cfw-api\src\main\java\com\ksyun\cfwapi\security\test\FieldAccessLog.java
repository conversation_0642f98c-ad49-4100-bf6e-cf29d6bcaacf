package com.ksyun.cfwapi.security.test;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 字段访问日志记录类
 * 用于记录反射访问字段的详细信息，作为漏洞证据
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldAccessLog {
    
    /**
     * 字段名称
     */
    private String fieldName;
    
    /**
     * 字段类型
     */
    private String fieldType;
    
    /**
     * 字段修饰符（private, protected, public等）
     */
    private String fieldModifiers;
    
    /**
     * 是否为私有字段
     */
    private boolean isPrivate;
    
    /**
     * 是否为受保护字段
     */
    private boolean isProtected;
    
    /**
     * 访问前字段是否可访问
     */
    private boolean wasAccessibleBefore;
    
    /**
     * 访问后字段是否可访问
     */
    private boolean isAccessibleAfter;
    
    /**
     * 字段值（敏感信息！）
     */
    private String fieldValue;
    
    /**
     * 字段值类型
     */
    private String fieldValueType;
    
    /**
     * 访问方法描述
     */
    private String accessMethod;
    
    /**
     * 访问时间戳
     */
    private String accessTimestamp;
    
    /**
     * 安全风险等级
     */
    private String riskLevel;
    
    /**
     * 是否为敏感字段（根据字段名判断）
     */
    private boolean isSensitive;
    
    /**
     * 漏洞描述
     */
    private String vulnerabilityDescription;
    
    /**
     * 构造函数 - 创建字段访问日志
     */
    public FieldAccessLog(String fieldName, String fieldType, int modifiers, 
                         boolean wasAccessibleBefore, Object fieldValue, String accessMethod) {
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.fieldModifiers = Modifier.toString(modifiers);
        this.isPrivate = Modifier.isPrivate(modifiers);
        this.isProtected = Modifier.isProtected(modifiers);
        this.wasAccessibleBefore = wasAccessibleBefore;
        this.isAccessibleAfter = true; // setAccessible(true)后都变为可访问
        this.fieldValue = fieldValue != null ? fieldValue.toString() : "null";
        this.fieldValueType = fieldValue != null ? fieldValue.getClass().getSimpleName() : "null";
        this.accessMethod = accessMethod;
        this.accessTimestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        
        // 判断是否为敏感字段
        this.isSensitive = isSensitiveField(fieldName);
        
        // 设置风险等级
        this.riskLevel = calculateRiskLevel();
        
        // 设置漏洞描述
        this.vulnerabilityDescription = generateVulnerabilityDescription();
    }
    
    /**
     * 判断是否为敏感字段
     */
    private boolean isSensitiveField(String fieldName) {
        if (fieldName == null) return false;
        
        String lowerFieldName = fieldName.toLowerCase();
        String[] sensitiveKeywords = {
            "password", "secret", "key", "token", "credential", 
            "auth", "admin", "root", "database", "db", "api",
            "encryption", "jwt", "session", "backup"
        };
        
        for (String keyword : sensitiveKeywords) {
            if (lowerFieldName.contains(keyword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 计算风险等级
     */
    private String calculateRiskLevel() {
        if (isPrivate && isSensitive) {
            return "🔴 CRITICAL - 私有敏感字段被非授权访问";
        } else if (isPrivate) {
            return "🟡 HIGH - 私有字段被非授权访问";
        } else if (isProtected && isSensitive) {
            return "🟠 MEDIUM - 受保护敏感字段被访问";
        } else if (isSensitive) {
            return "🟡 MEDIUM - 敏感字段被访问";
        } else {
            return "🟢 LOW - 普通字段访问";
        }
    }
    
    /**
     * 生成漏洞描述
     */
    private String generateVulnerabilityDescription() {
        StringBuilder desc = new StringBuilder();
        
        if (isPrivate) {
            desc.append("通过setAccessible(true)绕过Java访问控制，");
            desc.append("非授权访问私有字段'").append(fieldName).append("'");
        } else if (isProtected) {
            desc.append("通过setAccessible(true)访问受保护字段'").append(fieldName).append("'");
        } else {
            desc.append("访问公共字段'").append(fieldName).append("'");
        }
        
        if (isSensitive) {
            desc.append("，该字段包含敏感信息，存在信息泄露风险");
        }
        
        if (!wasAccessibleBefore && isAccessibleAfter) {
            desc.append("。原本不可访问的字段被强制设置为可访问状态");
        }
        
        return desc.toString();
    }
    
    /**
     * 获取格式化的访问报告
     */
    public String getFormattedReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 字段访问报告 ===\n");
        report.append("字段名称: ").append(fieldName).append("\n");
        report.append("字段类型: ").append(fieldType).append("\n");
        report.append("访问修饰符: ").append(fieldModifiers).append("\n");
        report.append("风险等级: ").append(riskLevel).append("\n");
        report.append("字段值: ").append(fieldValue).append("\n");
        report.append("访问方法: ").append(accessMethod).append("\n");
        report.append("访问时间: ").append(accessTimestamp).append("\n");
        report.append("漏洞描述: ").append(vulnerabilityDescription).append("\n");
        report.append("========================\n");
        return report.toString();
    }
}
