package com.ksyun.cfwapi.security.test;

import lombok.Data;

import java.util.Set;

/**
 * 反射测试请求参数类
 * 用于接收Postman测试请求的参数
 * 
 * <AUTHOR>
 */
@Data
public class ReflectionTestRequest {
    
    /**
     * 测试名称
     */
    private String testName;
    
    /**
     * 测试描述
     */
    private String testDescription;
    
    /**
     * 敏感测试数据
     */
    private SensitiveTestData sensitiveData;
    
    /**
     * 特殊字段集合（可以为null的字段）
     */
    private Set<String> specialFields;
    
    /**
     * 是否启用递归测试
     */
    private boolean enableRecursiveTest = true;
    
    /**
     * 测试模式
     */
    private TestMode testMode = TestMode.FULL;
    
    /**
     * 攻击者信息（模拟）
     */
    private AttackerInfo attackerInfo;
    
    /**
     * 测试模式枚举
     */
    public enum TestMode {
        /**
         * 完整测试 - 测试所有字段
         */
        FULL,
        
        /**
         * 仅私有字段测试
         */
        PRIVATE_ONLY,
        
        /**
         * 仅敏感字段测试
         */
        SENSITIVE_ONLY,
        
        /**
         * 快速测试 - 基础验证
         */
        QUICK
    }
    
    /**
     * 攻击者信息类（用于模拟真实攻击场景）
     */
    @Data
    public static class AttackerInfo {
        /**
         * 攻击者ID
         */
        private String attackerId;
        
        /**
         * 攻击来源IP
         */
        private String sourceIp;
        
        /**
         * 攻击时间
         */
        private String attackTime;
        
        /**
         * 攻击目标
         */
        private String target;
        
        /**
         * 攻击方法
         */
        private String attackMethod = "Java Reflection setAccessible Abuse";
        
        /**
         * 攻击载荷
         */
        private String payload;
        
        /**
         * 预期获取的敏感信息类型
         */
        private String[] expectedSensitiveData;
    }
    
    /**
     * 创建默认的测试请求
     */
    public static ReflectionTestRequest createDefaultRequest() {
        ReflectionTestRequest request = new ReflectionTestRequest();
        request.setTestName("CFW反射漏洞演示测试");
        request.setTestDescription("演示CommonUtils.getOtherFieldAllNullExceptSpecial方法中的setAccessible滥用漏洞");
        request.setSensitiveData(new SensitiveTestData());
        request.setTestMode(TestMode.FULL);
        request.setEnableRecursiveTest(true);
        
        // 设置攻击者信息
        AttackerInfo attackerInfo = new AttackerInfo();
        attackerInfo.setAttackerId("test-attacker-001");
        attackerInfo.setSourceIp("*************");
        attackerInfo.setTarget("CFW云防火墙系统");
        attackerInfo.setPayload("SensitiveTestData with private fields");
        attackerInfo.setExpectedSensitiveData(new String[]{
            "adminPassword", "databaseCredentials", "apiSecretKey", 
            "encryptionMasterKey", "jwtSigningSecret"
        });
        request.setAttackerInfo(attackerInfo);
        
        return request;
    }
    
    /**
     * 创建权限提升攻击测试请求
     */
    public static ReflectionTestRequest createPrivilegeEscalationRequest() {
        ReflectionTestRequest request = new ReflectionTestRequest();
        request.setTestName("权限提升攻击演示");
        request.setTestDescription("通过反射漏洞尝试获取管理员权限和敏感系统信息");
        
        // 创建包含权限信息的敏感数据
        SensitiveTestData sensitiveData = new SensitiveTestData();
        request.setSensitiveData(sensitiveData);
        request.setTestMode(TestMode.SENSITIVE_ONLY);
        
        // 设置攻击者信息
        AttackerInfo attackerInfo = new AttackerInfo();
        attackerInfo.setAttackerId("privilege-escalation-attacker");
        attackerInfo.setSourceIp("**********");
        attackerInfo.setTarget("系统管理员权限");
        attackerInfo.setPayload("Privilege escalation via reflection");
        attackerInfo.setExpectedSensitiveData(new String[]{
            "adminPassword", "systemConfigPassword", "internalServiceToken"
        });
        request.setAttackerInfo(attackerInfo);
        
        return request;
    }
    
    /**
     * 创建数据渗透攻击测试请求
     */
    public static ReflectionTestRequest createDataExfiltrationRequest() {
        ReflectionTestRequest request = new ReflectionTestRequest();
        request.setTestName("数据渗透攻击演示");
        request.setTestDescription("通过反射漏洞尝试获取数据库连接信息和API密钥");
        
        SensitiveTestData sensitiveData = new SensitiveTestData();
        request.setSensitiveData(sensitiveData);
        request.setTestMode(TestMode.FULL);
        request.setEnableRecursiveTest(true);
        
        // 设置攻击者信息
        AttackerInfo attackerInfo = new AttackerInfo();
        attackerInfo.setAttackerId("data-exfiltration-attacker");
        attackerInfo.setSourceIp("***********");
        attackerInfo.setTarget("数据库和API凭据");
        attackerInfo.setPayload("Data exfiltration via nested object reflection");
        attackerInfo.setExpectedSensitiveData(new String[]{
            "databaseCredentials", "apiSecretKey", "encryptionMasterKey",
            "databaseConfig.password", "apiConfig.openstackApiKey"
        });
        request.setAttackerInfo(attackerInfo);
        
        return request;
    }
}
