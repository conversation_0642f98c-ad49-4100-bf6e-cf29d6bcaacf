package com.ksyun.cfwapi.security.test;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 反射测试结果类
 * 包含完整的反射漏洞测试结果和分析报告
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReflectionTestResult {
    
    /**
     * 测试执行时间
     */
    private String testExecutionTime;
    
    /**
     * 测试对象类名
     */
    private String testObjectClassName;
    
    /**
     * 字段访问日志列表
     */
    private List<FieldAccessLog> fieldAccessLogs;
    
    /**
     * 测试总结
     */
    private TestSummary testSummary;
    
    /**
     * 漏洞分析报告
     */
    private VulnerabilityAnalysis vulnerabilityAnalysis;
    
    /**
     * 构造函数
     */
    public ReflectionTestResult(List<FieldAccessLog> fieldAccessLogs, String testObjectClassName) {
        this.testExecutionTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
        this.testObjectClassName = testObjectClassName;
        this.fieldAccessLogs = fieldAccessLogs;
        this.testSummary = generateTestSummary();
        this.vulnerabilityAnalysis = generateVulnerabilityAnalysis();
    }
    
    /**
     * 生成测试总结
     */
    private TestSummary generateTestSummary() {
        TestSummary summary = new TestSummary();
        
        summary.totalFieldsAccessed = fieldAccessLogs.size();
        summary.privateFieldsAccessed = (int) fieldAccessLogs.stream()
                .filter(FieldAccessLog::isPrivate)
                .count();
        summary.protectedFieldsAccessed = (int) fieldAccessLogs.stream()
                .filter(FieldAccessLog::isProtected)
                .count();
        summary.sensitiveFieldsAccessed = (int) fieldAccessLogs.stream()
                .filter(FieldAccessLog::isSensitive)
                .count();
        summary.criticalVulnerabilities = (int) fieldAccessLogs.stream()
                .filter(log -> log.getRiskLevel().contains("CRITICAL"))
                .count();
        summary.highRiskVulnerabilities = (int) fieldAccessLogs.stream()
                .filter(log -> log.getRiskLevel().contains("HIGH"))
                .count();
        
        return summary;
    }
    
    /**
     * 生成漏洞分析报告
     */
    private VulnerabilityAnalysis generateVulnerabilityAnalysis() {
        VulnerabilityAnalysis analysis = new VulnerabilityAnalysis();
        
        // 漏洞存在性判断
        analysis.vulnerabilityExists = testSummary.privateFieldsAccessed > 0 || testSummary.sensitiveFieldsAccessed > 0;
        
        // 风险等级评估
        if (testSummary.criticalVulnerabilities > 0) {
            analysis.overallRiskLevel = "🔴 CRITICAL";
        } else if (testSummary.highRiskVulnerabilities > 0) {
            analysis.overallRiskLevel = "🟡 HIGH";
        } else if (testSummary.sensitiveFieldsAccessed > 0) {
            analysis.overallRiskLevel = "🟠 MEDIUM";
        } else {
            analysis.overallRiskLevel = "🟢 LOW";
        }
        
        // 漏洞描述
        analysis.vulnerabilityDescription = generateVulnerabilityDescription();
        
        // 影响评估
        analysis.impactAssessment = generateImpactAssessment();
        
        // 修复建议
        analysis.remediationSuggestions = generateRemediationSuggestions();
        
        // 证据链
        analysis.evidenceChain = generateEvidenceChain();
        
        return analysis;
    }
    
    /**
     * 生成漏洞描述
     */
    private String generateVulnerabilityDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append("Java反射setAccessible滥用漏洞验证结果：\n");
        
        if (testSummary.privateFieldsAccessed > 0) {
            desc.append("✅ 成功绕过Java访问控制，访问了 ").append(testSummary.privateFieldsAccessed).append(" 个私有字段\n");
        }
        
        if (testSummary.sensitiveFieldsAccessed > 0) {
            desc.append("🚨 发现敏感信息泄露，访问了 ").append(testSummary.sensitiveFieldsAccessed).append(" 个敏感字段\n");
        }
        
        desc.append("通过field.setAccessible(true)方法成功绕过了Java的访问控制机制");
        
        return desc.toString();
    }
    
    /**
     * 生成影响评估
     */
    private String generateImpactAssessment() {
        StringBuilder impact = new StringBuilder();
        impact.append("漏洞影响评估：\n");
        impact.append("1. 访问控制绕过：可以访问本应受保护的私有字段\n");
        impact.append("2. 敏感信息泄露：可能暴露密码、密钥等敏感数据\n");
        impact.append("3. 数据完整性风险：可能修改关键系统状态\n");
        impact.append("4. 安全边界突破：违反了Java的封装原则\n");
        
        if (testSummary.criticalVulnerabilities > 0) {
            impact.append("⚠️ 发现关键漏洞，建议立即修复");
        }
        
        return impact.toString();
    }
    
    /**
     * 生成修复建议
     */
    private String generateRemediationSuggestions() {
        return "修复建议：\n" +
               "1. 限制setAccessible的使用，添加安全检查\n" +
               "2. 实现字段访问白名单机制\n" +
               "3. 对敏感字段添加额外保护\n" +
               "4. 增强异常处理，避免信息泄露\n" +
               "5. 添加安全审计日志\n" +
               "6. 使用SecurityManager限制反射操作";
    }
    
    /**
     * 生成证据链
     */
    private String generateEvidenceChain() {
        StringBuilder evidence = new StringBuilder();
        evidence.append("漏洞证据链：\n");
        evidence.append("1. 测试对象：").append(testObjectClassName).append("\n");
        evidence.append("2. 执行时间：").append(testExecutionTime).append("\n");
        evidence.append("3. 访问字段总数：").append(testSummary.totalFieldsAccessed).append("\n");
        evidence.append("4. 私有字段访问：").append(testSummary.privateFieldsAccessed).append(" 个\n");
        evidence.append("5. 敏感字段访问：").append(testSummary.sensitiveFieldsAccessed).append(" 个\n");
        evidence.append("6. 关键漏洞数量：").append(testSummary.criticalVulnerabilities).append(" 个\n");
        
        return evidence.toString();
    }
    
    /**
     * 获取敏感字段访问日志
     */
    public List<FieldAccessLog> getSensitiveFieldLogs() {
        return fieldAccessLogs.stream()
                .filter(FieldAccessLog::isSensitive)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取私有字段访问日志
     */
    public List<FieldAccessLog> getPrivateFieldLogs() {
        return fieldAccessLogs.stream()
                .filter(FieldAccessLog::isPrivate)
                .collect(Collectors.toList());
    }
    
    /**
     * 测试总结内部类
     */
    @Data
    @NoArgsConstructor
    public static class TestSummary {
        private int totalFieldsAccessed;
        private int privateFieldsAccessed;
        private int protectedFieldsAccessed;
        private int sensitiveFieldsAccessed;
        private int criticalVulnerabilities;
        private int highRiskVulnerabilities;
    }
    
    /**
     * 漏洞分析内部类
     */
    @Data
    @NoArgsConstructor
    public static class VulnerabilityAnalysis {
        private boolean vulnerabilityExists;
        private String overallRiskLevel;
        private String vulnerabilityDescription;
        private String impactAssessment;
        private String remediationSuggestions;
        private String evidenceChain;
    }
}
