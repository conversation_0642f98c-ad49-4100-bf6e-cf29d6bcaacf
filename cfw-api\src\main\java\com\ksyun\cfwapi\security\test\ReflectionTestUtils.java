package com.ksyun.cfwapi.security.test;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 反射测试工具类
 * 增强版的CommonUtils，专门用于漏洞演示和测试
 * 提供详细的字段访问日志记录功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class ReflectionTestUtils {
    
    /**
     * 增强版的getOtherFieldAllNullExceptSpecial方法
     * 在原有功能基础上增加详细的访问日志记录
     * 
     * @param object 需要检查的对象
     * @param specialFieldSet 特殊字段集合（可以为null的字段）
     * @return 反射测试结果，包含详细的字段访问日志
     */
    public static ReflectionTestResult getOtherFieldAllNullExceptSpecialWithLogging(
            Object object, Set<String> specialFieldSet) throws Exception {
        
        log.info("🚨 开始执行反射漏洞测试 - 目标对象: {}", object.getClass().getName());
        
        List<FieldAccessLog> accessLogs = new ArrayList<>();
        boolean allFieldsNull = processObjectFields(object, specialFieldSet, accessLogs, 0);
        
        ReflectionTestResult result = new ReflectionTestResult(accessLogs, object.getClass().getName());
        
        log.info("✅ 反射漏洞测试完成 - 访问字段数: {}, 私有字段数: {}, 敏感字段数: {}", 
                accessLogs.size(), 
                result.getTestSummary().getPrivateFieldsAccessed(),
                result.getTestSummary().getSensitiveFieldsAccessed());
        
        return result;
    }
    
    /**
     * 处理对象字段（支持递归）
     * 
     * @param object 要处理的对象
     * @param specialFieldSet 特殊字段集合
     * @param accessLogs 访问日志列表
     * @param depth 递归深度
     * @return 是否所有字段都为null
     */
    private static boolean processObjectFields(Object object, Set<String> specialFieldSet, 
                                             List<FieldAccessLog> accessLogs, int depth) throws Exception {
        
        if (object == null) {
            return true;
        }
        
        // 防止无限递归
        if (depth > 3) {
            log.warn("⚠️ 递归深度超过限制，停止处理");
            return true;
        }
        
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        log.info("🔍 处理类: {} (深度: {}), 字段数量: {}", clazz.getName(), depth, fields.length);
        
        for (Field field : fields) {
            // 跳过特殊字段
            if (!CollectionUtils.isEmpty(specialFieldSet) && specialFieldSet.contains(field.getName())) {
                log.debug("⏭️ 跳过特殊字段: {}", field.getName());
                continue;
            }
            
            // 记录访问前状态
            boolean wasAccessible = field.isAccessible();
            
            try {
                // 🚨 漏洞触发点：强制设置字段可访问
                field.setAccessible(true);
                log.warn("🚨 漏洞触发！调用 field.setAccessible(true) - 字段: {} (原访问性: {})", 
                        field.getName(), wasAccessible);
                
                // 获取字段值
                Object fieldValue = field.get(object);
                
                // 创建访问日志
                FieldAccessLog accessLog = new FieldAccessLog(
                    field.getName(),
                    field.getType().getSimpleName(),
                    field.getModifiers(),
                    wasAccessible,
                    fieldValue,
                    "field.setAccessible(true) + field.get(object)"
                );
                
                accessLogs.add(accessLog);
                
                // 输出详细日志
                logFieldAccess(accessLog, depth);
                
                // 处理非null值
                if (fieldValue != null) {
                    if (isPrimitiveOrWrapper(fieldValue)) {
                        return false;
                    } else {
                        // 递归处理复杂对象
                        log.info("🔄 递归处理嵌套对象: {} -> {}", field.getName(), fieldValue.getClass().getName());
                        if (!processObjectFields(fieldValue, specialFieldSet, accessLogs, depth + 1)) {
                            return false;
                        }
                    }
                }
                
            } catch (IllegalAccessException e) {
                log.error("❌ 字段访问失败: {} - {}", field.getName(), e.getMessage());
                
                // 即使访问失败也要记录日志
                FieldAccessLog errorLog = new FieldAccessLog(
                    field.getName(),
                    field.getType().getSimpleName(),
                    field.getModifiers(),
                    wasAccessible,
                    "ACCESS_FAILED: " + e.getMessage(),
                    "field.setAccessible(true) + field.get(object) [FAILED]"
                );
                accessLogs.add(errorLog);
            }
        }
        
        return true;
    }
    
    /**
     * 生成缩进字符串（兼容Java 8）
     */
    private static String generateIndent(int depth) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < depth; i++) {
            sb.append("  ");
        }
        return sb.toString();
    }
    
    /**
     * 生成重复字符串（兼容Java 8）
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 记录字段访问详情
     */
    private static void logFieldAccess(FieldAccessLog accessLog, int depth) {
        String indent = generateIndent(depth);
        
        if (accessLog.isPrivate() && accessLog.isSensitive()) {
            log.error("{}🔴 CRITICAL: 私有敏感字段被访问 - {} = '{}'", 
                    indent, accessLog.getFieldName(), accessLog.getFieldValue());
        } else if (accessLog.isPrivate()) {
            log.warn("{}🟡 HIGH: 私有字段被访问 - {} = '{}'", 
                    indent, accessLog.getFieldName(), accessLog.getFieldValue());
        } else if (accessLog.isSensitive()) {
            log.warn("{}🟠 MEDIUM: 敏感字段被访问 - {} = '{}'", 
                    indent, accessLog.getFieldName(), accessLog.getFieldValue());
        } else {
            log.info("{}🟢 LOW: 普通字段被访问 - {} = '{}'", 
                    indent, accessLog.getFieldName(), accessLog.getFieldValue());
        }
        
        log.debug("{}   字段类型: {}, 修饰符: {}, 原可访问性: {}", 
                indent, accessLog.getFieldType(), accessLog.getFieldModifiers(), accessLog.isWasAccessibleBefore());
    }
    
    /**
     * 判断是否为基本类型或包装类型
     */
    private static boolean isPrimitiveOrWrapper(Object obj) {
        return obj instanceof Number || 
               obj instanceof Boolean || 
               obj instanceof String || 
               obj instanceof Character;
    }
    
    /**
     * 简化版测试方法 - 仅用于快速验证
     * 
     * @param object 测试对象
     * @return 简化的测试结果
     */
    public static ReflectionTestResult quickReflectionTest(Object object) {
        try {
            return getOtherFieldAllNullExceptSpecialWithLogging(object, null);
        } catch (Exception e) {
            log.error("快速反射测试失败", e);
            
            // 返回错误结果
            List<FieldAccessLog> errorLogs = new ArrayList<>();
            errorLogs.add(new FieldAccessLog(
                "ERROR",
                "Exception",
                0,
                false,
                e.getMessage(),
                "quickReflectionTest [FAILED]"
            ));
            
            return new ReflectionTestResult(errorLogs, object.getClass().getName());
        }
    }
    
    /**
     * 生成漏洞演示报告
     * 
     * @param result 测试结果
     * @return 格式化的演示报告
     */
    public static String generateVulnerabilityDemoReport(ReflectionTestResult result) {
        StringBuilder report = new StringBuilder();
        
        report.append(repeatString("=", 80)).append("\n");
        report.append("🚨 CFW反射漏洞演示报告\n");
        report.append(repeatString("=", 80)).append("\n");
        report.append("测试时间: ").append(result.getTestExecutionTime()).append("\n");
        report.append("测试对象: ").append(result.getTestObjectClassName()).append("\n");
        report.append("风险等级: ").append(result.getVulnerabilityAnalysis().getOverallRiskLevel()).append("\n");
        report.append("\n");
        
        // 测试总结
        report.append("📊 测试总结:\n");
        report.append("- 总访问字段数: ").append(result.getTestSummary().getTotalFieldsAccessed()).append("\n");
        report.append("- 私有字段访问: ").append(result.getTestSummary().getPrivateFieldsAccessed()).append(" 个\n");
        report.append("- 敏感字段访问: ").append(result.getTestSummary().getSensitiveFieldsAccessed()).append(" 个\n");
        report.append("- 关键漏洞数量: ").append(result.getTestSummary().getCriticalVulnerabilities()).append(" 个\n");
        report.append("\n");
        
        // 敏感字段详情
        if (!result.getSensitiveFieldLogs().isEmpty()) {
            report.append("🚨 敏感字段访问详情:\n");
            for (FieldAccessLog log : result.getSensitiveFieldLogs()) {
                report.append("- ").append(log.getFieldName())
                      .append(" (").append(log.getFieldModifiers()).append("): ")
                      .append(log.getFieldValue()).append("\n");
            }
            report.append("\n");
        }
        
        // 漏洞分析
        report.append("🔍 漏洞分析:\n");
        report.append(result.getVulnerabilityAnalysis().getVulnerabilityDescription()).append("\n");
        report.append("\n");
        
        report.append(repeatString("=", 80)).append("\n");
        
        return report.toString();
    }
}
