package com.ksyun.cfwapi.security.test;

import lombok.Data;

/**
 * 敏感数据测试类 - 用于演示反射漏洞
 * 包含各种类型的敏感私有字段，这些字段应该被访问控制保护
 * 
 * <AUTHOR>
 */
@Data
public class SensitiveTestData {
    
    // ==================== 私有敏感字段 ====================
    // 这些字段应该被访问控制保护，不应该被外部直接访问
    
    /**
     * 管理员密码 - 最高敏感级别
     */
    private String adminPassword = "ADMIN_SECRET_PASSWORD_2024!@#";
    
    /**
     * 数据库连接凭据 - 高敏感级别
     */
    private String databaseCredentials = "root:<EMAIL>:3306/cfw_production";
    
    /**
     * API密钥 - 高敏感级别
     */
    private String apiSecretKey = "sk-1234567890abcdef-PRODUCTION-KEY";
    
    /**
     * 加密主密钥 - 最高敏感级别
     */
    private String encryptionMasterKey = "AES-256-MASTER-KEY-PRODUCTION-2024";
    
    /**
     * JWT签名密钥 - 高敏感级别
     */
    private String jwtSigningSecret = "jwt-signing-secret-key-production-xyz789";
    
    /**
     * 内部服务令牌 - 中敏感级别
     */
    private String internalServiceToken = "internal-service-token-abc123def456";
    
    /**
     * 系统配置密码 - 中敏感级别
     */
    private String systemConfigPassword = "system-config-password-2024";
    
    /**
     * 备份加密密钥 - 高敏感级别
     */
    private String backupEncryptionKey = "backup-encryption-key-xyz789";
    
    // ==================== 嵌套敏感对象 ====================
    
    /**
     * 嵌套的数据库配置对象 - 用于测试递归反射访问
     */
    private DatabaseConfig databaseConfig = new DatabaseConfig();
    
    /**
     * 嵌套的API配置对象 - 用于测试递归反射访问
     */
    private ApiConfig apiConfig = new ApiConfig();
    
    // ==================== 受保护字段 ====================
    
    /**
     * 内部配置信息 - 受保护级别
     */
    protected String internalConfig = "internal-config-data-protected";
    
    /**
     * 系统版本信息 - 受保护级别
     */
    protected String systemVersion = "CFW-SYSTEM-VERSION-2024.1.0";
    
    // ==================== 公共字段 ====================
    // 这些字段可以被正常访问
    
    /**
     * 公共信息 - 可以被正常访问
     */
    public String publicInfo = "public_information_accessible";
    
    /**
     * 版本号 - 可以被正常访问
     */
    public String version = "1.0.0";
    
    /**
     * 描述信息 - 可以被正常访问
     */
    public String description = "Reflection vulnerability test data";
    
    // ==================== 嵌套敏感类定义 ====================
    
    /**
     * 数据库配置类 - 包含敏感的数据库连接信息
     */
    public static class DatabaseConfig {
        private String host = "prod-db.company.com";
        private int port = 3306;
        private String username = "cfw_admin";
        private String password = "DB_PASSWORD_SUPER_SECRET_2024";
        private String database = "cfw_production";
        private String connectionString = "***********************************************************************************";
        
        // 公共字段
        public String driver = "com.mysql.cj.jdbc.Driver";
        public int maxConnections = 100;
        
        // Getters and Setters
        public String getHost() { return host; }
        public void setHost(String host) { this.host = host; }
        public int getPort() { return port; }
        public void setPort(int port) { this.port = port; }
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
        public String getDatabase() { return database; }
        public void setDatabase(String database) { this.database = database; }
        public String getConnectionString() { return connectionString; }
        public void setConnectionString(String connectionString) { this.connectionString = connectionString; }
    }
    
    /**
     * API配置类 - 包含敏感的API密钥信息
     */
    public static class ApiConfig {
        private String openstackApiKey = "openstack-api-key-production-xyz789";
        private String etcdClusterToken = "etcd-cluster-access-token-abc123";
        private String redisPassword = "redis-secret-password-2024";
        private String elasticsearchAuth = "elastic:elastic_password_secret_2024";
        
        // 公共字段
        public String apiVersion = "v1.0";
        public int timeout = 30000;
        
        // Getters and Setters
        public String getOpenstackApiKey() { return openstackApiKey; }
        public void setOpenstackApiKey(String openstackApiKey) { this.openstackApiKey = openstackApiKey; }
        public String getEtcdClusterToken() { return etcdClusterToken; }
        public void setEtcdClusterToken(String etcdClusterToken) { this.etcdClusterToken = etcdClusterToken; }
        public String getRedisPassword() { return redisPassword; }
        public void setRedisPassword(String redisPassword) { this.redisPassword = redisPassword; }
        public String getElasticsearchAuth() { return elasticsearchAuth; }
        public void setElasticsearchAuth(String elasticsearchAuth) { this.elasticsearchAuth = elasticsearchAuth; }
    }
}
