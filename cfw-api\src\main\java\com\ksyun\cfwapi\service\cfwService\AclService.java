package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwapi.convert.AclConvert;
import com.ksyun.cfwapi.dao.entity.*;
import com.ksyun.cfwapi.dao.service.*;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.acl.*;
import com.ksyun.cfwapi.domain.etcd.AclEtcd;
import com.ksyun.cfwapi.domain.etcd.PriorityEtcd;
import com.ksyun.cfwapi.enums.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.enums.WallChangeTypeEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AclService {

    @Autowired
    private CfwAreaService cfwAreaService;

    @Autowired
    private CfwAclRelateService cfwAclRelateService;

    @Autowired
    private CfwAclService cfwAclService;

    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwAddrbookService addrbookService;

    @Autowired
    private CfwServicegroupService servicegroupService;

    @Autowired
    private CfwClusterService cfwClusterService;

    @Autowired
    private JedisTemplate jedisTemplate;

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse createCfwAcl(CreateCfwAclParam param) throws Exception {
        List<CfwAclDO> cfwAclDOList = cfwAclService.countCfwAclDOByFwId(param.getCfwInstanceId());
        if (CollectionUtil.isNotEmpty(cfwAclDOList) && cfwAclDOList.size() >= 5000) {
            throw new CfwException("防火墙下ACL数量已达到上限5000");
        }
        
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        //after/before+优先级 处理优先级
        int priority = 0;
        if (param.getPriorityPosition().startsWith(PriorityEnum.BEFORE.getType())) {
            if (param.getPriorityPosition().equals(PriorityEnum.BEFORE.getType())) {
                priority = 1;
            } else {
                priority = Integer.parseInt(param.getPriorityPosition().replace(PriorityEnum.BEFORE.getType(), ""));
            }
        } else {
            if (!param.getPriorityPosition().equals(PriorityEnum.AFTER.getType())) {
                priority = Integer.parseInt(param.getPriorityPosition().replace(PriorityEnum.AFTER.getType(), "")) + 1;
            }
        }

        //组装数据
        String srcIps = null;
        if(CollectionUtils.isNotEmpty(param.getSrcIps())){
            srcIps = param.getSrcIps().stream().filter(Objects::nonNull).map(s -> s.replaceAll("[\\s\\n]", "")).filter(s -> !s.isEmpty()).distinct().collect(Collectors.joining(CommonConstant.COMMA));
        }
        String destIps = null;
        if(CollectionUtils.isNotEmpty(param.getDestIps())){
            destIps = param.getDestIps().stream().filter(Objects::nonNull).map(s -> s.replaceAll("[\\s\\n]", "")).filter(s -> !s.isEmpty()).distinct().collect(Collectors.joining(CommonConstant.COMMA));
        }
        String srcAddrbooks = null;
        if(CollectionUtils.isNotEmpty(param.getSrcAddrbooks())){
            srcAddrbooks = param.getSrcAddrbooks().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String destAddrbooks = null;
        if(CollectionUtils.isNotEmpty(param.getDestAddrbooks())){
            destAddrbooks = param.getDestAddrbooks().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String srcZones = CollectionUtils.isNotEmpty(param.getSrcZones()) ? JSONUtil.toJsonStr(param.getSrcZones()) : null;
        String destZones = CollectionUtils.isNotEmpty(param.getDestZones()) ? JSONUtil.toJsonStr(param.getDestZones()) : null;

        String serviceInfo = null;
        if(CollectionUtils.isNotEmpty(param.getServiceInfos())){
            serviceInfo = param.getServiceInfos().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String serviceGroups = null;
        if(CollectionUtils.isNotEmpty(param.getServiceGroups())){
            serviceGroups = param.getServiceGroups().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String appValues = null;
        if(CollectionUtils.isNotEmpty(param.getAppValues())){
            appValues = param.getAppValues().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }

        CfwAclDO aclDO = AclConvert.INSTANCE.convert2CfwAclDO(param, auth.getAccount_id(), srcIps, destIps, srcAddrbooks, destAddrbooks, srcZones, serviceInfo, serviceGroups, priority, appValues, destZones);

        //设置ruleId
        int ruleId = 0;
        if (CollectionUtil.isNotEmpty(cfwAclDOList)) {
            for(int i=1;i<=cfwAclDOList.size();i++){
                if(cfwAclDOList.get(i-1).getRuleId()==i){
                    continue;
                }
                ruleId = i;
                break;
            }
            if(ruleId == 0){
                ruleId = cfwAclDOList.size()+1;
            }
        }else{
            ruleId = 1;
        }
        aclDO.setRuleId(ruleId);
        
        //校验参数
        boolean duplicateName = cfwAclService.checkAclNameDuplicate(aclDO.getFwId(), "", aclDO.getAclName());
        if (duplicateName) {
            throw new CfwException("Acl名称重复");
        }
        checkParam(aclDO);

        //保存地址簿和服务组引用关系
        Map<String, CfwAclRelateDO> cfwAclRelateDOMap = getCfwAclRelateDOMap(param.getSrcAddrbooks(), param.getDestAddrbooks(), param.getServiceGroups(), aclDO);
        if (CollectionUtil.isNotEmpty(cfwAclRelateDOMap)) {
            cfwAclRelateService.saveBatch(cfwAclRelateDOMap.values());
        }

        //查询排序num转name
        String priorityPosition = param.getPriorityPosition();
        priorityPosition = getEtcdPriorityPosition(aclDO.getFwId(), priorityPosition);

        //保存acl
        cfwAclService.saveAcl(aclDO, priority, cfwAclRelateDOMap);

        //发送etcd
        AclEtcd aclEtcd = aclConvert2AclEtcd(aclDO, auth.getRequest_id(), priorityPosition);
        String key = String.format(EtcdConstants.ACL, aclDO.getFwId(), aclDO.getAclId());
        log.info("创建Acl规则 key:{},Etcd:{}",key, JSONUtil.toJsonStr(aclEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(aclEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(aclDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ACL.getCode(), WallChangeTypeEnum.CREATE.getCode(), Collections.singletonList(aclDO.getAclId()));

        //组装返回参数
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true).setId(aclDO.getAclId());
    }

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse deleteCfwAcl(List<String> aclIds) throws CfwException {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        List<CfwAclDO> cfwAclDOs = cfwAclService.getByAclIds(aclIds,auth.getAccount_id());
        if (CollectionUtil.isEmpty(cfwAclDOs)) {
            throw new CfwException("Acl规则不存在");
        }

        //删除acl关联地址簿和服务组关联关系
        List<String> cfwAclIdRelates = cfwAclDOs.stream().filter(e -> AddrTypeEnum.ADDRBOOK.getType().equals(e.getSrcType()) || AddrTypeEnum.ADDRBOOK.getType().equals(e.getDestType()) || ServiceTypeEnum.SERVICEGROUP.getType().equals(e.getServiceType()))
                .map(CfwAclDO::getAclId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(cfwAclIdRelates)) {
            cfwAclRelateService.deleteAclrelate(cfwAclIdRelates);
        }

        //删除acl规则
        cfwAclService.deleteAcl(cfwAclDOs);

        //处理acl优先级
        cfwAclService.handleAllPriority(cfwAclDOs.get(0).getFwId());

        //变更操作通知
        cfwClusterService.changeWallOperation(cfwAclDOs.get(0).getFwId(), auth.getRequest_id(), WallChangeActionEnum.ACL.getCode(), WallChangeTypeEnum.DELETE.getCode(), aclIds);

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse modifyCfwAcl(ModifyCfwAclParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwAclDO cfwAclDOOld = cfwAclService.getByAclId(param.getAclId(),auth.getAccount_id());
        if (Objects.isNull(cfwAclDOOld)) {
            throw new CfwException("Acl规则不存在");
        }

        //处理集合数据
        String srcIps = null;
        if(CollectionUtils.isNotEmpty(param.getSrcIps())){
            srcIps = param.getSrcIps().stream().filter(Objects::nonNull).map(s -> s.replaceAll("[\\s\\n]", "")).filter(s -> !s.isEmpty()).distinct().collect(Collectors.joining(CommonConstant.COMMA));
        }
        String destIps = null;
        if(CollectionUtils.isNotEmpty(param.getDestIps())){
            destIps = param.getDestIps().stream().filter(Objects::nonNull).map(s -> s.replaceAll("[\\s\\n]", "")).filter(s -> !s.isEmpty()).distinct().collect(Collectors.joining(CommonConstant.COMMA));
        }
        String srcAddrbooks = null;
        if(CollectionUtils.isNotEmpty(param.getSrcAddrbooks())){
            srcAddrbooks = param.getSrcAddrbooks().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String destAddrbooks = null;
        if(CollectionUtils.isNotEmpty(param.getDestAddrbooks())){
            destAddrbooks = param.getDestAddrbooks().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String srcZones = CollectionUtils.isNotEmpty(param.getSrcZones()) ? JSONUtil.toJsonStr(param.getSrcZones()) : null;
        String destZones = CollectionUtils.isNotEmpty(param.getDestZones()) ? JSONUtil.toJsonStr(param.getDestZones()) : null;

        String serviceInfo = null;
        if(CollectionUtils.isNotEmpty(param.getServiceInfos())){
            serviceInfo = param.getServiceInfos().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String serviceGroups = null;
        if(CollectionUtils.isNotEmpty(param.getServiceGroups())){
            serviceGroups = param.getServiceGroups().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }
        String appValues = null;
        if(CollectionUtils.isNotEmpty(param.getAppValues())){
            appValues = param.getAppValues().stream().distinct().filter(s -> s != null && !s.isEmpty()).collect(Collectors.joining(CommonConstant.COMMA));
        }

        CfwAclDO aclDO = AclConvert.INSTANCE.convert2CfwAclDOForUpdate(param, auth.getAccount_id(), srcIps, destIps, srcAddrbooks, destAddrbooks,
                srcZones, serviceInfo, serviceGroups, cfwAclDOOld.getFwId(), cfwAclDOOld.getId(), cfwAclDOOld.getCreateTime(), cfwAclDOOld.getHitCount(), cfwAclDOOld.getPriority(), appValues, destZones);
        aclDO.setRuleId(cfwAclDOOld.getRuleId());
        //校验参数
        boolean duplicateName = cfwAclService.checkAclNameDuplicate(aclDO.getFwId(), param.getAclId(), aclDO.getAclName());
        if (duplicateName) {
            throw new CfwException("Acl名称重复");
        }
        checkParam(aclDO);

        //删除旧的acl关联地址簿和服务组关联关系
        if (AddrTypeEnum.ADDRBOOK.getType().equals(cfwAclDOOld.getSrcType()) || AddrTypeEnum.ADDRBOOK.getType().equals(cfwAclDOOld.getDestType()) || ServiceTypeEnum.SERVICEGROUP.getType().equals(cfwAclDOOld.getServiceType())) {
            cfwAclRelateService.deleteAclrelate(Collections.singletonList(cfwAclDOOld.getAclId()));
        }

        //添加新的地址簿和服务组引用关系
        Map<String, CfwAclRelateDO> cfwAclRelateDOMap = getCfwAclRelateDOMap(param.getSrcAddrbooks(), param.getDestAddrbooks(), param.getServiceGroups(), aclDO);
        if (CollectionUtil.isNotEmpty(cfwAclRelateDOMap)) {
            cfwAclRelateService.saveBatch(cfwAclRelateDOMap.values());
        }

        //保存acl
        cfwAclService.updateById(aclDO);

        //发送etcd
        AclEtcd aclEtcd = aclConvert2AclEtcd(aclDO, auth.getRequest_id(), "");
        aclEtcd.setPriority(null);
        String key = String.format(EtcdConstants.ACL, aclDO.getFwId(), aclDO.getAclId());
        log.info("修改Acl规则 key:{},Etcd:{}", key, JSONUtil.toJsonStr(aclEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(aclEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(aclDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ACL.getCode(), WallChangeTypeEnum.UPDATE.getCode(), Collections.singletonList(param.getAclId()));
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    private void checkParam(CfwAclDO aclDO) throws Exception {
        switch (AddrTypeEnum.getAddrType(aclDO.getSrcType())) {
            case IP:
                if (StringUtils.isBlank(aclDO.getSrcIp())) {
                    throw new CfwException("源地址类型为ip，请填写源地址");
                }
                break;
            case ADDRBOOK:
                if (StringUtils.isBlank(aclDO.getSrcAddrbook())) {
                    throw new CfwException("源地址类型为地址簿，请填写源地址簿");
                }
                break;
            case ZONE:
                if (StringUtils.isBlank(aclDO.getSrcZone())) {
                    throw new CfwException("源地址类型为地域，请填写源地域");
                }
                break;
        }

        //目的地址簿
        switch (AddrTypeEnum.getAddrType(aclDO.getDestType())) {
            case IP:
                if (StringUtils.isBlank(aclDO.getDestIp())) {
                    throw new CfwException("目的地址类型为ip，请填写目的地址");
                }
                break;
            case ADDRBOOK:
                if (StringUtils.isBlank(aclDO.getDestAddrbook())) {
                    throw new CfwException("目的地址类型为地址簿，请填写目的地址簿");
                }
                break;
            case ZONE:
                if (StringUtils.isBlank(aclDO.getDestZone())) {
                    throw new CfwException("目的地址类型为地域，请填写目的地域");
                }
                break;
        }

        //服务组信息
        switch (ServiceTypeEnum.getServiceType(aclDO.getServiceType())) {
            case SERVICE:
                if (StringUtils.isBlank(aclDO.getServiceInfo())) {
                    throw new CfwException("服务类型为服务，请填写服务信息");
                }
                break;
            case SERVICEGROUP:
                if (StringUtils.isBlank(aclDO.getServiceGroup())) {
                    throw new CfwException("服务类型为服务组，请填写服务组");
                }
                break;
        }
    }

    private String getEtcdPriorityPosition(String fwId, String priorityPosition) throws CfwException {
        String num = priorityPosition.replace(PriorityEnum.BEFORE.getType(), "").replace(PriorityEnum.AFTER.getType(), "");
        if (StringUtils.isNotBlank(num) && NumberUtils.isNumber(num)) {
            CfwAclDO cfwAclDO = cfwAclService.getCfwAclByFwIdAndPriority(fwId, Integer.valueOf(num));
            if (Objects.isNull(cfwAclDO)) {
                throw new CfwException("优先级不存在" + fwId + ":" + num);
            }
            priorityPosition = priorityPosition.replace(num, "") + CommonConstant.COMMA + cfwAclDO.getAclId();
        }
        return priorityPosition;
    }

    private AclEtcd aclConvert2AclEtcd(CfwAclDO aclDO, String requestId, String priorityPosition) {
        AclEtcd aclEtcd = AclConvert.INSTANCE.convert2AclEtcd(aclDO, requestId, priorityPosition);
        //源地址簿
        switch (AddrTypeEnum.getAddrType(aclDO.getSrcType())) {
            case IP:
                aclEtcd.setSrc_value(aclDO.getSrcIp());
                break;
            case ADDRBOOK:
                if (StringUtils.isNotBlank(aclDO.getSrcAddrbook())) {
                    aclEtcd.setSrc_value(aclDO.getSrcAddrbook());
                }
                break;
            case ZONE:
                if (StringUtils.isNotBlank(aclDO.getSrcZone())) {
                    //组装山石地域字段值
                    aclEtcd.setSrc_value(getShAreaInfos(aclDO.getSrcZone()));
                    aclEtcd.setSrc_type("region");
                }
                break;
            default:
                aclEtcd.setSrc_value("");
        }

        //目的地址簿
        switch (AddrTypeEnum.getAddrType(aclDO.getDestType())) {
            case IP:
                aclEtcd.setDest_value(aclDO.getDestIp());
                break;
            case ADDRBOOK:
                if (StringUtils.isNotBlank(aclDO.getDestAddrbook())) {
                    aclEtcd.setDest_value(aclDO.getDestAddrbook());
                }
                break;
            case ZONE:
                if (StringUtils.isNotBlank(aclDO.getDestZone())) {
                    aclEtcd.setDest_value(getShAreaInfos(aclDO.getDestZone()));
                    aclEtcd.setDest_type("region");
                }
                break;
            default:
                aclEtcd.setDest_value("");
        }

        //服务组信息
        switch (ServiceTypeEnum.getServiceType(aclDO.getServiceType())) {
            case SERVICE:
                aclEtcd.setService_value(aclDO.getServiceInfo());
                break;
            case SERVICEGROUP:
                if (StringUtils.isNotBlank(aclDO.getServiceGroup())) {
                    aclEtcd.setService_value(aclDO.getServiceGroup());
                }
                break;
            default:
                aclEtcd.setService_value("");
        }

        //appvalue
        if(StringUtils.isNotBlank(aclDO.getAppValue())){
            aclEtcd.setApp_value(Arrays.asList(aclDO.getAppValue().split(CommonConstant.COMMA)));
        }else{
            aclEtcd.setApp_value(Collections.emptyList());
        }
        return aclEtcd;
    }

    /**
     * 组装山石需要的地址类型
     * @param zoneStr
     * @return
     */
    private List<ShAreaInfo> getShAreaInfos(String zoneStr) {
        Gson gson = new Gson();
        Type type = new TypeToken<List<AreaInfo>>() {
        }.getType();
        List<AreaInfo> destZones = gson.fromJson(zoneStr, type);
        List<CfwAreaDO> areaInfoList = cfwAreaService.list();
        Map<String, CfwAreaDO> areaCodeMap = areaInfoList.stream().collect(Collectors.toMap(CfwAreaDO::getAreaCode, p -> p, (k1, k2) -> k1));
        List<ShAreaInfo> shAreaInfoList = new ArrayList<>();
        for (AreaInfo areaInfo : destZones) {
            CfwAreaDO cfwAreaDO = areaCodeMap.get(areaInfo.getAreaCode());
            if (Objects.isNull(cfwAreaDO)) {
                continue;
            }
            ShAreaInfo shAreaInfo = new ShAreaInfo();
            if ("CN".equals(cfwAreaDO.getParentCode())) {
                shAreaInfo.setCountry(cfwAreaDO.getParentCode());
                shAreaInfo.setProvince(cfwAreaDO.getAliasCode());
            } else {
                shAreaInfo.setCountry(cfwAreaDO.getAliasCode());
            }
            shAreaInfoList.add(shAreaInfo);
        }
        return shAreaInfoList;
    }


    private static Map<String, CfwAclRelateDO> getCfwAclRelateDOMap(List<String> srcAddrbooks, List<String> destAddrbooks, List<String> serviceGroups, CfwAclDO aclDO) {
        Map<String, CfwAclRelateDO> cfwAclRelateDOMap = new HashMap<>();
        Date nowTime = new Date();
        if (AclRelateTypeEnum.ADDRBOOK.getType().equals(aclDO.getSrcType())) {
            srcAddrbooks.forEach(e -> {
                CfwAclRelateDO cfwAclRelateDO = AclConvert.INSTANCE.convert2CfwAclRelateDO(aclDO.getAclId(), e, AclRelateTypeEnum.ADDRBOOK.getType(), nowTime);
                cfwAclRelateDOMap.put(AclRelateTypeEnum.ADDRBOOK.getType() + e, cfwAclRelateDO);
            });
        }
        if (AclRelateTypeEnum.ADDRBOOK.getType().equals(aclDO.getDestType())) {
            destAddrbooks.forEach(e -> {
                CfwAclRelateDO cfwAclRelateDO = AclConvert.INSTANCE.convert2CfwAclRelateDO(aclDO.getAclId(), e, AclRelateTypeEnum.ADDRBOOK.getType(), nowTime);
                cfwAclRelateDOMap.put(AclRelateTypeEnum.ADDRBOOK.getType() + e, cfwAclRelateDO);
            });
        }
        if (AclRelateTypeEnum.SERVICEGROUP.getType().equals(aclDO.getServiceType())) {
            serviceGroups.forEach(e -> {
                CfwAclRelateDO cfwAclRelateDO = AclConvert.INSTANCE.convert2CfwAclRelateDO(aclDO.getAclId(), e, AclRelateTypeEnum.SERVICEGROUP.getType(), nowTime);
                cfwAclRelateDOMap.put(AclRelateTypeEnum.SERVICEGROUP.getType() + e, cfwAclRelateDO);
            });
        }
        return cfwAclRelateDOMap;
    }

    /**
     * 获取最底层code
     *
     * @param srcZones
     * @param srcZoneLeafs
     */
    private void getAreaCodeIds(List<AreaInfo> srcZones, List<AreaInfo> srcZoneLeafs) {
        for (AreaInfo srcZone : srcZones) {
            if (CollectionUtil.isEmpty(srcZone.getSubAreas())) {
                srcZoneLeafs.add(srcZone);
                return;
            } else {
                getAreaCodeIds(srcZone.getSubAreas(), srcZoneLeafs);
            }
        }
    }

    /**
     * 获取地域信息
     *
     * @return
     */
    public DescribeZoneResponse describeZone() {
        //todo:加到redis缓存
        List<CfwAreaDO> allArea = cfwAreaService.list();
        Map<String, List<CfwAreaDO>> areaMap = new HashMap<>();
        for (CfwAreaDO areaDO : allArea) {
            areaMap.putIfAbsent(areaDO.getParentCode(), new ArrayList<>());
            areaMap.get(areaDO.getParentCode()).add(areaDO);
        }
        List<AreaInfo> areaInfos = getAreaList(areaMap, "0");
        return new DescribeZoneResponse().setZones(areaInfos);
    }

    private List<AreaInfo> getAreaList(Map<String, List<CfwAreaDO>> areaMap, String parentCode) {
        List<CfwAreaDO> areaDOList = areaMap.get(parentCode);
        List<AreaInfo> areaInfos = new ArrayList<>();
        for (CfwAreaDO areaDO : areaDOList) {
            AreaInfo areaInfo = new AreaInfo();
            areaInfo.setAreaCode(areaDO.getAreaCode()).setAreaName(areaDO.getAreaName());
            if (CollectionUtil.isNotEmpty(areaMap.get(areaDO.getAreaCode()))) {
                areaInfo.setSubAreas(getAreaList(areaMap, areaDO.getAreaCode()));
            }
            areaInfos.add(areaInfo);
        }
        return areaInfos;
    }

    public OperateResponse resetCfwAclHitCount(AclIdParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        cfwAclService.resetHitCount(param.getAclIds());
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }


    public DescribeCfwAclResponse describeCfwAcl(DescribeCfwAclParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        int offset = 0;
        if (StringUtils.isNotBlank(param.getNextToken()) && NumberUtils.isNumber(param.getNextToken())) {
            offset = Integer.getInteger(param.getNextToken()) - 1;
        }
        List<CfwAclDO> cfwAclDOList = cfwAclService.queryByParam(param, offset, auth.getAccount_id());
        Integer totalCount = cfwAclService.countCfwAclDOByParam(param);

        List<String> addrbookList = new ArrayList<>();
        List<String> serviceInfoList = new ArrayList<>();
        Map<String, String> addrbookMap = new HashMap<>();
        Map<String, String> serviceInfoMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(cfwAclDOList)) {
            for (CfwAclDO cfwAclDO : cfwAclDOList) {
                if (StringUtils.isNotBlank(cfwAclDO.getSrcAddrbook())) {
                    addrbookList.addAll(Arrays.asList(cfwAclDO.getSrcAddrbook().split(CommonConstant.COMMA)));
                }
                if (StringUtils.isNotBlank(cfwAclDO.getDestAddrbook())) {
                    addrbookList.addAll(Arrays.asList(cfwAclDO.getDestAddrbook().split(CommonConstant.COMMA)));
                }
                if (StringUtils.isNotBlank(cfwAclDO.getServiceGroup())) {
                    serviceInfoList.addAll(Arrays.asList(cfwAclDO.getServiceGroup().split(CommonConstant.COMMA)));
                }
            }
            if (CollectionUtil.isNotEmpty(addrbookList)) {
                List<CfwAddrbookDO> addrbookDOlist = addrbookService.getByAddrbookIdList(addrbookList);
                if (CollectionUtil.isNotEmpty(addrbookDOlist)) {
                    addrbookMap.putAll(addrbookDOlist.stream().collect(Collectors.toMap(CfwAddrbookDO::getAddrbookId, CfwAddrbookDO::getAddrbookName, (k1, k2) -> k1)));
                }

            }
            if (CollectionUtil.isNotEmpty(serviceInfoList)) {
                List<CfwServicegroupDO> servicegroupDOList = servicegroupService.getByServiceGroupIdList(serviceInfoList);
                if (CollectionUtil.isNotEmpty(servicegroupDOList)) {
                    serviceInfoMap.putAll(servicegroupDOList.stream().collect(Collectors.toMap(CfwServicegroupDO::getServiceGroupId, CfwServicegroupDO::getServiceGroupName, (k1, k2) -> k1)));
                }
            }
        }

        List<CfwAcl> cfwAclList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(cfwAclDOList)) {
            String keyRedis = String.format(RedisConstants.ACL_HIT_COUNT, param.getCfwInstanceId());
            Map<String, String> hitCountMap = jedisTemplate.getMap(keyRedis);
            for (CfwAclDO cfwAclDO : cfwAclDOList) {
                CfwAcl cfwAcl = getCfwAcl(cfwAclDO, addrbookMap, serviceInfoMap);
                //redis缓存击中数
                if (CollectionUtil.isNotEmpty(hitCountMap) && StringUtils.isNotBlank(hitCountMap.get(cfwAclDO.getAclId()))) {
                    long hitCount = cfwAclDO.getHitCount() == null ? 0L : cfwAclDO.getHitCount();
                    cfwAcl.setHitCount(Long.parseLong(hitCountMap.get(cfwAclDO.getAclId())) + hitCount);
                }
                cfwAclList.add(cfwAcl);
            }
        }
        String nextToken = (offset + cfwAclDOList.size()) < totalCount ? String.valueOf(offset + cfwAclDOList.size() + 1) : "";
        return new DescribeCfwAclResponse().setCfwAcls(cfwAclList).setTotalCount(totalCount).setNextToken(nextToken).setRequestId(auth.getRequest_id());
    }

    private CfwAcl getCfwAcl(CfwAclDO cfwAclDO, Map<String, String> addrbookMap, Map<String, String> serviceInfoMap) {
        CfwAcl cfwAcl = AclConvert.INSTANCE.convert2CfwAcl(cfwAclDO);
        if (StringUtils.isNotBlank(cfwAclDO.getDestAddrbook())) {
            String[] addrbookIds = cfwAclDO.getDestAddrbook().split(CommonConstant.COMMA);
            List<KeyInfo> addrbookKeyInfos = new ArrayList<>();
            for (String addrbookId : addrbookIds) {
                KeyInfo keyInfo = new KeyInfo();
                keyInfo.setId(addrbookId);
                if (StringUtils.isNotBlank(addrbookMap.get(addrbookId))) {
                    keyInfo.setName(addrbookMap.get(addrbookId));
                } else {
                    keyInfo.setName("");
                }
                addrbookKeyInfos.add(keyInfo);
            }
            cfwAcl.setDestAddrbooks(addrbookKeyInfos);
        }
        if (StringUtils.isNotBlank(cfwAclDO.getSrcAddrbook())) {
            String[] addrbookIds = cfwAclDO.getSrcAddrbook().split(CommonConstant.COMMA);
            List<KeyInfo> addrbookKeyInfos = new ArrayList<>();
            for (String addrbookId : addrbookIds) {
                KeyInfo keyInfo = new KeyInfo();
                keyInfo.setId(addrbookId);
                if (StringUtils.isNotBlank(addrbookMap.get(addrbookId))) {
                    keyInfo.setName(addrbookMap.get(addrbookId));
                } else {
                    keyInfo.setName("");
                }
                addrbookKeyInfos.add(keyInfo);
            }
            cfwAcl.setSrcAddrbooks(addrbookKeyInfos);
        }
        if (StringUtils.isNotBlank(cfwAclDO.getServiceGroup())) {
            String[] serviceGroupIds = cfwAclDO.getServiceGroup().split(CommonConstant.COMMA);
            List<KeyInfo> serviceGroupKeyInfos = new ArrayList<>();
            for (String serviceGroupId : serviceGroupIds) {
                KeyInfo keyInfo = new KeyInfo();
                keyInfo.setId(serviceGroupId);
                if (StringUtils.isNotBlank(serviceInfoMap.get(serviceGroupId))) {
                    keyInfo.setName(serviceInfoMap.get(serviceGroupId));
                } else {
                    keyInfo.setName("");
                }
                serviceGroupKeyInfos.add(keyInfo);
            }
            cfwAcl.setServiceGroups(serviceGroupKeyInfos);
        }
        if (StringUtils.isNotBlank(cfwAclDO.getServiceInfo())) {
            cfwAcl.setServiceInfos(Arrays.asList(cfwAclDO.getServiceInfo().split(CommonConstant.COMMA)));
        }
        if (StringUtils.isNotBlank(cfwAclDO.getSrcIp())) {
            cfwAcl.setSrcIps(Arrays.asList(cfwAclDO.getSrcIp().split(CommonConstant.COMMA)));
        }
        if (StringUtils.isNotBlank(cfwAclDO.getDestIp())) {
            cfwAcl.setDestIps(Arrays.asList(cfwAclDO.getDestIp().split(CommonConstant.COMMA)));
        }
        if (StringUtils.isNotBlank(cfwAclDO.getAppValue())) {
            cfwAcl.setAppValues(Arrays.asList(cfwAclDO.getAppValue().split(CommonConstant.COMMA)));
        }
        if (StringUtils.isNotBlank(cfwAclDO.getSrcZone())) {
            Gson gson = new Gson();
            Type type = new TypeToken<List<AreaInfo>>() {
            }.getType();
            List<AreaInfo> srcZones = gson.fromJson(cfwAclDO.getSrcZone(), type);
            cfwAcl.setSrcZones(srcZones);
        }
        if (StringUtils.isNotBlank(cfwAclDO.getDestZone())) {
            Gson gson = new Gson();
            Type type = new TypeToken<List<AreaInfo>>() {
            }.getType();
            List<AreaInfo> destZones = gson.fromJson(cfwAclDO.getDestZone(), type);
            cfwAcl.setDestZones(destZones);
        }
        return cfwAcl;
    }

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse alterAclPriority(AlterAclPriorityParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwAclDO aclDO = cfwAclService.getByAclId(param.getAclId(),auth.getAccount_id());
        if (Objects.isNull(aclDO)) {
            throw new CfwException("ACL不存在");
        }

        //查询排序num转name
        String priorityPosition = param.getPriorityPosition();
        priorityPosition = getEtcdPriorityPosition(aclDO.getFwId(), priorityPosition);

        int priority = getUpdatePriority(param.getPriorityPosition(), aclDO);
        //不需要调整优先级
        if (priority == 0 || aclDO.getPriority() == priority) {
            return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
        }
        //更改本规则优先级
        cfwAclService.updatePriority(param.getAclId(), priority);

        //发送etcd
        PriorityEtcd aclPriorityEtcd = AclConvert.INSTANCE.convert2PriorityEtcd(String.valueOf(aclDO.getRuleId()), aclDO.getAclId(), auth.getRequest_id(), priorityPosition);
        String key = String.format(EtcdConstants.ACL_PRIORITY, aclDO.getFwId(), aclDO.getAclId());
        log.info("修改Acl规则排序 key:{}, Etcd:{}", key, JSONUtil.toJsonStr(aclPriorityEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(aclPriorityEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(aclDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ACL.getCode(), WallChangeTypeEnum.UPDATE_PRIORITY.getCode(), Collections.singletonList(param.getAclId()));

        return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
    }

    private int getUpdatePriority(String priorityPosition, CfwAclDO cfwAclDO) {
        int priority = 0;
        String paramNum = priorityPosition.replace(PriorityEnum.BEFORE.getType(), "").replace(PriorityEnum.AFTER.getType(), "");
        //数字相同，不变动优先级
        if (StringUtils.isNotBlank(paramNum) && paramNum.equals(String.valueOf(cfwAclDO.getPriority()))) {
            return priority;
        }
        if (priorityPosition.startsWith(PriorityEnum.BEFORE.getType())) {
            if (priorityPosition.equals(PriorityEnum.BEFORE.getType())) {
                if (cfwAclDO.getPriority() != 1) {
                    priority = 1;
                    cfwAclService.priorityMoveBack(cfwAclDO.getFwId(), 1, cfwAclDO.getPriority() - 1);
                }
            } else {
                priority = Integer.parseInt(priorityPosition.replace(PriorityEnum.BEFORE.getType(), ""));
                if (priority != cfwAclDO.getPriority()) {
                    //向前移
                    if (cfwAclDO.getPriority() > priority) {
                        cfwAclService.priorityMoveBack(cfwAclDO.getFwId(), priority, cfwAclDO.getPriority() - 1);
                    } else {
                        cfwAclService.priorityMoveForward(cfwAclDO.getFwId(), cfwAclDO.getPriority() + 1, priority);
                    }
                }
            }
        } else {
            if (priorityPosition.equals(PriorityEnum.AFTER.getType())) {
                priority = cfwAclService.getMaxPriority(cfwAclDO.getFwId());
                cfwAclService.priorityMoveForward(cfwAclDO.getFwId(), cfwAclDO.getPriority() + 1, priority);
            } else {
                priority = Integer.parseInt(priorityPosition.replace(PriorityEnum.AFTER.getType(), "")) + 1;
                if (priority != cfwAclDO.getPriority()) {
                    //向前移
                    if (cfwAclDO.getPriority() > priority) {
                        cfwAclService.priorityMoveBack(cfwAclDO.getFwId(), priority, cfwAclDO.getPriority() - 1);
                    } else {
                        cfwAclService.priorityMoveForward(cfwAclDO.getFwId(), cfwAclDO.getPriority() + 1, priority);
                    }
                }
            }
        }
        return priority;
    }

    public OperateResponse alterCfwAclStatus(AlterCfwAclStatusParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        List<CfwAclDO> aclDOs = cfwAclService.getByAclIds(param.getAclIds(),auth.getAccount_id());
        if (CollectionUtil.isEmpty(aclDOs)) {
            throw new CfwException("该Acl规则不存在");
        }
        String priorityPosition = PriorityEnum.BEFORE.getType();
        cfwAclService.alterCfwAclStatus(param.getAclIds(), param.getStatus());
        for (CfwAclDO aclDO : aclDOs) {
            if (aclDO.getPriority() != 1) {
                priorityPosition = PriorityEnum.AFTER.getType() + (aclDO.getPriority() - 1);
            }
            //查询排序num转name
            priorityPosition = getEtcdPriorityPosition(aclDO.getFwId(), priorityPosition);

            //发送etcd
            aclDO.setStatus(param.getStatus());
            AclEtcd aclEtcd = aclConvert2AclEtcd(aclDO, auth.getRequest_id(), priorityPosition);
            String key = String.format(EtcdConstants.ACL, aclDO.getFwId(), aclDO.getAclId());
            log.info("修改Acl规则状态 key:{}, Etcd:{}", key, JSONUtil.toJsonStr(aclEtcd));
            etcdService.putValue(key, JSONUtil.toJsonStr(aclEtcd));
        }

        //变更操作通知
        cfwClusterService.changeWallOperation(aclDOs.get(0).getFwId(), auth.getRequest_id(), WallChangeActionEnum.ACL.getCode(), WallChangeTypeEnum.UPDATE.getCode(), param.getAclIds());

        return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
    }
}
