package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.AclConvert;
import com.ksyun.cfwapi.dao.entity.CfwAddrbookDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.entity.CitationCountDO;
import com.ksyun.cfwapi.dao.service.CfwAclRelateService;
import com.ksyun.cfwapi.dao.service.CfwAddrbookService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.addrbook.*;
import com.ksyun.cfwapi.domain.etcd.AddrbookEtcd;
import com.ksyun.cfwapi.domain.etcd.WallChangeOperationEtcd;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.enums.WallChangeTypeEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.util.DateUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AddrbookService {
    @Autowired
    private CfwAddrbookService cfwAddrbookService;

    @Autowired
    private CfwClusterService cfwClusterService;
    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwAclRelateService cfwAclRelateService;
    @Transactional(rollbackFor = Exception.class)
    public CreateAddrbookResponse createCfwAddrbook(CreateCfwAddrbookParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        checkCfwAddrbook(param.getCfwInstanceId(),param.getIpAddress(),"");

        CfwAddrbookDO addrbookDO = AclConvert.INSTANCE.convert2CfwAddrbookDO(param, String.join(CommonConstant.COMMA, param.getIpAddress()), auth.getAccount_id());
        cfwAddrbookService.save(addrbookDO);

        //发送etcd
        AddrbookEtcd addrbookEtcd = AclConvert.INSTANCE.convert2AddrbookEtcd(addrbookDO, auth.getRequest_id());
        String key = String.format(EtcdConstants.ADDR_BOOK, addrbookDO.getFwId(), addrbookDO.getAddrbookId());
        log.info("创建地址簿 key:{},Etcd:{}", key,JSONUtil.toJsonStr(addrbookEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(addrbookEtcd));
        CfwAddrbook cfwAddrbook = new CfwAddrbook(addrbookDO);
        //变更操作通知
        cfwClusterService.changeWallOperation(addrbookDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ADDRBOOK.getCode(), WallChangeTypeEnum.CREATE.getCode(),Collections.singletonList(addrbookDO.getAddrbookId()));

        return new CreateAddrbookResponse().setCfwAddrbook(cfwAddrbook).setRequestId(auth.getRequest_id());
    }

    private void checkCfwAddrbook(String cfwInstanceId, List<String> ipAddress, String addrbookId) throws CfwException {

        List<CfwAddrbookDO>  addrbookList = cfwAddrbookService.listCfwAddrbookByFwId(cfwInstanceId);
        if(StringUtils.isBlank(addrbookId)){
            //校验是否超过3800个地址簿
            Integer totalCount = cfwAddrbookService.countCfwAddrbookByFwId(cfwInstanceId);
            if (totalCount > 3800) {
                throw new CfwException("地址簿数量不能超过3800个");
            }
        }
        int count = ipAddress.size();
        for (CfwAddrbookDO cfwAddrbookDO : addrbookList) {
            if(cfwAddrbookDO.getAddrbookId().equals(addrbookId)){
                continue;
            }
            count = cfwAddrbookDO.getIpAddress().split(CommonConstant.COMMA).length + count;
        }
        if(count > 30000){
            throw new CfwException("每个防火墙实例下最多添加30000个IP地址");
        }
    }


    public OperateResponse modifyCfwAddrbook(ModifyCfwAddrbookParam param) throws CfwException {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwAddrbookDO addrbookDO = cfwAddrbookService.getByAddrbookId(param.getAddrbookId(),auth.getAccount_id());
        //校验地址簿
        checkCfwAddrbook(addrbookDO.getFwId(),param.getIpAddress(),param.getAddrbookId());
        cfwAddrbookService.updateAddrbook(param);
        //发送etcd
        AddrbookEtcd addrbookEtcd = AclConvert.INSTANCE.convert2AddrbookEtcd(addrbookDO, auth.getRequest_id());
        String key = String.format(EtcdConstants.ADDR_BOOK, addrbookDO.getFwId(), addrbookDO.getAddrbookId());
        log.info("修改地址簿 key:{},Etcd:{}", key,JSONUtil.toJsonStr(addrbookEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(addrbookEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(addrbookDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ADDRBOOK.getCode(), WallChangeTypeEnum.UPDATE.getCode(),Collections.singletonList(param.getAddrbookId()));

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    public OperateResponse deleteCfwAddrbook(DeleteCfwAddrbookParam param) throws CfwException {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwAddrbookDO addrbookDO = cfwAddrbookService.getByAddrbookId(param.getAddrbookId(),auth.getAccount_id());
        if (Objects.isNull(addrbookDO)) {
            throw new CfwException("地址簿不存在");
        }
        boolean exist = cfwAclRelateService.existByRelateId(param.getAddrbookId());
        if (exist) {
            throw new CfwException("地址簿被引用，无法删除");
        }
        cfwAddrbookService.deleteAddrbook(param.getAddrbookId());


        //变更操作通知
        cfwClusterService.changeWallOperation(addrbookDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.ADDRBOOK.getCode(), WallChangeTypeEnum.DELETE.getCode(),Collections.singletonList(param.getAddrbookId()));

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }


    public DescribeAddrbookResponse describeCfwAddrbook(DescribeAddrbookParam param) {
        int offset = 0;
        if (StringUtils.isNotBlank(param.getNextToken()) && NumberUtils.isNumber(param.getNextToken())) {
            offset = Integer.getInteger(param.getNextToken()) - 1;
        }
        List<CfwAddrbookDO> addrbookDOList = cfwAddrbookService.queryCfwAddrbook(param, offset);
        //查询结果为空
        if (CollectionUtil.isEmpty(addrbookDOList)) {
            return new DescribeAddrbookResponse().setTotalCount(0);
        }
        //查询引用数
        List<String> addrbookIds = addrbookDOList.stream().map(CfwAddrbookDO::getAddrbookId).collect(Collectors.toList());
        List<CitationCountDO> citationCountDOList = cfwAclRelateService.getCitationCountByRelateIds(addrbookIds);
        Map<String, Integer> citationCountMap = new HashMap<>(citationCountDOList.size());
        if (CollectionUtil.isNotEmpty(citationCountDOList)) {
            citationCountMap.putAll(citationCountDOList.stream().collect(Collectors.toMap(CitationCountDO::getRelateId, CitationCountDO::getCitationCount, (k1, k2) -> k1)));
        }

        Integer totalCount = cfwAddrbookService.countCfwAddrbook(param);
        List<CfwAddrbook> cfwAddrbooks = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(addrbookDOList)) {
            for (CfwAddrbookDO addrbookDO : addrbookDOList) {
                CfwAddrbook cfwAddrbook = new CfwAddrbook(addrbookDO);
                if (Objects.nonNull(citationCountMap.get(cfwAddrbook.getAddrbookId()))) {
                    cfwAddrbook.setCitationCount(citationCountMap.get(cfwAddrbook.getAddrbookId()));
                }
                cfwAddrbooks.add(cfwAddrbook);
            }
        }
        String nextToken = (offset + addrbookDOList.size()) < totalCount ? String.valueOf(offset + addrbookDOList.size() + 1) : "";
        return new DescribeAddrbookResponse().setCfwAddrbooks(cfwAddrbooks).setTotalCount(totalCount).setNextToken(nextToken);
    }

}
