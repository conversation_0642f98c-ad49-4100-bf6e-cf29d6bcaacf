package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.AvConvert;
import com.ksyun.cfwapi.dao.entity.CfwAvDO;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.service.CfwAvService;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.av.CfwAv;
import com.ksyun.cfwapi.domain.av.DescribeCfwAvParam;
import com.ksyun.cfwapi.domain.av.DescribeCfwAvResponse;
import com.ksyun.cfwapi.domain.av.ModifyCfwAvParam;
import com.ksyun.cfwapi.domain.etcd.AvEtcd;
import com.ksyun.cfwapi.enums.AvProtectEnum;
import com.ksyun.cfwapi.enums.AvProtocolEnum;
import com.ksyun.cfwapi.enums.StatusEnum;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.enums.WallChangeTypeEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AvService {
    @Autowired
    private CfwAvService cfwAvService;

    @Autowired
    private CfwClusterService cfwClusterService;

    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    public DescribeCfwAvResponse describeCfwAv(DescribeCfwAvParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        List<CfwAvDO> cfwAvDOList = cfwAvService.queryByFwId(param.getCfwInstanceId(),auth.getAccount_id());

        DescribeCfwAvResponse result = new DescribeCfwAvResponse().setRequestId(auth.getRequest_id());
        if (CollectionUtil.isEmpty(cfwAvDOList)) {
            return result;
        }
        List<CfwAv> cfwAvList = AvConvert.INSTANCE.convert2CfwAvList(cfwAvDOList);
        result.setCfwAvs(cfwAvList);
        log.info("调用成功{}", JSONUtil.toJsonStr(result));
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public OperateResponse modifyCfwAv(ModifyCfwAvParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();

        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getCfwInstanceId(),auth.getAccount_id());
        if(Objects.isNull(cfwInstanceDO)||InstanceTypeEnum.ADVANCED.getType().equals(cfwInstanceDO.getInstanceType())){
            throw new CfwException("云防火墙为高级版不能修改ips");
        }

        cfwAvService.modifyCfwAv(param,auth.getAccount_id());

        List<CfwAvDO> cfwAvDOList = cfwAvService.queryByFwId(param.getCfwInstanceId(),auth.getAccount_id());
        if (CollectionUtil.isEmpty(cfwAvDOList)) {
            throw new CfwException("防病毒数据不存在");
        }

        //发送Etcd
        sendAvEtcd(cfwAvDOList, auth, WallChangeTypeEnum.UPDATE.getCode());

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeModifyCfwAv(String fwId,String instanceType) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();

        List<CfwAvDO> cfwAvDOList = cfwAvService.queryByFwId(fwId,auth.getAccount_id());
        String avId = GUIDGeneratorUtil.generateGUID();
        String status = InstanceTypeEnum.ENTERPRISE.getType().equals(instanceType)?StatusEnum.START.getStatusStr():StatusEnum.STOP.getStatusStr();
        if(CollectionUtil.isEmpty(cfwAvDOList)){
            for (AvProtocolEnum avProtocolEnum : AvProtocolEnum.values()) {
                CfwAvDO cfwAvDO = AvConvert.INSTANCE.convert2CfwAvDO(avId, fwId, avProtocolEnum.getType(), AvProtectEnum.MONITOR.getType(), status, auth.getAccount_id());
                cfwAvDOList.add(cfwAvDO);
            }
            cfwAvService.saveBatch(cfwAvDOList);
        }else{
            for(CfwAvDO cfwAvDO:cfwAvDOList){
                cfwAvDO.setStatus(status);
            }
            cfwAvService.updateBatchById(cfwAvDOList);
        }
        //发送Etcd
        sendAvEtcd(cfwAvDOList, auth, WallChangeTypeEnum.UPDATE.getCode());
    }

    public void createCfwAv(String fwId,String instanceType) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        List<CfwAvDO> cfwAvDOList = new ArrayList<>();
        String avId = GUIDGeneratorUtil.generateGUID();
        String status = InstanceTypeEnum.ENTERPRISE.getType().equals(instanceType)?StatusEnum.START.getStatusStr():StatusEnum.STOP.getStatusStr();
        for (AvProtocolEnum avProtocolEnum : AvProtocolEnum.values()) {
            CfwAvDO cfwAvDO = AvConvert.INSTANCE.convert2CfwAvDO(avId, fwId, avProtocolEnum.getType(), AvProtectEnum.MONITOR.getType(), status, auth.getAccount_id());
            cfwAvDOList.add(cfwAvDO);
        }

        cfwAvService.saveBatch(cfwAvDOList);
        //发送Etcd
        sendAvEtcd(cfwAvDOList, auth, WallChangeTypeEnum.CREATE.getCode());
    }

    private void sendAvEtcd(List<CfwAvDO> cfwAvDOList, ProxyAuth auth, String actionType) {
        //etcd
        AvEtcd avEtcd = new AvEtcd();
        avEtcd.setId(cfwAvDOList.get(0).getAvId());
        avEtcd.setName("防病毒");
        HashMap<String, String> protocol = new HashMap<>();
        for (CfwAvDO cfwAvDO : cfwAvDOList) {
            if (StatusEnum.START.getStatusStr().equals(cfwAvDO.getStatus())) {
                protocol.put(cfwAvDO.getProtocol(), cfwAvDO.getProtectType());
            }
        }
        avEtcd.setProtocol(protocol);
        avEtcd.setStatus(StatusEnum.START.getStatusStr());
        if (CollectionUtil.isEmpty(protocol)) {
            avEtcd.setStatus(StatusEnum.STOP.getStatusStr());
        }

        //发送etcd
        String key = String.format(EtcdConstants.AV, cfwAvDOList.get(0).getFwId());
        log.info("Av防病毒,actionType:{},  Etcd:{}", actionType, JSONUtil.toJsonStr(avEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(avEtcd));
        cfwClusterService.changeWallOperation(cfwAvDOList.get(0).getFwId(), auth.getRequest_id(), WallChangeActionEnum.AV.getCode(), actionType, Collections.emptyList());
    }

}
