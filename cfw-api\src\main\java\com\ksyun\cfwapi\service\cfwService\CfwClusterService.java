package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ksyun.cfwapi.config.CfwApiCommonConfig;
import com.ksyun.cfwapi.convert.AclConvert;
import com.ksyun.cfwapi.convert.EtcdConvert;
import com.ksyun.cfwapi.dao.entity.CfwEtcdDO;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwRegionConfigDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.service.CfwEtcdService;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.dao.service.CfwRegionConfigService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.cluster.ClusterOperationParam;
import com.ksyun.cfwapi.domain.cluster.UpdateStartParam;
import com.ksyun.cfwapi.domain.etcd.FileEtcd;
import com.ksyun.cfwapi.domain.etcd.WallChangeOperationEtcd;
import com.ksyun.cfwapi.domain.etcd.WallclusterEtcd;
import com.ksyun.cfwapi.domain.fw.InstanceIdStartParam;
import com.ksyun.cfwapi.enums.ClusterFileActionEnum;
import com.ksyun.cfwapi.enums.ClusterFileTypeEnum;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.config.Ks3Config;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.enums.ExecutedStatusEnum;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.ksyun.comm.util.DateUtils;
import com.ksyun.common.proxy.ProxyAuth;
import com.ksyun.ks3.dto.HeadObjectResult;
import com.ksyun.ks3.http.HttpClientConfig;
import com.ksyun.ks3.service.Ks3;
import com.ksyun.ks3.service.Ks3Client;
import com.ksyun.ks3.service.Ks3ClientConfig;
import io.etcd.jetcd.KeyValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CfwClusterService implements Serializable {
    private static final long serialVersionUID = -1342247118461353769L;


    @Autowired
    private Ks3Config ks3Config;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwEtcdService cfwEtcdService;

    @Autowired
    private CfwRegionConfigService regionConfigService;

    @Autowired
    private CfwApiCommonConfig cfwApiCommonConfig;


    public OperateResponse uploadFileUrl(ClusterOperationParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(param.getRegion());
        if(Objects.isNull(regionConfigDO)){
            throw new CfwException("region配置查询错误");
        }
        //获取文件MD5
        Ks3 client = getKs3(regionConfigDO.getKs3Endpoint());
        String bucketName = regionConfigDO.getKs3Bucket();
        HeadObjectResult headObject = client.headObject(bucketName, param.getFileName());
        String md5 = null;
        if (Objects.nonNull(headObject.getObjectMetadata())) {
            String md5Base64 = headObject.getObjectMetadata().getContentMD5();
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] md5Bytes = decoder.decode(md5Base64);
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5Bytes) {
                hexString.append(String.format("%02x", b));
            }
            md5 = hexString.toString().toUpperCase();
        }
        if (Objects.isNull(md5)) {
            throw new CfwException("获取文件MD5失败");
        }

        log.info("正在执行的文件fileName：{}",param.getFileName());


        String instanceType = null;
        if (param.getFileName().contains(WallChangeActionEnum.AV.getCode()) || param.getFileName().contains(WallChangeActionEnum.IPS.getCode())) {
            instanceType = InstanceTypeEnum.ENTERPRISE.getType();
        }
        int i = 0;
        Date current = new Date();
        while (true) {
            List<CfwInstanceDO> fwDOs = cfwInstanceService.pageFwDOByFwId(i * Constants.DEFAULT_MAX_RESULTS, Constants.DEFAULT_MAX_RESULTS, param.getCfwInstanceIds(),instanceType);
            if (CollectionUtil.isEmpty(fwDOs)) {
                break;
            }

            for (CfwInstanceDO fwDO : fwDOs) {
                WallclusterEtcd wallclusterEtcd = EtcdConvert.INSTANCE.convert2WallclusterEtcd(auth.getRequest_id(), md5, param, ClusterFileTypeEnum.FILE_CHANGE.getType());
                String key = String.format(EtcdConstants.FIREWALL_CLUSTER, fwDO.getFwId());
                wallclusterEtcd.setTimestamp(com.ksyun.cfwcore.utils.DateUtils.formatDate(current, com.ksyun.cfwcore.utils.DateUtils.DATETIME_FORMAT));
                etcdService.putValue(key, JSONUtil.toJsonStr(wallclusterEtcd));
                log.info("文件变更,key:{}, Etcd:{}", key, JSONUtil.toJsonStr(wallclusterEtcd));

                //如果是镜像需要异步重启
                if (ClusterFileActionEnum.MIRROR.getType().equals(wallclusterEtcd.getAction())) {
                    int count = cfwRsService.countRsByFwId(fwDO.getFwId());
                    if (count == 0) {
                        continue;
                    }

                    //单次重启数量
                    int singleExecutionCount = count / 3;
                    //重启批次
                    int cyclesNum;
                    if (singleExecutionCount == 0) {
                        cyclesNum = count;
                        singleExecutionCount = 1;
                    } else {
                        cyclesNum = count % singleExecutionCount == 0 ? count / singleExecutionCount : count / singleExecutionCount + 1;
                    }
                    List<CfwEtcdDO> cfwEtcdDOList = new ArrayList<>();
                    for (int n = 0; n < cyclesNum; n++) {
                        int offsetRs = n * singleExecutionCount;
                        List<CfwRsDO> cfwRsDOList = cfwRsService.pageInstanceIdByFwId(fwDO.getFwId(), offsetRs, singleExecutionCount);
                        if (CollectionUtil.isEmpty(cfwRsDOList)) {
                            break;
                        }
                        String instanceIds = cfwRsDOList.stream().map(CfwRsDO::getFwInstanceId).collect(Collectors.joining(CommonConstant.COMMA));
                        wallclusterEtcd.setInstanceids(instanceIds);
                        wallclusterEtcd.setType(ClusterFileTypeEnum.RESTART_CLUSTER.getType());
                        wallclusterEtcd.setOperationId(GUIDGeneratorUtil.generateGUID());
                        CfwEtcdDO cfwEtcdDO = EtcdConvert.INSTANCE.convert2CfwEtcdDO(key, JSONUtil.toJsonStr(wallclusterEtcd), auth.getAccount_id(), ExecutedStatusEnum.UNEXECUTED.getCode());
                        cfwEtcdDOList.add(cfwEtcdDO);
                    }
                    if (CollectionUtil.isNotEmpty(cfwEtcdDOList)) {
                        cfwEtcdService.saveBatch(cfwEtcdDOList);
                    }
                }
            }
            i++;
        }

        //特征库变更
        if (param.getFileName().contains(WallChangeActionEnum.AV.getCode()) || param.getFileName().contains(WallChangeActionEnum.IPS.getCode())) {
            String key = null;
            if (param.getFileName().contains(WallChangeActionEnum.AV.getCode())) {
                key = String.format(EtcdConstants.FIREWALL_CLUSTER_SIGNATURE, WallChangeActionEnum.AV.getCode());
            }
            if (param.getFileName().contains(WallChangeActionEnum.IPS.getCode())) {
                key = String.format(EtcdConstants.FIREWALL_CLUSTER_SIGNATURE, WallChangeActionEnum.IPS.getCode());
            }
            FileEtcd fileEtcd = new FileEtcd().setFileName(param.getFileName()).setMd5(md5);
            etcdService.putValue(key, JSONUtil.toJsonStr(fileEtcd));
        }
        return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
    }

    /**
     * 获取ks3客户端
     *
     * @return
     */
    private Ks3 getKs3(String endpoint) {
        String accessKeyId = ks3Config.getKs3Ak();
        String accessKeySecret = ks3Config.getKs3Sk();
        Ks3ClientConfig config = new Ks3ClientConfig();
        config.setEndpoint(endpoint);
        config.setPathStyleAccess(false);
        HttpClientConfig hconfig = new HttpClientConfig();
        config.setHttpClientConfig(hconfig);
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        return client;
    }

    public void changeWallOperation(String fwId, String traceId, String action, String type, List<String> instanceIds) {
        WallChangeOperationEtcd wallChangeOperationEtcd = AclConvert.INSTANCE.convert2WallChangeEtcd(traceId, type, action, instanceIds, DateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        String actionKey = String.format(EtcdConstants.WALL_CHANGE_OPERATION, fwId);
        String actionvalue = JSONUtil.toJsonStr(wallChangeOperationEtcd);
        log.info("变更操作通知,key:{}, Etcd:{}", actionKey, actionvalue);
        etcdService.putValue(actionKey, actionvalue);
    }

    public void updateStartParam(UpdateStartParam param) {
        List<KeyValue> keyValueList = new ArrayList<>();
        List<CfwRsDO> fwDOs = cfwRsService.getCfwRsByFwIdRegion(param.getCfwInstanceIds(),param.getRegion());

        if (CollectionUtil.isEmpty(fwDOs)) {
            log.info("没有查询到要处理的防火墙");
            return ;
        }

        for (CfwRsDO rsDO : fwDOs) {
            String key = String.format(EtcdConstants.START_INSTANCE, rsDO.getFwInstanceId());
            List<KeyValue> entityList = etcdService.getValueByPrefix(key);
            if (CollectionUtil.isNotEmpty(entityList)) {
                keyValueList.addAll(entityList);
            }
        }

        if (CollectionUtil.isEmpty(keyValueList)) {
            log.info("没有查询到要处理的值");
        }

        CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(param.getRegion());
        for (KeyValue keyValue : keyValueList) {
            String value = String.valueOf(keyValue.getValue());
            log.info("变更前启动参数,key:{}, Etcd:{}", keyValue.getKey(), value);
            InstanceIdStartParam firewallEtcdStartData = JSON.parseObject(value, InstanceIdStartParam.class);

            InstanceIdStartParam.KafkaInfo kafkaInfo = firewallEtcdStartData.getKafka();
            kafkaInfo.setUrl(cfwApiCommonConfig.getKafkaUrl());
            kafkaInfo.setUsername(cfwApiCommonConfig.getKafkaUser());
            kafkaInfo.setPassword(cfwApiCommonConfig.getKafkPassword());
            kafkaInfo.setSaslEnable(cfwApiCommonConfig.getKafkSaslEnable());
            kafkaInfo.setCertPath(regionConfigDO.getKafkaCertPath());
            kafkaInfo.setMd5(regionConfigDO.getKafkaCertMd5());
            firewallEtcdStartData.setKafka(kafkaInfo);
            log.info("变更后启动参数,key:{}, Etcd:{}", keyValue.getKey(), JSONUtil.toJsonStr(firewallEtcdStartData));
            etcdService.putValue(String.valueOf(keyValue.getKey()), JSONUtil.toJsonStr(firewallEtcdStartData));
        }
    }
}
