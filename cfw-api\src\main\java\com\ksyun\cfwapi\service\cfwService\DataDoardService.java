package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.constants.LogConstants;
import com.ksyun.cfwapi.dao.entity.CfwAclDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.service.CfwAclService;
import com.ksyun.cfwapi.dao.service.CfwDictionaryService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwapi.domain.dataDoard.*;
import com.ksyun.cfwapi.es.log.CfwEsUtils;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DataDoardService {
    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwAclService cfwAclService;


    public DescribeLogResponse<CfwAclLog> describeAclLog(DescribeLogParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        DescribeLogResponse<CfwAclLog> response = new DescribeLogResponse<>();
        response.setRequestId(auth.getRequest_id()).setTotalCount(0L);
        SearchResponse searchResponse = this.queryLogEs(param, LogTopicEnum.CFW_BLOCK.getTopic());
        if (searchResponse == null) {
            return response;
        }
        long total = searchResponse.getHits().getTotalHits().value;
        response.setTotalCount(total);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        List<CfwAclLog> dataList = new ArrayList<>((int) total);
        List<String> aclIdList = new ArrayList<>((int) total);
        if (searchHits.length > 0) {
            for (SearchHit searchHit : searchHits) {
                try {
                    Map<String, Object> bodyMap = searchHit.getSourceAsMap();
                    if (CollectionUtil.isEmpty(bodyMap)) {
                        continue;
                    }
                    CfwAclLog cfwAclLog = new CfwAclLog();
                    cfwAclLog.setCfwInstanceId(param.getCfwInstanceId());
                    cfwAclLog.setAclId(bodyMap.get("rule-name").toString());
                    aclIdList.add(bodyMap.get("rule-name").toString());
                    cfwAclLog.setStartTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("start-time").toString())), DateUtils.DATETIME_FORMAT));
                    cfwAclLog.setAclName(bodyMap.get(LogConstants.ACL_NAME).toString());
                    cfwAclLog.setSrcIp(bodyMap.get("source-ip").toString());
                    cfwAclLog.setSrcPort(Integer.valueOf(bodyMap.get("source-port").toString()));
                    cfwAclLog.setDestIp(bodyMap.get("destination-ip").toString());
                    cfwAclLog.setDestPort(Integer.valueOf(bodyMap.get("destination-port").toString()));
                    cfwAclLog.setProtocol(bodyMap.get(LogConstants.KC_PROTOCOL).toString());
                    cfwAclLog.setApp(bodyMap.get(LogConstants.KC_APP).toString());
                    cfwAclLog.setPolicy(bodyMap.get(LogConstants.KC_ACTION).toString());
                    cfwAclLog.setDirection(bodyMap.get(LogConstants.DIRECTION).toString());
                    cfwAclLog.setCreateTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("time").toString())), DateUtils.DATETIME_FORMAT));
                    dataList.add(cfwAclLog);
                } catch (Exception e) {
                    log.error("Acl log parse error,param:{},errMsg:{}", JSONUtil.toJsonStr(searchHit), e.getMessage());
                }
            }
        }
        response.setDataList(dataList);
        if(param.getMaxResults() == dataList.size()){
            int offset = StringUtils.isBlank(param.getNextToken()) ? 0 : Integer.parseInt(param.getNextToken());
            response.setNextToken(String.valueOf(offset+ dataList.size()));
        }
        return response;
    }

    public DescribeLogResponse<CfwAttackLog> describeAttackLog(DescribeLogParam param)  {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        DescribeLogResponse<CfwAttackLog> response = new DescribeLogResponse<>();
        response.setRequestId(auth.getRequest_id()).setTotalCount(0L);
        SearchResponse searchResponse = this.queryLogEs(param, LogTopicEnum.CFW_RISK.getTopic());
        if (searchResponse == null) {
           return response;
        }

        long total = searchResponse.getHits().getTotalHits().value;
        response.setTotalCount(total);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        List<CfwAttackLog> dataList = new ArrayList<>();
        if (searchHits.length > 0) {
            for (SearchHit searchHit : searchHits) {
                try {
                    Map<String, Object> bodyMap = searchHit.getSourceAsMap();
                    if (CollectionUtil.isEmpty(bodyMap)) {
                        continue;
                    }
                    CfwAttackLog cfwAttackLog = new CfwAttackLog();
                    cfwAttackLog.setCfwInstanceId(param.getCfwInstanceId());
                    cfwAttackLog.setThreatType(bodyMap.get(LogConstants.KC_ATTACK_TYPE).toString());
                    cfwAttackLog.setThreatName(bodyMap.get("attack-name").toString());
                    cfwAttackLog.setSeverity(bodyMap.get(LogConstants.KC_SEVERITY).toString());
                    cfwAttackLog.setSrcIp(bodyMap.get("source-ip").toString());
                    cfwAttackLog.setSrcPort(Integer.valueOf(bodyMap.get("source-port").toString()));
                    cfwAttackLog.setDestIp(bodyMap.get("destination-ip").toString());
                    cfwAttackLog.setDestPort(Integer.valueOf(bodyMap.get("destination-port").toString()));
                    cfwAttackLog.setProtocol(bodyMap.get(LogConstants.KC_PROTOCOL).toString());
                    cfwAttackLog.setApp(bodyMap.get(LogConstants.KC_APP).toString());
                    cfwAttackLog.setPolicy(bodyMap.get(LogConstants.KC_ACTION).toString());
                    cfwAttackLog.setDirection(bodyMap.get(LogConstants.DIRECTION).toString());
                    if (Objects.nonNull(bodyMap.get("payload"))) {
                        String payload = bodyMap.get("payload").toString();
                        if (!payload.isEmpty()) {
                            byte[] decodedBytes = Base64.getDecoder().decode(payload);
                            cfwAttackLog.setPayLoad(new String(decodedBytes));
                        }
                    }
                    cfwAttackLog.setCreateTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("time").toString())), DateUtils.DATETIME_FORMAT));
                    dataList.add(cfwAttackLog);
                } catch (Exception e) {
                    log.error("risk log parse error,param:{},errMsg:{}", JSONUtil.toJsonStr(searchHit), e.getMessage());
                }
            }
        }
        response.setDataList(dataList);
        if(param.getMaxResults() == dataList.size()){
            int offset = StringUtils.isBlank(param.getNextToken()) ? 0 : Integer.parseInt(param.getNextToken());
            response.setNextToken(String.valueOf(offset+ dataList.size()));
        }
        return response;
    }

    public DescribeLogResponse<CfwTrafficLog> describeFlowLog(DescribeLogParam param) throws CfwException {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        DescribeLogResponse<CfwTrafficLog> response = new DescribeLogResponse<>();
        response.setRequestId(auth.getRequest_id()).setTotalCount(0L);
        SearchResponse searchResponse = this.queryLogEs(param, LogTopicEnum.CFW_FLOW.getTopic());
        if (searchResponse == null) {
            return response;
        }
        long total = searchResponse.getHits().getTotalHits().value;
        response.setTotalCount(total);
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        List<CfwTrafficLog> dataList = new ArrayList<>();
        if (searchHits.length > 0) {
            for (SearchHit searchHit : searchHits) {
                try {
                    Map<String, Object> bodyMap = searchHit.getSourceAsMap();
                    if (CollectionUtil.isEmpty(bodyMap)) {
                        continue;
                    }
                    CfwTrafficLog cfwTrafficLog = new CfwTrafficLog();
                    cfwTrafficLog.setCfwInstanceId(param.getCfwInstanceId());
                    cfwTrafficLog.setStartTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("start-time").toString())), DateUtils.DATETIME_FORMAT));
                    cfwTrafficLog.setEndTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("end-time").toString())), DateUtils.DATETIME_FORMAT));
                    cfwTrafficLog.setSrcIp(bodyMap.get("source-ip").toString());
                    cfwTrafficLog.setSrcPort(Integer.valueOf(bodyMap.get("source-port").toString()));
                    cfwTrafficLog.setDestIp(bodyMap.get("destination-ip").toString());
                    cfwTrafficLog.setDestPort(Integer.valueOf(bodyMap.get("destination-port").toString()));
                    cfwTrafficLog.setProtocol(bodyMap.get(LogConstants.KC_PROTOCOL).toString());
                    cfwTrafficLog.setPacketSize(Integer.valueOf(bodyMap.get("receive-bytes").toString()));
                    cfwTrafficLog.setCreateTime(DateUtils.getDate(new Date(Long.parseLong(bodyMap.get("time").toString())), DateUtils.DATETIME_FORMAT));
                    dataList.add(cfwTrafficLog);
                } catch (Exception e) {
                    log.error("flow log parse error,param:{},errMsg:{}", JSONUtil.toJsonStr(searchHit), e.getMessage());
                }
            }
        }
        if(param.getMaxResults() == dataList.size()){
            int offset = StringUtils.isBlank(param.getNextToken()) ? 0 : Integer.parseInt(param.getNextToken());
            response.setNextToken(String.valueOf(offset+ dataList.size()));
        }
        response.setDataList(dataList);
        return response;
    }

    public SearchResponse queryLogEs(DescribeLogParam param, String topic) {
        //查询实例rs的Id
        List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(param.getCfwInstanceId());
        if (CollectionUtil.isEmpty(cfwRsDOList)) {
            return null;
        }
        List<String> rsIdList = cfwRsDOList.stream().map(CfwRsDO::getFwInstanceId).collect(Collectors.toList());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.from(0);
        if (StringUtils.isNotBlank(param.getNextToken()) && NumberUtils.isNumber(param.getNextToken())) {
            sourceBuilder.from(Integer.parseInt(param.getNextToken()));
        }
        sourceBuilder.size(param.getMaxResults());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("fw-instance-id", rsIdList));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("time").gte(param.getStartTime().getTime()).lte(param.getEndTime().getTime()));

        //支持通过IP、端口、协议、应用、动作、方向、规则名称进行模糊检索。
        if(StringUtils.isNotBlank(param.getQueryKeyword())){
            BoolQueryBuilder queryKeyword = QueryBuilders.boolQuery();
            queryKeyword.should(QueryBuilders.wildcardQuery("source-ip", "*"+param.getQueryKeyword()+"*"));
            queryKeyword.should(QueryBuilders.wildcardQuery("source-port", "*"+param.getQueryKeyword()+"*"));
            queryKeyword.should(QueryBuilders.wildcardQuery("destination-ip", "*"+param.getQueryKeyword()+"*"));
            queryKeyword.should(QueryBuilders.wildcardQuery("destination-port", "*"+param.getQueryKeyword()+"*"));
            queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.KC_PROTOCOL, "*"+param.getQueryKeyword()+"*"));
            queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.DIRECTION, "*"+param.getQueryKeyword()+"*"));
            if (LogTopicEnum.CFW_RISK.getTopic().equals(topic)) {
                queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.KC_ATTACK_TYPE, "*"+param.getQueryKeyword()+"*"));
                queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.KC_SEVERITY, "*"+param.getQueryKeyword()+"*"));
                queryKeyword.minimumShouldMatch(1);
                boolQueryBuilder.must(queryKeyword);
            }
            if (LogTopicEnum.CFW_BLOCK.getTopic().equals(topic)) {
                queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.KC_APP, "*"+param.getQueryKeyword()+"*"));
                queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.KC_ACTION, "*"+param.getQueryKeyword()+"*"));
                queryKeyword.should(QueryBuilders.wildcardQuery(LogConstants.ACL_NAME, "*"+param.getQueryKeyword()+"*"));
            }
            queryKeyword.minimumShouldMatch(1);
            boolQueryBuilder.must(queryKeyword);
        }

        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.sort("time", SortOrder.DESC);
        List<String> indexs = cfwEsUtils.fetchLogIndex(topic, param.getStartTime(), param.getEndTime());
        if(CollectionUtil.isEmpty(indexs)){
            return null;
        }
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);
        searchRequest.indices(indexs.toArray(new String[0]));
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            return cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("describeFireWall:topic:{},auth;{},error:{}", topic, JSONUtil.toJsonStr(InnerAPIHolder.getProxyAuth()), e.getMessage(), e);
        }
        return null;
    }

    public long countRiskEs(DescribeLogParam param, String topic,List<CfwRsDO> cfwRsDOList) {
        if (CollectionUtil.isEmpty(cfwRsDOList)) {
            return 0L;
        }
        List<String> rsIdList = cfwRsDOList.stream().map(CfwRsDO::getFwInstanceId).collect(Collectors.toList());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.size(0);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("fw-instance-id", rsIdList));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("time").gte(param.getStartTime().getTime()).lte(param.getEndTime().getTime()));
        sourceBuilder.query(boolQueryBuilder);
        List<String> indexs = cfwEsUtils.fetchLogIndex(topic, param.getStartTime(), param.getEndTime());
        if(CollectionUtil.isEmpty(indexs)){
            return 0L;
        }
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);
        searchRequest.indices(indexs.toArray(new String[0]));
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            SearchResponse  response = cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            if(Objects.nonNull(response)&&Objects.nonNull(response.getHits())){
                return response.getHits().getTotalHits().value;
            }
        } catch (Exception e) {
            log.error("describeFireWall:topic:{},auth;{},error:{}", topic, JSONUtil.toJsonStr(InnerAPIHolder.getProxyAuth()), e.getMessage(), e);
        }
        return 0L;
    }

    public long countBlockDenyEs(Date startTime,Date endTime,List<CfwRsDO> cfwRsDOList) {
        if (CollectionUtil.isEmpty(cfwRsDOList)) {
            return 0L;
        }
        List<String> rsIdList = cfwRsDOList.stream().map(CfwRsDO::getFwInstanceId).collect(Collectors.toList());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.size(0);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termsQuery("fw-instance-id", rsIdList));
        boolQueryBuilder.must(QueryBuilders.termsQuery("kc_action", "拒绝"));
        boolQueryBuilder.must(QueryBuilders.rangeQuery("time").gte(startTime.getTime()).lte(endTime.getTime()));

        sourceBuilder.query(boolQueryBuilder);
        List<String> indexs = cfwEsUtils.fetchLogIndex(LogTopicEnum.CFW_BLOCK.getTopic(), startTime, endTime);
        if(CollectionUtil.isEmpty(indexs)){
            return 0L;
        }
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);
        searchRequest.indices(indexs.toArray(new String[0]));
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            SearchResponse  response = cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
            if(Objects.nonNull(response)&&Objects.nonNull(response.getHits())){
                return response.getHits().getTotalHits().value;
            }
        } catch (Exception e) {
            log.error("describeFireWall:topic:{},auth;{},error:{}", LogTopicEnum.CFW_BLOCK.getTopic(), JSONUtil.toJsonStr(InnerAPIHolder.getProxyAuth()), e.getMessage(), e);
        }
        return 0L;
    }
}
