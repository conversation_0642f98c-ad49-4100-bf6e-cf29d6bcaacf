package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwapi.config.SuperConfig;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.eip.QueryEipsResponse;
import com.ksyun.cfwapi.domain.eip.QueryEipsResult;
import com.ksyun.cfwapi.domain.fw.DescribeCfwEipsParam;
import com.ksyun.cfwapi.domain.fw.ModifyCfwEipParam;
import com.ksyun.cfwapi.enums.InstanceType;
import com.ksyun.cfwapi.enums.StatusEnum;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.vpcapi.VpcOpenApiClient;
import com.ksyun.cfwcore.component.PageInfoUtils;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.HeaderConstant;
import com.ksyun.cfwcore.enums.ResourceType;
import com.ksyun.cfwcore.enums.VifHostType;
import com.ksyun.cfwcore.es.ElasticSearchUtils;
import com.ksyun.cfwcore.es.domain.CommonQueryParam;
import com.ksyun.cfwcore.es.domain.ElasticSearchQueryResponse;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.iam.api.IamAPI;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfos;
import com.ksyun.cfwcore.iam.api.domain.IamProjectResult;
import com.ksyun.cfwcore.openapi.network.domain.InstanceInfo;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenStackFloatingipsLinks;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingip;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingipsResponse;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.QueryFloatingipsResponse;
import com.ksyun.cfwcore.openstack.cfw.firewall.FirewallEipAPI;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.EipsToFwParam;
import com.ksyun.cfwcore.openstack.domain.OpenStackLink;
import com.ksyun.cfwcore.openstack.wapper.domain.Floatingip;
import com.ksyun.cfwcore.trade.wapper.TradeWapper;
import com.ksyun.comm.thirdpart.trade.api.domain.SubOrder;
import com.ksyun.comm.util.PropertyUtils;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FireWallEipService {
    @Autowired
    private FirewallEipAPI firewallEipAPI;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private PageInfoUtils pageInfoUtils;

    @Autowired
    private VpcOpenApiClient vpcOpenApiClient;

    @Autowired
    private ElasticSearchUtils elasticSearchUtils;

    @Autowired
    private IamAPI iamAPI;

    @Autowired
    private TradeWapper tradeWapper;

    @Autowired
    private SuperConfig superConfig;

    public OperateResponse modifyCfwEip(ModifyCfwEipParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwId(param.getCfwInstanceId());
        if (Objects.isNull(cfwInstanceDO)) {
            throw new CfwException("该防火墙不存在");
        }

        EipsToFwParam eipsToFwParam = new EipsToFwParam().eip_id_list(param.getEipIds());
        if (StatusEnum.START.getStatusStr().equals(param.getEipProtectStatus())) {
            //校验Eip是否超限
            checkParam(cfwInstanceDO, auth, param);
            try {
                firewallEipAPI.attachEipsToFw(superAuth, eipsToFwParam, cfwInstanceDO.getFwLbId());
            } catch (Exception e) {
                log.error("绑定弹性网卡失败,eipIds:{},fwId:{},error:{}", param.getEipIds(), param.getCfwInstanceId(), e.getMessage());
                if (e.getMessage().contains("EsgweipNotFound")) {
                    throw new CfwException("该弹性网卡未绑定实例，不支持开启防护");
                } else if (e.getMessage().contains("FirewallRetryAttach")) {
                    throw new CfwException("开启防护中，请勿重复操作");
                } else if (e.getMessage().contains("FirewallEipNotAttached")) {
                    throw new CfwException("关闭防护中，请勿重复操作");
                }
                throw new CfwException("该弹性网卡开启防护失败");
            }

        } else {
            try {
                firewallEipAPI.detachEipsToFw(superAuth, eipsToFwParam, cfwInstanceDO.getFwLbId());
            } catch (Exception e) {
                log.error("解绑弹性网卡失败,eipIds:{},fwId:{},error:{}", param.getEipIds(), param.getCfwInstanceId(), e.getMessage());
                if (e.getMessage().contains("FirewallEipNotAttached")) {
                    throw new CfwException("关闭防护中，请勿重复操作");
                }
                throw new CfwException("该弹性网卡关闭防护失败");
            }
        }
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    public void checkParam(CfwInstanceDO cfwInstanceDO, ProxyAuth auth, ModifyCfwEipParam param) throws CfwException {
        //调整后的Eip有没有低于已开启的防护Eip数
        Map<String, Object> paramMap = getUsedEipCountParamMap(cfwInstanceDO.getFwLbId());
        String vpcOpenApiResponseStr = vpcOpenApiClient.post(auth, paramMap);
        Gson gson = new Gson();
        Type type = new TypeToken<ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip>>() {
        }.getType();
        ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip> vpcOpenApiResponse = gson.fromJson(vpcOpenApiResponseStr, type);
        if (vpcOpenApiResponse.getTotal() + param.getEipIds().size() > cfwInstanceDO.getTotalEipNum()) {
            throw new CfwException("防火墙防护Eip数量最大数量：" + cfwInstanceDO.getTotalEipNum());
        }
    }

    /**
     * 获取参数数据
     *
     * @return
     */
    public Map<String, Object> getUsedEipCountParamMap(String fwLbId) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("productLine", ResourceType.EIP.getProductLine());
        dataMap.put("Ex.firewall_id", fwLbId);
        dataMap.put("Ex.resourceStatus", "0");
        dataMap.put(HeaderConstant.ACTION, "CommonQuery");
        dataMap.put(HeaderConstant.SERVICE, "vpc");
        dataMap.put(HeaderConstant.VERSION, "2016-03-04");
        return dataMap;
    }

    public QueryEipsResponse describeCfwEips(DescribeCfwEipsParam param, String source) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        QueryEipsResponse response;
        QueryEipsResult query_response = this.queryEips(param,auth);
        response = new QueryEipsResponse(query_response, source);

        String next_token = pageInfoUtils.getNextToken(query_response.getNext_token(),
                param.getNextToken(), param.getMaxResults());
        if (!org.springframework.util.StringUtils.isEmpty(next_token)) {
            response.setNext_token(next_token);
        }
        response.setRequest_id(auth.getRequest_id());
        return response;
    }

    public QueryEipsResult queryEips(DescribeCfwEipsParam param,ProxyAuth auth) throws Exception {
        QueryFloatingipsResponse floatingips = this.queryFloatingips(param,auth);
        List<Floatingip> list = floatingips.getFloatingips();
        if (CollectionUtils.isNotEmpty(list)) {
            newSetInstance(auth, floatingips);
        }
        QueryEipsResult response = new QueryEipsResult(floatingips);
        response.setPre_token(floatingips.getPre_token());
        response.setNext_token(floatingips.getNext_token());
        return response;
        }

    public QueryFloatingipsResponse queryFloatingips(DescribeCfwEipsParam param,ProxyAuth auth) throws Exception {
        com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingipsResponse floatingips = this.queryApiFloatingips(param,auth);
        QueryFloatingipsResponse response = new QueryFloatingipsResponse(floatingips);
        List<OpenStackFloatingipsLinks> floatingips_links = floatingips.getFloatingips_links();
        if (CollectionUtils.isNotEmpty(floatingips_links)) {
            for (OpenStackFloatingipsLinks links : floatingips_links) {
                if (links.getRel().equals("next")) {
                    String href = links.getHref();
                    String hs = href.substring(0);
                    String[] splits = hs.split("&");
                    for (String split : splits) {
                        if (split.startsWith("marker")) {
                            String[] strings = split.split("=");
                            response.setNext_token(strings[1]);
                        }
                    }
                } else if (links.getRel().equals("previous")) {
                    String href = links.getHref();
                    String hs = href.substring(0);
                    String[] splits = hs.split("&");
                    for (String split : splits) {
                        if (split.startsWith("marker")) {
                            String[] strings = split.split("=");
                            response.setPre_token(strings[1]);
                        }
                    }
                }
            }
        }
        return response;
    }

    public com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingipsResponse queryApiFloatingips(DescribeCfwEipsParam param,ProxyAuth auth) throws Exception {
        List<CfwInstanceDO> cfwInstanceDOList = cfwInstanceService.getCfwInstancesByAccountId(auth.getAccount_id());
        if (CollectionUtil.isEmpty(cfwInstanceDOList)) {
            throw new CfwException("该账户下不存在防火墙");
        }
        List<String> fwLbIdList = cfwInstanceDOList.stream().filter(e->!param.getCfwInstanceId().equals(e.getFwId())).map(CfwInstanceDO::getFwLbId).collect(Collectors.toList());
        //构建参数
        Map<String, Object> paramMap = getEipInfoParam(param, fwLbIdList,auth);
        //调用openapi
        String vpcOpenApiResponseStr = vpcOpenApiClient.post(auth, paramMap);

        //调用openapi
        com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingipsResponse floatingips = new OpenstackFloatingipsResponse();
        if(StringUtils.isBlank(vpcOpenApiResponseStr)){
            log.info("获取弹性公网ip返回结果eipResultStr为空");
            floatingips.setTotalCount(0);
            return floatingips;
        }

        //字符串转换对象
        Gson gson = new Gson();
        Type type = new TypeToken<ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip>>(){}.getType();
        ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip> vpcOpenApiResponse = gson.fromJson( vpcOpenApiResponseStr, type);

        //模拟分页
        List<OpenstackFloatingip> _list = elasticSearchUtils.mergeResponseData(vpcOpenApiResponse);
        
        floatingips.setFloatingips(_list);
        //模拟分页
        List<OpenStackLink> openStackLinks = elasticSearchUtils.simulationNetronPage(vpcOpenApiResponse, _list);
        floatingips.setFloatingips_links(openStackLinks);
        if (vpcOpenApiResponse != null) {
            floatingips.setTotalCount(vpcOpenApiResponse.getTotal());
        }
        return floatingips;
    }

    /**
     * 获取参数数据
     *
     * @param param
     * @param fwLbIdList
     * @return
     */
    public Map<String, Object> getEipInfoParam(DescribeCfwEipsParam param, List<String> fwLbIdList, ProxyAuth auth) {
        Map<String,Object> dataMap = new HashMap<>();
        dataMap.put("productLine", ResourceType.EIP.getProductLine());

        if(Objects.nonNull(param.getNextToken())&& NumberUtils.isNumber(param.getNextToken())){
            Integer marker = Integer.parseInt(param.getNextToken()) - 1;
            dataMap.put("from",marker);
        }else{
            dataMap.put("from",0);
        }
        dataMap.put("size", param.getMaxResults());
        dataMap.put("Ex_Not.type", "private");
        dataMap.put("Ex_Not.user_tag", "admin");
        dataMap.put("Ex.ip_version", "4");
        dataMap.put("Ex.resourceStatus", "0");
        dataMap.put("Ex.resourceRegion", auth.getRegion());
        dataMap.put("Ex_Sort.resourceCreateTime", "desc");

        if(CollectionUtil.isNotEmpty(fwLbIdList)){
            dataMap.put("Ex_Not.firewall_id", fwLbIdList);
        }
        dataMap.put("Ex.resourceUserId",auth.getAccount_id());
        if(StringUtils.isNotBlank(param.getEipAddress())){
            dataMap.put("Ex.floating_ip_address", param.getEipAddress());
        }
        dataMap.put(HeaderConstant.ACTION, "CommonQuery");
        dataMap.put(HeaderConstant.SERVICE, "vpc");
        dataMap.put(HeaderConstant.VERSION, "2016-03-04");
        return dataMap;
    }

    private void newSetInstance(final ProxyAuth auth, QueryFloatingipsResponse floatingips) {
        final List<String> vifIds = Lists.newArrayList();
        for (Floatingip floatingip : floatingips.getFloatingips()) {
            if (InstanceType.KvmType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.EpcType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.DockerType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.SmartNic.getName().equals(floatingip.getUsage_type())) {
                vifIds.add(floatingip.getDevice_uuid());
            }
        }
        Map<String, InstanceInfo> vifMap = new HashMap<>();
        List<String> instanceIds=new ArrayList<>();
        if (CollectionUtils.isNotEmpty(vifIds)) {
            CommonQueryParam commonQueryParam = new CommonQueryParam();
            commonQueryParam.setSize(vifIds.size());
            commonQueryParam.setProductLine("VIF");
            commonQueryParam.setFields("nova_vif_id,vm_id,host_type,ip,vif_type");
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("Ex.nova_vif_id", vifIds);
            commonQueryParam.setQueryMap(queryMap);
            try {
                ElasticSearchQueryResponse<Map> response = elasticSearchUtils.commonQuery(auth, commonQueryParam);
                if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getItems())) {
                    for (ElasticSearchQueryResponse.Item<Map> item : response.getData().getItems()) {
                        String vifId = (String) PropertyUtils.getValue(item.getExtension(), "nova_vif_id");
                        String vmId = (String) PropertyUtils.getValue(item.getExtension(), "vm_id");
                        String vifType = (String) PropertyUtils.getValue(item.getExtension(), "vif_type");
                        String ip = (String) PropertyUtils.getValue(item.getExtension(), "ip");
                        String hostType = (String) PropertyUtils.getValue(item.getExtension(), "host_type");
                        if(StringUtils.isNotBlank(vmId)){
                            instanceIds.add(vmId);
                        }
                        InstanceInfo instanceInfo = new InstanceInfo(
                                vmId, ip,
                                "eni".equals(vifType) ? "extension" : vifType,
                                VifHostType.getAlias(hostType));
                        vifMap.put(vifId, instanceInfo);
                    }
                }
            } catch (Exception e) {
                log.error("获取弹性IP绑定的网卡信息失败 Message [{}]", e.getMessage(), e);
                throw new OpenAPIException(ErrorCode.InternalError, "query eip vif error", HttpStatus.INTERNAL_SERVER_ERROR);
            }
        }


        IamProjectInfos iamProjectInfos = iamAPI.getIamProjectInfos(auth);
        Map<String, String> projectMap = new HashMap<>();
        if (Objects.nonNull(iamProjectInfos) && Objects.nonNull(iamProjectInfos.getInfos())
                && CollectionUtil.isNotEmpty(iamProjectInfos.getInfos().getProjectList())) {
            projectMap = iamProjectInfos.getInfos().getProjectList().stream().collect(Collectors.toMap(IamProjectResult::getProjectId, IamProjectResult::getProjectName, (k1, k2) -> k1));
        }

        InstanceInfo instanceInfo;
        for (Floatingip floatingip : floatingips.getFloatingips()) {
            if(StringUtils.isNotBlank(floatingip.getIamProjectId())){
                double floatValue = Double.parseDouble(floatingip.getIamProjectId());
                floatingip.setProjectName(projectMap.get(Integer.toString((int) floatValue)));
            }
            String type = floatingip.getUsage_type();
            if (InstanceType.KvmType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.EpcType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.DockerType.getName().equals(floatingip.getUsage_type())
                    || InstanceType.SmartNic.getName().equals(floatingip.getUsage_type())) {
                String device_uuid = floatingip.getDevice_uuid();
                if (vifMap.containsKey(device_uuid)) {
                    instanceInfo = vifMap.get(device_uuid);
                    if (instanceInfo != null) {
                        floatingip.setInstance_id(instanceInfo.getInstanceId());
                        floatingip.setVif_type(instanceInfo.getVifType());
//						floatingip.setIp(instanceInfo.getIp());
                        floatingip.setHostType(instanceInfo.getHostType());
                    }
                }
            } else if (InstanceType.SlbType.getName().equals(type)
                    || InstanceType.AlbAdvancedType.getName().equals(type)) {
                floatingip.setInstance_id(floatingip.getLb_pool_uuid());
            } else if (InstanceType.Havip.getName().equals(type)) {
                floatingip.setInstance_id(floatingip.getDevice_uuid());
            } else if (InstanceType.PortfwdType.getName().equals(type)) {
                floatingip.setInstance_id(floatingip.getIgw_uuid());
            } else if (InstanceType.Nat.getName().equals(type)) {
                floatingip.setInstance_id(floatingip.getNatpool_id());
            }
        }
    }

}
