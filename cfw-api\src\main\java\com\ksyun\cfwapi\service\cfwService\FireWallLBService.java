package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwapi.config.CfwApiCommonConfig;
import com.ksyun.cfwapi.config.SuperConfig;
import com.ksyun.cfwapi.convert.FwConvert;
import com.ksyun.cfwapi.dao.entity.*;
import com.ksyun.cfwapi.dao.service.*;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.dataDoard.DescribeLogParam;
import com.ksyun.cfwapi.domain.fw.*;
import com.ksyun.cfwapi.enums.StatusEnum;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwapi.utils.SubnetUtil;
import com.ksyun.cfwapi.vpcapi.VpcOpenApiClient;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.constants.HeaderConstant;
import com.ksyun.cfwcore.enums.*;
import com.ksyun.cfwcore.es.domain.ElasticSearchQueryResponse;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.iam.api.IamAPI;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfos;
import com.ksyun.cfwcore.iam.api.domain.IamProjectResult;
import com.ksyun.cfwcore.monitor.api.domain.MonitorDeleteParam;
import com.ksyun.cfwcore.monitor.enums.MonitorProductTypeEnum;
import com.ksyun.cfwcore.openapi.kec.RunInstancesAPI;
import com.ksyun.cfwcore.openapi.kec.domain.*;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingip;
import com.ksyun.cfwcore.openstack.cfw.firewall.FirewallEipAPI;
import com.ksyun.cfwcore.openstack.cfw.firewall.FirewallLbAPI;
import com.ksyun.cfwcore.openstack.cfw.firewall.FirewallRsAPI;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.*;
import com.ksyun.cfwcore.rabbitmq.SyncMessageSendService;
import com.ksyun.cfwcore.trade.wapper.FwNotifyService;
import com.ksyun.cfwcore.trade.wapper.TradeWapper;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.ksyun.comm.thirdpart.trade.api.domain.InstanceSubOrder;
import com.ksyun.comm.thirdpart.trade.api.domain.Product;
import com.ksyun.comm.thirdpart.trade.api.domain.QuerySubOrdersByInstanceIdsParam;
import com.ksyun.comm.thirdpart.trade.api.domain.SubOrder;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.comm.util.ConcurrentStack;
import com.ksyun.comm.util.DateUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FireWallLBService {

    @Autowired
    private CfwApiCommonConfig cfwApiCommonConfig;

    @Autowired
    private FirewallLbAPI firewallLbAPI;

    @Autowired
    private FirewallRsAPI firewallRsAPI;

    @Autowired
    private CfwRsVifService cfwRsVifService;

    @Autowired
    private RunInstancesAPI runInstancesAPI;

    @Autowired
    private EtcdService etcdService;

    @Autowired
    private IpsService ipsService;

    @Autowired
    private AvService avService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private SubnetService subnetService;

    @Autowired
    private CfwAclService cfwAclService;

    @Autowired
    private IamAPI iamAPI;


    @Autowired
    private TradeUtils tradeUtils;

    @Autowired
    private TradeWapper tradeWapper;

    @Autowired
    private FirewallEipAPI firewallEipAPI;

    @Autowired
    private FwNotifyService fwNotifyService;

    @Autowired
    private SyncMessageSendService syncMessageSendService;

    @Autowired
    private SuperConfig superConfig;

    @Autowired
    private CfwUpdteHistoryService updteHistoryService;

    @Autowired
    private FireWallEipService fireWallEipService;

    @Autowired
    private DataDoardService dataDoardService;

    @Autowired
    private VpcOpenApiClient vpcOpenApiClient;

    @Autowired
    private CfwRegionConfigService regionConfigService;


    private final ExecutorService executorServiceCloudServer = new ThreadPoolExecutor(50, 100,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    private final ExecutorService executorServiceRs = new ThreadPoolExecutor(50, 100,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    private final ExecutorService executorServiceFwAsync = new ThreadPoolExecutor(50, 100,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(500), new ThreadPoolExecutor.CallerRunsPolicy());

    public OperateResponse createFireWall(CreateFireWallLBParam param, String subOrderId) throws Exception {

        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());

        checkCreateFw(auth.getAccount_id());

        OperateResponse result = new OperateResponse().setRequestId(auth.getRequest_id()).setResult(false);
        //创建实例Id
        String wallId = GUIDGeneratorUtil.generateGUID();
        InnerAPIHolder.setInstanceId(wallId);
        result.setId(wallId);

        //获取子网最新未用子网
        List<SubnetDO> subNets = subnetService.getAvailableSubNet(3, auth.getRegion());
        List<String> subNetIds = subNets.stream().map(SubnetDO::getSubnetId).collect(Collectors.toList());
        String securityGroupId = subNets.get(0).getSecurityGroupId();
        Map<String, String> subNetMap = subNets.stream().collect(Collectors.toMap(SubnetDO::getSubnetId, SubnetDO::getCidr, (k1, k2) -> k1));

        CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(auth.getRegion());
        if (Objects.isNull(regionConfigDO)) {
            throw new CfwException("查询机房配置信息失败");
        }
        //异步创建防火墙
        CompletableFuture.runAsync(() -> {
            try {
                //调用neutron 创建flb
                CfwLbOtResponse.FirewallResponse lbResponse = createProxyFirewall(param, superAuth);
                if (Objects.isNull(lbResponse)) {
                    throw new CfwException("创建firewall_lb失败");
                }
                //创建云防火墙 mysql
                CfwInstanceDO cfwInstanceDO = saveFirewallLb(param, wallId, auth, String.join(CommonConstant.COMMA, subNetIds), lbResponse, subOrderId);

                //计算要创建多少个实例
                int cloudServerNum = fwNotifyService.getRsNum(param.getInstanceType(), param.getBandwidth());
                //创建实例主键
                List<String> rsIdList = new ArrayList<>();
                for (int i = 0; i < cloudServerNum; i++) {
                    rsIdList.add(GUIDGeneratorUtil.generateGUID());
                }

                //创建云服务器
                //key:云服务器id value:实例id
                ConcurrentHashMap<String, String> instancesIdMap = createCloudServer(rsIdList, cfwInstanceDO, superAuth, subNetIds, securityGroupId, regionConfigDO.getImageId(),regionConfigDO.getEcsType());
                if (CollectionUtil.isEmpty(instancesIdMap)) {
                    throw new CfwException("创建云服务器失败");
                }

                //暂停5秒再查询云服务器信息
                Thread.sleep(5000);
                //查询云服务器信息
                HashMap<String, DescribeInstancesResponse.InstanceResponse> cloudServerMap = describeInstances(String.valueOf(cfwApiCommonConfig.getCfwProjectId()), superAuth, instancesIdMap,rsIdList);

                //调用neutron 创建rs
                ConcurrentHashMap<String, CreateCfwRsOtResponse.FirewallNodeResponse> rsOtResponsesMap = createProxyFirewallRs(rsIdList, cloudServerMap, lbResponse.getId(), superAuth);

                //创建实例mysql
                saveFirewallRs(rsIdList, cloudServerMap, rsOtResponsesMap, auth, cfwInstanceDO, securityGroupId, regionConfigDO.getImageId());

                //通过etcd启动Instance
                for (Map.Entry<String, DescribeInstancesResponse.InstanceResponse> entry : cloudServerMap.entrySet()) {
                    if (CollectionUtil.isEmpty(entry.getValue().getNetworkInterfaceSet())) {
                        continue;
                    }
                    List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList = new ArrayList<>(entry.getValue().getNetworkInterfaceSet());
                    sortNetworkInterface(networkInterfaceList);
                    sendEtcd(auth.getRequest_id(), wallId, String.valueOf(lbResponse.getFirewall_vni()), entry.getKey(), networkInterfaceList, subNetMap, regionConfigDO,auth.getRegion());
                }

                avService.createCfwAv(wallId, param.getInstanceType());
                ipsService.createCfwIps(wallId, param.getInstanceType());
            } catch (Exception e) {
                log.error("异步创建节点异常:wallId：{},error{}", wallId, e.getStackTrace());
            }
        }, executorServiceFwAsync);
        return result.setResult(true);
    }

    private void checkCreateFw(String accountId) throws Exception {
        int count = cfwInstanceService.countCfwInstancesByAccountId(accountId);
        if (count >= 100) {
            throw new CfwException("防火墙创建数量不能超过100，已创建：" + count);
        }
    }

    public void sortNetworkInterface(List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList) {
        networkInterfaceList.sort(Comparator.comparing(DescribeInstancesResponse.NetworkInterfaceSet::getNetworkInterfaceType).reversed().thenComparing(DescribeInstancesResponse.NetworkInterfaceSet::getNetworkInterfaceId));
        log.info("networkInterfaceList:{}", JSONUtil.toJsonStr(networkInterfaceList));
    }

    public void sendEtcd(String requestId, String wallId, String firewallVni, String rsId, List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList, Map<String, String> subNetMap,CfwRegionConfigDO regionConfigDO,String region) throws CfwException {

        DescribeInstancesResponse.NetworkInterfaceSet networkInterfaceLog = networkInterfaceList.get(2);
        DescribeInstancesResponse.NetworkInterfaceSet networkInterfaceBusiness = networkInterfaceList.get(1);
        log.info("sendEtcd ，FwId:{}, 业务网卡ip：{}", wallId, networkInterfaceBusiness.getPrivateIpAddress());

        InstanceIdStartParam instance = new InstanceIdStartParam();
        instance.setTraceId(requestId);
        instance.setOperationId(GUIDGeneratorUtil.generateGUID());
        instance.setWallId(wallId);

        instance.setUploadLogUseVif(networkInterfaceLog.getPrivateIpAddress() + "/24/" + networkInterfaceLog.getMacAddress());
        String cidrLog = subNetMap.get(networkInterfaceLog.getSubnetId());
        if (StringUtils.isBlank(cidrLog)) {
            throw new CfwException("子网段查询错误" + networkInterfaceLog.getSubnetId());
        }
        instance.setSubnetGatewayIpLog(SubnetUtil.getGateWayIp(cidrLog));

        instance.setUploadBusinessUseVif(networkInterfaceBusiness.getPrivateIpAddress() + "/24/" + networkInterfaceBusiness.getMacAddress());
        String cidrBusiness = subNetMap.get(networkInterfaceBusiness.getSubnetId());
        if (StringUtils.isBlank(cidrBusiness)) {
            throw new CfwException("子网段查询错误" + networkInterfaceBusiness.getSubnetId());
        }
        instance.setSubnetGatewayIpBusiness(SubnetUtil.getGateWayIp(cidrBusiness));

        instance.setHttpUrl(cfwApiCommonConfig.getDownFileUrl());

        InstanceIdStartParam.KafkaInfo kafkaInfo = new InstanceIdStartParam.KafkaInfo();
        kafkaInfo.setUrl(cfwApiCommonConfig.getKafkaUrl());
        kafkaInfo.setUsername(cfwApiCommonConfig.getKafkaUser());
        kafkaInfo.setPassword(cfwApiCommonConfig.getKafkPassword());
        kafkaInfo.setSaslEnable(cfwApiCommonConfig.getKafkSaslEnable());
        kafkaInfo.setCertPath(regionConfigDO.getKafkaCertPath());
        kafkaInfo.setMd5(regionConfigDO.getKafkaCertMd5());

        instance.setKafka(kafkaInfo);

        List<InstanceIdStartParam.VxlanInfo> vxlanList = new ArrayList<>();
        vxlanList.add(getVxlanInfo("untrust", regionConfigDO.getEsgwInIp(), firewallVni, networkInterfaceBusiness));
        vxlanList.add(getVxlanInfo("trust", regionConfigDO.getEsgwOutIp(), firewallVni, networkInterfaceBusiness));
        instance.setVxlan(vxlanList);
        String key = String.format(EtcdConstants.START_INSTANCE, rsId);
        log.info("发送启动instance,key:{}，value:{}", key, JSONUtil.toJsonStr(instance));
        etcdService.putValue(key, JSONUtil.toJsonStr(instance));
    }

    private static InstanceIdStartParam.VxlanInfo getVxlanInfo(String direction, String cidr, String firewall_vni, DescribeInstancesResponse.NetworkInterfaceSet networkInterface) {
        InstanceIdStartParam.VxlanInfo vxlanInfo = new InstanceIdStartParam.VxlanInfo();
        vxlanInfo.setDirection(direction);
        vxlanInfo.setDest(cidr);
        vxlanInfo.setVni(String.valueOf(firewall_vni));
        vxlanInfo.setMac(networkInterface.getMacAddress());
        vxlanInfo.setIp(networkInterface.getPrivateIpAddress());
        return vxlanInfo;
    }

    private void saveFirewallRs(List<String> rsIdList, HashMap<String, DescribeInstancesResponse.InstanceResponse> cloudServerMap, ConcurrentHashMap<String, CreateCfwRsOtResponse.FirewallNodeResponse> rsOtResponsesMap, ProxyAuth auth, CfwInstanceDO cfwInstanceDO,String securityGroupId,String imageId) {
        List<CfwRsDO> cfwRsDOList = new ArrayList<>(rsIdList.size());
        List<CfwRsVifDO> cfwRsVifDOList = new ArrayList<>(rsIdList.size() * 3);
        for (String rsId : rsIdList) {
            DescribeInstancesResponse.InstanceResponse runInstancesResponse = cloudServerMap.get(rsId);
            CreateCfwRsOtResponse.FirewallNodeResponse firewallNodeResponse = rsOtResponsesMap.get(rsId);
            CfwRsDO cfwRs = new CfwRsDO();
            // 设置属性值
            cfwRs.setFwId(cfwInstanceDO.getFwId());
            cfwRs.setRsStatus(FirewallRsStatusEnum.UN_CREATE.getStatus());
            if (Objects.nonNull(firewallNodeResponse)) {
                cfwRs.setLbRsId(firewallNodeResponse.getId());
                cfwRs.setRsStatus(FirewallRsStatusEnum.CREATEING.getStatus());
            }
            cfwRs.setFwInstanceId(rsId);
            cfwRs.setAccountId(auth.getAccount_id());
            cfwRs.setFwLbId(cfwInstanceDO.getFwLbId());
            cfwRs.setKecId(runInstancesResponse.getInstanceId());
            cfwRs.setSubnetId(cfwInstanceDO.getSubnetId());
            cfwRs.setRegion(auth.getRegion());
            cfwRs.setSgId(securityGroupId);
            cfwRs.setImageId(imageId);
            cfwRs.setHsType(cfwApiCommonConfig.getHsType());
            cfwRs.setDeleteStatus(DeleteFlagEnum.RESERVE.getStatus());
            cfwRs.setUnhealthTimes(0);
            cfwRs.setCreateTime(new Date());
            cfwRs.setUpdateTime(new Date());
            cfwRsDOList.add(cfwRs);

            //没有网卡信息用定时任务处理
            if (CollectionUtil.isEmpty(runInstancesResponse.getNetworkInterfaceSet())) {
                continue;
            }
            List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList = new ArrayList<>(runInstancesResponse.getNetworkInterfaceSet());
            sortNetworkInterface(networkInterfaceList);
            cfwRsVifDOList.add(getCfwRsVifDO(rsId, networkInterfaceList.get(0), "etcd", runInstancesResponse.getInstanceId()));
            cfwRsVifDOList.add(getCfwRsVifDO(rsId, networkInterfaceList.get(1), "flow", runInstancesResponse.getInstanceId()));
            log.info("saveFirewallRs ，FwId:{}, 业务网卡ip：{}", cfwInstanceDO.getFwId(), networkInterfaceList.get(1).getPrivateIpAddress());
            cfwRsVifDOList.add(getCfwRsVifDO(rsId, networkInterfaceList.get(2), "log", runInstancesResponse.getInstanceId()));
        }
        if (CollectionUtil.isNotEmpty(cfwRsDOList)) {
            cfwRsService.saveBatch(cfwRsDOList);
        }
        if (CollectionUtil.isNotEmpty(cfwRsVifDOList)) {
            cfwRsVifService.saveBatch(cfwRsVifDOList);
        }
    }

    public CfwRsVifDO getCfwRsVifDO(String rsId, DescribeInstancesResponse.NetworkInterfaceSet etcdNetwork, String useTo, String kecId) {
        CfwRsVifDO cfwRsVif = new CfwRsVifDO();
        // 设置属性值
        cfwRsVif.setRsId(rsId);
        cfwRsVif.setKecId(kecId);
        cfwRsVif.setNovaVifId(etcdNetwork.getNetworkInterfaceId());
        cfwRsVif.setVifMac(etcdNetwork.getMacAddress());
        cfwRsVif.setVifType(etcdNetwork.getNetworkInterfaceType());
        cfwRsVif.setUseTo(useTo);
        cfwRsVif.setIp(etcdNetwork.getPrivateIpAddress());
        cfwRsVif.setCreateTime(new Date());
        cfwRsVif.setUpdateTime(new Date());
        return cfwRsVif;
    }

    private ConcurrentHashMap<String, CreateCfwRsOtResponse.FirewallNodeResponse> createProxyFirewallRs(List<String> rsIdList, HashMap<String, DescribeInstancesResponse.InstanceResponse> cloudServerMap, String firewalllbId, ProxyAuth auth) throws InterruptedException {
        ConcurrentHashMap<String, CreateCfwRsOtResponse.FirewallNodeResponse> rsOtResponsesList = new ConcurrentHashMap<>(rsIdList.size());
        List<List<String>> cloudServerLists = Lists.partition(rsIdList, CommonConstant.BATCH_HANDLE_SIZE_FIVE);
        for (List<String> list : cloudServerLists) {
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (String rsId : list) {
                executorServiceRs.execute(() -> {
                    try {
                        DescribeInstancesResponse.InstanceResponse networkInterface = cloudServerMap.get(rsId);
                        if (CollectionUtil.isEmpty(networkInterface.getNetworkInterfaceSet())) {
                            throw new CfwException("创建RS实例失败createProxyFirewallRs:网络接口信息为空");
                        }
                        CreateCfwRsOtResponse.FirewallNodeResponse rsResponse = createFirewallNode(firewalllbId, auth, rsId, networkInterface.getNetworkInterfaceSet());
                        rsOtResponsesList.put(rsId, rsResponse);
                    } catch (Exception e) {
                        log.error("创建RS实例失败:{}失败 errormsg:{}, error:{}", JSONUtil.toJsonStr(rsId), e.getMessage(),e.getStackTrace());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return rsOtResponsesList;
    }

    public CreateCfwRsOtResponse.FirewallNodeResponse createFirewallNode(String firewalllbId, ProxyAuth auth, String rsId, Set<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceSet) throws Exception {
        List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList = new ArrayList<>(networkInterfaceSet);
        sortNetworkInterface(networkInterfaceList);

        CreateCfwRsOtParam rsOtParam = new CreateCfwRsOtParam();
        CreateCfwRsOtParam.FirewallNodeParam firewallNodeParam = new CreateCfwRsOtParam.FirewallNodeParam();
        firewallNodeParam.setFirewall_id(firewalllbId);
        firewallNodeParam.setNova_vif_id(networkInterfaceList.get(1).getNetworkInterfaceId());
        log.info("createFirewallNode ，firewalllbId:{}, 业务网卡ip：{}", firewalllbId, networkInterfaceList.get(1).getPrivateIpAddress());
        rsOtParam.setFirewall_node(firewallNodeParam);
        log.info("创建RS实例参数：{}", JSONUtil.toJsonStr(rsOtParam));
        CreateCfwRsOtResponse.FirewallNodeResponse rsResponse = firewallRsAPI.createFirewallRs(auth, rsOtParam);
        log.info("创建RS实例结果{}", JSONUtil.toJsonStr(rsId));
        return rsResponse;
    }

    private ConcurrentHashMap<String, String> createCloudServer(List<String> rsIdList, CfwInstanceDO cfwInstanceDO, ProxyAuth auth, List<String> subNetIds,String securityGroupId,String imageId,String ecsType) throws Exception {
        List<List<String>> lists = Lists.partition(rsIdList, CommonConstant.BATCH_HANDLE_SIZE_FIVE);
        ConcurrentHashMap<String, String> instancesIdMap = new ConcurrentHashMap<>();
        for (List<String> list : lists) {
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (String rsId : list) {
                executorServiceCloudServer.execute(() -> {
                    try {
                        RunInstancesRequest createParam = getData(rsId, cfwInstanceDO, subNetIds,securityGroupId,imageId,ecsType);
                        Set<RunInstancesResponse.InstanceResponse> instancesSetResponseSet = runInstancesAPI.createRunInstances(createParam, auth);
                        if (CollectionUtil.isNotEmpty(instancesSetResponseSet)) {
                            Iterator<RunInstancesResponse.InstanceResponse> iterator = instancesSetResponseSet.iterator();
                            if (iterator.hasNext()) {
                                RunInstancesResponse.InstanceResponse instanceResponse = iterator.next();
                                instancesIdMap.put(instanceResponse.getInstanceId(), rsId);
                            }
                        } else {
                            throw new CfwException("创建云服务器错误");
                        }
                    } catch (Exception e) {
                        log.error("创建云服务器:{}失败 error:{}", rsId, e.getStackTrace());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
        return instancesIdMap;
    }

    private HashMap<String, DescribeInstancesResponse.InstanceResponse> describeInstances(String projectId, ProxyAuth auth, ConcurrentHashMap<String, String> instancesIdMap,List<String> rsIdList) throws Exception {
        HashMap<String, DescribeInstancesResponse.InstanceResponse> result = new HashMap<>();
        DescribeInstancesRequest describeParam = new DescribeInstancesRequest();
        describeParam.setInstanceId(new ArrayList<>(instancesIdMap.keySet()));
        describeParam.setProjectId(Collections.singletonList(projectId));
        Set<DescribeInstancesResponse.InstanceResponse> describeInstancesSet = runInstancesAPI.describeInstances(describeParam, auth);
        log.info("批量查询云主机信息返回结果：{}",JSONUtil.toJsonStr(describeInstancesSet));
        if (CollectionUtil.isNotEmpty(describeInstancesSet)) {
            for (DescribeInstancesResponse.InstanceResponse instanceResponse : describeInstancesSet) {
                result.put(instancesIdMap.get(instanceResponse.getInstanceId()), instanceResponse);
            }
        }

        for(String rsId:rsIdList){
            if(Objects.isNull(result.get(rsId))){
                result.put(rsId,new DescribeInstancesResponse.InstanceResponse());
            }
            DescribeInstancesResponse.InstanceResponse rsInstance = result.get(rsId);
            if(StringUtils.isBlank(rsInstance.getInstanceId())){
                for (Map.Entry<String, String> entry : instancesIdMap.entrySet()) {
                    if(entry.getValue().equals(rsId)){
                        rsInstance.setInstanceId(entry.getKey());
                    }
                }
            }
        }
        return result;
    }

    private CfwInstanceDO saveFirewallLb(CreateFireWallLBParam param, String wallId, ProxyAuth auth, String subNetIds, CfwLbOtResponse.FirewallResponse lbResponse, String subOrderId) {
        CfwInstanceDO cfwInstanceDO = new CfwInstanceDO();
        cfwInstanceDO.setFwId(wallId);
        cfwInstanceDO.setInstanceType(param.getInstanceType());
        cfwInstanceDO.setRegion(auth.getRegion());
        cfwInstanceDO.setAccountId(auth.getAccount_id());
        if (StringUtils.isNotBlank(param.getInstanceName())) {
            cfwInstanceDO.setName(param.getInstanceName());
        } else {
            cfwInstanceDO.setName(cfwApiCommonConfig.getDefaultName());
        }

        cfwInstanceDO.setChargeType(param.getChargeType());
        cfwInstanceDO.setSubnetId(subNetIds);
        cfwInstanceDO.setBandwidth(param.getBandwidth());
        cfwInstanceDO.setStatus(FirewallStatusEnum.CREATEING.getStatus());
        cfwInstanceDO.setTotalEipNum(param.getTotalEipNum());
        cfwInstanceDO.setUsedEipNum(0);
        cfwInstanceDO.setProjectId(param.getProjectId());
        if (InstanceTypeEnum.ENTERPRISE.getType().equals(param.getInstanceType())) {
            cfwInstanceDO.setIpsStatus(StatusEnum.START.getStatus());
            cfwInstanceDO.setAvStatus(StatusEnum.START.getStatus());
        } else {
            cfwInstanceDO.setIpsStatus(StatusEnum.STOP.getStatus());
            cfwInstanceDO.setAvStatus(StatusEnum.STOP.getStatus());
        }
        cfwInstanceDO.setFwLbId(lbResponse.getId());
        cfwInstanceDO.setFwLbVni(lbResponse.getFirewall_vni());
        cfwInstanceDO.setDeleteStatus(DeleteFlagEnum.RESERVE.getStatus());
        cfwInstanceDO.setCreateTime(new Date());
        cfwInstanceDO.setSubOrderId(subOrderId);
        cfwInstanceService.save(cfwInstanceDO);
        return cfwInstanceDO;
    }


    private CfwLbOtResponse.FirewallResponse createProxyFirewall(CreateFireWallLBParam param, ProxyAuth auth) throws Exception {
        CreateCfwLbOtParam lbOtParam = new CreateCfwLbOtParam();
        CreateCfwLbOtParam.FirewallParam firewallParam = new CreateCfwLbOtParam.FirewallParam();
        if (StringUtils.isNotBlank(param.getInstanceName())) {
            firewallParam.setName(param.getInstanceName());
        } else {
            firewallParam.setName(cfwApiCommonConfig.getDefaultName());
        }
        firewallParam.setDescription(param.getInstanceDesc());
        firewallParam.setAdmin_state_up(StatusEnum.START.isOpenStackStatus());
        firewallParam.setRate_in(param.getBandwidth());
        firewallParam.setRate_out(param.getBandwidth());
        lbOtParam.setFirewall(firewallParam);
        CfwLbOtResponse.FirewallResponse lbResponse = firewallLbAPI.createFirewallLb(auth, lbOtParam);
        if (Objects.isNull(lbResponse) || Objects.isNull(lbResponse.getId()) || Objects.isNull(lbResponse.getFirewall_vni())) {
            throw new CfwException("创建见cfwLb失败");
        }
        return lbResponse;
    }

    public RunInstancesRequest getData(String rsId, CfwInstanceDO cfwInstanceDO, List<String> subNetIds,String securityGroupId,String imageId,String ecsType) {
        RunInstancesRequest request = new RunInstancesRequest();
        // 设置基本属性
        request.setImageId(imageId);
        request.setInstanceType(ecsType);
        request.setMaxCount(1);
        request.setMinCount(1);
        request.setSubnetId(subNetIds.get(0));
        request.setChargeType("Daily");
        request.setKeepImageLogin(true);
        request.setPurchaseTime(0);
        request.setSecurityGroupId(securityGroupId);
        request.setProjectId(cfwApiCommonConfig.getCfwProjectId());
        if (StringUtils.isNotBlank(cfwInstanceDO.getName())) {
            request.setInstanceName(cfwInstanceDO.getName());
        } else {
            request.setInstanceName(cfwApiCommonConfig.getDefaultName());
        }
        String encodedString = Base64.getEncoder().encodeToString(String.format(cfwApiCommonConfig.getUserdata(), rsId).getBytes());
        log.info("******userData******{}", String.format(cfwApiCommonConfig.getUserdata(), rsId));
        request.setUserData(encodedString);
        request.setSystemDiskDiskType(cfwApiCommonConfig.getSystemdiskType());
        request.setSystemDiskDiskSize(cfwApiCommonConfig.getSystemdiskSize());
        // 设置 NetworkInterfaceList
        List<RunInstancesRequest.NetworkInterfaceDto> networkInterfaceList = new ArrayList<>();
        RunInstancesRequest.NetworkInterfaceDto networkInterface1 = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface1.setSubnetId(subNetIds.get(1));
        networkInterface1.setSecurityGroupId(cfwApiCommonConfig.getSecurityGroupId());
        RunInstancesRequest.NetworkInterfaceDto networkInterface2 = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface2.setSubnetId(subNetIds.get(2));
        networkInterface2.setSecurityGroupId(cfwApiCommonConfig.getSecurityGroupId());
        networkInterfaceList.add(networkInterface1);
        networkInterfaceList.add(networkInterface2);
        request.setNetworkInterfaceList(networkInterfaceList);
        return request;
    }

    public DescribeFireWallResponse queryFireWall(List<String> idList,String region) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        if(StringUtils.isBlank(region)){
            region = auth.getRegion();
        }

        List<CfwInstanceDO> cfwInstanceDOList = cfwInstanceService.getCfwInstanceByRegion(idList, auth.getAccount_id(),region);
        DescribeFireWallResponse response = new DescribeFireWallResponse();
        response.setRequestId(auth.getRequest_id());
        if (CollectionUtil.isEmpty(cfwInstanceDOList)) {
            return response;
        }
        List<CloudFireWallInstance> cloudFireWallInstanceList = new ArrayList<>(cfwInstanceDOList.size());
        List<String> fwIds = cfwInstanceDOList.stream().map(CfwInstanceDO::getFwId).collect(Collectors.toList());

        List<StatisticsDO> statisticsDOList = cfwAclService.getAclCountByFwIds(fwIds);
        Map<String, Integer> aclCountMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(statisticsDOList)) {
            aclCountMap = statisticsDOList.stream().collect(Collectors.toMap(StatisticsDO::getId, StatisticsDO::getNum, Integer::sum));
        }
        IamProjectInfos iamProjectInfos = iamAPI.getIamProjectInfos(auth);
        Map<String, String> projectMap = new HashMap<>();
        if (Objects.nonNull(iamProjectInfos) && Objects.nonNull(iamProjectInfos.getInfos())
                && CollectionUtil.isNotEmpty(iamProjectInfos.getInfos().getProjectList())) {
            projectMap = iamProjectInfos.getInfos().getProjectList().stream().collect(Collectors.toMap(IamProjectResult::getProjectId, IamProjectResult::getProjectName, (k1, k2) -> k1));
        }
        Map<String, InstanceSubOrder> instanceSubOrderMap = new HashMap<>();
        QuerySubOrdersByInstanceIdsParam query_subOrders_param = new QuerySubOrdersByInstanceIdsParam();
        query_subOrders_param.setInstanceIds(fwIds);
        List<InstanceSubOrder> instanceSubOrders = tradeWapper.querySubOrderByInstanceIdsWithInstance(query_subOrders_param);
        Map<String, Integer> durationMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(instanceSubOrders)) {
            instanceSubOrderMap = instanceSubOrders.stream().collect(Collectors.toMap(InstanceSubOrder::getInstanceId, p -> p, (k1, k2) -> k2));
            List<String> productIds = instanceSubOrders.stream().map(InstanceSubOrder::getProductId).collect(Collectors.toList());
            List<Product> products = tradeUtils.queryProductByInstanceId(productIds);
            if (CollectionUtil.isNotEmpty(products)) {
                durationMap = products.stream().collect(Collectors.toMap(Product::getProductId, Product::getDuration, (k1, k2) -> k2));
            }
        }

        for (CfwInstanceDO cfwInstanceDO : cfwInstanceDOList) {
            CloudFireWallInstance fireWallInstance = new CloudFireWallInstance();
            fireWallInstance.setInstanceId(cfwInstanceDO.getFwId());
            fireWallInstance.setInstanceName(cfwInstanceDO.getName());
            fireWallInstance.setInstanceType(cfwInstanceDO.getInstanceType());
            fireWallInstance.setBandwidth(cfwInstanceDO.getBandwidth());
            fireWallInstance.setStatus(cfwInstanceDO.getStatus());
            fireWallInstance.setTotalEipNum(cfwInstanceDO.getTotalEipNum());
            fireWallInstance.setUsedEipNum(cfwInstanceDO.getUsedEipNum());
            fireWallInstance.setIpsStatus(cfwInstanceDO.getIpsStatus());
            fireWallInstance.setAvStatus(cfwInstanceDO.getAvStatus());
            fireWallInstance.setTotalAclNum(cfwApiCommonConfig.getTotalAclNum());
            fireWallInstance.setProjectId(cfwInstanceDO.getProjectId());
            fireWallInstance.setFwLbId(cfwInstanceDO.getFwLbId());

            fireWallInstance.setChargeType(cfwInstanceDO.getChargeType());
            if (Objects.nonNull(aclCountMap.get(cfwInstanceDO.getFwId()))) {
                fireWallInstance.setUsedAclNum(aclCountMap.get(cfwInstanceDO.getFwId()));
            } else {
                fireWallInstance.setUsedAclNum(0);
            }
            fireWallInstance.setRegion(cfwInstanceDO.getRegion());
            fireWallInstance.setCreateTime(DateUtils.dateToString(cfwInstanceDO.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
            fireWallInstance.setProjectName(projectMap.get(cfwInstanceDO.getProjectId()));
            if (Objects.nonNull(instanceSubOrderMap.get(cfwInstanceDO.getFwId()))) {
                InstanceSubOrder instanceSubOrder = instanceSubOrderMap.get(cfwInstanceDO.getFwId());
                if (StringUtils.isNotBlank(instanceSubOrder.getServiceEndTime())) {
                    fireWallInstance.setServiceEndTime(instanceSubOrder.getServiceEndTime());
                }
                if (Objects.nonNull(durationMap.get(instanceSubOrder.getProductId()))) {
                    fireWallInstance.setDuration(durationMap.get(instanceSubOrder.getProductId()));
                }
            }
            cloudFireWallInstanceList.add(fireWallInstance);
        }
        response.setCloudFireWallInstanceList(cloudFireWallInstanceList);
        return response;
    }

    public OperateResponse alterCloudFireWallInstanceState(AlterFireWallLBParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
        try {
            //查询防火墙获取firewallLbId
            CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getCfwInstanceId(),auth.getAccount_id());
            if (Objects.isNull(cfwInstanceDO)) {
                throw new CfwException("防火墙实例不存在");
            }
            //调用proxy修改防火墙状态
            CreateCfwLbOtParam createCfwLbOtParam = new CreateCfwLbOtParam();
            CreateCfwLbOtParam.FirewallParam firewallParam = new CreateCfwLbOtParam.FirewallParam();
            firewallParam.setAdmin_state_up(StatusEnum.getStatusByCode(param.getStatus()));
            createCfwLbOtParam.setFirewall(firewallParam);
            CfwLbOtResponse.FirewallResponse firewallResponse = firewallLbAPI.updateFirewallLb(superAuth, createCfwLbOtParam, cfwInstanceDO.getFwLbId());
            if (Objects.isNull(firewallResponse) || !StatusEnum.getStatusByCode(param.getStatus()) == firewallResponse.getAdmin_state_up()) {
                throw new CfwException("设置失败");
            }

            //修改防火墙数据库状态
            if (StatusEnum.START.getStatusStr().equals(param.getStatus())) {
                cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.RUNNING.getStatus());
            } else {
                cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.STOPED.getStatus());
            }

        } catch (Exception e) {
            log.error("修改状态失败，auth：{}，Parameter：{},error:{}", auth, JSONUtil.toJsonStr(param),e.getMessage());
            return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(false);
        }
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse deleteFireWall(DeleteFireWallParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
        OperateResponse response = new OperateResponse().setRequestId(auth.getRequest_id());
        //查询mysql
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getCfwInstanceId(), auth.getAccount_id());
        if (Objects.isNull(cfwInstanceDO)) {
            log.error("防火墙实例不存在");
            response.setResult(false);
            return response;
        }

        //修改状态为退订中状态
        cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.UNSUBSCRIBING.getStatus());
        response.setResult(true);

        //异步删除防火墙
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        CompletableFuture.runAsync(() -> {
                            try {
                                //删除Eip

                                try {
                                    deleteEip(cfwInstanceDO, auth, superAuth);
                                } catch (Exception e) {
                                    log.error("解绑失败，fwId:{}，解除Eip失败,auth:{},error:{}", cfwInstanceDO.getFwId(), JSONUtil.toJsonStr(auth), e.getStackTrace());
                                }
                                //调用neutron删除防火墙
                                try {
                                    firewallLbAPI.deleteFirewallLb(superAuth, Collections.singletonList(cfwInstanceDO.getFwLbId()));
                                } catch (Exception e) {
                                    log.error("删除lb失败，fwId:{}，lbId:{} 绑定,auth:{},error:{}", cfwInstanceDO.getFwId(), cfwInstanceDO.getFwLbId(), JSONUtil.toJsonStr(auth), e.getStackTrace());
                                }

                                //查询rs Mysql数据
                                List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(cfwInstanceDO.getFwId());
                                if (CollectionUtil.isNotEmpty(cfwRsDOList)) {
                                    //关闭云服务器
                                    List<String> kecIdList = cfwRsDOList.stream().map(CfwRsDO::getKecId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                    try {
                                        if (CollectionUtil.isNotEmpty(kecIdList)) {
                                            TerminateInstancesRequest requestObj = new TerminateInstancesRequest();
                                            requestObj.setInstanceIdList(kecIdList);
                                            runInstancesAPI.deleteInstances(requestObj, superAuth);
                                        }
                                    } catch (Exception e) {
                                        log.error("删除云服务器失败，auth：{}，kecIdList：{}，errorMessage:{}", auth, kecIdList, e.getStackTrace());
                                    }
                                    //删除rs数据
                                    cfwRsService.removeBatchByFwId(cfwInstanceDO.getFwId());
                                }
                                //释放子网
                                subnetService.releaseSubnet(Arrays.asList(cfwInstanceDO.getSubnetId().split(CommonConstant.COMMA)));
                                //删除lb mysql
                                cfwInstanceService.removeFw(cfwInstanceDO.getId());
                                // 通知监控，删除实例
                                MonitorDeleteParam monitorDeleteParam = MonitorDeleteParam.builder()
                                        .productType(MonitorProductTypeEnum.KFW.getType())
                                        .instanceIdList(new String[]{cfwInstanceDO.getFwLbId()})
                                        .build();
                                syncMessageSendService.notifyMonitorDelete(auth, monitorDeleteParam);
                                if(!Constants.ORDER_SOURCE.equals(param.getSource())){
                                    syncMessageSendService.notifyOrder2DeleteInstance(auth, cfwInstanceDO.getFwId(), OrderSource.getValue(param.getSource()));
                                }
                                //删除所有的etcd
                                deleteEtcd(param.getCfwInstanceId());
                            } catch (Exception e) {
                                log.error("删除防火墙失败{}", e.getMessage());
                            }
                        }, executorServiceFwAsync);
                    }
                });
        return response;
    }

    public void deleteEtcd(String cfwInstanceId) {
        List<String> prefixes = Arrays.asList("/firewall/callback/start/" + cfwInstanceId,
                "/firewall/command/" + cfwInstanceId,
                "/firewall/command/callback/" + cfwInstanceId,
                "/firewall/cluster/" + cfwInstanceId,
                "/firewall/cluster/callback/" + cfwInstanceId,
                "/firewall/check/" + cfwInstanceId,
                "/firewall/" + cfwInstanceId,
                "/firewall/upload/" + cfwInstanceId);
        etcdService.deletePrefixes(prefixes);
    }

    public void deleteEip(CfwInstanceDO cfwInstanceDO, ProxyAuth auth, ProxyAuth superAuth) throws Exception {
        Map<String, Object> paramMap = getParamMap(cfwInstanceDO.getFwLbId());
        String vpcOpenApiResponseStr = vpcOpenApiClient.post(auth, paramMap);
        Gson gson = new Gson();
        Type type = new TypeToken<ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip>>() {
        }.getType();
        ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip> vpcOpenApiResponse = gson.fromJson(vpcOpenApiResponseStr, type);
        if (vpcOpenApiResponse.getSize() == 0) {
            return;
        }
        List<ElasticSearchQueryResponse.Item<OpenstackFloatingip>> eips = vpcOpenApiResponse.getItems();
        List<String> eipIds = new ArrayList<>();
        for (ElasticSearchQueryResponse.Item<OpenstackFloatingip> eip : eips) {
            Map<String, Object> base = eip.getBase();
            if (StringUtils.isNotBlank((String) base.get("instanceId"))) {
                eipIds.add((String) base.get("instanceId"));
            }
        }

        //解除防火墙Eip需要内部账号superAuth操作
        if (CollectionUtil.isNotEmpty(eipIds)) {
            EipsToFwParam eipsToFwParam = new EipsToFwParam().eip_id_list(eipIds);
            firewallEipAPI.detachEipsToFw(superAuth, eipsToFwParam, cfwInstanceDO.getFwLbId());
            log.info("fwId:{}，解除Eip:{} 绑定,superAuth:{},", cfwInstanceDO.getFwId(), eipIds, JSONUtil.toJsonStr(superAuth));
        }

    }

    /**
     * 获取参数数据
     *
     * @return
     */
    private static Map<String, Object> getParamMap(String fwLbId) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("productLine", ResourceType.EIP.getProductLine());
        dataMap.put("from", 0);
        dataMap.put("size", 5000);
        dataMap.put("Ex.firewall_id", fwLbId);
        dataMap.put("Ex.resourceStatus", "0");
        dataMap.put(HeaderConstant.ACTION, "CommonQuery");
        dataMap.put(HeaderConstant.SERVICE, "vpc");
        dataMap.put(HeaderConstant.VERSION, "2016-03-04");
        return dataMap;
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderOperteInfo modifyFireWall(ModifyFireWallLBParam param) throws Exception {
        OrderOperteInfo result = new OrderOperteInfo().setSendFlag(true).setStatus(NotifySubOrderResult.FAIL.getValue());
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
        //查询mysql
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getInstanceId(),auth.getAccount_id());

        if (Objects.isNull(cfwInstanceDO)) {
            return result.setMsg("该防火墙不存在");
        }
        if (InstanceTypeEnum.ENTERPRISE.getType().equals(cfwInstanceDO.getInstanceType()) && InstanceTypeEnum.ADVANCED.getType().equals(param.getInstanceType())) {
            return result.setMsg("不支持企业版套餐更改成高级版套餐");
        }

        //调整后的Eip有没有低于已开启的防护Eip数
        Map<String, Object> paramMap = getParamMap(cfwInstanceDO.getFwLbId());
        String vpcOpenApiResponseStr = vpcOpenApiClient.post(auth, paramMap);
        Gson gson = new Gson();
        Type type = new TypeToken<ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip>>() {
        }.getType();
        ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip> vpcOpenApiResponse = gson.fromJson(vpcOpenApiResponseStr, type);
        if (vpcOpenApiResponse.getTotal() > param.getTotalEipNum()) {
            return result.setStatus(NotifySubOrderResult.FAIL.getValue()).setMsg("调整后的Eip不能低于已开启的防护Eip数");
        }

        updteHistoryService.saveInfo(param,cfwInstanceDO);

        if (InstanceTypeEnum.ADVANCED.getType().equals(cfwInstanceDO.getInstanceType()) && InstanceTypeEnum.ENTERPRISE.getType().equals(param.getInstanceType())) {
            avService.changeModifyCfwAv(cfwInstanceDO.getFwId(), param.getInstanceType());
            ipsService.changeModifyCfwIps(cfwInstanceDO.getFwId(), param.getInstanceType());
            cfwInstanceDO.setAvStatus(StatusEnum.START.getStatus());
            cfwInstanceDO.setIpsStatus(StatusEnum.START.getStatus());
        }

        Integer bandwidthOld = cfwInstanceDO.getBandwidth();
        if (!Objects.equals(bandwidthOld, param.getBandwidth())) {
            //修改neutron防火墙带宽
            CreateCfwLbOtParam createCfwLbOtParam = new CreateCfwLbOtParam();
            CreateCfwLbOtParam.FirewallParam firewallParam = new CreateCfwLbOtParam.FirewallParam();
            firewallParam.setRate_out(param.getBandwidth());
            firewallParam.setRate_in(param.getBandwidth());
            createCfwLbOtParam.setFirewall(firewallParam);
            firewallLbAPI.updateFirewallLb(superAuth, createCfwLbOtParam, cfwInstanceDO.getFwLbId());

            int oldRsNum = cfwRsService.countRsByFwId(cfwInstanceDO.getFwId());
            int newRsNum = fwNotifyService.getRsNum(param.getInstanceType(), param.getBandwidth());
            if (oldRsNum != newRsNum) {
                result.setSendFlag(false);
                cfwInstanceDO.setStatus(FirewallStatusEnum.RECONFIGURING.getStatus());
                //修改实例个数
                TransactionSynchronizationManager.registerSynchronization(
                        new TransactionSynchronization() {
                            @Override
                            public void afterCommit() {
                                //异步修改防火墙 修改实例个数
                                CompletableFuture.runAsync(() -> {
                                    try {
                                        OrderOperteInfo changeResult = changeBandwidth(superAuth, auth, cfwInstanceDO, oldRsNum, newRsNum);
                                        if (changeResult.isSendFlag()) {
                                            log.info("防火墙更配-修改带宽结果,changeResult:{},auth:{}", JSONUtil.toJsonStr(changeResult), JSONUtil.toJsonStr(auth));
                                            fwNotifyService.notifySubOrder(param.getSubOrderId(), changeResult.getStatus(), param.getInstanceId(), auth, "修改带宽失败");
                                        }
                                    } catch (Exception e) {
                                        fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.FAIL.getValue(), param.getInstanceId(), auth, "修改带宽失败");
                                        log.error("防火墙更配-修改带宽失败,auth:{},错误原因{}", auth, e.getStackTrace());
                                    }
                                }, executorServiceFwAsync);
                            }
                        });
            }
        }

        //修改更配中数据
        cfwInstanceDO.setTotalEipNum(param.getTotalEipNum());
        cfwInstanceDO.setBandwidth(param.getBandwidth());
        cfwInstanceDO.setInstanceType(param.getInstanceType());
        cfwInstanceDO.setUpdateTime(new Date());
        cfwInstanceDO.setSubOrderId(param.getSubOrderId());
        cfwInstanceService.updateById(cfwInstanceDO);
        result.setStatus(NotifySubOrderResult.SUCCESS.getValue());
        return result;
    }

    /**
     * 修改带宽
     *
     * @param cfwInstanceDO
     */
    public OrderOperteInfo changeBandwidth(ProxyAuth superAuth, ProxyAuth auth, CfwInstanceDO cfwInstanceDO, int oldRsNum, int newRsNum) throws Exception {
        OrderOperteInfo result = new OrderOperteInfo().setSendFlag(true).setStatus(NotifySubOrderResult.FAIL.getValue());
        List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(cfwInstanceDO.getFwId());
        //缩容
        if (oldRsNum > newRsNum) {
            int num = oldRsNum - newRsNum;
            List<CfwRsDO> minusList = cfwRsDOList.subList(0, num);
            List<String> kecIdList = new ArrayList<>();
            List<String> lbRsIdList = new ArrayList<>();
            List<String> fwInstanceIdList = new ArrayList<>();
            minusList.forEach(e -> {
                kecIdList.add(e.getKecId());
                if (StringUtils.isNotBlank(e.getLbRsId())) {
                    lbRsIdList.add(e.getLbRsId());
                }
                fwInstanceIdList.add(e.getFwInstanceId());
            });
            //关闭云服务器
            TerminateInstancesRequest requestObj = new TerminateInstancesRequest();
            requestObj.setInstanceIdList(kecIdList);
            runInstancesAPI.deleteInstances(requestObj, superAuth);
            //删除rs数据neutron
            if (CollectionUtil.isNotEmpty(lbRsIdList)) {
                firewallRsAPI.deleteFirewallRs(superAuth, lbRsIdList);
            }
            //删除rs数据mysql
            cfwRsService.removeRsByIds(fwInstanceIdList);
            cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.RUNNING.getStatus());
            result.setStatus(NotifySubOrderResult.SUCCESS.getValue());
            //通知交易成功
            return result;
        } else {
            //扩容
            int num = newRsNum - oldRsNum;
            cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.RECONFIGURING.getStatus());
            List<SubnetDO> subnetDOs = subnetService.getSubnetBySubnetIds(Arrays.asList(cfwInstanceDO.getSubnetId().split(CommonConstant.COMMA)));
            if (CollectionUtil.isEmpty(subnetDOs)) {
                log.info("子网信息获取失败,fwId:{},subnetIds:{}", cfwInstanceDO.getFwId(), cfwInstanceDO.getSubnetId());
                throw new CfwException("子网信息获取失败");
            }
            Map<String, String> subnetMap = subnetDOs.stream().collect(Collectors.toMap(SubnetDO::getSubnetId, SubnetDO::getCidr, (k1, k2) -> k2));
            //创建实例主键
            List<String> rsIdList = new ArrayList<>();
            for (int i = 0; i < num; i++) {
                rsIdList.add(GUIDGeneratorUtil.generateGUID());
            }

            //创建云服务器
            //key:云服务器id value:实例id
            String securityGroupId = subnetDOs.get(0).getSecurityGroupId();
            CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(auth.getRegion());
            if(Objects.isNull(regionConfigDO)){
                throw new CfwException("region配置查询错误,Region:"+ auth.getRegion());
            }
            List<String> subnetIdList = Arrays.asList(cfwInstanceDO.getSubnetId().split(CommonConstant.COMMA));
            //查询安全组
            ConcurrentHashMap<String, String> instancesIdMap = createCloudServer( rsIdList, cfwInstanceDO, superAuth,subnetIdList,securityGroupId,regionConfigDO.getImageId(),regionConfigDO.getEcsType());
            if (CollectionUtil.isEmpty(instancesIdMap) || instancesIdMap.size() != num) {
                log.error("创建云服务器失败");
                throw new CfwException("创建云服务器失败");
            }

            result.setSendFlag(false);
            //修改实例个数
            try {
                Thread.sleep(5000);
                //查询云服务器信息
                HashMap<String, DescribeInstancesResponse.InstanceResponse> cloudServerMap = describeInstances(String.valueOf(cfwApiCommonConfig.getCfwProjectId()), superAuth, instancesIdMap,rsIdList);

                //调用neutron 创建rs
                ConcurrentHashMap<String, CreateCfwRsOtResponse.FirewallNodeResponse> rsOtResponsesMap = createProxyFirewallRs(rsIdList, cloudServerMap, cfwInstanceDO.getFwLbId(), superAuth);

                //创建实例mysql
                saveFirewallRs(rsIdList, cloudServerMap, rsOtResponsesMap, auth, cfwInstanceDO,securityGroupId,regionConfigDO.getImageId());

                //通过etcd启动Instance
                for (Map.Entry<String, DescribeInstancesResponse.InstanceResponse> entry : cloudServerMap.entrySet()) {
                    if (CollectionUtil.isEmpty(entry.getValue().getNetworkInterfaceSet())) {
                        continue;
                    }
                    List<DescribeInstancesResponse.NetworkInterfaceSet> networkInterfaceList = new ArrayList<>(entry.getValue().getNetworkInterfaceSet());
                    sortNetworkInterface(networkInterfaceList);
                    sendEtcd(auth.getRequest_id(), cfwInstanceDO.getFwId(), String.valueOf(cfwInstanceDO.getFwLbVni()), entry.getKey(), networkInterfaceList, subnetMap,regionConfigDO,auth.getRegion());
                }
            } catch (Exception e) {
                log.error("创建实例失败,error:{},auth:{}", e.getStackTrace(), JSONUtil.toJsonStr(auth));
            }
        }
        return result;
    }

    public OperateResponse allocatedProject(AllocatedProjectParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        cfwInstanceService.updateProjectByFwIds(param.getCfwInstanceIds(), param.getProjectId(),auth.getAccount_id());
        OperateResponse response = new OperateResponse();
        response.setRequestId(auth.getRequest_id());
        response.setResult(true);
        return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
    }

    public OperateResponse modifyCloudFireWallFeature(ModifyFireWallFeatureParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        cfwInstanceService.modifyCloudFireWallFeature(param,auth.getAccount_id());
        return new OperateResponse().setResult(true).setRequestId(auth.getRequest_id());
    }

    public CloudFireWallInstance queryCfwInstance(String cfwInstanceId) {
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwId(cfwInstanceId);
        return FwConvert.INSTANCE.toCloudFireWallInstance(cfwInstanceDO);
    }

    public OverviewDetailResponse queryOverviewDetail(OverviewDetailParam param) throws CfwException {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getCfwInstanceId(),auth.getAccount_id());
        if (Objects.isNull(cfwInstanceDO)) {
            throw new CfwException("云防火墙已被删除");
        }
        OverviewDetailResponse result = new OverviewDetailResponse();
        result.setRequestId(auth.getRequest_id());
        result.setFwLbId(cfwInstanceDO.getFwLbId());
        //查询实例rs的Id
        List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(param.getCfwInstanceId());
        //防御次数
        DescribeLogParam ipsParam =new DescribeLogParam();
        ipsParam.setCfwInstanceId(param.getCfwInstanceId());
        ipsParam.setStartTime(param.getStartTime());
        ipsParam.setEndTime(param.getEndTime());
        long count = dataDoardService.countRiskEs(ipsParam, LogTopicEnum.CFW_RISK.getTopic(),cfwRsDOList);
        result.setIpsCount(count);

        //访问拒绝次数
        long aclDenyCount = dataDoardService.countBlockDenyEs(param.getStartTime(),param.getEndTime(),cfwRsDOList);
        result.setAclDenyCount(aclDenyCount);
        return result;
    }

    public InstanceDetailResponse queryCfwInstanceDetail(QueryInstanceDetailParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(param.getCfwInstanceId(),auth.getAccount_id());
        InstanceDetailResponse response = new InstanceDetailResponse().setRequestId(auth.getRequest_id());
        response.setRequestId(auth.getRequest_id());
        if (Objects.isNull(cfwInstanceDO)) {
            return response;
        }

        CloudFireWallInstanceDetail data = new CloudFireWallInstanceDetail();
        data.setInstanceId(cfwInstanceDO.getFwId());
        data.setInstanceName(cfwInstanceDO.getName());
        data.setInstanceType(cfwInstanceDO.getInstanceType());
        data.setBandwidth(cfwInstanceDO.getBandwidth());
        data.setStatus(cfwInstanceDO.getStatus());
        data.setTotalEipNum(cfwInstanceDO.getTotalEipNum());
        data.setTotalAclNum(cfwApiCommonConfig.getTotalAclNum());
        data.setIpsStatus(cfwInstanceDO.getIpsStatus());
        data.setAvStatus(cfwInstanceDO.getAvStatus());
        data.setFwLbId(cfwInstanceDO.getFwLbId());
        data.setChargeType(cfwInstanceDO.getChargeType());
        data.setRegion(cfwInstanceDO.getRegion());

        //Acl使用数
        int aclCount = cfwAclService.countAclByFwId(param.getCfwInstanceId());
        data.setUsedAclNum(aclCount);

        try {
            Product instanceProduct = tradeUtils.findProductByInstanceId(param.getCfwInstanceId());
            if (Objects.nonNull(instanceProduct)) {
                data.setDuration(instanceProduct.getDuration());
            }
        } catch (Exception e) {
            log.error("查询产品信息失败,error:{}", e.getMessage());
        }

        try {
            SubOrder instanceSubOrder = tradeUtils.querySubOrder(param.getCfwInstanceId());
            if (Objects.nonNull(instanceSubOrder) && Objects.nonNull(instanceSubOrder.getServiceEndTime())) {
                data.setServiceEndTime(DateUtils.dateToString(new Date(instanceSubOrder.getServiceEndTime()), "yyyy-MM-dd HH:mm:ss"));
            }
        } catch (Exception e) {
            log.error("查询订单信息失败,error:{}", e.getMessage());
        }
        //查询已使用Eip数量
        Map<String, Object> paramMap = fireWallEipService.getUsedEipCountParamMap(cfwInstanceDO.getFwLbId());
        String vpcOpenApiResponseStr = vpcOpenApiClient.post(auth, paramMap);
        Gson gson = new Gson();
        Type type = new TypeToken<ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip>>() {
        }.getType();
        ElasticSearchQueryResponse.ResponseData<OpenstackFloatingip> vpcOpenApiResponse = gson.fromJson(vpcOpenApiResponseStr, type);
        data.setUsedEipNum(vpcOpenApiResponse.getTotal());
        response.setFireWallInstanceDetail(data);
        return response;
    }
}
