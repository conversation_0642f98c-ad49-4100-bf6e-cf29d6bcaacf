package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.IpsConvert;
import com.ksyun.cfwapi.dao.entity.CfwDictionaryDO;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwIpsDO;
import com.ksyun.cfwapi.dao.service.CfwDictionaryService;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.dao.service.CfwIpsService;
import com.ksyun.cfwapi.domain.CfwDictionary;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.etcd.IpsEtcd;
import com.ksyun.cfwapi.domain.ips.DescribeCfwIpsParam;
import com.ksyun.cfwapi.domain.ips.DescribeCfwIpsResponse;
import com.ksyun.cfwapi.domain.ips.IpsDictionaryResponse;
import com.ksyun.cfwapi.domain.ips.ModifyCfwIpsParam;
import com.ksyun.cfwapi.enums.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.enums.DictionaryTypeEnum;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.enums.WallChangeTypeEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class IpsService {


    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwDictionaryService dictionaryService;

    @Autowired
    private CfwClusterService cfwClusterService;

    @Autowired
    private CfwIpsService cfwIpsService;

    @Autowired
    private CfwInstanceService cfwInstanceService;



    public DescribeCfwIpsResponse describeCfwIps(DescribeCfwIpsParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwIpsDO cfwIpsDO = cfwIpsService.queryByFwId(param.getCfwInstanceId(),auth.getAccount_id());
        if(Objects.isNull(cfwIpsDO)){
            throw new CfwException("Ips不存在");
        }

        DescribeCfwIpsResponse result = new DescribeCfwIpsResponse();
        result.setRequestId(auth.getRequest_id());
        result.setCfwIps(IpsConvert.INSTANCE.convert2CfwIps(cfwIpsDO));
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public OperateResponse modifyCfwIps(ModifyCfwIpsParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwIpsDO cfwIpsDO = cfwIpsService.queryByIpsId(param.getIpsId(),auth.getAccount_id());
        if(Objects.isNull(cfwIpsDO)){
            throw new CfwException("Ips不存在");
        }
        CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwAccountId(cfwIpsDO.getFwId(),auth.getAccount_id());
        if(Objects.isNull(cfwInstanceDO)||InstanceTypeEnum.ADVANCED.getType().equals(cfwInstanceDO.getInstanceType())){
            throw new CfwException("云防火墙为高级版不能修改ips");
        }

        if(StringUtils.isNotBlank(param.getStatus())){
            cfwIpsDO.setStatus(param.getStatus());
        }
        if(StringUtils.isNotBlank(param.getMode())){
            cfwIpsDO.setMode(param.getMode());
        }
        cfwIpsDO.setUpdateTime(new Date());
        cfwIpsService.updateById(cfwIpsDO);

        //发送etcd
        sendEtcd(cfwIpsDO, auth.getRequest_id(), WallChangeTypeEnum.UPDATE.getCode());
        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    private void sendEtcd(CfwIpsDO cfwIpsDO, String requestId,String actionType) {
        IpsEtcd ipsEtcd = IpsConvert.INSTANCE.convert2IpsEtcd(cfwIpsDO);
        ipsEtcd.setName("ips");
        String key = String.format(EtcdConstants.IPS, cfwIpsDO.getFwId());
        log.info("ips,key:{} Etcd:{}",actionType, JSONUtil.toJsonStr(ipsEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(ipsEtcd));
        cfwClusterService.changeWallOperation(cfwIpsDO.getFwId(), requestId, WallChangeActionEnum.IPS.getCode(), actionType, Collections.emptyList());
    }

    @Transactional(rollbackFor = Exception.class)
    public void createCfwIps(String fwId,String instanceType) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        String status = InstanceTypeEnum.ENTERPRISE.getType().equals(instanceType)?StatusEnum.START.getStatusStr():StatusEnum.STOP.getStatusStr();
        CfwIpsDO cfwIpsDO =  IpsConvert.INSTANCE.convert2CfwIpsDO(fwId, IpsModeEnum.OBSERVER.getType(), status,auth.getAccount_id());
        cfwIpsService.save(cfwIpsDO);

        //发送etcd
        sendEtcd(cfwIpsDO, auth.getRequest_id(), WallChangeTypeEnum.CREATE.getCode());
    }

    public IpsDictionaryResponse queryIpsDictionary() {
        
        List<CfwDictionaryDO> ipsDicDOList =  dictionaryService.queryDictionaryByType(DictionaryTypeEnum.IPS.getType());
        if(CollectionUtil.isEmpty(ipsDicDOList)){
            return new IpsDictionaryResponse();
        }
        List<CfwDictionary> ipsDicList = IpsConvert.INSTANCE.convert2CfwDictionaryList(ipsDicDOList);
        return new IpsDictionaryResponse().setIpsDictionary(ipsDicList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void changeModifyCfwIps(String fwId, String instanceType) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        String status = InstanceTypeEnum.ENTERPRISE.getType().equals(instanceType)?StatusEnum.START.getStatusStr():StatusEnum.STOP.getStatusStr();
        CfwIpsDO cfwIpsDO = cfwIpsService.queryByFwId(fwId,auth.getAccount_id());
        if(Objects.isNull(cfwIpsDO)){
            cfwIpsDO =  IpsConvert.INSTANCE.convert2CfwIpsDO(fwId, IpsModeEnum.OBSERVER.getType(), status,auth.getAccount_id());
            cfwIpsService.save(cfwIpsDO);
        }else{
            cfwIpsDO.setStatus(status);
            cfwIpsService.updateById(cfwIpsDO);
        }

        //发送etcd
        sendEtcd(cfwIpsDO, auth.getRequest_id(), WallChangeTypeEnum.UPDATE.getCode());
    }

}
