package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.config.SuperConfig;
import com.ksyun.cfwapi.dao.entity.CfwInstanceDO;
import com.ksyun.cfwapi.dao.entity.CfwRsDO;
import com.ksyun.cfwapi.dao.entity.CfwUpdteHistoryDO;
import com.ksyun.cfwapi.dao.service.CfwInstanceService;
import com.ksyun.cfwapi.dao.service.CfwRsService;
import com.ksyun.cfwapi.dao.service.CfwUpdteHistoryService;
import com.ksyun.cfwapi.dao.service.SubnetService;
import com.ksyun.cfwapi.deadletter.domain.ProcessingOrderParam;
import com.ksyun.cfwapi.enums.UpdateStatusEnum;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.FirewallStatusEnum;
import com.ksyun.cfwcore.enums.NotifySubOrderResult;
import com.ksyun.cfwcore.enums.OperateTypeEnum;
import com.ksyun.cfwcore.fw.domain.RollbackFwParam;
import com.ksyun.cfwcore.openapi.kec.RunInstancesAPI;
import com.ksyun.cfwcore.openapi.kec.domain.TerminateInstancesRequest;
import com.ksyun.cfwcore.openstack.cfw.firewall.FirewallLbAPI;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CreateCfwLbOtParam;
import com.ksyun.cfwcore.trade.wapper.FwNotifyService;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RollbackFwService {
    @Autowired
    private FireWallLBService fireWallLBService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private CfwRsService cfwRsService;


    @Autowired
    private FirewallLbAPI firewallLbAPI;

    @Autowired
    private CfwUpdteHistoryService cfwUpdteHistoryService;

    @Autowired
    private SuperConfig superConfig;

    @Autowired
    private RunInstancesAPI runInstancesAPI;

    @Autowired
    private SubnetService subnetService;

    @Autowired
    private FwNotifyService fwNotifyService;


    @Autowired
    private RedissonTemplate redissonTemplate;

    @Autowired
    private CommonConfig commonConfig;


    public void rollbackFwService(ProxyAuth auth, RollbackFwParam rollbackFwParam) {

        if (Objects.isNull(rollbackFwParam) || StringUtils.isBlank(rollbackFwParam.getSubOrderId()) || StringUtils.isBlank(rollbackFwParam.getFwId())) {
            log.info("参数错误，Param：{}", JSONUtil.toJsonStr(rollbackFwParam));
            return;
        }
        String lockKey = String.format(RedisConstants.CFW_FW_LOCK, rollbackFwParam.getFwId());
        RLock lock = redissonTemplate.getRedissonClient().getLock(lockKey);
        lock.lock(60, TimeUnit.SECONDS);
        try {
            CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwId(rollbackFwParam.getFwId());
            if (Objects.isNull(cfwInstanceDO)) {
                log.info("rollbackFwParam:{} 的防火墙实例不存在", JSONUtil.toJsonStr(rollbackFwParam));
                return;
            }
            CfwUpdteHistoryDO updteHistoryDO = cfwUpdteHistoryService.queryUpdteHistory(rollbackFwParam.getSubOrderId(), rollbackFwParam.getFwId());
            ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
            log.info("cfwInstanceDO:{},updteHistoryDO:{},superAuth:{}",JSONUtil.toJsonStr(cfwInstanceDO),JSONUtil.toJsonStr(updteHistoryDO),JSONUtil.toJsonStr(superAuth));
            //创建回滚
            if (Objects.isNull(updteHistoryDO) && cfwInstanceDO.getSubOrderId().equals(rollbackFwParam.getSubOrderId())) {
                try {
                    rollbackCreateFwService(superAuth, auth, cfwInstanceDO);
                } catch (Exception e) {
                    log.error("rollbackFwParam:{},防火墙创建回滚失败:{}", JSONUtil.toJsonStr(rollbackFwParam), e.getMessage());
                }
            } else if (Objects.nonNull(updteHistoryDO) && cfwInstanceDO.getSubOrderId().equals(rollbackFwParam.getSubOrderId())) {
                //校验是否已回滚
                if (Objects.equals(UpdateStatusEnum.EXECUTED.getType(), updteHistoryDO.getStatus())) {
                    log.info("防火墙更新已回滚,rollbackFwParam:{},updteHistoryDO:{},auth:{}", JSONUtil.toJsonStr(rollbackFwParam), JSONUtil.toJsonStr(updteHistoryDO), JSONUtil.toJsonStr(auth));
                    return;
                }
                try {
                    rollbackUpdateFwService(superAuth, auth, cfwInstanceDO, rollbackFwParam, updteHistoryDO);
                } catch (Exception e) {
                    log.error("rollbackFwParam:{},防火墙回滚失败:{},auth：{}", JSONUtil.toJsonStr(rollbackFwParam), e.getMessage(), JSONUtil.toJsonStr(auth));
                }
                //修改记录
                cfwUpdteHistoryService.updateStatus(rollbackFwParam.getSubOrderId(), rollbackFwParam.getFwId(),UpdateStatusEnum.EXECUTED.getType());
            } else {
                log.info("不需要回滚，或者回滚已处理,rollbackFwParam:{},updteHistoryDO:{},auth：{}", JSONUtil.toJsonStr(rollbackFwParam), JSONUtil.toJsonStr(updteHistoryDO), JSONUtil.toJsonStr(auth));
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    /**
     * 创建回滚
     *
     * @param auth
     * @param cfwInstanceDO
     */
    private void rollbackCreateFwService(ProxyAuth superAuth, ProxyAuth auth, CfwInstanceDO cfwInstanceDO) {
        //删除Eip
        try {
            fireWallLBService.deleteEip(cfwInstanceDO, auth, superAuth);
        } catch (Exception e) {
            log.error("解绑失败，fwId:{}，解除Eip失败,auth:{},error:{}", cfwInstanceDO.getFwId(), JSONUtil.toJsonStr(auth), e.getStackTrace());
        }
        //调用neutron删除防火墙
        try {
            firewallLbAPI.deleteFirewallLb(superAuth, Collections.singletonList(cfwInstanceDO.getFwLbId()));
        } catch (Exception e) {
            log.error("删除lb失败，fwId:{}，lbId:{} 绑定,auth:{},error:{}", cfwInstanceDO.getFwId(), cfwInstanceDO.getFwLbId(), JSONUtil.toJsonStr(auth), e.getStackTrace());
        }

        //查询rs Mysql数据
        List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(cfwInstanceDO.getFwId());
        if (CollectionUtil.isNotEmpty(cfwRsDOList)) {
            //关闭云服务器
            List<String> kecIdList = cfwRsDOList.stream().map(CfwRsDO::getKecId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            try {
                log.info("是否删除服务器：{}",commonConfig.isCreateDeleteServer());
                if (CollectionUtil.isNotEmpty(kecIdList) && commonConfig.isCreateDeleteServer()) {
                    TerminateInstancesRequest requestObj = new TerminateInstancesRequest();
                    requestObj.setInstanceIdList(kecIdList);
                    runInstancesAPI.deleteInstances(requestObj, superAuth);
                }
            } catch (Exception e) {
                log.error("删除云服务器失败，auth：{}，superAuth:{},kecIdList：{}，errorMessage:{}", JSONUtil.toJsonStr(auth), JSONUtil.toJsonStr(superAuth), kecIdList, e.getStackTrace());
            }
            //删除rs数据
            cfwRsService.removeBatchByFwId(cfwInstanceDO.getFwId());
            //删除所有的etcd
            try {
                if (commonConfig.isCreateDeleteServer()) {
                    fireWallLBService.deleteEtcd(cfwInstanceDO.getFwId());
                }
            } catch (Exception e) {
                log.error("删除所有的etcd失败，fwId:{},auth:{},error:{}", cfwInstanceDO.getFwId(), JSONUtil.toJsonStr(auth), e.getStackTrace());
            }
        }
        //释放子网
        subnetService.releaseSubnet(Arrays.asList(cfwInstanceDO.getSubnetId().split(CommonConstant.COMMA)));
    }


    /**
     * 更新回滚
     *
     * @param auth
     * @param cfwInstanceDO
     * @param rollbackFwParam
     * @param updteHistoryDO
     */
    private void rollbackUpdateFwService(ProxyAuth superAuth, ProxyAuth auth, CfwInstanceDO cfwInstanceDO, RollbackFwParam rollbackFwParam, CfwUpdteHistoryDO updteHistoryDO) throws Exception {
        int newBandwidth = cfwInstanceDO.getBandwidth();

        cfwInstanceDO.setSubOrderId(updteHistoryDO.getOldSubOrderId());
        cfwInstanceDO.setInstanceType(updteHistoryDO.getOldInstanceType());
        cfwInstanceDO.setTotalEipNum(updteHistoryDO.getOldTotalEipNum());
        cfwInstanceDO.setBandwidth(updteHistoryDO.getOldBandwidth());
        cfwInstanceDO.setUpdateTime(new Date());
        cfwInstanceDO.setStatus(FirewallStatusEnum.RUNNING.getStatus());
        cfwInstanceService.updateById(cfwInstanceDO);

        //如果带宽不相等
        if (!Objects.equals(updteHistoryDO.getOldBandwidth(), newBandwidth)) {
            try {
                //修改neutron防火墙带宽
                CreateCfwLbOtParam createCfwLbOtParam = new CreateCfwLbOtParam();
                CreateCfwLbOtParam.FirewallParam firewallParam = new CreateCfwLbOtParam.FirewallParam();
                firewallParam.setRate_out(updteHistoryDO.getOldBandwidth());
                firewallParam.setRate_in(updteHistoryDO.getOldBandwidth());
                createCfwLbOtParam.setFirewall(firewallParam);
                firewallLbAPI.updateFirewallLb(superAuth, createCfwLbOtParam, cfwInstanceDO.getFwLbId());
                int count = fwNotifyService.getRsNum(updteHistoryDO.getOldInstanceType(), updteHistoryDO.getOldBandwidth());
                List<CfwRsDO> cfwRsDOList = cfwRsService.getCfwRsByFwId(rollbackFwParam.getFwId());
                if (cfwRsDOList.size() != count) {
                    fireWallLBService.changeBandwidth(superAuth, auth, cfwInstanceDO, cfwRsDOList.size(), count);
                }
            } catch (Exception e) {
                cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.ERROR.getStatus());
                log.error("回滚防火墙更配-修改带宽失败,auth:{},错误原因{}", auth, e.getStackTrace());
            }
        }
    }

    public void cancelProcessingOrder(ProxyAuth auth, ProcessingOrderParam param) {
        log.info("cancelProcessingOrder开始处理：param:{},auth:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(auth));
        String lockKey = String.format(RedisConstants.CFW_FW_LOCK, param.getFwId());
        RLock lock = redissonTemplate.getRedissonClient().getLock(lockKey);
        lock.lock(60, TimeUnit.SECONDS);
        try {
            CfwInstanceDO cfwInstanceDO = cfwInstanceService.getCfwInstanceByFwId(param.getFwId());

            if (Objects.isNull(cfwInstanceDO)) {
                log.info("cancelProcessingOrder:{} 的防火墙实例不存在,auth:{}", JSONUtil.toJsonStr(param), auth);
                return;
            }
            log.info("开始处理,cancelProcessingOrder,param:{},cfwInstanceDO:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(cfwInstanceDO));
            if (!cfwInstanceDO.getSubOrderId().equals(param.getSubOrderId())) {
                //修改记录
                if (OperateTypeEnum.UPDATE.getType().equals(param.getOperationType())) {
                    cfwUpdteHistoryService.updateStatus(param.getSubOrderId(), param.getFwId(), UpdateStatusEnum.EXECUTED.getType());
                }
                log.info("cancelProcessingOrder:{} 的防火墙实例不用做处理,auth:{}", JSONUtil.toJsonStr(param), auth);
                return;
            }
            ProxyAuth superAuth = new ProxyAuth(auth.getRequest_id(), superConfig.getSuperAccountId(), auth.getRegion(), auth.getType());
            try {
                if (OperateTypeEnum.CREATE.getType().equals(param.getOperationType()) && FirewallStatusEnum.CREATEING.getStatus().equals(cfwInstanceDO.getStatus())) {
                    //更新防火墙状态
                    cfwInstanceService.updateStatus(cfwInstanceDO.getId(), FirewallStatusEnum.ERROR.getStatus());
                    //通知订单失败
                    fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.FAIL.getValue(), param.getFwId(), auth, "过期失败");
                    //回滚创建的防火墙
                    rollbackCreateFwService(superAuth, auth, cfwInstanceDO);
                } else if (OperateTypeEnum.UPDATE.getType().equals(param.getOperationType()) && FirewallStatusEnum.RECONFIGURING.getStatus().equals(cfwInstanceDO.getStatus())) {
                    //通知订单失败
                    fwNotifyService.notifySubOrder(param.getSubOrderId(), NotifySubOrderResult.FAIL.getValue(), param.getFwId(), auth, "过期失败");
                    //查询历史记录
                    CfwUpdteHistoryDO updteHistoryDO = cfwUpdteHistoryService.queryUpdteHistory(param.getSubOrderId(), param.getFwId());
                    if (Objects.isNull(updteHistoryDO) || Objects.equals(UpdateStatusEnum.EXECUTED.getType(), updteHistoryDO.getStatus())) {
                        log.info("防火墙更新回滚错误,cancelProcessingOrder param:{},updteHistoryDO:{},auth:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(updteHistoryDO), JSONUtil.toJsonStr(auth));
                        return;
                    }
                    //回滚防火墙
                    RollbackFwParam rollbackFwParam = new RollbackFwParam().setFwId(param.getFwId()).setSubOrderId(param.getSubOrderId());
                    rollbackUpdateFwService(superAuth, auth, cfwInstanceDO, rollbackFwParam, updteHistoryDO);
                } else {
                    log.info("cancelProcessingOrder,未处理:param:{},auth:{},cfwInstanceDO:{}", JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(auth), JSONUtil.toJsonStr(cfwInstanceDO));
                }
                //修改记录
                if (OperateTypeEnum.UPDATE.getType().equals(param.getOperationType())) {
                    cfwUpdteHistoryService.updateStatus(param.getSubOrderId(), param.getFwId(), UpdateStatusEnum.EXECUTED.getType());
                }
            } catch (Exception e) {
                log.info("cancelProcessingOrder,更新处理失败,error:{},param:{},auth:{},cfwInstanceDO:{}", e.getMessage(), JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(auth), JSONUtil.toJsonStr(cfwInstanceDO));
            }
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }

        }
        log.info("处理结束,");
    }
}
