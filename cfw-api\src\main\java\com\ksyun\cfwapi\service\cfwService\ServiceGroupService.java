package com.ksyun.cfwapi.service.cfwService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.convert.AclConvert;
import com.ksyun.cfwapi.dao.entity.CfwAddrbookDO;
import com.ksyun.cfwapi.dao.entity.CfwServicegroupDO;
import com.ksyun.cfwapi.dao.entity.CitationCountDO;
import com.ksyun.cfwapi.dao.service.CfwAclRelateService;
import com.ksyun.cfwapi.dao.service.CfwServicegroupService;
import com.ksyun.cfwapi.domain.OperateResponse;
import com.ksyun.cfwapi.domain.etcd.ServiceGroupEtcd;
import com.ksyun.cfwapi.domain.serviceGroup.*;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.EtcdConstants;
import com.ksyun.cfwcore.enums.WallChangeActionEnum;
import com.ksyun.cfwcore.enums.WallChangeTypeEnum;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ServiceGroupService {
    @Autowired
    private CfwServicegroupService servicegroupService;



    @Autowired
    private EtcdService etcdService;

    @Autowired
    private CfwAclRelateService cfwAclRelateService;


    @Autowired
    private CfwClusterService cfwClusterService;


    public CreateServiceGroupResponse createCfwServiceGroup(CreateServiceGroupParam param) throws Exception {
        checkServiceGroup(param.getCfwInstanceId(),param.getServiceInfo(),"");
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        String service = String.join(CommonConstant.COMMA, param.getServiceInfo());
        CfwServicegroupDO serviceGroupDO = AclConvert.INSTANCE.convert2CfwServicegroupDO(param, auth.getAccount_id(), service);
        servicegroupService.save(serviceGroupDO);

        //发送etcd
        ServiceGroupEtcd serviceGroupEtcd = AclConvert.INSTANCE.convert2ServiceGroupEtcd(serviceGroupDO, auth.getRequest_id(), param.getServiceInfo());
        String key = String.format(EtcdConstants.SERVICE_GROUP, serviceGroupDO.getFwId(), serviceGroupDO.getServiceGroupId());
        log.info("创建服务组 key:{},Etcd:{}", key,JSONUtil.toJsonStr(serviceGroupEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(serviceGroupEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(serviceGroupDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.SERVICEGROUP.getCode(), WallChangeTypeEnum.CREATE.getCode(),Collections.singletonList(serviceGroupDO.getServiceGroupId()));
        CfwServiceGroup cfwServiceGroup = AclConvert.INSTANCE.convert2CfwServiceGroup(serviceGroupDO, param.getServiceInfo());
        return new CreateServiceGroupResponse().setCfwServiceGroup(cfwServiceGroup).setRequestId(auth.getRequest_id());
    }

    private void checkServiceGroup(String cfwInstanceId, List<String> serviceInfo, String serviceGroupId) throws CfwException {
        if(StringUtils.isBlank(serviceGroupId)){
            int total = servicegroupService.countByFwId(cfwInstanceId);
            if (total >= 512) {
                throw new CfwException("每个防火墙实例下最多添加512个服务组");
            }
        }

        List<CfwServicegroupDO>  servicegroupList = servicegroupService.listByFwId(cfwInstanceId);
        int count = serviceInfo.size();
        for (CfwServicegroupDO cfwServicegroupDO : servicegroupList) {
            if(cfwServicegroupDO.getServiceGroupId().equals(serviceGroupId)){
                continue;
            }
            count = cfwServicegroupDO.getService().split(CommonConstant.COMMA).length + count;
        }
        if(count > 900){
            throw new CfwException("每个防火墙实例下最多添加900个服务成员");
        }
    }

    public OperateResponse deleteCfwServiceGroup(DeleteServiceGroupParam param) throws Exception {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        CfwServicegroupDO serviceGroupDO = servicegroupService.getByServiceGroupId(param.getServiceGroupId(),auth.getAccount_id());
        if (Objects.isNull(serviceGroupDO)) {
            throw new CfwException("地址簿不存在");
        }
        boolean exist = cfwAclRelateService.existByRelateId(param.getServiceGroupId());
        if (exist) {
            throw new CfwException("服务组被引用，无法删除");
        }
        servicegroupService.deleteServicegroup(param.getServiceGroupId());

        //变更操作通知
        cfwClusterService.changeWallOperation(serviceGroupDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.SERVICEGROUP.getCode(), WallChangeTypeEnum.DELETE.getCode(),Collections.singletonList(serviceGroupDO.getServiceGroupId()));

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }


    public OperateResponse modifyCfwServiceGroup(ModifyServiceGroupParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        servicegroupService.updateServiceGroup(param,auth.getAccount_id());

        //发送etcd
        CfwServicegroupDO cfwServiceGroupDO = servicegroupService.getByServiceGroupId(param.getServiceGroupId(), auth.getAccount_id());
        ServiceGroupEtcd serviceGroupEtcd = AclConvert.INSTANCE.convert2ServiceGroupEtcd(cfwServiceGroupDO, auth.getRequest_id(), Arrays.asList(cfwServiceGroupDO.getService().split(",")));
        String key = String.format(EtcdConstants.SERVICE_GROUP, cfwServiceGroupDO.getFwId(), cfwServiceGroupDO.getServiceGroupId());
        log.info("修改地址簿 Etcd:{}", JSONUtil.toJsonStr(serviceGroupEtcd));
        etcdService.putValue(key, JSONUtil.toJsonStr(serviceGroupEtcd));
        //变更操作通知
        cfwClusterService.changeWallOperation(cfwServiceGroupDO.getFwId(), auth.getRequest_id(), WallChangeActionEnum.SERVICEGROUP.getCode(), WallChangeTypeEnum.UPDATE.getCode(),Collections.singletonList(cfwServiceGroupDO.getServiceGroupId()));

        return new OperateResponse().setRequestId(auth.getRequest_id()).setResult(true);
    }

    public DescribeServiceGroupResponse describeServiceGroup(DescribeServiceGroupParam param) {
        ProxyAuth auth = InnerAPIHolder.getProxyAuth();
        List<CfwServicegroupDO> servicegroupDOList = servicegroupService.getByServiceGroup(param,auth.getAccount_id());

        if (CollectionUtil.isEmpty(servicegroupDOList)) {
            return new DescribeServiceGroupResponse().setRequestId(auth.getRequest_id());
        }

        //查询引用数
        List<String> serviceGroupId = servicegroupDOList.stream().map(CfwServicegroupDO::getServiceGroupId).collect(Collectors.toList());
        List<CitationCountDO> citationCountDOList = cfwAclRelateService.getCitationCountByRelateIds(serviceGroupId);
        Map<String, Integer> citationCountMap = new HashMap<>(citationCountDOList.size());
        if (CollectionUtil.isNotEmpty(citationCountDOList)) {
            citationCountMap.putAll(citationCountDOList.stream().collect(Collectors.toMap(CitationCountDO::getRelateId, CitationCountDO::getCitationCount, (k1, k2) -> k1)));
        }

        List<CfwServiceGroup> cfwServiceGroups = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(servicegroupDOList)) {
            for (CfwServicegroupDO servicegroupDO : servicegroupDOList) {
                CfwServiceGroup cfwServiceGroup = AclConvert.INSTANCE.convert2CfwServiceGroup(servicegroupDO, Arrays.asList(servicegroupDO.getService().split(CommonConstant.COMMA)));
                if (Objects.nonNull(citationCountMap.get(servicegroupDO.getServiceGroupId()))) {
                    cfwServiceGroup.setCitationCount(citationCountMap.get(servicegroupDO.getServiceGroupId()));
                }
                cfwServiceGroups.add(cfwServiceGroup);
            }
        }
        return new DescribeServiceGroupResponse().setRequestId(auth.getRequest_id()).setCfwServiceGroups(cfwServiceGroups);
    }
}
