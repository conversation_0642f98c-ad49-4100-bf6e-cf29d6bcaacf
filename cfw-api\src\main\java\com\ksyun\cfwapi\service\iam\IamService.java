package com.ksyun.cfwapi.service.iam;

import com.google.common.collect.Lists;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.iam.api.IamAPI;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfos;
import com.ksyun.cfwcore.iam.api.domain.IamProjectResult;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class IamService {

    private IamAPI iamAPI;

    /**
     * 拼接查询项目制参数
     * <p>
     * 规则：
     * 1、只要传了项目制ID，不论主子账号，传啥查啥
     * 2、若没传项目制ID，查询子账号下所有项目制下资源
     * * 控制台来源，查询所有项目（理论上子账号只能查询属于自己项目的，但由于控制台调用都会传入项目制ID，这里不做细致区分）
     *
     * @param auth
     * @param productIdList
     */
    public void setIamProjectId(ProxyAuth auth, List<String> productIdList) {
        log.info("设置项目制ID列表 [{}]", productIdList);
        if (CollectionUtils.isNotEmpty(productIdList)) {
            InnerAPIHolder.setIamProjectId(productIdList);
        } else {
            if (StringUtils.isNotBlank(auth.getUser_id())) {
                IamProjectInfos iamProjectInfos = iamAPI.getIamProjectInfos(auth);
                List<String> projectIds = Lists.newArrayList();
                if (iamProjectInfos != null && iamProjectInfos.getInfos() != null
                        && CollectionUtils.isNotEmpty(iamProjectInfos.getInfos().getProjectList())) {
                    for (IamProjectResult iamProjectResult : iamProjectInfos.getInfos().getProjectList()) {
                        projectIds.add(iamProjectResult.getProjectId());
                    }
                }
                InnerAPIHolder.setIamProjectId(projectIds);
            }
        }
    }

    @Autowired
    public void setIamAPI(IamAPI iamAPI) {
        this.iamAPI = iamAPI;
    }
}
