package com.ksyun.cfwapi.thread;

import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@Data
public class ApiThreadPool {

    private ThreadPoolExecutor mqConsumeThreadPoolExecutor;

    private ListeningExecutorService listeningExecutorService;

    @PostConstruct
    private void init() throws Exception {
        log.info("消费者消息队列线程池初始化开始");
        mqConsumeThreadPoolExecutor = new ThreadPoolExecutor(
                50,
                50,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>()
        );
        listeningExecutorService = MoreExecutors.listeningDecorator(mqConsumeThreadPoolExecutor);
        log.info("消费者消息队列线程池初始化结束");
    }

    @PreDestroy
    private void shutdown() throws Exception {
        log.info("消费者消息队列线程池shutdown开始");
        if (this.mqConsumeThreadPoolExecutor != null) {
            this.mqConsumeThreadPoolExecutor.shutdown();
        }
        if (this.listeningExecutorService != null) {
            this.listeningExecutorService.shutdown();
        }
        log.info("消费者消息队列线程池shutdown结束");
    }
}
