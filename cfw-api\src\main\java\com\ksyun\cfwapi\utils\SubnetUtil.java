package com.ksyun.cfwapi.utils;

import com.ksyun.cfwapi.exception.CfwException;

import java.net.InetAddress;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class SubnetUtil {

    public static String getGateWayIp(String subnet){
        // 分割子网地址和掩码长度
        String[] netArr = subnet.split("/");
        if (netArr.length != 2) {
            throw new IllegalArgumentException("Invalid network segment format");
        }
        //将ip转换成数组
        int[] net = convertIPToIntArray(netArr[0]);
        // 计算网关IP（通常是网络地址+1）
        int gatewayTemp =  net[3] + 1;
        return net[0] + "." + net[1] + "." + net[2] + "." + gatewayTemp;
    }

    public static int[] convertIPToIntArray(String ipAddress) {
        String[] parts = ipAddress.split("\\.");
        int[] ipNumbers = new int[4];

        for (int i = 0; i < 4; i++) {
            ipNumbers[i] = Integer.parseInt(parts[i]);
        }
        return ipNumbers;
    }

    /**
     * 获取子网段
     * @param maskLenNew  新想获取的网段的掩码长度
     * @param subNetNum 想获取子网段的个数
     * @param netSeg 上一个使用了的网段
     * @return
     */
    public static List<String> getSubNetSeg(int maskLenNew, int subNetNum, String netSeg) throws Exception {
        if(subNetNum<1){
            return new ArrayList<>();
        }
        String[] netArr = netSeg.split("/");
        if (netArr.length != 2) {
            throw new IllegalArgumentException("Invalid network segment format");
        }
        // 上一个使用掩码长度
        int maskLenLast = Integer.parseInt(netArr[1]);

        //将ip转换成数组
        int[] net = convertIPToIntArray(netArr[0]);

        int targetLen = Math.min(maskLenLast, maskLenNew);

        int segIndex = (targetLen - 1) / 8;
        int localMaskLen = targetLen - segIndex * 8;
        int moveLen = 8 - localMaskLen;
        List<String> result = new ArrayList<>(subNetNum);
        for (int i = 0; i < subNetNum; i++) {
            int res = ((net[segIndex] >> moveLen) + 1) << moveLen;
            net[segIndex] = res;
            while (segIndex > 0 && net[segIndex] > 255) {
                segIndex--;
                net[segIndex]++;
            }
            if (segIndex == 0 && net[segIndex] > 255) {
                throw new CfwException("The subnet is not enough");
            }
            segIndex++;
            while (segIndex < 4) {
                net[segIndex] = 0;
                segIndex++;
            }
            segIndex = (maskLenNew - 1) / 8;
            moveLen = 8 - (maskLenNew - segIndex * 8);
            result.add(net[0] + "." + net[1] + "." + net[2] + "." + net[3]+"/"+maskLenNew);
        }
        return result;
    }

    /**
     * 判断是不是子网段
     * @param subNetStr
     * @param parentNetStr
     * @return
     */
    public static boolean judgeSubNetSeg(String subNetStr,String parentNetStr) {
        String[] subNetArr = subNetStr.split("/");
        String[] parentNetArr = parentNetStr.split("/");

        int[] subNet =convertIPToIntArray(subNetArr[0]);
        int[] parentNet = convertIPToIntArray(parentNetArr[0]);

        int subMaskLen = Integer.parseInt(subNetArr[1]);
        int parentMaskLen = Integer.parseInt(parentNetArr[1]);

        if (subMaskLen < parentMaskLen) {
            return false;
        }
        int segIndex = (parentMaskLen - 1) / 8;
        int localMaskLen = parentMaskLen - segIndex * 8;
        int moveLen = 8 - localMaskLen;

        for (int segi = 0; segi < segIndex; segi++) {
            if (subNet[segi] != parentNet[segi]){
                return false;
            }
        }

        int subSeg = subNet[segIndex] >> moveLen;
        int parentSeg = parentNet[segIndex] >> moveLen;
        if (subSeg == parentSeg){
            return true;
        }
        return false;
    }

    public static String ipToBinaryString(String ip) throws Exception {
        InetAddress inetAddress = InetAddress.getByName(ip);
        byte[] bytes = inetAddress.getAddress();
        StringBuilder binaryString = new StringBuilder();
        for (byte b : bytes) {
            String binaryByte = String.format("%8s", Integer.toBinaryString(b & 0xFF)).replace(' ', '0');
            binaryString.append(binaryByte);
        }
        return binaryString.toString();
    }

    public static void main(String[] args) throws Exception {

        String netSegLast = "**********/19";

        int maskLenNew =19;
        // 输入想获取的网段个数
        int subNetNum = 2;
        
        String parentNetSeg = "10.0.0.0/8";

        List<String> netList = getSubNetSeg(maskLenNew, subNetNum, netSegLast);
        System.out.println("netList:"+netList);
        for (String net : netList) {
            boolean result = judgeSubNetSeg(net,parentNetSeg);
            if(!result){
                //判断是不是子网段
                System.out.println(net +"不是"+parentNetSeg+"的子网段");
            }else {
                System.out.println(net +"是"+parentNetSeg+"的子网段");
            }
        }

        System.out.println(getGateWayIp(netSegLast));
        String parentNetIp = netSegLast.substring(0, netSegLast.length() - 1);
        System.out.println("parentNetIp:"+parentNetIp);

        String ip = netSegLast.substring(0, netSegLast.length() - 3);
        System.out.println(ip);
        String ip_Binary = SubnetUtil.ipToBinaryString(netSegLast.substring(0, netSegLast.length() - 3));
        System.out.println(ip_Binary);
    }
}