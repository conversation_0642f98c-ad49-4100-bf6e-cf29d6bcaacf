package com.ksyun.cfwapi.validators;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/10 19:54
 */
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(
        validatedBy = {ControllerTradeParamValidator.class}
)
public @interface ControllerTradeParamCase {
    //默认错误消息
    String message() default "";
    //分组
    Class<?>[] groups() default { };
    //负载
    Class<? extends Payload>[] payload() default { };
}
