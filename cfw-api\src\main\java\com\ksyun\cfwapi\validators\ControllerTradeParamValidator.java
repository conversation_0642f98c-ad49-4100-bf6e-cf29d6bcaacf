package com.ksyun.cfwapi.validators;

import com.ksyun.cfwapi.domain.trade.TradeParam;
import com.ksyun.cfwapi.utils.CommonUtils;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.constants.Validation;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.thirdpart.trade.api.domain.SubOrder;
import com.ksyun.comm.thirdpart.trade.enums.OrderUse;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/10 19:55
 */
@Slf4j
public class ControllerTradeParamValidator implements ConstraintValidator<ControllerTradeParamCase, TradeParam> {

    @Autowired(required = false)
    private HttpServletRequest request;

    @Autowired
    private TradeUtils tradeUtils;

    @Override
    public boolean isValid(TradeParam tradeParam, ConstraintValidatorContext constraintValidatorContext) {
        boolean isSuccess = true;

        String source = InnerAPIHolder.getSource();

        String action = request.getParameter("Action");

        // 如果来源非user、非opcenter,则设置成控制台来源
        if (source != null && !source.equals(Constants.SDK_SOURCE)
                && !source.equals(Constants.OPCENTER_SOURCE)) {
            tradeParam.setSource(Constants.CONSOLE_SOURCE);
        }

        boolean isMonthly = true;// 单位是否为包年包月
        if (Constants.CONSOLE_SOURCE.equals(tradeParam.getSource())) {
            String sub_order_id = tradeParam.getSubOrderId();
            if (StringUtils.isEmpty(sub_order_id)) {
                if(action.startsWith("Create")) {
                    isSuccess = false;
                    this.checkParamError(constraintValidatorContext, ErrorCode.SubOrderIdEmpty, "SubOrderId");
                }
            } else {
                SubOrder subOrder = tradeUtils.querySubOrderBySubOrderId(sub_order_id);
                if (subOrder != null && OrderUse.TRIAL.getValue() == subOrder.getOrderUse()) {
                    isMonthly = false;// 试用单位为天，不进行时长校验
                }
            }
        } else {
            if (StringUtils.isEmpty(tradeParam.getChargeType()) && (action.startsWith("Create"))) {
                isSuccess = false;
                this.checkParamError(constraintValidatorContext, ErrorCode.ChargeTypeEmpty, "ChargeType");
            }
        }
        if (isMonthly && tradeParam.getPurchaseTime() != null
                && !CommonUtils.isMatchPattern(Validation.REGEX_MONTHLY_DURATION, tradeParam.getPurchaseTime().toString())) {
            isSuccess = false;
            this.checkParamError(constraintValidatorContext, ErrorCode.PurchaseTimeInvalid, "PurchaseTime");
        }
        return isSuccess;
    }

    private void checkParamError(ConstraintValidatorContext constraintValidatorContext, String errorCode, String propertyNodeName) {
        constraintValidatorContext.disableDefaultConstraintViolation();
        constraintValidatorContext.buildConstraintViolationWithTemplate(errorCode)
                .addPropertyNode(propertyNodeName).addConstraintViolation();
    }
}
