package com.ksyun.cfwapi.vpcapi;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.google.gson.Gson;
import com.ksyun.cfwapi.config.CfwApiCommonConfig;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.HeaderConstant;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class VpcOpenApiClient {

    @Autowired
    private CfwApiCommonConfig cfwApiCommonConfig;
    public <R> R  get(ProxyAuth auth, Map<String, Object> parameters, Class<R> clazz) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderConstant.X_KSC_REQUEST_ID, auth.getRequest_id());
        headers.put(HeaderConstant.X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.put(HeaderConstant.X_KSC_REGION, auth.getRegion());
        //发送get请求并接收响应数据

        String url = String.format(CommonConstant.HOST, cfwApiCommonConfig.getVpcApiHost(), cfwApiCommonConfig.getVpcApiPort());
        HttpResponse response = HttpUtil.createGet(url).addHeaders(headers).form(parameters).timeout(cfwApiCommonConfig.getHttpTimeOut()).execute();
        if(response.getStatus() == 200){
            String result = response.body();
            Gson gson = new Gson();
            return gson.fromJson(result, clazz);
        }
        log.error("查询url {}失败：{}", headers, response.body());
        return null;
    }

    public String  post(ProxyAuth auth, Map<String, Object> data) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderConstant.X_KSC_REQUEST_ID, auth.getRequest_id());
        headers.put(HeaderConstant.X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.put(HeaderConstant.X_KSC_REGION, auth.getRegion());
        headers.put("Content-Type", "application/x-www-form-urlencoded");

        String url = String.format(CommonConstant.HOST, cfwApiCommonConfig.getVpcApiHost(), cfwApiCommonConfig.getVpcApiPort());

        HttpResponse response = HttpUtil.createPost(url).addHeaders(headers).form(data).timeout(cfwApiCommonConfig.getHttpTimeOut()).execute();
        if(response.getStatus() == 200){
            return response.body();
        }
        log.error("查询url {}失败：{}", headers, response.body());
        return null;
    }
}
