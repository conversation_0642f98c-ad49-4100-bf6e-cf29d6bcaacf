package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * Created by xuyaming on 2018/11/20.
 */
@Data
public abstract class CommonDomain {
    @Expose
    @SerializedName("CreateTime")
    @XmlElement(name = "CreateTime")
    protected String create_time;

    @Expose
    @SerializedName("VpcId")
    @XmlElement(name = "VpcId")
    protected String uuid;

    @Expose
    @SerializedName("VpcName")
    @XmlElement(name = "VpcName")
    protected String name;

    @Expose
    @SerializedName("CidrBlock")
    @XmlElement(name = "CidrBlock")
    protected String cidr;

    @Expose
    @SerializedName("IsDefault")
    @XmlElement(name = "IsDefault")
    protected Boolean is_default;

    @Expose
    @SerializedName("ProvidedIpv6CidrBlock")
    @XmlElement(name = "ProvidedIpv6CidrBlock")
    protected Boolean providedIpv6CidrBlock;

    @Expose
    @SerializedName("Ipv6CidrBlockAssociationSet")
    @XmlElementWrapper(name = "Ipv6CidrBlockAssociationSet")
    @XmlElement(name = "item")
    protected List<VpcIpv6CidrBlockAssociation> ipv6Ciders;

    @Expose
    @SerializedName("CenId")
    @XmlElement(name = "CenId")
    protected String cenId;

    @Expose
    @SerializedName("CenAccountId")
    @XmlElement(name = "CenAccountId")
    protected String cenAccountId;

    @Expose
    @SerializedName("SecondaryCidrSet")
    @XmlElementWrapper(name = "SecondaryCidrSet")
    @XmlElement(name = "item")
    protected List<SecondaryCidr> secondaryCidrSet;

    @Expose
    @SerializedName("ProductTag")
    @XmlElement(name = "ProductTag")
    protected String productTag;

}
