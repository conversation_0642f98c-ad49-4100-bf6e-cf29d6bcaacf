package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import java.util.List;

/**
 * Created by xuyaming on 2018/11/21.
 */
@Data
public abstract class CommonVnet {
    @Expose
    @SerializedName("CreateTime")
    @XmlElement(name = "CreateTime")
    protected String create_time;

    @Expose
    @SerializedName("SubnetId")
    @XmlElement(name = "SubnetId")
    protected String uuid;

    @Expose
    @SerializedName("VpcId")
    @XmlElement(name = "VpcId")
    protected String domain_uuid;

    @Expose
    @SerializedName("SubnetName")
    @XmlElement(name = "SubnetName")
    protected String name;

    @Expose
    @SerializedName("SubnetType")
    @XmlElement(name = "SubnetType")
    protected String type;

    @Expose
    @SerializedName("CidrBlock")
    @XmlElement(name = "CidrBlock")
    protected String cidr;

    @Expose
    @SerializedName("DhcpIpFrom")
    @XmlElement(name = "DhcpIpFrom")
    protected String dhcp_ip_start;

    @Expose
    @SerializedName("DhcpIpTo")
    @XmlElement(name = "DhcpIpTo")
    protected String dhcp_ip_end;

    @Expose
    @SerializedName("NetworkAclId")
    @XmlElement(name = "NetworkAclId")
    protected String acl_uuid;

    @Expose
    @SerializedName("Dns1")
    @XmlElement(name = "Dns1")
    protected String dns1;

    @Expose
    @SerializedName("Dns2")
    @XmlElement(name = "Dns2")
    protected String dns2;

    @Expose
    @SerializedName("GatewayIp")
    @XmlElement(name = "GatewayIp")
    protected String gateway_ip;

    @Expose
    @SerializedName("NatId")
    @XmlElement(name = "NatId")
    protected String nat_uuid;

    @Expose
    @SerializedName("AvailabilityZoneName")
    @XmlElement(name = "AvailabilityZoneName")
    protected String availabilityZoneName;

    @Expose
    @SerializedName("AvailableIpNumber")
    @XmlElement(name = "AvailableIpNumber")
    protected String availableIpNumber;

    @Expose
    @SerializedName("RandomAvailableIpNum")
    @XmlElement(name = "RandomAvailableIpNum")
    protected String randomAvailableIpNum;

    @Expose
    @SerializedName("AvailbleIPNumber")
    @XmlElement(name = "AvailbleIPNumber")
    protected String availbleIPNumber;

    @Expose
    @SerializedName("RouteTableId")
    @XmlElement(name = "RouteTableId")
    protected String routeTableId;

    @Expose
    @SerializedName("ProvidedIpv6CidrBlock")
    @XmlElement(name = "ProvidedIpv6CidrBlock")
    protected Boolean providedIpv6CidrBlock;

    @Expose
    @SerializedName("Ipv6CidrBlockAssociationSet")
    @XmlElementWrapper(name = "Ipv6CidrBlockAssociationSet")
    @XmlElement(name = "item")
    protected List<VpcIpv6CidrBlockAssociation> ipv6Ciders;

    @Expose
    @SerializedName("SecondaryCidrId")
    @XmlElement(name = "SecondaryCidrId")
    protected String secondaryCidrId;

    @Expose
    @SerializedName("ProductTag")
    @XmlElement(name = "ProductTag")
    protected String productTag;

    @Expose
    @SerializedName("VisitInternet")
    @XmlElement(name = "VisitInternet")
    protected Boolean visitInternet;
}
