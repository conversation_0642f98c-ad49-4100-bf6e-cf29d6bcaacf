package com.ksyun.cfwapi.vpcapi.domain;

import lombok.Data;

import java.io.Serializable;

@Data
public class CreateCfwLBNtrParam implements Serializable {
    /**
     * 防火墙lb名称（必填项，不可为空）
     */
     private String name;
    /**
     * 防火墙lb描述
     */
     private String description;
    /**
     * 防护墙开关
     */
     private Integer admin_state_up;
    /**
     * 防火墙限速入速率（单位：Mbps）
     */
     private Integer rate_in;
    /**
     * 防火墙限速出速率（单位：Mbps）
     */
     private Integer rate_out;
    /**
     * 项目id
     */
    private String project_id;
}
