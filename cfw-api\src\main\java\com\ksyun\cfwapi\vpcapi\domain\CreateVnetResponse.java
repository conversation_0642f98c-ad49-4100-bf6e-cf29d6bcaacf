package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@ToString
@Data
@NoArgsConstructor
public class CreateVnetResponse {
	@Expose
	@SerializedName("RequestId")
	private String request_id;

	@Expose
	@SerializedName("Subnet")
	private Vnet vnet;

	@Setter
	public static class Vnet extends CommonVnet {
	}
}
