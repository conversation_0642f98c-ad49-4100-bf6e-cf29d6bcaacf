package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.xml.bind.annotation.*;
import java.util.List;

@XmlRootElement(name = "DescribeVpcsResponse")
@XmlAccessorType(XmlAccessType.NONE)
@Setter
@ToString
@NoArgsConstructor
public class QueryDomainsResponse {

	@Expose
	@SerializedName("RequestId")
	private String request_id;

	@Expose
	@SerializedName("VpcSet")
	private List<Domain> domains;

	@Expose
	@SerializedName("NextToken")
	private String nextToken;

	@Expose
	@SerializedName("TotalCount")
	private Integer totalCount;

	@XmlElementWrapper(name = "VpcSet")
	@XmlElement(name = "item")
	public List<Domain> getDomains() {
		return domains;
	}

	@XmlElement(name = "RequestId")
	public String getRequest_id() {
		return request_id;
	}

	@XmlElement(name = "TotalCount")
	public Integer getTotalCount() {
		return totalCount;
	}

	@ToString
	@Setter
	@NoArgsConstructor
	public static class Domain extends CommonDomain {

	}

}
