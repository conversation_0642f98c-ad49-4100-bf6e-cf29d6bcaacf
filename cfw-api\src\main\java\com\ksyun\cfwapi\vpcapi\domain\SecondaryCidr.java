package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlElement;

/**
 * @author: hueason
 * @date: 2021/8/3 16:10
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecondaryCidr {
    @Expose
    @SerializedName("SecondaryCidrId")
    @XmlElement(name = "SecondaryCidrId")
    private String secondaryCidrId;

    @Expose
    @SerializedName("Cidr")
    @XmlElement(name = "Cidr")
    private String cidr;

    @Expose
    @SerializedName("Type")
    @XmlElement(name = "Type")
    private String type;
}
