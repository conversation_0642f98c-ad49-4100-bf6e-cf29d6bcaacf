package com.ksyun.cfwapi.vpcapi.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlElement;

/**
 * Created by xuyaming on 2018/11/19.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VpcIpv6CidrBlockAssociation {
    @Expose
    @SerializedName("Ipv6CidrBlock")
    @XmlElement(name="Ipv6CidrBlock")
    private String ipv6CidrBlock;
}
