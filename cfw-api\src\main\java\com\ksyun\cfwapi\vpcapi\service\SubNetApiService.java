package com.ksyun.cfwapi.vpcapi.service;

import com.ksyun.cfwapi.vpcapi.VpcOpenApiClient;
import com.ksyun.cfwcore.constants.HeaderConstant;
import com.ksyun.cfwapi.domain.fw.CreateSubnetResponse;
import com.ksyun.cfwapi.vpcapi.domain.CreateVnetResponse;
import com.ksyun.cfwapi.vpcapi.domain.QueryDomainsResponse;
import com.ksyun.cfwapi.utils.SubnetUtil;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class SubNetApiService {
    @Autowired
    private VpcOpenApiClient vpcOpenApiClient;
    /**
     * 调用vpc-api创建子网
     *
     * @param cidr
     * @param vpcId
     * @throws Exception
     */
    public CreateSubnetResponse createSubNet(String cidr, String vpcId, ProxyAuth auth) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(HeaderConstant.ACTION, Collections.singletonList("CreateSubnet"));
        parameters.put(HeaderConstant.SERVICE, Collections.singletonList("vpc"));
        parameters.put("VpcId", vpcId);
        parameters.put("CidrBlock", cidr);
        parameters.put("SubnetType", "Normal");
        parameters.put("GatewayIp", SubnetUtil.getGateWayIp(cidr));
        parameters.put("ProvidedIpv6CidrBlock", "false");

        CreateVnetResponse createVnetResponse = vpcOpenApiClient.get(auth, parameters, CreateVnetResponse.class);
        CreateSubnetResponse result = new CreateSubnetResponse();
        result.setSubCidr(cidr);

        if (createVnetResponse == null) {
            log.error("创建子网:{}失败", cidr);
            result.setResult(false);
            return result;
        }
        result.setSubNetId(createVnetResponse.getVnet().getUuid());
        result.setResult(true);
        return result;
    }

    /**
     * 调用vpc-api查询vpc信息
     */
    public QueryDomainsResponse describeVpc(String vpcId, ProxyAuth auth) {

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(HeaderConstant.ACTION, Collections.singletonList("DescribeVpcs"));
        parameters.put(HeaderConstant.SERVICE, Collections.singletonList("vpc"));
        parameters.put("VpcId.1", Collections.singletonList(vpcId));
        return vpcOpenApiClient.get(auth, parameters, QueryDomainsResponse.class);
    }
}
