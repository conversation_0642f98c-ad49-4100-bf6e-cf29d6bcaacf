<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_HOME">../logs/cfw-parent/cfw-api</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %-6p [%c{1}:%L] [%t] : %m%n" />
        </Console>

        <RollingRandomAccessFile name="cfw-api"
                                 fileName="${LOG_HOME}/cfw-api-debug.log"
                                 filePattern="${LOG_HOME}/cfw-api-debug.log.%d{yyyy-MM-dd}">
            <PatternLayout charset="utf-8">
                <Pattern>%d{yyyy-MM-dd HH:mm:ss,SSS} %-6p [%X{RequestId}] [%X{Account}] [%X{Action}] [%X{Region}] [%X{Source}] [%c{1}:%L] [%t] : %m%n</Pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="90"/>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile name="cfw-api-error"
                                 fileName="${LOG_HOME}/errorLogs/cfw-api-error.log"
                                 filePattern="${LOG_HOME}/errorLogs/cfw-api-error.log.%d{yyyy-MM-dd}">
            <NetworkLayout lineInfo="true" methodInfo="true" dateFormat="YYYY-MM-dd HH:mm:ss" collectionName="222"
                           single="true"
                           propertys="Address,AccountId,UserId,UserType,Region,Service,Version,Action,Url"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1"
                                           modulate="true"/>
            </Policies>
            <DefaultRolloverStrategy max="90"/>
        </RollingRandomAccessFile>

    </Appenders>

    <Loggers>
        <logger name="com.ksyun" level="debug" additivity="false">
            <AppenderRef ref="cfw-api"  />
            <AppenderRef ref="Console" />
        </logger>
        <logger name="com.ksyun.cfwcore.log.ScheduleWarnLog" level="debug" additivity="false">
            <AppenderRef ref="cfw-api-error"/>
        </logger>
        <logger name ="com.ksyun.common.http.ObjectPlaceholderResolver" level="OFF"/>
    </Loggers>
</Configuration>