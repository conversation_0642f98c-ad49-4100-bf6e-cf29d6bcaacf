
#dns
#openstack resource
InvalidParameterValue.domainName=Domain Name Invalid
InvalidParameterValue.domainType=Domain Type Invalid
Duplicate.domain=Duplicated Domain
NotFound.domain=Domain Not Found
InUse.record=Domain Has Records
dns.ip_segment_overlap=ip segment overlap
dns.private_location_has_IP_segment=private location has IP segment,can not remove
dns.private_location_not_found=private location not found
dns.invalid_ip_segment=invalid ip_segment
dns.ip_segment_not_found=ip_segment not found

InvalidParameterValue.recordName=Record Name Invalid
InvalidParameterValue.recordGeo=Record Line Invalid
InvalidParameterValue.recordType=Record Type Invalid
InvalidParameterValue.recordData=Record Data Invalid
InvalidParameterValue.recordTtl=Record TTL Invalid
InvalidParameterValue.recordIp=Record Ip Format Invalid
InvalidParameterValue.recordIpv6=Record Ipv6 Format Invalid
InvalidParameterValue.recordWeight=Record Weight Invalid
InvalidParameterValue.recordCnameFormat=CNAME Record Format Invalid
InvalidParameterValue.recordMxFormat=MX Record Format Invalid
InvalidParameterValue.recordNsFormat=NS Record Format Invalid
InvalidParameterValue.recordTxtFormat=TXT Record Format Invalid
InvalidParameterValue.changeType=Only A or AX Can Change Record Type
InvalidParameterValue.domain_already_active=This domain is already actived
InvalidParameterValue.domain_already_inactive=This domain is already suspended
InvalidParameterValue.record_already_active=This domain record is already actived
InvalidParameterValue.record_already_inactive=This domain record is already suspended

NotFound.record=Record Not Found
Duplicate.record=Duplicated Record
Duplicate.cnameRecord=Duplicated CNAME Record
Conflict.recordType=Conflict Record Type
CheckFailed.line=Line dependency check fails, it is necessary to ensure that the records in the default line
NotFound.geo=Geoline Not Found
InOperation.domain_processing=Domain Operating\uFFFD\uFFFDTry Later
SystemError.sys=System Error, Please Try Later
Conflict.recordA=A and AX/CNAME/NS can not coexist
Conflict.recordAX=AX and A/CNAME/NS can not coexist
Conflict.recordAAAA=AAAA and CNAME/NS can not coexist
Conflict.recordCname=CNAME and A/AX/AAAA/MX/TXT/NS can not coexist
Conflict.recordNs=NS can not coexist with other type
Conflict.recordMx=MX and CNAME/NS can not coexist
Conflict.recordTxt=TXT and CNAME/NS can not coexist
