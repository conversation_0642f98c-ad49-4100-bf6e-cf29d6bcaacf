typeMismatch.java.lang.Integer=InvalidIntegerType
typeMismatch.java.lang.Boolean=InvalidBooleanType
InvalidIntegerType={0} must be Integer value
InvalidBooleanType={0} must be true or false

InvalidField={0} is invalid,{0} only allows letters, numbers, Chinese ,\u3001,(,), - ,_and the max length of Name is 128 digits

InvalidParameter.InstanceIdMalformedIndex=the Parameter is invalid,.N must be number
InvalidParameter.InstanceIdSameKey=the Parameter is invalid,the Parameter can not be same
InvalidParameter.InstanceIdMalformedPoint=the Parameter is invalid,refer Id.num,must has .
InvalidParameter.InstanceIdMaxlengthIndex=the Parameter is invalid,.N length <= 4
InvalidParameter.InstanceIdRangeIndex=the Parameter is invalid,.N must <= 1000 and >=1
InvalidParameterValue.InstanceId=the Id value is invalid
InvalidParameter.FilterPrefix=the Parameter is invalid,the Prefix is not the required parameters for the request
InvalidParameter.FilterMalformedIndex=the Parameter is invalid,refer Filter.N.Name and Filter.N.Value.N,.N can not be empty and must be number
InvalidParameter.FilterMaxIndexDigits=the Parameter is invalid,,refer Filter.N.Name and Filter.N.Value.N,the digits of N must be <=4
InvalidParameter.FilterIndexRange=the Parameter is invalid,refer Filter.N.Name and Filter.N.Value.N,N must be <=1000 and >=1
InvalidParameter.FilterNameSameKey=the Parameter is invalid,refer Filter.N.Name,the Filter.N.Name can not be same
InvalidParameterValue.FilterNameSameValue=the Parameter is invalid,refer the value of Filter.N.Name,the value can not be same
InvalidParameter.FilterMalformedNameParts=the Parameter is invalid,refer Filter.N.Name,the parameter at least must be 3 parts
InvalidParameterValue.NameNotEmptyValue=the Parameter is invalid,the parameter value of Filter.N.Name is not empty
InvalidParameter.FilterValueEndPartsMalformed=the Parameter is invalid,refer Filter.N.Value.M,the M must be number
InvalidParameter.FilterValueEndPartsDigits=the Parameter is invalid,,refer Filter.N.Value.M,the digits of M must be <=4
InvalidParameter.FilterValueEndPartsRange=the Parameter is invalid,refer Filter.N.Value.M,the value of M must be <=1000 and >=1
InvalidParameterValue.FilterValueSameValue=the Parameter is invalid,refer Filter.N.Value.M,the value of Filter.N.Value.M can not be same
InvalidParameterValue.FilterValueNotEmptyValue=the value of Filter.N.Value.M can not be empty
InvalidParameter.FilterValueSameKey=the Filter.N.Value.M can not be same
InvalidParameter.FilterNameMatch=the parameter have no value to match Filter.N.Name
InvalidParameter.FilterValueMatch=the parameter have no value to match Filter.N.Value.M
InvalidParameter.KeyMatch=the Parameter is invalid,Please check the input parameter [{0}]
InvalidParameter.FilterMalformedNoNameValue=the parameter do not contain 'Name' and 'Value',refer Filter.N.Value.M and Filter.N.Name
RepeatNum={0} is repeat
EmptyField={0} can not be empty
HeaderEmptyField = Http Header [{0}] can not be empty
InvalidAccountId = Http Header [{0}] must be number
InvalidId={0} is invalid,{0} only allows letters, numbers, - and the length of ID must be 36 digits
InvalidName={0} is invalid,{0} only allows letters, numbers, Chinese, -_\u3001/.() and the maximum length of the name is 128 bits
InvalidCidr={0} is invalid
InvalidValue={0} is invalid
FieldInvalid={0} is invalid
InvalidRegion={0} region is invalid
NatVersionInvalid=Nat version is invalid, only allows 1,2
IpAreaCase=IP address can not be set aside in network {0}
ReservedCidrMatch=IP address can not be set aside in network {0}
InvalidIp={0} is invalid,{0} must be a valid Ip
NotFound={0} not found
AlreadyExists={0} has already exists
NotMatch={0} not match
InvalidPort={0} must be  1 - 65535
PortMustBeAny={0} must be Any
DependencyViolation=The specified object has dependent resources. A number of resources in a VPC may have dependent resources, which prevent you from deleting or detaching them. Remove the dependencies first, then retry your request. For example, this error occurs if you try to delete a VPC that is in use by another route.
VpcTypeInvalid=Vpc Type:IsDefault only allows true or false 
NetworkAclNotAttachedSubnet=the Subnet not Associate with the NetworkAcl
CannotDelete=You cannot delete the 'default' security group in your VPC
CheckReplaceSecurityGroupRuleDuplic=replace security grouprule duplic
CheckParamAndSecurityGroupRule=check param and security grouprule duplic
RuleNumberInvalid=the RuleNumber must be in between 1 and 32766
DirectionInvalid=the Direction must be in or out
ProtocolInvalid=Protocol must be udp,tcp,ip or icmp
RuleActionInvalid=RuleAction must be allow or deny
SlbDescriptionInvalidField=The prompt length is not more than 128 bits, and only letters, Chinese, numbers, - , _ , , , / , \ , ( \u3001)
SubnetTypeInvalid=SubnetType must be Reserve or Normal
NetworkAclRuleRuleNumberRepeated=the RuleNumber of NetworkAclRule can not be repeated
###route
RouteTypeInvalid=RouteType must be InternetGateway\u3001Tunnel\u3001Host\u3001Peering\u3001DirectConnect\u3001Vpn\u3001Host-ext\u3001HaVip\u3001VpnGateway
RouteTypeNotSupport=The trust VPC does not support creating this type of route
RouteTypeBindHaVipMasterNetworkInterfaceNotFound=When binding the highly available virtual IP, the primary network card ID (haVipMasterNetworkInterfaceId) bound to the highly available virtual IP needs to be passed in at the same time. Please check the input parameter
###peering
PeerRegionInvalid=PeerRegion is invalid
####natpool
SubnetInstanceAlreadyBoundByAnotherNat=The host {0} under this subnet is already bound to another NAT
NatTypeInvalid=NatType only allows public or private
NatMappingRangeInvalid=NatMode only allows Vpc or Subnet
BandWidthInvalid=BandWidth must be in 1 - 15000Mbps,Each customer's bandwidth range is subject to quota control
ModifyPeeringMinBandWidthInvalid=Modify Peering BandWidth Min Equal 1
CreatePeeringMinBandWidthInvalid=Create Peering BandWidth Min Equal 1
NatpoolBandWidthInvalid=BandWidth must be in 1 - 20000Mbps,Each customer's bandwidth range is subject to quota control
EipBandWidthInvalid=BandWidth must be in 1 - 100000Mbps,Each customer's bandwidth range is subject to quota control
InBandWidthInvalid=BandWidth must be in 1 - 15000Mbps,Each customer's bandwidth range is subject to quota control
VifBandWidthInvalid=BandWidth must be in 1 - 100000Mbps
VifInBandWidthInvalid=In BandWidth must be in 1 - 100000Mbps
VifSendPPSInvalid=SendPPS must be in 1 - 100000000
VifRecvPPSInvalid=RecvPPS must be in 1 - 100000000
NatBandWidthInvalid=BandWidth must be in 1 - 25000Mbps,Each customer's bandwidth range is subject to quota control
NatIpNumberInvalid=NatIpNumber must be in 1 - 20,Each customer's NatIpNumber range is subject to quota control
ChangeTypeNotSupport=ChangeType Hourly Not Support
BandWidthNotChange=BandWidth Not Change
BandWidthNotLarger=BandWidth Not Larger than before
ChargeTypeInvalid.Nat=ChargeType only allows Monthly or Daily
ChargeTypeInvalid.ApplicationSlb=ChargeType only allows Monthly or Daily
####eip
ChargeTypeInvalid=ChargeType only allows Monthly,Peak,Daily,Hourly
PurchaseTimeInvalid=PurchaseTime must be in 1 - 36 and be int value
PublicIpInvalid=PublicIp must be a valid Ip
InstanceTypeInvalid=InstanceType must be Ipfwd, Slb or Portfwd or Nat
PublicIpNotAssociatedPort=PublicIp not associated with any Port
InstanceNotMactchNetworkInterface=The Instance not exists or The Instance not match NetworkInterface
NextTokenInvalid=NextTokenInvalid is invalid
MaxResultsInvalid=MaxResults must be int value and from 5 to 1000.
MaxResultsOneHundredInvalid=MaxResults must be int value and from 5 to 100.
DhcpMaxResultsInvalid=MaxResults must be int value and from 5 to 252.
RepeatedAssociatingAddress.Address=the AllocationId  has be Associated with Slb or Ipfwd
RepeatedAssociatingAddress.Slb=Load balancing has been bound to elastic IP addresses
EipHasJoinInBandWidthShare=the eip have join in BandWidthShare, not allow to modify BandWidth
EipBindingLbRs=the eip has bind a slb HTTP/HTTPS listener RealServer,not allow to Disassociate

##### lb
LoadBalancerStateInvalid=LoadBalancerState only allows start,stop
LoadBalancerTypeInvalid=type only allows public,internal
AssociateStateInvalid=State only allows associate,disassociate
ListenerStateInvalid=ListenerState only allows start,stop
ListenerProtocolInvalid=ListenerProtocol only allows TCP,HTTP,HTTPS
ListenerMethodlInvalid=Only TCP,UDP are supported in master-slave mode or QUIC_CID only support UDP
RegionNotSupportMasterSlave={0} Region not support master-slave mode
ListenerMasterSlaveTypeInvalid=MasterSlaveType only allows Master,Slave
BatchRegisterInstancesNotSupportMasterSlave= The batch register instances is not supported in master-slave
BatchRegisterInstancesOnlySupportInstanceType=The batch register instances only support host  and DirectConnection , not support {0}
ListenerMasterSlaveNotSupportChange=master-slave rules are not allowed to be interchanged with other rules
ApplicationListenerProtocolInvalid=ListenerProtocol only allows HTTP,HTTPS
Lb7ListenerPortNotSupport22And1999And111Between65501And65535=The load balance listening port 22,1999,111,65501-65535 has been occupied and cannot be created
Lb7ListenerPortNotSupport22And179And111Between65501And65535=The load balance listening port 22,179,111,65501-65535 has been occupied and cannot be created
LbListenerPortNotSupportBetween65501And65535=The load balance listening port 65501-65535 has been occupied and cannot be created
LbMethodInvalid=LbMethod only allows RoundRobin,LeastConnections,MasterSlave,QUIC_CID,IPHash
LbMethodEmpty=LbMethod is not empty
SessionStateInvalid=SessionState only allows start,stop
SessionPersistencePeriodInvalid=SessionPersistencePeriod must be  1 - 86400
SessionPersistencePeriodEmpty=SessionPersistencePeriod is null,if use default,do not add SessionPersistencePeriod in param
CookieTypeInvalid=CookieType only allows ImplantCookie,RewriteCookie
LbKindInvalid=LbKind only allows NAT,FNAT
HealthyThresholdInvalid=HealthyThreshold must be 1 - 10
IntervalInvalid=Interval must be 1 - 1000
TimeoutInvalid=Timeout must be 1 - 3600
UnhealthyThresholdInvalid=UnhealthyThreshold must be 1 - 10
HealthCheckNotExistsUnderListener=the HealthCheck not exists under the Listener
HealthCheckHttpMethodInvalid=HttpMethod must be GET or HEAD
HealthCheckStateInvalid=HealthCheckState must be start or stop
UrlPathInvalid=the length of UrlPath must be 250 digits
HealthCheckConnectPortInvalid=The HealthCheckPort value must be between 1 and 65535
HealthCheckProtocolInvalid=HealthProtocol only allows TCP,ICMP,UDP,HTTP
HealthCheckProtocolTcpNotMatch=This type of LB does not support {0} HealthCheck
PathInvalid=URL specification: The length is limited to 80 characters. Only characters such as letters, numbers, \u2018-\u2019, \u2018/\u2019, \u2018.\u2019, \u2018?\u2019, \u2018=\u2019 can be used; the URL must start with /.
RuleUrlPathInvalid=the length of UrlPath must be 80 digits
RealServerGroupProtocolInvalid=Protocol only support one of HTTP|TCP|UDP
MirrorBackendServerGroupOnlySupportHttp=The Protocol of Mirror BackendServerGroup only support HTTP
ListenerBindTypeInvalid=BindType only support one of RealServer|BackendServerGroup
LbListenerHttpProtocolInvalid=The listener HTTP protocol version is illegal.
LbListenerHttpProtocolMushHttpOrHttps=The listener protocol that supports backward forwarding of the HTTP protocol version must be HTTP or HTTPS.
ListenerTslPolicyInvalid=TLS security policy value is illegal
LbListenerTslPolicyMustHttps=The listener protocol that supports the TSL security policy must be HTTPS
LbListenerHttp2MustHttps=Only HTTPS listeners can enable HTTP/2 features
RealServerTypeInvalid=RealServerType must be host or DirectConnectGateway
WeightInvalid=Weight must be 0 - 255
WeightInvalidMX=Weight must be 1 - 50
WeightInvalidAX=Weight must be 1 - 100
WeightInvalidA=Weight must be 0 - 100
WeightIsNotEmpty=Weight not support
WeightIsEmpty=Weight cannot be empty
RealServerTypeMissMatchListenerType=RealServerTypeMissMatchListenerType miss match Listener protocol HTTP
RealServerIpNotInEip=RealServerIp not in eip
InvalidTunnelMode=only TunnelMode is Subnet can operate
InvalidProtocol=Protocol must be TCP or UDP
InvalidDnatProtocol=Protocol must be TCP\u3001UDP or Any
LoadBalanceNotExists=LoadBalancer <<< {0} >>> not found
LbAclNotFound=ACL <<< {0} >>> not found
LbListenerNotFound=Listener <<< {0} >>> not found
AvailabilityZoneInvalid=Availability zone <<< {0} >>> not found.
RegionNotSupportValue=This Region is not support <<< {0} >>>.
ListenerNotSupportHealthProtocol=This Listener is not support HealthProtocol <<< {0} >>>.
UdpHealthProtocolNotSupportModify=This UDP listener health check protocol is not support Modify.
###subnet
InvalidDNS=0.0.0.0 is invalid DNS
##payment
PaymentFailure=pay failed
Payment.CreateProductFailed=create product failed
Payment.CreateOrderFailed=create order failed
Payment.ProductNotFound=product not found
Payment.OrderNotFound=order not found
Payment.NotifySubOrderFailed=notify subOrder failed
MissingParameter.TunnelId=TunnelId not empty when RouteType is Tunnel
MissingParameter.vpnGatewayId=vpnGatewayId not empty when RouteType is VpnGateway
MissingParameter.InstanceId=InstanceId not empty when RouteType is Kec
MissingParameter.VpcPeeringConnectionId=VpcPeeringConnectionId not empty when RouteType is Peering
MissingParameter.DirectConnectGatewayId=DirectConnectGatewayId not empty when RouteType is DirectConnect

QuotaIdOutRange=<<< {0} >>> Quota exceeded Max
BandWidthShareBindEip=BandWidthShare exist binding EIP,Please unbind EIP and try again
BandWidthShareChargeTypeBind=BandWidthShare ChargeType not equal Eip ChargeType
BandWidthShareHasBindThisEip=Eip has bind other BandWidthShare
BandWidthShareNameTooLong=BandWidthShare Name is too long or name is invalid
ChargeTypeInvalid.Bws=BandWidthShare chargeType only support Daily or Peak or TrafficMonthly
BandWidthShareIdTooMuch=BandWidthShare query id is too much Max is 20
BandWidthShareHasNotBindThisEip=Eip has not band this BandWidthShare,Can not unbind
BandWidthShareForPrePaidByMonthNotAllowDelete=PrePaidByMonth BandWidthShare is not allowed to be deleted
BandWidthShareForPrePaidByMonthNotAllowReduce=PrePaidByMonth BandWidthShare is not allowed reduce BandWidth
BandWidthShareMonthlyBandWidthInvalid=PrePaidByMonth BandWidthShare BandWidth must > 10

#tag
DeleteTagFailed=the respose which delete tag is null
CreateTagFailed=Failed to create tag
QueryResourceByTagFailed=Failed to query resource by tag
ReplaceTagsFailed=Failed to bind tag
CreatedSuccessTagFailed=Resource <<< {0} >>> created successfully, but tag binding failed

#QUOTA
QuotaMaxCount={0}Over Quota,Max Quota is {1}
ReserveSubnet=ReserveSubnet
Subnet=Subnet
Vpc=VPC
Ipv6Vpc=IPV6 VPC
SecondaryCidrBlock=Secondary CidrBlock

#dns
InvalidDnsStatus=Modify status parameter type only support ACTIVE or SUSPEND
#quota
FullQuota.HostedZoneReachMAX=you have reach the MAX count of HostedZones
FullQuota.RecordReachMAX=you have reach the MAX count of Records
QuotaError=quota internal error

#hostedzone
InvalidParameterValue.HostedzoneNameNotEmpty=domain name can not empty
InvalidParameterValue.HostedzoneNameLengthLimit=domain name length can not exceed 255
InvalidParameterValue.HostedzoneNameInvalid=domain name Invalid
InvalidParameterValue.HostedzoneNameSegmentLengthLimit=the length of the segment in the  domain name can not exceed 63
InvalidParameterValue.HostedzoneNameStandardInvalid=The domain name level is at most 1 levels above the top level domain
InvalidParameterValue.HostedzoneIdNotEmpty=hostedzone id is not empty
InvalidParameterValue.HostedzoneNameNotContainsChinese=hostedzone name not contains Chinese
InvalidParameterValue.invalid_lock_or_unlock_type=invalid lock or unlock type
ZoneNameExistedHasInstance=Please delete all instances on the intranet DNS1.0 to create a zone with the same name {0}.

#record
InvalidParameterValue.RecordNameNotEmpty=record name is not empty
InvalidParameterValue.RecordNameNotContainsChinese=record name not contains Chinese
InvalidParameterValue.RecordIdNotEmpty=record id not empty
InvalidParameterValue.RecordNameNotwithUnderline=Record name can not have '_'
InvalidParameterValue.RecordNameSegmentLimit=the segment of record name can not more than {0}
InvalidParameterValue.RecordNameNotwithMidlineAtStartOrEnd='-' can not appear at the beginning and end of each segment
InvalidParameterValue.RecordNameStartWithSignInvalid=Pan-domain name must be the beginning, * must be followed by point, similar to '* .a'
InvalidParameterValue.RecordNameSegmentNumberLimit=Pan-domain name resolution up to Level 2
InvalidParameterValue.RecordNameInvalidWithNS=record name is invalid
InvalidParameterValue.RecordValueNotContainsChinese=record value can not contain Chinese or full-width symbols
InvalidParameterValue.RecordTypeNotEmpty=record type is not empty
InvalidParameterValue.GeoLocationIdNotEmpty=GeoLocation id is not empty
InvalidParameterValue.RecordValueNotIP=Ip is invalid
InvalidParameterValue.RecordValueTooLong=value must less then 255
InvalidParameterValue.RecordValueNotIPV6=IPV6 is invalid
InvalidParameterValue.RecordValueNotDomain=domain name is invalid
InvalidParameterValue.RecordTypeInvalid= RecordType is invalid
InvalidParameterValue.RecordTypeOnlyAOrAX=RecordType must A or AX
InvalidParameterValue.RecordTargetTypeOnlyAOrAX=RecordTargetType must A or AX
InvalidParameterValue.RecordValueNotHttpUrl=Record value must http url
InvalidParameterValue.ResourceRecordNameTooMuch=Only one record name can be entered
InvalidParameterValue.ResourceRecordValueTooMuch=Only one record value is allowed
#param
InvalidParameterValue.TypeEmpty=type is not empt
InvalidParameterValue.TtlNull=ttl is not null
InvalidParameterValue.TTLMixValue=ttl is >= 60
InvalidParameterValue.TTLMaxValue=ttl is <= 604800
InvalidParameterValue.ZoneTTLMaxValue=ttl is <= 86400
InvalidParameterValue.GeoEmpty=geo id is not empty
InvalidParameterValue.ValueEmpty= the value of recrod is not empty
InvalidParameterValue.ValueLengthMoreThan255=the length of the value not more than 255

#description
DescriptionTooLong=the length of description is over the limit 128
NameTooLong==the length of {0} is over the limit 128
InstanceCanNotUnSuspend=instance status is not open,Can not UnSuspend

#Peering
ChargeTypeInvalid.Peering=ChargeTypeInvalid
PeeringChargeAndSubOrderIdEmpty=PeeringChargeAndSubOrderIdEmpty
RegionNotSupport={0} Region {1} not support
VpcIdCheckError={0} value {1} check failure
PeeringIsExist=PeeringIsExist
PeeringCheckError=PeeringCheckError
TunnelsCheckError=TunnelsCheckError
VpcCidrCover=VpcCidrCover
VpcTunnelsCidrCover=VpcTunnelsCidrCover
PeerVpcTunnelsCidrCover=PeerVpcTunnelsCidrCover
PeeringIdMustRemoteId=PeeringIdMustRemoteId

SourceIsNotSupport=api not support source
BatchSizeIsTooMax=batch size is too max
InstanceIsInProcess=instance is in process
SecurityGroupMaasPermission=You do not have permission to create SecurityGroup. Please contact Kingsoft Cloud to activate 
VPNGatewayMaasPermission=You do not have permission to create VPNGateway. Please contact Kingsoft Cloud to activate 
DCGatewayMaasPermission=You do not have permission to create DirectConnectGateway. Please contact Kingsoft Cloud to activate 
PeerMaasPermission=You do not have permission to create peering. Please contact Kingsoft Cloud to activate
VPCMaasPermission=You do not have permission to create the type of VPC. Please contact Kingsoft Cloud to activate
EipPoolMaasPermission=You do not have permission to create EIPPOOL. Please contact Kingsoft Cloud to activate 
VPCMaasNotSupportIpv6=The trust VPC does not support the use of Ipv6
InstanceMustInProcess=instance must in process

PhysicalVifSGRuleTooMax=Physical vif sg rule too max

PeeringInstanceIdNotInTrade=Peering instance must not in trade system
PeeringIdMustLocalId=Peering id type must local
PeeringStatusMustActive=Peering status must active

DirectConnectNameInvalid=DirectConnectNameInvalid
DirectConnectTypeInvalid=DirectConnectTypeInvalid
ConstructionModeInvalid=ConstructionModeInvalid
DirectConnectProviderInvalid=DirectConnectProviderInvalid
AccessLocationInvalid=AccessLocationInvalid
DirectConnectIdInvalid=DirectConnectIdInvalid
DirectConnectInterfaceNameInvalid=DirectConnectInterfaceNameInvalid
DirectConnectInterfaceIdInvalid=DirectConnectInterfaceIdInvalid
DirectConnectGatewayNameInvalid=DirectConnectGatewayNameInvalid
DirectConnectGatewayIdInvalid=DirectConnectGatewayIdInvalid
VpnTunnelIdGatewayInvalid=VpnTunnelIdGatewayInvalid
DirectConnectGatewayStatusError=DirectConnectGatewayStatusError
DirectConnectGatewayRemoteCidrIsExist=DirectConnectGatewayRemoteCidrIsExist
DirectConnectGatewayIsNotFound=DirectConnectGatewayIsNotFound
DcNatTypeInvalid= DirectConnectGatewayNat type only support 'snat' and 'dnat'
DcNatUnbindSwitchInterfaceInvalid=Please clear the DirectConnectGateway [{0}] NatIP  before unbinding the dedicated line channel operation
DirectConnectBgpRoutTypeInvalid=DirectConnectBgpRoutTypeInvalid
DirectConnectReliabilityMethodInvalid=DirectConnectReliabilityMethodInvalid
DirectConnectBgpRoutAsnInvalid=DirectConnectBgpRoutAsnInvalid
DirectConnectBgpRoutAsnNotEmpty=DirectConnectBgpRoutAsnNotEmpty
DirectConnectVlanInvalid=The Vlan value of the private line channel is illegal. Please enter the Vlan value between 2 and 4000.
DirectConnectHaVlanNotEmpty=Private line dual line high availability mode VLANID cannot be empty
DirectConnectIdAndHaNotSame=Dedicated line two-line high availability mode physical line cannot be the same
DirectConnectVlanAndHaMushSame=Dedicated line two-line high-availability mode physical line does support VLAN must be the same
DirectConnectVlanNotSupport=This physical line does not support VLAN
DirectConnectIPMultiVersionNotSupport=The same gateway does not support binding multiple IP type link channels
DirectConnectCustomerIpv6PeerIpInvalid=Client side interconnection IPv6 Peer IP format is illegal
DirectConnectCustomerIpv4PeerIpInvalid=Client side interconnection IPv4 Peer IP format is illegal
DirectConnectLocalIpv6PeerIpInvalid=KingSoft side interconnection IPv6 Peer IP format is illegal
DirectConnectLocalIpv4PeerIpInvalid=KingSoft side interconnection IPv4 Peer IP format is illegal
DestinationCidrBlockInvalid=The target network segment format is illegal
InputParameterFormatInvalid=The current product line calling method does not support calling productLine with this method, [{0}]. Please use the productType input method to call
ResourceTypeFormatFail=Resource type not obtained. Please confirm the parameters ProductLine [{0}] and ProductType [{1}]
RealServerInstanceTypeInvalid=The ServerGroup does not support [{0}] type

EipChargeTypeInvalid=EipChargeTypeInvalid

VpnGatewayBandWidthInvalid=BandWith must in <<< {0} >>>
VpnGatewayChargeTypeInvalid=VpnGateway ChargeType must in Monthly,Daily
VpnGatewayBandWithOnlyUpdate=VpnGateway BandWidth only support up upgrade by monthly chargeType
CustomerGatewayIdInvalid=CustomerGatewayId Invalid

CheckInstanceTradeStatus=Instance ID {0} not support operate in current chargetype
CheckInstanceStatus=Instance ID {0} not support operate in current instance status
QueryEsResourceError=Query Resource Error

LineNotSupport=Line Id <<<{0}>>> not support 
BandWidthShareEipIsTrial=BandWidthShareEipIsTrial
BandWidthShareEipIsMonthly=BandWidthShareEipIsMonthly
SubOrderIdDup=SubOrderId repeat request
SubUserIdWithoutPolicy=SubUserIdWithoutPolicy

HostHeaderNotExists=HostHeader <<< {0} >>>  not found

ProjectMemberNotExist=User is not a member of Project [{0}]
NotExistProjectId=Project [{0}] not exist

PackageNotExists=You do not have the appropriate permissions, Region[{0}], BandWidth[{1}], ChargeType[{2}]
SlbPackageNotExists=You do not have the appropriate permissions, Region[{0}]
NatPackageNotExists=You do not have the appropriate permissions, Region[{0}], BandWidth[{1}], ChargeType[{2}], NatIpNumber[{3}]
PeerPackageNotExists=You do not have the appropriate permissions, From[{0}], To[{1}], ChargeType[{2}], BandWidth[{3}]

NatInAddIpNumProcess=NatId {0} in add ipNum process
CheckDistributedLockError=Check {0} instance {1} operate lock error,Please retry or contact admin
NatIpNumberOverQuota=CreateOrUpdate Nat ip over quota {0}
NatIpNotSupportReduce=Not support reduce ip number
NatIpMustHaveOne=Nat {0} must have one IP at least
NatIpMustHaveOneUnboundDNatIpMap=Nat {0} must have one IP at least unbound DNAT IP map
NatIpNotEnabled=This IP {0} is in a disabled state. Please check the status of this IP before proceeding with the operation
DnatIpDisEnabled=The Nat Ip {0} bound to this Dnat is disabled and Dnat has not taken effect. Please start the corresponding Nat Ip before proceeding with the relevant operation
NatIpNumError=Nat IP num is not equal order ip num
NatIpIsUsedByDnat=IP {0} is used by DNAT
NatIpIsUsedBySnat=IP {0} is used by SNAT
NatPortIsUsedByDnat=NAT Port {0} is used by DNAT
NatHasNoAvailableIp=Nat has no Ip available
PrivateNatNotSupportModifyIpNum=Nat {0} type is private,not support ip num
BwsEipChargeTypeError=BandWidthShare EIP ChargeType Only Support {0}
EipBindwidthOverLimit=Pay as you go bandwidth cannot be greater than {0}
InstanceNotFound=Resource ID [{0}] Not found
InvalidStartEndIPAddr=IP address <<< {0} >>> not in cidr <<< {1} >>> range
PrivateIpAddressOnProcess= Address <<< {0} >>> in process
NatAddNumberAndNatIpBothNull= AddNumber and Natip cannot be empty at the same time
NatAddNumberAndNatIpBothNotNull= AddNumber and Natip cannot exist at the same time
NatAddNumberMoreThanQuota= AddNumber and Natip more than quota
NatVersionNotSupportBindEip=The Nat {0} Version 1.0 does not support bind EIP
NatAvailableIPLess=Nat {0} currently has no available IP/EIP for snat, all IPs are occupied by the full port dnat


PeerProviderNotExists=Unable to get supplier information, please contact operation or after sales.
ApiNotSupportRegion=Region {0} does not support this interface.
MirrorGroupApiNotSupportRegion=Region {1} does not support Mirror BackendServerGroup.

RedisSlbTimeCheck=Redis Slb Time Check

SlbCloneSwitchOff=None has the clone function permission
NatPrivateTypeNotSupport=Private Nat Not Support this Region
IPV6CidrOnlySupportNormal=Ipv6 not support physical vnet
RouteTableOnlySupportNormal=Route table only support normal vnet
Ipv6PublicIpIsExist=IPV6 Public is exist
QuotaNotAllowAttach={0} not allow attach in quota,Please contact customer service
QuotaNotAllowNotSupportedUse=This field [{0}] is not allowed to be used in the quota system. Please contact customer service to activate and use it
ServiceNotActivated=You have not activated this service usage permission. Please contact customer service
UserTagNotSupport={0} user tag not support this operation
EipOnlySupportBindTagConsole=EIP only supports the binding of the user tag to the resource of the console, the current user tag is {0}.

BindWafFailed = Binding waf failed
UnBindWafFailed = Untied waf failed
EIPIsSourceIp=The EIP has been bound to the WAF as source IP,it can't bind the WAF instance at the same time,please delete the source IP and rebind it.
HostZoneIsReserved=HostZoneName is Reserved,Please try other
BandwidthIllegal=Charge type [{0}]\uFF0Cline type [{1}]\uFF0CMinimum bandwidth  allowed to be purchased [{2}]\uFF0CThe current bandwidth [{3}]
UnlimitChargeTypeBandwidthIllegal=Charge type [{0}]\uFF0CMinimum bandwidth  allowed to be purchased [{1}]\uFF0CThe current bandwidth [{2}]


AlbSpecInvalid=LoadBalancerSpec Invalid, Effective value {0}
AlbSpecNotLarger=LoadBalancerSpec Not Larger than before, before {0}, now {1}

DhcpListIpAddressExistMustSubNetId=Query the IP address of the internal network. The IP address exists in the query. The subnet ID cannot be empty.
AllocationStatusSizeInvalid=Only support one IP status query
SubnetIpStatusAndAllocateStatusInvalid= The subnet IP status and allocation status cannot coexist and cannot be empty at the same time
SubnetIdNotEmpty=Subnet ID is not empty.
SubnetIpStatusNotEmpty=Subnet IP status is not empty.
SubnetIdSizeInvalid=Only support single-subnet IP query
ReserveSubnetNotSupportReserve=Terminal subnet reserved IP address is not supported.
SubnetNotExist=Subnet not exist.
IpAddressInvalid=Ip address invalid.

AccessLogsEnabledEmpty=accessLogsEnabled cannot be empty
AccessLogsBucketNameInvalid=Illegal bucket address
SlbNotAuthorizationKs3Log=Not authorized to access SLB official service account access ks3 permissions
OpenLogBucketNameEmpty=Bucket address cannot be empty.
OpenLbLogFailed=Failed to enable 7-layer load balancing access log.
CloseLbLogFailed=Failed to close the 7-layer load balancing access log.
BucketNameNullOpenLbLogFailed=The bucket in the log open state cannot be empty.
GetTempAkSkFaild=Failed to get temporary AKSK
AccessLogsOnlyPublicLb=Only the public network SLB is enabled to open the access log.
AccessLogsInternalOnlySeventh= Private network type SLB only supports HTTP/HTTPS type to open the access log.
VnetNotBelongThisVpc=The subnet does not belong to the VPC
SubnetNameNotSupportExactAndFuzzyQuery=Subnet names cannot support both exact and fuzzy queries.
EndpointVnetTypeError=Subnet type must be endpoint
VnetVisitInternetNotMatch=The VisitInternet of this Vif does not match its Vnet
VnetVisitInternetNotSupportEip=The not VisitInternet of this Vif does not support binding EIP
VnetVisitInternetNotSupportNat=The not VisitInternet of this subnet does not support binding nat
VifVisitInternetNotSupportNat=The not VisitInternet of this vif does not support binding nat
VisitInternetEpcNotSupportLb=The subnet to which EPC/KEC belongs is not support binding the Public Load Balance
VisitInternetEpcNotSupportLbMemberGroup= The subnet to which EPC/KEC belongs is not support binding Real Server Group
VnetNotVisitInternetNotSupportHavip=The VisitInternet of this Vif does not support creating Havip
VnetHasVPCNat=This subnet has been successfully created to prohibit public access, but the VPC it belongs to has VPC mode NAT<< {0} >>and automatic unbinding has failed. Please go to the Nat page to unbind this subnet under snat before using it again
DcGatewayStatusInvalid=DirectConnectGateway {0} Not active or in bind or version is 2.0
DcRouteIdNotFound=DirectConnectRoute {0} could not be found
DcRouteTypeNotDC=DirectConnectRoute Type Not DirectConnect, or be published
DcRouteMustStatic=DirectConnectRoute must be static
DcRouteNextHopTypeMustVpc=DirectConnectRoute nexthop must be vpc
DcRouteTypeNotVpcOrCen=DirectConnectRoute Not Cen or Vpc, or be published
CrtInvalid=Crt Invalid
CrtKeyNotTogetherUpdate=Public and private keys must be updated together
BgpDcInterfaceNotSupportNqa=The direct interface of the BGP route does not support the nqa reliability method
DcGatewayHaveInterfaces=DirectConnectGateway is bound to interfaces, Can't be deleted
DcGatewayRouteNextHopTypeInvalid=NextHoptype must be one of Vpc|Cen|DirectConnect
DcGatewayRoutePriorityInvalid=Priority must be one of 20|40|60|80|100
DCGatewayRouteCidrBlocksEmpty=The target network segment be empty
DCGatewayRouteTypeNotSupportIpv6=This next hop type does not support Ipv6
DCGatewayRouteNextHopQuantityLimit=The number of target network segments exceeds the limit
DCGatewayRouteCidrInvalid = CIDR {0} format is invalid
VpcNotSupportIpv6=The VPC where the subnet is located does not support IPv6
CreateRouteVpcNotSupportIpv6=VPC bound to the gateway does not support IPV6
VnetTypeOrHostTypeIllegal=The type of the subnet can not support IPv6
CannotEnableIPv6=No permission to enable IPv6
VpcHasDcRoute=Vpc {0} Have Some DirectConnectRoute, could not be detach
ListenerNotHttpOrHttps=Listener protocol is neither HTTP nor HTTPS
ListenerNotTcpOrUdp=Listener protocol is neither TCP nor UDP
ListenerBindTypeIsRealServer=The bindType of listener is RealServer
RealServerGroupNotMirror=BackendServerGroup not mirror type
RealServerGroupInvalid=BackendServerGroup is mirror type or alb type
ListenerAssociatedBackendServerGroup=The Listener has Associated with Other BackendServerGroup
ServerGroupParamInvalid=HealthCheckState|HealthyThreshold|Interval|Timeout|UnhealthyThreshold Any item cannot be empty
VpcHasDcRouteTableRoute=Vpc {0} Have Some Route Table DirectConnectRoute, could not be detach
ResourceTagNotSupport=Resource Tag <<< {0} >>> not support operate
ProductTypeNotSupport=Product Type <<< {0} >>> Not Support operate
ProductTagNotSupport=Special Resource Tag <<< {0} >>> not support operate
StartTimeAndEndTimeInvalid=StartTime And EndTime Invalid
EndTimeLessStartTime=EndTime Less StartTime Invalid
EndTimeSubtractStartTimeLessTen=EndTime Subtract StartTime Less Ten
EndTimeLessSystemTime=EndTime Less System Time
EndTimeSubtractStartTimeLessThreeTenDay=EndTime Subtract StartTime Three Ten

UserNotSupportInterface=User does not support this interface in this region.
DestinationCidrNotSupport=Host route does not support this cidr in this region.
ProductGroupNotSupport=This product group is not currently supported.
InstanceTypeError=Incorrect package ID or the user does not have permission for this package.
UserNotPermissThisProduct=You do not have permission for this product.
ProductTypeError=This product type does not exist under the wrong product type or entered product group.
MonthlyNotSupportTrial=The monthly billType not support trial.
SubOrderNotExist=SubOrder not exist.
InstanceResourceNotExist=Instance resource not exist.
SubOrderStatusNotModify=SubOrder status <<< {0} >>> is already final and cannot be modified.
NotThisInstanceOrder=Not a subOrder of this instance, please check and try again
ProductNotExist=Product not exist
InstanceIdEmpty=Instance id not empty.

BandwidthAndInBandwidthLimitNotNull=Bandwidth and inBandwidth limit not null

BackendServerGroupTypeInvalid=BackendServerGroupType must be Server or Mirror
BackendServerGroupInstanceTypeInvalid=Type must be Host or DirectConnect
BackendServerGroupNotAssociatedListener=BackendServerGroup is not associated with Listener
ThisActionNotSupportDirectOpt=Kead product Type Not Support this operate
CommonConfigProductTypeActionAuthConfError=The ProductTypeActionAuth configuration interface intercepts the instance ID field not exist in CommonConfig.
Ipv4VipNotSupportIpv6Rs=IPV4 Listener can not add IPV6 RealServer
DomainNotMatchError=VPC not match or Instance could not be found
Ipv6RealServerTypeMissMatchListenerType=HTTP or HTTPS Listener can not add IPV6 RealServer
InnerSlbHttpsInvalid=HTTPS listener is not supported for internal loadBalancer
MemberTypeInvalid=The real server added now is different with the existing server type, please confirm before joining
VipBandwidthInvalid=BandWidth of listener must be in 1-10000
RegionNotSupportInnerBandWidth=Region <<< {0} >>> can not support modify bandWidth of listener
PublicVipNotSupportBandWidth=Public loadbalancer can not support bandWidth
DirectConnectGatewayMemberNotSupportUdpProtocol=UDP Listener can not add DirectConnectGateway RealServer

VpnCiscoIkeEncryAlgorithmInvalid=IkeEncryAlgorithm must be one of 3des|aes|des
VpnH3cIkeEncryAlgorithmInvalid=IkeEncryAlgorithm must be one of 3des|aes|des|aes-cbc-192|aes-cbc-256|sm1-cbc-128|sm4-cbc

VpnCiscoIpsecEncryAlgorithmInvalid=IpsecEncryAlgorithm must be one of the esp-3des|esp-aes|esp-des|esp-null|esp-seal
VpnH3cIpsecEncryAlgorithmInvalid=IpsecEncryAlgorithm must be one of the esp-3des|esp-aes|esp-des|esp-null|aes-cbc-192|aes-cbc-256|sm1-cbc-128|sm4-cbc

VpnCiscoIpsecAuthenAlgorithmInvalid=IpsecAuthenAlgorithm must be one of the esp-md5-hmac|esp-sha-hmac
VpnH3cIpsecAuthenAlgorithmInvalid=IpsecAuthenAlgorithm must be one of the esp-md5-hmac|esp-sha-hmac|sha256|sha384|sha512|sm3

VpnNotFound=The VPN gateway was not found, please confirm the operation
NetworkInterfaceNotExists=NetworkInterface <<< {0} >>> not found
NetworkInterfaceAndIpNotEq= NetworkInterface <<< {0} >>> is inconsistent with BackendServerIp <<< {1} >>>
NetworkInterfaceBind=NetworkInterface has been bound to the instance, please unbind before operation
SecurityGroupIdsEmpty=The security group cannot be empty
SecurityGroupIdExceedMax=There are more than 5 security groups
ApigwCanNotBindVmOrEpc=This elastic IP does not support binding resources other than loadbalancer
EipFlowNumQuotaOut=The EIP of Pay-As-You-Go (By Traffic) and Pay-By-Traffic(Monthly) exceeds the maximum quota, Which is {0}
IpVersionInvaild=The IP version is not legal and only IPv4 or ipv6 is supported
IpVersionNotMatch=The ACL does not match the IP version of the listener
IpVersionMustSame=The IP version must same
SlbTypeInvalid=Only the type of LoadBalancer is public can associate with Address
LbTypeInvaild=LbType must be one of the classic|application
ApplicationSlbOnlySupportIpv4AndPublic=Application LoadBalancer only supports IPv4 and public network
VpcLbNotSupportRegion=region {0} not support vpc lb  
ZoneNotExist= the zone {0} not existed
ThisVpcNotAssociatePrivateDns=The VPC is not associated with the private DNS instance of the region to which it belongs
ThisRegionToNeutronInnerDnsRegionNotExist=The private DNS region corresponding to this region does not exist
MinTxIntervalRangeInvalid=MinTxInterval must be [100, 1000]
MinRxIntervalRangeInvalid=MinRxInterval must be [100, 1000]
DetectMultiplierRangeInvalid=DetectMultiplier must be [3, 50]
BwsMonthlyNotSupportRegion=BandWidthShare monthly billType not support this region
KeadEipMustBindBws=Kead EIP mush bind BWS,and its product item mush contain bws="1"
BwsEipNetMustZero=The bandwidth value of the EIP order bound to BWS must be 0
InstanceIdNotEmptyOrOrder=The instance ID field must be passed in to notify the failure of the order. If the instance ID does not exist for the order, please call the order interface directly
ItemValueTypeError=Incoming item: {0} parameter type is wrong, supported parameter type: {1}
RecordValueInvalid=Record Value Invalid

InstanceIdAndNetworkInterfaceIdCannotEmptyAtTheSameTime=InstanceId and NetworkInterfaceId cannot empty at the same time
AddressAndAddressCountCannotEmptyAtTheSameTime=PrivateIpAddress and SecondaryPrivateIpAddressCount cannot empty at the same time
PrivateIpOnlySupportVmAndEpc=PrivateIp only support kec and epc
PrivateIpOnlySupportVm=PrivateIp only support kec
ExtensionNetworkInterfaceNeedParameterMode=NetworkInterface <<< {0} >>> requires the Mode parameter
NormalNeedParameterPrivateIpAddress=Normal mode requires parameters PrivateIpAddress
UserNotSupportEipBindedMode=user not support eip binded mode, Please contact customer service
EipBindModeIpNumberOverQuota=EIP of NetworkInterface {0} quota has been exhausted, Please contact customer service
EipNotAllowChangeBindedMode=eip not allow change binded mode
BindedNotSupportPrivateIpAddress=eip binded mode not support parameters PrivateIpAddress
PrimaryVifNotSupportParameterPrivateIpAddressAndMode=Primary NetworkInterface not support parameters PrivateIpAddress and Mode
MustUsePrivateLinkServerAccount=Incorrect deletion of PrivateLink account
SecurityGroupTypeInvalid=The SecurityGroupType only support global or other, default other
MaasNotSupportGlobalSecurityGroup=The trust VPC does not support the type of global security group

DeleteLbDeleteProtectionStatus=he loadbalancer can't be deleted due to DeleteProtection is enabled.
TagProtectionDeleteInvalid= the {0} can't be deleted due to DeleteProtection is enabled.
TagProtectionModifyInvalid= the {0} can't be modified due to ModifyProtection is enabled.
ProtectionStatusInvalid = the protection status only support 'off' or 'on'
SystemRouteTableNotSupportDisassociate=The System RouteTable does not support disassociating operations
SubnetAndRouteTableNotMatch=The Subnet is not bound to the RouteTable and cannot be unbound
InstanceHasOrder=Instance <<< {0} >>> had order
NoPermissionCreateGlobalSg=The current account does not support creating global security groups
EndPointIpConfigLessTwo=EndPoint IpConfig Cannot be less than two
NotSupportBackendServerGroup=Listeners of non TCP or UDP protocol types only support BindType = RealServer
CanNotUpdateBindType=Please unbind the previous servers or server group before changing the BindType
ProtocolNotMatch=The protocol of the listener and server group does not match

RuleDescriptionInvalid={0} is invalid,{0} only allows letters, numbers, Chinese ,\u3001,(,), - ,_and the max length of Name is 64 digits
TmSessionTargetTypeEmptyInvalid=The TargetType must be \u2018vif\u2019 or \u2018lb\u2019
TmFilterRuleDirectionInvalid=The Direction must be \u2018ingress\u2019 or \u2018egress\u2019
TmFilterRuleProtocolInvalid=The Protocol Invalid. Only IP, ICMP, TCP, UDP, ICMPv6 are supported
TmFilterRulePolicyInvalid=The Policy must be \u2018accept\u2019 or \u2018deny\u2019
TmPriorityInvalid=Priority must be in 1-32766
TmSessionPriorityInvalid=Priority must be in 1-65535
TmSessionPkgLenInvalid=PacketLength must be in 1-1450
TmSessionSourceCountPassQuota=Traffic Mirror Session can bind up to {0} mirror sources
TmSessionVniInvalid = VNI must be in 0-16777215

FlowlogResourceTypeInvalid=The ResourceType must be 'NetworkInterface' or 'Subnet' or 'VPC'
FlowlogTrafficTypeInvalid=The ResourceType must be 'All'
FlowLogNumberPassQuota=The number of flowLogs for the region cannot exceed {0}

ProjectOrLogPoolNotExist = The KLog project or LogPool does not exist.

InAndOutBandwidthLimitNotExists=BandwidthLimit and inBandwidthLimit Cannot be empty at the same time
CidrBlockAndAuthorizedSecurityGroupIdMustHaveOne=The CidrBlock and AuthorizedSecurityGroupId must have one
VpcPeerRouteConflict=The upcoming PEERING route conflicts with other routes. Please check before creating a peering connection
