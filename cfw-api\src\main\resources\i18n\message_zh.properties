typeMismatch.java.lang.Integer=InvalidIntegerType
typeMismatch.java.lang.Boolean=InvalidBooleanType
InvalidIntegerType={0}\u5FC5\u987B\u4E3A\u6574\u6570
InvalidBooleanType={0}\u53C2\u6570\u5FC5\u987B\u4E3Atrue\u6216\u8005false
InvalidField={0} \u6700\u5927\u662F128\u4F4D\uFF0C\u53EA\u5141\u8BB8\u5B57\u6BCD\u3001\u4E2D\u6587\u3001\u6570\u5B57\u3001-\u3001_\u3001\u3001\u3001(\u3001)
InvalidParameter.InstanceIdMalformedIndex=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.InstanceIdSameKey=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.InstanceIdMalformedPoint=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.InstanceIdMaxlengthIndex=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.InstanceIdRangeIndex=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameterValue.InstanceId=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterPrefix=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterMalformedIndex=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterMaxIndexDigits=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterIndexRange=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterNameSameKey=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameterValue.FilterNameSameValue=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterMalformedNameParts=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameterValue.NameNotEmptyValue=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterValueEndPartsMalformed=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterValueEndPartsDigits=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterValueEndPartsRange=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameterValue.FilterValueSameValue=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameterValue.FilterValueNotEmptyValue=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterValueSameKey=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterNameMatch=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterValueMatch=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
InvalidParameter.FilterMalformedNoNameValue=\u8FC7\u6EE4\u5668\u53C2\u6570\u683C\u5F0F\u9519\u8BEF
RepeatNum={0}\u91CD\u590D
EmptyField={0}\u4E0D\u80FD\u4E3A\u7A7A
HeaderEmptyField = Http Header [{0}] \u4E0D\u80FD\u4E3A\u7A7A
InvalidAccountId = Http Header [{0}] \u5FC5\u987B\u4E3A\u6570\u5B57
InvalidId= ID {0} \u4E0D\u5408\u6CD5
InvalidName={0}\u540D\u79F0\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u5141\u8BB8\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E2D\u6587\u4EE5\u53CA-_\u3001/.()\uFF0C\u6700\u5927\u957F\u5EA6\u4E3A128
InvalidCidr={0}\u4E0D\u5408\u6CD5
InvalidValue={0}\u4E0D\u5408\u6CD5
FieldInvalid={0}\u4E0D\u5408\u6CD5
InvalidRegion={0}\u673A\u623F\u4E0D\u5408\u6CD5
IpAreaCase=IP\u5730\u5740\u4E0D\u80FD\u5728\u9884\u7559\u7F51\u6BB5\u5185 {0}
ReservedCidrMatch=IP\u5730\u5740\u4E0D\u80FD\u5728\u9884\u7559\u7F51\u6BB5\u5185 {0}
InvalidIp={0}\u5FC5\u987B\u662F\u5408\u6CD5\u7684IP
NotFound={0}\u627E\u4E0D\u5230
AlreadyExists={0}\u5DF2\u7ECF\u5B58\u5728
NotMatch={0}\u4E0D\u5339\u914D
InvalidPort={0}\u5FC5\u987B\u57281\u81F365535
PortMustBeAny={0} \u5FC5\u987B\u662F Any
DependencyViolation=VPC\u4E0B\u6709\u6B63\u5728\u4F7F\u7528\u7684\u8D44\u6E90\uFF0C\u65E0\u6CD5\u5220\u9664VPC
VpcTypeInvalid=VPC\u5B57\u6BB5\u53EA\u80FD\u4E3Atrue\u6216false
NetworkAclNotAttachedSubnet=\u5B50\u7F51\u672A\u5173\u8054ACL
CannotDelete=\u9ED8\u8BA4\u5B89\u5168\u7EC4\u4E0D\u53EF\u7528\u5220\u9664
CheckReplaceSecurityGroupRuleDuplic=\u6279\u91CF\u66FF\u6362\u5B89\u5168\u7EC4\u89C4\u5219\u53C2\u6570\u4E2D\u6709\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u7F16\u8F91
CheckParamAndSecurityGroupRule=\u6279\u91CF\u6DFB\u52A0\u5B89\u5168\u7EC4\u89C4\u5219\u53C2\u6570\u548C\u5DF2\u6709\u89C4\u5219\u4E2D\u6709\u91CD\u590D\uFF0C\u8BF7\u91CD\u65B0\u7F16\u8F91
RuleNumberInvalid=ACL\u89C4\u5219\u7684\u4F18\u5148\u7EA7\u5FC5\u987B\u57281\u81F332766\u4E4B\u95F4
DirectionInvalid=\u89C4\u5219\u65B9\u5411\u5FC5\u987B\u4E3A\u5165\u7AD9\u89C4\u5219in\u6216\u51FA\u7AD9\u89C4\u5219out
ProtocolInvalid=\u89C4\u5219\u534F\u8BAE\u5FC5\u987B\u4E3Audp\uFF0Ctcp\uFF0Cip\u6216icmp
RuleActionInvalid=\u89C4\u5219\u65B9\u5411\u5FC5\u987B\u4E3A\u5141\u8BB8\u6216\u62D2\u7EDD
SlbDescriptionInvalidField=\u63D0\u793A\u0020\u957F\u5EA6\u4E0D\u5927\u4E8E128\u4F4D\uFF0C\u4E14\u53EA\u5141\u8BB8\u5B57\u6BCD\u3001\u4E2D\u6587\u3001\u6570\u5B57\u0020-\u0020\u3001_\u0020\u3001,\u0020\u3001/\u0020\u3001\\u0020\u3001(\u0020\u3001)
SubnetTypeInvalid=\u5B50\u7F51\u7C7B\u578B\u5FC5\u987B\u4E3A\u7EC8\u7AEF\u5B50\u7F51\u6216\u8005\u666E\u901A\u5B50\u7F51
NetworkAclRuleRuleNumberRepeated=ACL\u89C4\u5219\u7684\u4F18\u5148\u7EA7\u4E0D\u80FD\u91CD\u590D
###route
RouteTypeInvalid=\u8DEF\u7531\u7C7B\u578B\u5FC5\u987B\u4E3AInternetGateway\u3001Tunnel\u3001Host\u3001Peering\u3001DirectConnect\u3001Vpn\u3001Host-ext\u3001HaVip\u3001VpnGateway\u5176\u4E2D\u4E00\u79CD
RouteTypeNotSupport=\u4E92\u4FE1vpc\u4E0D\u652F\u6301\u521B\u5EFA\u6B64\u7C7B\u578B\u8DEF\u7531
RouteTypeBindHaVipMasterNetworkInterfaceNotFound=\u7ED1\u5B9A\u9AD8\u53EF\u7528\u865A\u62DFIP\u65F6\uFF0C\u4F20\u5165\u9AD8\u53EF\u7528\u865A\u62DFIP\u6240\u7ED1\u5B9A\u7684\u4E3B\u7F51\u5361ID\uFF08haVipMasterNetworkInterfaceId\uFF09\u672A\u627E\u5230
###peering
PeerRegionInvalid=\u5BF9\u7AEF\u533A\u57DF\u4E0D\u5408\u6CD5
####natpool
NatVersionInvalid=NAT\u7248\u672C\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u652F\u63011,2
SubnetInstanceAlreadyBoundByAnotherNat=\u8FD9\u4E2A\u5B50\u7F51\u4E0B\u7684\u4E3B\u673A{0}\u5DF2\u7ECF\u7ED1\u5B9A\u5230\u53E6\u4E00\u4E2ANAT
NatTypeInvalid=NAT\u7684\u7C7B\u578B\u53EA\u80FD\u4E3A\u516C\u7F51\u6216\u91D1\u5C71\u4E91\u5185\u7F51
NatMappingRangeInvalid=NAT\u7684\u6620\u5C04\u8303\u56F4\u53EA\u80FD\u4E3AVPC\u6216\u5B50\u7F51
BandWidthInvalid=\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F315000Mbps,\u6BCF\u4E2A\u5BA2\u6237\u7684\u5E26\u5BBD\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
ModifyPeeringMinBandWidthInvalid=\u5E26\u5BBD\u6700\u5C0F\u503C\u4E3A1
CreatePeeringMinBandWidthInvalid=\u5E26\u5BBD\u6700\u5C0F\u503C\u4E3A1
NatpoolBandWidthInvalid=\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F320000Mbps,\u6BCF\u4E2A\u5BA2\u6237\u7684\u5E26\u5BBD\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
NatBandWidthInvalid=\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F325000Mbps,\u6BCF\u4E2A\u5BA2\u6237\u7684\u5E26\u5BBD\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
EipBandWidthInvalid=\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F3100000Mbps,\u6BCF\u4E2A\u5BA2\u6237\u7684\u5E26\u5BBD\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
InBandWidthInvalid=\u5165\u7F51\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F315000Mbps,\u6BCF\u4E2A\u5BA2\u6237\u7684\u5E26\u5BBD\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
VifSendPPSInvalid=\u6BCF\u79D2\u53D1\u5305\u6570\u5FC5\u987B\u57281\u81F3100000000\u4E2A\u4E4B\u95F4
VifRecvPPSInvalid=\u6BCF\u79D2\u6536\u5305\u6570\u5FC5\u987B\u57281\u81F3100000000\u4E2A\u4E4B\u95F4
VifBandWidthInvalid=\u7F51\u5361\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F3100000Mbps
VifInBandWidthInvalid=\u7F51\u5361\u5165\u7F51\u5E26\u5BBD\u5FC5\u987B\u57281Mbps\u81F3100000Mbps
NatIpNumberInvalid=Nat\u7684IP\u6570\u91CF\u5FC5\u987B\u57281\u81F320,\u6BCF\u4E2A\u5BA2\u6237\u7684NatIp\u6570\u91CF\u8303\u56F4\u53D7\u5230\u914D\u989D\u63A7\u5236
BandWidthNotChange=\u5E26\u5BBD\u6CA1\u6709\u66F4\u6539
ChangeTypeNotSupport=\u6309\u5C0F\u65F6\u5148\u7ED3\u4E0D\u652F\u6301\u66F4\u914D
BandWidthNotLarger=\u5305\u5E74\u5305\u6708\u5E26\u5BBD\u53EA\u5141\u8BB8\u5347\u914D
ChargeTypeInvalid.Nat=\u8BA1\u8D39\u7C7B\u578B\u53EA\u652F\u6301\u5305\u5E74\u5305\u6708\uFF0C\u6309\u65E5\u6708\u7ED3\uFF0C\u6309\u5CF0\u503C\u6708\u7ED3\uFF0C\u6309\u91CF\u6708\u7ED3,\u6309\u91CF\u4ED8\u8D39
ChargeTypeInvalid.ApplicationSlb=\u8BA1\u8D39\u7C7B\u578B\u53EA\u652F\u6301\u5305\u5E74\u5305\u6708\u548C\u6309\u65E5\u6708\u7ED3
EipHasJoinInBandWidthShare=\u8BE5\u5F39\u6027IP\u5DF2\u7ECF\u52A0\u5165\u5171\u4EAB\u5E26\u5BBD\u4E2D\uFF0C\u4E0D\u5141\u8BB8\u4FEE\u6539\u5E26\u5BBD
####eip
ChargeTypeInvalid=\u8BA1\u8D39\u7C7B\u578B\u5FC5\u987B\u4E3A\u5305\u5E74\u5305\u6708\uFF0C\u6309\u5CF0\u503C\u6708\u7ED3\uFF0C\u6309\u65E5\u6708\u7ED3\uFF0C\u6309\u5C0F\u65F6\u5148\u7ED3\uFF0C\u6309\u6D41\u91CF\u6708\u7ED3
PurchaseTimeInvalid=\u5305\u5E74\u5305\u6708\u7684\u8D2D\u4E70\u65F6\u957F\u5FC5\u987B\u4E3A1\u81F336\u4E2A\u6708
PublicIpInvalid=IP\u683C\u5F0F\u4E0D\u5408\u6CD5
InstanceTypeInvalid=\u5B9E\u4F8B\u7C7B\u578B\u5FC5\u987B\u4E3A\u4E91\u670D\u52A1\u5668\uFF0C\u8D1F\u8F7D\u5747\u8861\uFF0C\u7AEF\u53E3\u6620\u5C04, Nat
PublicIpNotAssociatedPort=\u5F39\u6027IP\u672A\u7ED1\u5B9A\u4EFB\u4F55\u7AEF\u53E3
InstanceNotMactchNetworkInterface=\u5B9E\u4F8B\u4E0D\u5B58\u5728\u6216\u5B9E\u4F8B\u4E0D\u5339\u914D\u7F51\u7EDC\u63A5\u53E3
NextTokenInvalid=\u5206\u9875\u53C2\u6570\u4E0D\u5408\u6CD5
MaxResultsInvalid=\u6700\u5927\u7ED3\u679C\u5FC5\u987B\u4E3A\u6570\u5B57\u4E14\u57285\u81F31000\u4E4B\u95F4
MaxResultsOneHundredInvalid=\u6700\u5927\u7ED3\u679C\u5FC5\u987B\u4E3A\u6570\u5B57\u4E14\u57285\u81F3100\u4E4B\u95F4
DhcpMaxResultsInvalid=\u6700\u5927\u7ED3\u679C\u5FC5\u987B\u4E3A\u6570\u5B57\u4E14\u57285\u81F3252\u4E4B\u95F4
RepeatedAssociatingAddress.Address=\u5F39\u6027IP\u5DF2\u7ECF\u7ED1\u5B9Aslb\u6216\u8005Ipfwd
RepeatedAssociatingAddress.Slb=\u8D1F\u8F7D\u5747\u8861\u5DF2\u7ECF\u7ED1\u5B9A\u5F39\u6027IP
EipBindingLbRs=\u5F39\u6027IP\u5DF2\u7ECF\u7ED1\u5B9A\u5230\u8D1F\u8F7D\u5747\u8861\u7684(HTTP/HTTPS)\u7C7B\u578B\u7684\u76D1\u542C\u5668\u7684\u771F\u5B9E\u670D\u52A1\u5668\u4E2D\uFF0C\u65E0\u6CD5\u89E3\u7ED1
BandwidthAndInBandwidthLimitNotNull=\u51FA\u5E26\u5BBD\u9650\u901F\u548C\u5165\u5E26\u5BBD\u9650\u901F\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A

##### lb
LoadBalancerStateInvalid=\u8D1F\u8F7D\u5747\u8861\u72B6\u6001\u53EA\u80FD\u4E3A\u5F00\u542F\u6216\u5173\u95ED
LoadBalancerTypeInvalid=\u8D1F\u8F7D\u5747\u8861\u7C7B\u578B\u53EA\u80FD\u4E3A\u516C\u7F51\u6216\u79C1\u7F51
AssociateStateInvalid=State\u53EA\u80FD\u4E3Aassociate\u6216disassociate
ListenerStateInvalid=\u76D1\u542C\u5668\u72B6\u6001\u53EA\u80FD\u4E3A\u5F00\u542F\u6216\u5173\u95ED
ListenerProtocolInvalid=\u76D1\u542C\u5668\u534F\u8BAE\u53EA\u80FD\u4E3ATCP\u6216HTTP\u6216\u8005HTTPS
ListenerMethodlInvalid=\u4E3B\u4ECE\u6A21\u5F0F\u4E0B\u4EC5\u652F\u6301TCP\u3001UDP\u6216QUIC_CID\u6A21\u5F0F\u53EA\u652F\u6301UDP
RegionNotSupportMasterSlave={0}\u533A\u57DF\u4E0D\u652F\u6301\u4E3B\u4ECE\u6A21\u5F0F
ListenerMasterSlaveNotSupportChange=\u4E3B\u4ECE\u89C4\u5219\u4E0D\u5141\u8BB8\u4E0E\u5176\u4ED6\u89C4\u5219\u4EA4\u6362
ListenerMasterSlaveTypeInvalid=\u4E3B\u4ECE\u7C7B\u578B\u53EA\u5141\u8BB8\u4E3B\uFF0C\u4ECE
BatchRegisterInstancesNotSupportMasterSlave=\u4E3B\u4ECE\u7C7B\u578B\u76D1\u542C\u5668\u6302\u8F7D\u670D\u52A1\u5668\u4E0D\u652F\u6301\u6B64\u6279\u91CF\u63A5\u53E3
BatchRegisterInstancesOnlySupportInstanceType=\u6B64\u6279\u91CF\u6302\u8F7D\u63A5\u53E3\u4EC5\u652F\u6301\u6302\u8F7Dhost\u4EE5\u53CADirectConnection\u7C7B\u578B\u7684\u771F\u5B9E\u670D\u52A1\u5668,\u4E0D\u652F\u6301{0}
ApplicationListenerProtocolInvalid=\u76D1\u542C\u5668\u534F\u8BAE\u53EA\u80FD\u4E3AHTTP\u6216HTTPS
Lb7ListenerPortNotSupport22And1999And111Between65501And65535=\u76D1\u542C\u7AEF\u53E3\u4E3A22,1999,111,65501-65535\u5DF2\u88AB\u5360\u7528,\u4E0D\u5141\u8BB8\u521B\u5EFA
Lb7ListenerPortNotSupport22And179And111Between65501And65535=\u76D1\u542C\u7AEF\u53E3\u4E3A22,179,111,65501-65535\u5DF2\u88AB\u5360\u7528,\u4E0D\u5141\u8BB8\u521B\u5EFA
LbListenerPortNotSupportBetween65501And65535=\u76D1\u542C\u7AEF\u53E3\u4E3A65501-65535\u5DF2\u88AB\u5360\u7528,\u4E0D\u5141\u8BB8\u521B\u5EFA
LbMethodInvalid=\u76D1\u542C\u5668\u8F6C\u53D1\u65B9\u5F0F\u5FC5\u987B\u4E3A\u8F6E\u8BE2,\u6700\u5C0F\u8FDE\u63A5\u6570\u6216\u4E3B\u5907
LbMethodEmpty=\u76D1\u542C\u5668\u7684\u8F6C\u53D1\u65B9\u5F0F\u4E0D\u80FD\u4E3A\u7A7A
SessionStateInvalid=\u4F1A\u8BDD\u4FDD\u6301\u53EA\u80FD\u5F00\u542F\u6216\u5173\u95ED
SessionPersistencePeriodInvalid=\u4F1A\u8BDD\u4FDD\u6301\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57281\u81F386400\u4E4B\u95F4
SessionPersistencePeriodEmpty=\u4F1A\u8BDD\u4FDD\u6301\u53C2\u6570\u4E3A\u7A7A\uFF0C\u5982\u679C\u91C7\u7528\u9ED8\u8BA4\u503C\uFF0C\u8BF7\u4E0D\u8981\u5728\u9610\u8FF0\u4E2D\u52A0\u5165SessionPersistencePeriod
CookieTypeInvalid=\u4F1A\u8BDD\u4FDD\u6301\u7C7B\u578B\u53EA\u80FD\u4E3A\u690D\u5165cookie\u6216\u91CD\u5199cookie
LbKindInvalid=\u8D1F\u8F7D\u5747\u8861\u7C7B\u578B\u53EA\u80FD\u4E3ANAT\u6216FNAT
HealthyThresholdInvalid=\u5065\u5EB7\u68C0\u67E5\u9608\u503C\u5FC5\u987B\u57281\u81F310\u4E4B\u95F4
IntervalInvalid=\u5065\u5EB7\u68C0\u67E5\u65F6\u95F4\u95F4\u9694\u5FC5\u987B\u57281\u81F31000\u4E4B\u95F4
TimeoutInvalid=\u5065\u5EB7\u68C0\u67E5\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57281\u81F33600\u4E4B\u95F4
UnhealthyThresholdInvalid=\u4E0D\u5065\u5EB7\u9608\u503C\u5FC5\u987B\u57281\u81F310\u4E4B\u95F4
HealthCheckNotExistsUnderListener=\u5065\u5EB7\u68C0\u67E5\u4E0D\u5B58\u5728
HealthCheckHttpMethodInvalid=HTTP\u8BF7\u6C42\u65B9\u6CD5\u53EA\u80FD\u4E3AGET\u6216HEAD
HealthCheckStateInvalid=\u5065\u5EB7\u68C0\u67E5\u72B6\u6001\u5FC5\u987B\u4E3A\u5F00\u542F\u6216\u5173\u95ED
HealthCheckProtocolInvalid=\u5065\u5EB7\u68C0\u67E5\u534F\u8BAE\u53EA\u80FD\u4E3ATCP\u6216ICMP\u6216UDP\u6216HTTP
HealthCheckProtocolTcpNotMatch=\u6B64\u7C7B\u578BLB\u4E0D\u652F\u6301{0}\u5065\u5EB7\u68C0\u67E5
HealthCheckConnectPortInvalid=\u5065\u5EB7\u68C0\u67E5\u7AEF\u53E3\u7684\u503C\u5FC5\u987B\u4ECB\u4E8E1\u548C65535\u4E4B\u95F4
UrlPathInvalid=HTTP\u8BF7\u6C42\u94FE\u63A5\u957F\u5EA6\u4E3A1\u81F3250
PathInvalid=URL\u89C4\u8303\uFF1A\u957F\u5EA6\u9650\u5236\u4E3A80\u4E2A\u5B57\u7B26\u4EE5\u5185\uFF0C\u53EA\u80FD\u4F7F\u7528\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u2018-\u2019\u3001\u2018/\u2019\u3001\u2018.\u2019\u3001\u2018?\u2019\u3001\u2018=\u2019\u8FD9\u4E9B\u5B57\u7B26; URL\u5FC5\u987B\u4EE5/\u5F00\u5934\u3002
RuleUrlPathInvalid=\u8BF7\u6C42\u94FE\u63A5\u957F\u5EA6\u4E3A1\u81F380
RealServerGroupProtocolInvalid=Protocol\u53EA\u652F\u6301 HTTP\u3001TCP\u3001UDP\u5176\u4E2D\u4E00\u4E2A
MirrorBackendServerGroupOnlySupportHttp=\u955C\u50CF\u670D\u52A1\u5668\u7EC4\u534F\u8BAE\u53EA\u652F\u6301HTTP
ListenerBindTypeInvalid=BindType \u53EA\u652F\u6301 RealServer\u3001BackendServerGroup\u5176\u4E2D\u4E00\u4E2A
LbListenerHttpProtocolInvalid=\u76D1\u542C\u5668HTTP\u534F\u8BAE\u7248\u672C\u975E\u6CD5
LbListenerHttpProtocolMushHttpOrHttps=\u652F\u6301\u5411\u540E\u8F6C\u53D1HTTP\u534F\u8BAE\u7248\u672C\u7684\u76D1\u542C\u5668\u534F\u8BAE\u5FC5\u987B\u4E3AHTTP\u6216HTTPS
ListenerTslPolicyInvalid=TLS\u5B89\u5168\u7B56\u7565\u503C\u975E\u6CD5
LbListenerTslPolicyMustHttps=\u652F\u6301TSL\u5B89\u5168\u7B56\u7565\u7684\u76D1\u542C\u5668\u534F\u8BAE\u5FC5\u987B\u4E3AHTTPS
LbListenerHttp2MustHttps=\u53EA\u6709HTTPS\u76D1\u542C\u5668\u53EF\u4EE5\u5F00\u542FHTTP/2\u7279\u6027
RealServerTypeInvalid=\u771F\u5B9E\u670D\u52A1\u5668\u7684\u7C7B\u578B\u5FC5\u987B\u4E3A\u4E91\u670D\u52A1\u5668\u6216\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5
WeightInvalid=\u6743\u91CD\u5FC5\u987B\u57280\u81F3255\u4E4B\u95F4
WeightInvalidMX=\u6743\u91CD\u5FC5\u987B\u57281\u81F350\u4E4B\u95F4
WeightInvalidAX=\u4F18\u5148\u7EA7\u5FC5\u987B\u57281\u81F3100\u4E4B\u95F4
WeightInvalidA=\u4F18\u5148\u7EA7\u5FC5\u987B\u57280\u81F3100\u4E4B\u95F4
WeightIsNotEmpty=\u4E0D\u80FD\u4F20\u5165\u6743\u91CD\u503C
WeightIsEmpty=\u4F18\u5148\u7EA7\u4E0D\u80FD\u4E3A\u7A7A
RealServerTypeMissMatchListenerType=HTTP\u7C7B\u578B\u7684\u76D1\u542C\u5668\u4E0D\u80FD\u76F4\u63A5\u7ED1\u5B9A\u4E91\u670D\u52A1\u5668
RealServerIpNotInEip=HTTP\u7C7B\u578B\u76D1\u542C\u5668\u4E0B\u7684\u771F\u5B9E\u670D\u52A1\u5668\u5FC5\u987B\u662F\u5F39\u6027IP\u7ED1\u5B9A\u4E91\u670D\u52A1\u5668\u6216\u5F39\u6027IP\u7ED1\u5B9A\u7AEF\u53E3\u6620\u5C04
InvalidTunnelMode=\u53EA\u6709\u5B50\u7F51\u7C7B\u578B\u7684\u96A7\u9053\u7F51\u5173\u624D\u53EF\u4EE5\u7ED1\u5B9A\u5B50\u7F51
InvalidProtocol=\u534F\u8BAE\u5FC5\u987B\u4E3ATCP\u6216\u8005UDP
InvalidDnatProtocol=\u534F\u8BAE\u5FC5\u987B\u4E3ATCP\u3001UDP\u6216\u8005Any
LoadBalanceNotExists=\u8D1F\u8F7D\u5747\u8861 <<< {0} >>> \u4E0D\u5B58\u5728
LbAclNotFound=ACL <<< {0} >>> \u4E0D\u5B58\u5728
LbListenerNotFound=\u76D1\u542C\u5668 <<< {0} >>> \u4E0D\u5B58\u5728
AvailabilityZoneInvalid= \u53EF\u7528\u533A <<< {0} >>> \u65E0\u6548
RegionNotSupportValue=\u8BE5\u673A\u623F\u4E0D\u652F\u6301 <<< {0} >>>
ListenerNotSupportHealthProtocol=\u8BE5\u76D1\u542C\u5668\u4E0D\u652F\u6301\u5065\u5EB7\u68C0\u67E5\u534F\u8BAE <<< {0} >>>
UdpHealthProtocolNotSupportModify=UDP\u7C7B\u578B\u76D1\u542C\u5668\u4E0D\u652F\u6301\u4FEE\u6539\u5065\u5EB7\u68C0\u67E5\u534F\u8BAE
##subnet
InvalidDNS=0.0.0.0 \u662F\u65E0\u6548\u7684DNS
##payment
PaymentFailure=\u652F\u4ED8\u5931\u8D25
Payment.CreateProductFailed=\u521B\u5EFA\u5546\u54C1\u5931\u8D25
Payment.CreateOrderFailed=\u521B\u5EFA\u8BA2\u5355\u5931\u8D25
Payment.ProductNotFound=\u5B9E\u4F8B\u5BF9\u5E94\u7684\u5546\u54C1\u4E0D\u5B58\u5728
Payment.OrderNotFound=\u5B9E\u4F8B\u5BF9\u5E94\u7684\u5546\u54C1\u4E0D\u5B58\u5728
Payment.NotifySubOrderFailed=\u56DE\u5199\u5B50\u8BA2\u5355\u5931\u8D25
MissingParameter.TunnelId=\u5F53\u8DEF\u7531\u7C7B\u578B\u4E3A\u96A7\u9053\u7F51\u5173\u65F6\uFF0C\u96A7\u9053\u7F51\u5173ID\u4E0D\u80FD\u4E3A\u7A7A
MissingParameter.vpnGatewayId=\u5F53\u8DEF\u7531\u7C7B\u578B\u4E3AVpn\u7F51\u5173\u65F6\uFF0CVpn\u7F51\u5173ID\u4E0D\u80FD\u4E3A\u7A7A
MissingParameter.InstanceId=\u5F53\u8DEF\u7531\u7C7B\u578B\u4E3A\u4E91\u670D\u52A1\u5668\u8DEF\u7531\u65F6\uFF0C\u4E91\u670D\u52A1\u5668\u7684ID\u4E0D\u80FD\u4E3A\u7A7A
MissingParameter.DirectConnectGatewayId=\u5F53\u8DEF\u7531\u7C7B\u578B\u4E3A\u8FB9\u754C\u7F51\u5173\u7C7B\u578B\u65F6\uFF0C\u8FB9\u754C\u7F51\u5173ID\u4E0D\u80FD\u4E3A\u7A7A
MissingParameter.VpcPeeringConnectionId=\u5F53\u8DEF\u7531\u7C7B\u578B\u4E3A\u5BF9\u7B49\u8FDE\u63A5\u7C7B\u578B\u65F6\uFF0C\u5BF9\u7B49\u8FDE\u63A5ID\u4E0D\u80FD\u4E3A\u7A7A


QuotaIdOutRange=<<< {0} >>>\u8D85\u51FA\u914D\u989D\u6700\u5927\u503C
BandWidthShareBindEip=\u5171\u4EAB\u5E26\u5BBD\u5DF2\u7ECF\u7ED1\u5B9AEIP,\u8BF7\u5148\u5220\u9664\u7ED1\u5B9A\u7684EIP,\u518D\u5220\u9664\u5171\u4EAB\u5E26\u5BBD
BandWidthShareChargeTypeBind=\u5171\u4EAB\u5E26\u5BBD\u7684\u652F\u4ED8\u7C7B\u578B\u548CEIP\u7684\u652F\u4ED8\u7C7B\u578B\u4E0D\u540C,\u65E0\u6CD5\u7ED1\u5B9A
BandWidthShareHasBindThisEip=\u5F39\u6027IP\u5DF2\u7ECF\u7ED1\u5B9A\u5230\u4E86\u5176\u4ED6\u5171\u4EAB\u5E26\u5BBD,\u65E0\u6CD5\u7ED1\u5B9A
BandWidthShareNameTooLong=\u5171\u4EAB\u5E26\u5BBD\u540D\u79F0\u8FC7\u957F\u6216\u8005\u540D\u79F0\u683C\u5F0F\u4E0D\u5408\u6CD5
ChargeTypeInvalid.Bws=\u5171\u4EAB\u5E26\u5BBD\u8BA1\u8D39\u65B9\u5F0F\u53EA\u652F\u6301\u6309\u65E5\u6708\u7ED3\u3001\u6309\u5CF0\u503C\u6708\u7ED3\u548C\u6309\u91CF\u6708\u7ED3
BandWidthShareIdTooMuch=\u5171\u4EAB\u5E26\u5BBD\u67E5\u8BE2\u7684\u6279\u91CFid\u8FC7\u591A,\u6700\u591A\u4E3A20\u4E2A
BandWidthShareHasNotBindThisEip=\u5F39\u6027IP\u672A\u7ED1\u5B9A\u5230\u8FD9\u4E2A\u5171\u4EAB\u5E26\u5BBD,\u65E0\u6CD5\u89E3\u7ED1
BandWidthShareForPrePaidByMonthNotAllowDelete=\u5305\u5E74\u5305\u6708\u5171\u4EAB\u5E26\u5BBD\u4E0D\u5141\u8BB8\u5220\u9664
BandWidthShareForPrePaidByMonthNotAllowReduce=\u5305\u5E74\u5305\u6708\u5171\u4EAB\u5E26\u5BBD\u4E0D\u5141\u8BB8\u964D\u914D
BandWidthShareMonthlyBandWidthInvalid=\u5305\u5E74\u5305\u6708\u5171\u4EAB\u5E26\u5BBD\u5B9E\u4F8B\u5E26\u5BBD\u8D77\u552E10M

#tag
DeleteTagFailed=\u5220\u9664Tag\u5931\u8D25
CreateTagFailed=\u521B\u5EFAtag\u5931\u8D25
QueryResourceByTagFailed=\u6839\u636ETag\u67E5\u8BE2\u8D44\u6E90\u5931\u8D25
ReplaceTagsFailed=\u7ED1\u5B9Atag\u5931\u8D25
CreatedSuccessTagFailed=\u8D44\u6E90<<< {0} >>>\u521B\u5EFA\u6210\u529F,\u4F46tag\u7ED1\u5B9A\u5931\u8D25

#QUOTA
QuotaMaxCount={0}\u8D85\u8FC7\u6700\u5927\u914D\u989D, \u6700\u5927\u914D\u989D\u4E3A{1}\u4E2A
ReserveSubnet=\u7EC8\u7AEF\u5B50\u7F51
Subnet=\u5B50\u7F51
Vpc=\u865A\u62DF\u79C1\u6709\u7F51\u7EDC
Ipv6Vpc=\u652F\u6301IPV6\u7F51\u6BB5\u7684\u865A\u62DF\u79C1\u6709\u7F51\u7EDC
SecondaryCidrBlock=\u9644\u52A0IPv4\u7F51\u6BB5

#dns
InvalidDnsStatus=\u4FEE\u6539\u72B6\u6001\u53C2\u6570\u7C7B\u578B\u4EC5\u652F\u6301\u5F00\u542F\u6216\u6682\u505C
#quota
FullQuota.HostedZoneReachMAX=\u57DF\u540D\u6DFB\u52A0\u6570\u91CF\u5DF2\u8FBE\u5230\u4E0A\u9650
FullQuota.RecordReachMAX=\u8BE5\u57DF\u540D\u4E0B\u8BB0\u5F55\u6DFB\u52A0\u6570\u91CF\u5DF2\u8FBE\u5230\u4E0A\u9650
QuotaError=\u914D\u989D\u5185\u90E8\u9519\u8BEF

#hostedzone
InvalidParameterValue.HostedzoneNameNotEmpty=\u57DF\u540D\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.HostedzoneNameLengthLimit=\u57DF\u540D\u540D\u79F0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7255
InvalidParameterValue.HostedzoneNameInvalid=\u57DF\u540D\u540D\u79F0\u4E0D\u6B63\u786E
InvalidParameterValue.HostedzoneNameSegmentLengthLimit=\u57DF\u540D\u540D\u79F0\u4E2D\u6BCF\u6BB5\u7684\u5B57\u7B26\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC763
InvalidParameterValue.HostedzoneNameStandardInvalid=\u57DF\u540D\u7EA7\u522B\u6700\u591A\u4E3A\u9876\u7EA7\u57DF\u540D\u4E4B\u4E0A1\u7EA7
InvalidParameterValue.HostedzoneIdNotEmpty=\u57DF\u540DID\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.HostedzoneNameNotContainsChinese=\u57DF\u540D\u540D\u79F0\u4E0D\u80FD\u542B\u6709\u4E2D\u6587
InvalidParameterValue.invalid_lock_or_unlock_type=\u65E0\u6CD5\u6682\u505C\u6216\u8005\u6062\u590D\u89E3\u6790\uFF0C\u8BF7\u68C0\u67E5\u5F53\u524D\u72B6\u6001
ZoneNameExistedHasInstance=\u8BF7\u5728\u5185\u7F51dns1.0\u5C06\u6240\u6709\u5B9E\u4F8B\u5220\u9664,\u624D\u53EF\u521B\u5EFA\u540C\u540Dzone {0}.

#record
InvalidParameterValue.RecordNameNotEmpty=\u8BB0\u5F55\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.RecordNameNotContainsChinese=\u8BB0\u5F55\u540D\u79F0\u4E0D\u80FD\u542B\u6709\u4E2D\u6587
InvalidParameterValue.RecordIdNotEmpty=\u8BB0\u5F55ID\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.RecordNameNotwithUnderline=\u8BB0\u5F55\u540D\u79F0\u4E0D\u80FD\u542B\u6709'_'
InvalidParameterValue.RecordNameSegmentLimit=\u8BB0\u5F55\u540D\u79F0\u7684\u89E3\u6790\u4E0D\u8981\u8D85\u8FC7{0}\u7EA7
InvalidParameterValue.RecordNameNotwithMidlineAtStartOrEnd='-'\u4E0D\u80FD\u51FA\u73B0\u5728\u6BCF\u4E2A\u6BB5\u5F00\u5934\u548C\u7ED3\u5C3E
InvalidParameterValue.RecordNameStartWithSignInvalid=\u5FC5\u987B\u662F'*'\u5F00\u5934\u7684\uFF0C*\u540E\u9762\u5FC5\u987B\u662F\u70B9\uFF0C\u7C7B\u4F3C'*.a'
InvalidParameterValue.RecordNameSegmentNumberLimit=\u6CDB\u57DF\u540D\u89E3\u6790\u6700\u591A2\u7EA7
InvalidParameterValue.RecordNameInvalidWithNS=\u4E0D\u5141\u8BB8\u586B\u5199\u4E00\u4E2A\u6216\u8005\u591A\u4E2A\u7A7A\u683C\uFF0C\u4E5F\u4E0D\u53EF\u4EE5\u586B\u5199@\uFF0C\u53EA\u5141\u8BB8\u4E00\u7EA7\u8BB0\u5F55
InvalidParameterValue.RecordValueNotContainsChinese=\u8BB0\u5F55\u503C\u4E0D\u80FD\u5305\u542B\u4E2D\u6587\u6216\u5168\u89D2\u7B26\u53F7
InvalidParameterValue.RecordTypeNotEmpty=\u8BB0\u5F55\u7684\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.GeoLocationIdNotEmpty=\u57DF\u540D\u8BB0\u5F55\u7684\u7EBF\u8DEFID\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.RecordValueNotIP=IP\u5730\u5740\u975E\u6CD5
InvalidParameterValue.RecordValueTooLong=\u503C\u5FC5\u987B\u5C0F\u4E8E255\u4E2A\u5B57\u7B26
InvalidParameterValue.RecordValueNotIPV6=IP\u5730\u5740\u975E\u6CD5
InvalidParameterValue.RecordValueNotDomain=CNAME,MX,NS\u7684value\u4E0D\u6B63\u786E,\u53EA\u5141\u8BB8\u8F93\u5165\u57DF\u540D
InvalidParameterValue.RecordTypeInvalid=\u8BB0\u5F55\u7C7B\u578B\u4E0D\u5408\u6CD5
InvalidParameterValue.RecordTypeOnlyAOrAX=\u57DF\u540D\u8BB0\u5F55\u7684\u5F53\u524D\u7C7B\u578B\u53EA\u80FD\u662Fa\u6216\u8005ax
InvalidParameterValue.RecordTargetTypeOnlyAOrAX=\u57DF\u540D\u8BB0\u5F55\u9700\u8981\u66F4\u6362\u7684\u7C7B\u578B\u53EA\u80FD\u662Fa\u6216\u8005ax
InvalidParameterValue.RecordValueNotHttpUrl=\u8BB0\u5F55\u503C\u5FC5\u987B\u662Fhttp://\u5F00\u5934\u7684url
InvalidParameterValue.ResourceRecordNameTooMuch=\u8BB0\u5F55\u540D\u79F0\u53EA\u5141\u8BB8\u8F93\u5165\u4E00\u4E2A
InvalidParameterValue.ResourceRecordValueTooMuch=\u8BB0\u5F55\u503C\u53EA\u5141\u8BB8\u8F93\u5165\u4E00\u4E2A
#param
InvalidParameterValue.TypeEmpty=\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.TtlNull=TTL\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.TTLMixValue=TTL\u503C\u5FC5\u4E0D\u80FD\u5C0F\u4E8E60
InvalidParameterValue.TTLMaxValue=TTL\u503C\u4E0D\u80FD\u5927\u4E8E604800
InvalidParameterValue.ZoneTTLMaxValue=TTL\u503C\u4E0D\u80FD\u5927\u4E8E86400
InvalidParameterValue.GeoEmpty=\u7EBF\u8DEF\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.GeoLengthMoreThan255=\u5730\u7406\u4FE1\u606F\u7684\u957F\u5EA6\u5FC5\u987B\u5C0F\u4E8E255\u4E2A\u5B57\u8282
InvalidParameterValue.ValueEmpty=\u8BB0\u5F55\u503C\u4E0D\u80FD\u4E3A\u7A7A
InvalidParameterValue.ValueLengthMoreThan255=\u8BB0\u5F55\u503C\u957F\u5EA6\u5FC5\u987B\u5C0F\u4E8E255\u4E2A\u5B57\u8282

#description
DescriptionTooLong=\u63CF\u8FF0\u4FE1\u606F\u8FC7\u957F,\u4E0D\u80FD\u8D85\u8FC7128\u4E2A\u5B57\u8282
NameTooLong={0}\u8FC7\u957F\uFF0C\u4E0D\u80FD\u8D85\u8FC764\u4E2A\u5B57\u8282
InstanceCanNotUnSuspend=\u5B9E\u4F8B\u72B6\u6001\u4E3A\u672A\u5F00\u901A\uFF0C\u65E0\u6CD5\u6267\u884C\u6062\u590D\u64CD\u4F5C

#Peering
ChargeTypeInvalid.Peering=\u5BF9\u7B49\u8FDE\u63A5\u8BA1\u8D39\u7C7B\u578B\u9519\u8BEF
PeeringChargeAndSubOrderIdEmpty=\u4E0D\u540C\u673A\u623F\u4E4B\u95F4\u7684\u5BF9\u7B49\u8FDE\u63A5\u5FC5\u987B\u63D0\u4F9BSubOrderId\u6216\u8005ChargeType
RegionNotSupport={0}\u503C\u5BF9\u5E94\u7684\u673A\u623F{1}\u4E0D\u652F\u6301
VpcIdCheckError={0}\u503C{1}\u6821\u9A8C\u5931\u8D25
PeeringIsExist=\u5BF9\u7B49\u8FDE\u63A5\u5DF2\u7ECF\u5B58\u5728
PeeringCheckError=\u5BF9\u7B49\u8FDE\u63A5\u521B\u5EFA\u9884\u6821\u9A8C\u5931\u8D25
TunnelsCheckError=\u5BF9\u7B49\u8FDE\u63A5\u521B\u5EFA\u9884\u6821\u9A8C\u5931\u8D25
VpcCidrCover=Vpc\u7684\u7F51\u6BB5\u5B58\u5728\u4EA4\u96C6
VpcTunnelsCidrCover=Vpc\u7684\u7F51\u6BB5\u548C\u5BF9\u7AEF\u7684Vpn\u7684\u901A\u9053\u7684\u7F51\u6BB5\u5B58\u5728\u4EA4\u96C6
PeerVpcTunnelsCidrCover=\u5BF9\u7AEFVpc\u7684\u7F51\u6BB5\u548C\u672C\u5730Vpn\u7684\u901A\u9053\u7684\u7F51\u6BB5\u5B58\u5728\u4EA4\u96C6
PeeringIdMustRemoteId=VpcPeeringConnectionId\u5FC5\u987B\u4E3A\u63A5\u6536\u7AEF\u7684\u5BF9\u7B49\u8FDE\u63A5ID

SourceIsNotSupport=\u63A5\u53E3\u4E0D\u652F\u6301\u5F53\u524D\u6765\u6E90
BatchSizeIsTooMax=\u6279\u6B21\u7684\u6570\u91CF\u8FC7\u5927
InstanceIsInProcess=\u5B9E\u4F8B\u6B63\u5728\u521B\u5EFA\u4E2D\u6216\u8005\u672A\u5B8C\u6210\u521B\u5EFA,\u8BF7\u7A0D\u540E\u518D\u8BD5
SecurityGroupMaasPermission=\u60A8\u6CA1\u6709\u521B\u5EFASecurityGroup\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
VPNGatewayMaasPermission=\u60A8\u6CA1\u6709\u521B\u5EFAVPNGateway\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
DCGatewayMaasPermission=\u60A8\u6CA1\u6709\u521B\u5EFADirectConnectGateway\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
PeerMaasPermission=\u60A8\u6CA1\u6709\u521B\u5EFAPeer\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
VPCMaasPermission = \u60A8\u6CA1\u6709\u521B\u5EFA\u6B64\u7C7B\u578BVPC\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
EipPoolMaasPermission=\u60A8\u6CA1\u6709\u521B\u5EFAEIP\u5730\u5740\u6C60\u7684\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u91D1\u5C71\u4E91\u5F00\u901A
VPCMaasNotSupportIpv6= \u4E92\u4FE1vpc\u4E0D\u652F\u6301\u4F7F\u7528ipv6
InstanceMustInProcess=\u5B9E\u4F8B\u7684\u8BA2\u5355\u72B6\u6001\u5FC5\u987B\u662F\u521B\u5EFA\u4E2D

PhysicalVifSGRuleTooMax=\u7269\u7406\u673A\u7F51\u5361\u7684\u5B89\u5168\u7EC4\u7684\u89C4\u5219\u8D85\u8FC7\u6700\u5927\u6570

PeeringInstanceIdNotInTrade=\u8DE8\u673A\u623F\u7684Peering\u7684\u5B9E\u4F8BID\u5728\u8BA2\u5355\u7CFB\u7EDF\u4E2D\u6CA1\u6709\u627E\u5230
PeeringIdMustLocalId=\u5BF9\u7B49\u8FDE\u63A5ID\u5BF9\u5E94\u7684\u5B9E\u4F8B\u7C7B\u578B\u5FC5\u987B\u662F\u53D1\u8D77\u7AEF
PeeringStatusMustActive=\u5BF9\u7B49\u8FDE\u63A5\u7684\u72B6\u6001\u5FC5\u987B\u662F\u5DF2\u7ECF\u8FDE\u63A5\u72B6\u6001

DirectConnectNameInvalid=\u7269\u7406\u7AEF\u53E3\u540D\u79F0\u975E\u6CD5
DirectConnectTypeInvalid=\u7269\u7406\u7AEF\u53E3\u7C7B\u578B\u975E\u6CD5
ConstructionModeInvalid=\u7269\u7406\u7AEF\u53E3\u65B9\u5F0F\u975E\u6CD5
DirectConnectProviderInvalid=\u7269\u7406\u7AEF\u53E3\u8FD0\u8425\u5546\u975E\u6CD5
AccessLocationInvalid=\u7269\u7406\u7AEF\u53E3\u91D1\u5C71\u4E91\u63A5\u5165\u70B9\u975E\u6CD5
DirectConnectIdInvalid=\u7269\u7406\u7AEF\u53E3ID\u975E\u6CD5
DirectConnectInterfaceNameInvalid=\u8FDE\u63A5\u901A\u9053\u540D\u79F0\u975E\u6CD5
DirectConnectInterfaceIdInvalid=\u8FDE\u63A5\u901A\u9053ID\u975E\u6CD5
DirectConnectGatewayNameInvalid=\u8FB9\u754C\u7F51\u5173\u540D\u79F0\u975E\u6CD5
DirectConnectGatewayIdInvalid=\u8FB9\u754C\u7F51\u5173ID\u975E\u6CD5
VpnTunnelIdGatewayInvalid=\u901A\u9053ID\u975E\u6CD5
DirectConnectGatewayStatusError=\u8FB9\u754C\u7F51\u5173\u72B6\u6001\u5FC5\u987B\u4E3Aattach\u72B6\u6001
DirectConnectGatewayRemoteCidrIsExist=\u8FB9\u754C\u7F51\u5173\u7684\u8FDC\u7AEF\u5730\u5740\u5DF2\u7ECF\u5B58\u5728
DirectConnectGatewayIsNotFound=\u8FB9\u754C\u7F51\u5173\u6CA1\u6709\u627E\u5230
DcNatTypeInvalid=\u4E13\u7EBFNat\u7C7B\u578B \u4EC5\u652F\u6301 snat \u548C dnat
DcNatUnbindSwitchInterfaceInvalid=\u8BF7\u5148\u6E05\u9664\u8FB9\u754C\u7F51\u5173{0}\u7684NatIP\u8F6C\u6362\u89C4\u5219,\u518D\u8FDB\u884C\u89E3\u7ED1\u4E13\u7EBF\u901A\u9053\u64CD\u4F5C
DirectConnectBgpRoutTypeInvalid=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u6A21\u5F0F\u975E\u6CD5
DirectConnectReliabilityMethodInvalid=\u53EF\u9760\u6027\u65B9\u6CD5\u975E\u6CD5,\u53D6\u503C\u4E3Abfd\u6216nqa
DirectConnectBgpRoutAsnInvalid=\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5BGP\u8DEF\u7531\u6A21\u5F0FASN\u503C\u975E\u6CD5
DirectConnectBgpRoutAsnNotEmpty=\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5BGP\u8DEF\u7531\u6A21\u5F0FASN\u503C\u4E0D\u80FD\u4E3A\u7A7A
DirectConnectVlanInvalid=\u8FDE\u63A5\u901A\u9053Vlan\u503C\u975E\u6CD5\uFF0C\u8BF7\u8F93\u51652-4000\u4E4B\u95F4\u7684Vlan\u503C
DirectConnectHaVlanNotEmpty=\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5\u53CC\u7EBF\u9AD8\u53EF\u7528\u6A21\u5F0FVLANID\u4E0D\u80FD\u4E3A\u7A7A
DirectConnectIdAndHaNotSame=\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5\u53CC\u7EBF\u9AD8\u53EF\u7528\u6A21\u5F0F\u7269\u7406\u7AEF\u53E3\u4E0D\u80FD\u76F8\u540C
DirectConnectVlanAndHaMushSame=\u4E13\u7528\u7F51\u7EDC\u8FDE\u63A5\u53CC\u7EBF\u9AD8\u53EF\u7528\u6A21\u5F0F\u7269\u7406\u7AEF\u53E3\u662F\u5426\u652F\u6301VLAN\u5FC5\u987B\u76F8\u540C
DirectConnectVlanNotSupport=\u8BE5\u7269\u7406\u7AEF\u53E3\u4E0D\u652F\u6301VLAN
DirectConnectIPMultiVersionNotSupport=\u540C\u4E00\u7F51\u5173\u4E0D\u652F\u6301\u7ED1\u5B9A\u591A\u79CDip\u7C7B\u578B\u7684\u94FE\u63A5\u901A\u9053
DirectConnectCustomerIpv6PeerIpInvalid=\u5BA2\u6237\u4FA7\u4E92\u8054ipv6\u4E92\u8054ip\u683C\u5F0F\u4E0D\u5408\u6CD5
DirectConnectCustomerIpv4PeerIpInvalid=\u5BA2\u6237\u4FA7\u4E92\u8054ipv4\u4E92\u8054ip\u683C\u5F0F\u4E0D\u5408\u6CD5
DirectConnectLocalIpv6PeerIpInvalid=\u91D1\u5C71\u4E91\u4FA7\u4E92\u8054ipv6\u4E92\u8054ip\u683C\u5F0F\u4E0D\u5408\u6CD5
DirectConnectLocalIpv4PeerIpInvalid=\u91D1\u5C71\u4E91\u4FA7\u4E92\u8054ipv4\u4E92\u8054ip\u683C\u5F0F\u4E0D\u5408\u6CD5
DestinationCidrBlockInvalid=\u76EE\u6807\u7F51\u6BB5\u683C\u5F0F\u4E0D\u5408\u6CD5
InputParameterFormatInvalid=\u5F53\u524D\u4EA7\u54C1\u7EBF\u8C03\u7528\u65B9\u5F0F\u4E0D\u652F\u6301\u6B64\u65B9\u6CD5\u8C03\u7528 productline \uFF0C[{0}] ,\u8BF7\u4F7F\u7528producttype\u5165\u53C2\u65B9\u5F0F\u8C03\u7528
ResourceTypeFormatFail=\u672A\u83B7\u53D6\u5230\u8D44\u6E90\u7C7B\u578B\uFF0C\u8BF7\u786E\u8BA4\u53C2\u6570productline [{0}] \u4EE5\u53CAproducttype [{1}]
RealServerInstanceTypeInvalid=\u670D\u52A1\u5668\u7EC4\u4E0D\u652F\u6301[{0}]\u7C7B\u578B

EipChargeTypeInvalid=\u8BA1\u8D39\u7C7B\u578B\u5FC5\u987B\u4E3A\u5305\u5E74\u5305\u6708\uFF0C\u6309\u5CF0\u503C\u6708\u7ED3\uFF0C\u6309\u65E5\u6708\u7ED3\uFF0C\u6309\u6B21\u7ED3\u7B97\uFF0C\u6309\u6B21\u540E\u7ED3\uFF0C\u6309\u6D41\u91CF\u6708\u7ED3\uFF0C\u5730\u57DF\u5171\u4EAB\u5E26\u5BBD
CheckInstanceTradeStatus=\u5B9E\u4F8BID{0}\u4E0D\u652F\u6301\u5F53\u524D\u8BA1\u8D39\u7C7B\u578B\u4E0B\u7684\u5220\u9664\u64CD\u4F5C
CheckInstanceStatus=\u5B9E\u4F8BID{0}\u4E0D\u652F\u6301\u5F53\u524D\u5B9E\u4F8B\u72B6\u6001\u7684\u5220\u9664\u64CD\u4F5C

VpnGatewayBandWidthInvalid=\u5E26\u5BBD\u503C\u5FC5\u987B\u662F <<< {0} >>> \u4E2D\u7684\u4E00\u4E2A
VpnGatewayChargeTypeInvalid=Vpn\u7F51\u5173\u7684\u8BA1\u8D39\u65B9\u5F0F\u5FC5\u987B\u6709\u4E3AMonthly\u6216\u8005Daily
VpnGatewayBandWithOnlyUpdate=\u5305\u5E74\u5305\u6708\u53EA\u652F\u6301\u5E26\u5BBD\u5347\u914D
CustomerGatewayIdInvalid=\u5BA2\u6237\u7F51\u5173\u65E0\u6548

QueryEsResourceError=\u67E5\u8BE2\u8D44\u6E90\u51FA\u73B0\u9519\u8BEF

LineNotSupport=\u94FE\u8DEFId <<<{0}>>> \u4E0D\u652F\u6301\u5728openapi\u8D2D\u4E70
BandWidthShareEipIsTrial=\u8BD5\u7528\u7684EIP\u65E0\u6CD5\u52A0\u5165\u5171\u4EAB\u5E26\u5BBD
BandWidthShareEipIsMonthly=\u5305\u5E74\u5305\u6708\u7684EIP\u65E0\u6CD5\u52A0\u5165\u5171\u4EAB\u5E26\u5BBD
SubOrderIdDup=\u5B50\u8BA2\u5355\u53F7\u88AB\u91CD\u590D\u8C03\u7528

ProjectMemberNotExist=\u5F53\u524D\u7528\u6237\u5E76\u4E0D\u5728\u9879\u76EE[{0}]\u4E2D
NotExistProjectId=\u9879\u76EE[{0}]\u4E0D\u5B58\u5728

SubUserIdWithoutPolicy=\u5B50\u8D26\u53F7\u6CA1\u6709\u652F\u4ED8\u7684\u6743\u9650

HostHeaderNotExists=\u57DF\u540D <<< {0} >>> \u4E0D\u5B58\u5728

PackageNotExists=\u60A8\u6CA1\u6709\u8BE5\u4EA7\u54C1\u5408\u9002\u7684\u5957\u9910\u6743\u9650, \u5F53\u524D\u673A\u623F[{0}], \u5E26\u5BBD\u503C[{1}], \u8BA1\u8D39\u65B9\u5F0F[{2}]
SlbPackageNotExists=\u60A8\u6CA1\u6709\u8BE5\u4EA7\u54C1\u5408\u9002\u7684\u5957\u9910\u6743\u9650, \u5F53\u524D\u673A\u623F[{0}]
NatPackageNotExists=\u60A8\u6CA1\u6709\u8BE5\u4EA7\u54C1\u5408\u9002\u7684\u5957\u9910\u6743\u9650, \u5F53\u524D\u673A\u623F[{0}], \u5E26\u5BBD\u503C[{1}], \u8BA1\u8D39\u65B9\u5F0F[{2}], IP\u6570\u91CF[{3}]
PeerPackageNotExists=\u60A8\u6CA1\u6709\u8BE5\u4EA7\u54C1\u5408\u9002\u7684\u5957\u9910\u6743\u9650,\u53D1\u8D77\u7AEF\u673A\u623F[{0}], \u63A5\u6536\u7AEF\u673A\u623F[{1}], \u8BA1\u8D39\u65B9\u5F0F[{2}], \u5E26\u5BBD\u503C[{3}]

NatInAddIpNumProcess=NatId {0} \u6B63\u5728\u8FDB\u884C\u589E\u52A0IP\u7684\u64CD\u4F5C
CheckDistributedLockError=\u6821\u9A8C {0} \u5B9E\u4F8B {1} \u64CD\u4F5C\u9501\u51FA\u73B0\u95EE\u9898,\u8BF7\u91CD\u8BD5\u6216\u8005\u8054\u7CFB\u7BA1\u7406\u5458
NatIpNumberOverQuota=\u521B\u5EFA\u6216\u8005\u4FEE\u6539Nat\u4E2D\u7684Ip\u8D85\u8FC7\u914D\u989D\u503C{0}
NatIpNotSupportReduce=\u5305\u5E74\u5305\u6708\u7684NAT\u4E0D\u652F\u6301IP\u6570\u91CF\u51CF\u5C11
NatIpMustHaveOne=Nat {0} \u81F3\u5C11\u9700\u8981\u5B58\u5728\u4E00\u4E2AIP
NatIpMustHaveOneUnboundDNatIpMap=Nat {0} \u81F3\u5C11\u9700\u8981\u5B58\u5728\u4E00\u4E2A\u672A\u7ED1\u5B9A\u7684 DNAT IP \u6620\u5C04\u7684 IP
NatIpNotEnabled=\u6B64 IP {0} \u5904\u4E8E\u88AB\u7981\u7528\u72B6\u6001,\u8BF7\u68C0\u67E5\u6B64IP\u72B6\u6001\u540E\u518D\u8FDB\u884C\u64CD\u4F5C
DnatIpDisEnabled=\u6B64 dnat\u7ED1\u5B9A\u7684nat ip/eip {0} \u5904\u4E8E\u7981\u7528\u72B6\u6001\uFF0Cdnat\u672A\u751F\u6548\uFF0C\u8BF7\u542F\u52A8\u76F8\u5E94\u7684nat ip/eip\u5728\u8FDB\u884C\u76F8\u5173\u64CD\u4F5C
NatIpNumError=Nat\u7684IP\u6570\u91CF\u548C\u8BA2\u5355\u7684\u6570\u91CF\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u4E0D\u8981\u5728\u63A7\u5236\u53F0\u5E76\u53D1\u5BF9nat\u505A\u589E\u51CFIP\u7684\u64CD\u4F5C
NatIpIsUsedByDnat=\u0020Ip {0} \u88ABDnat\u4F7F\u7528
NatIpIsUsedBySnat=\u0020Ip {0} \u88ABSnat\u4F7F\u7528
NatPortIsUsedByDnat=Nat\u0020Port\u0020 {0} \u0020\u88ABDnat\u4F7F\u7528
NatHasNoAvailableIp=Nat\u6CA1\u6709\u53EF\u7528\u7684Ip/EIP
PrivateNatNotSupportModifyIpNum=Nat {0} \u7C7B\u578B\u662F\u5185\u7F51\uFF0C\u4E0D\u652F\u6301\u66F4\u6539IP\u4E2A\u6570
BwsEipChargeTypeError=\u79FB\u5165\u5171\u4EAB\u5E26\u5BBD\u7684EIP\u7684\u8BA1\u8D39\u65B9\u5F0F\u53EA\u652F\u6301{0}
EipBindwidthOverLimit=\u6309\u91CF\u4ED8\u8D39\u65F6\u5E26\u5BBD\u4E0D\u80FD\u5927\u4E8E {0}
InstanceNotFound=\u8D44\u6E90ID [{0}] \u6CA1\u6709\u627E\u5230
NatAddNumberAndNatIpBothNull= AddNumber \u548C Natip \u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
NatAddNumberAndNatIpBothNotNull= AddNumber \u548C Natip \u4E0D\u80FD\u540C\u65F6\u5B58\u5728
NatAddNumberMoreThanQuota= NAT\u5141\u8BB8\u6DFB\u52A0NAT IP/EIP\u7684\u4E2A\u6570\u8D85\u8FC7\u914D\u989D
NatVersionNotSupportBindEip= 1.0\u7248\u672C\u7684Nat {0} \u4E0D\u652F\u6301\u7ED1\u5B9A\u5F39\u6027IP
NatAvailableIPLess=Nat {0} \u5F53\u524D\u65E0\u53EF\u7528\u4E8Esant\u7684IP/EIP,\u6240\u6709IP/EIP\u5747\u88AB\u5168\u7AEF\u53E3Dnat\u5360\u7528
PeerProviderNotExists=\u65E0\u6CD5\u83B7\u53D6\u5230\u4F9B\u5E94\u5546\u4FE1\u606F\uFF0C\u8BF7\u8054\u7CFB\u8FD0\u8425\u6216\u552E\u540E

AlbSpecInvalid=\u8D1F\u8F7D\u5747\u8861\u7684\u89C4\u683C\u4E0D\u6B63\u786E\uFF0C\u6709\u6548\u503C{0}
AlbSpecNotLarger=\u8D1F\u8F7D\u5747\u8861\u7684\u89C4\u683C\u4E0D\u5141\u8BB8\u964D\u914D, \u4E4B\u524D {0}, \u73B0\u5728{1}
InvalidStartEndIPAddr=IP\u5730\u5740 <<< {0} >>> \u4E0D\u5728\u5B50\u7F51\u7F51\u6BB5 <<< {1} >>> \u8303\u56F4
PrivateIpAddressOnProcess = IP\u5730\u5740 <<< {0} >>> \u6B63\u5728\u64CD\u4F5C, \u8BF7\u7A0D\u5019\u91CD\u8BD5
ApiNotSupportRegion=\u673A\u623F {0} \u4E0D\u652F\u6301\u8BE5\u63A5\u53E3
MirrorGroupApiNotSupportRegion=\u673A\u623F {1} \u4E0D\u652F\u6301\u521B\u5EFA\u955C\u50CF\u670D\u52A1\u5668

SlbCloneSwitchOff=\u65E0\u514B\u9686\u529F\u80FD\u6743\u9650
NatPrivateTypeNotSupport=\u5F53\u524D\u673A\u623F\u4E0D\u652F\u6301\u91D1\u5C71\u4E91\u5185\u7F51\u7C7B\u578Bnat\u521B\u5EFA
IPV6CidrOnlySupportNormal=IPv6\u4E0D\u652F\u6301\u4E91\u7269\u7406\u673A\u7C7B\u578B\u7684\u5B50\u7F51
RouteTableOnlySupportNormal=\u8DEF\u7531\u8868\u4EC5\u652F\u6301\u4E91\u670D\u52A1\u5668\u7C7B\u578B\u7684\u5B50\u7F51
Ipv6PublicIpIsExist=IPV6\u516C\u7F51\u80FD\u529B\u5DF2\u7ECF\u5B58\u5728
RedisSlbTimeCheck=slb\u514B\u9686\u4E00\u5206\u949F\u53EA\u80FD\u70B9\u51FB\u4E00\u6B21

QuotaNotAllowAttach={0} \u5728\u914D\u989D\u7CFB\u7EDF\u4E2D\u914D\u7F6E\u4E0D\u652F\u6301\u7ED1\u5B9A,\u8BF7\u8054\u7CFB\u5BA2\u6237
QuotaNotAllowNotSupportedUse = {0} \u5728\u914D\u989D\u7CFB\u7EDF\u4E2D\u4E0D\u5141\u8BB8\u4F7F\u7528\u8BE5\u5B57\u6BB5,\u8BF7\u8054\u7CFB\u5BA2\u670D\u5F00\u901A\u540E\u4F7F\u7528
ServiceNotActivated=\u60A8\u672A\u5F00\u901A\u6B64\u670D\u52A1\u4F7F\u7528\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D
UserTagNotSupport={0} user tag \u4E0D\u652F\u6301\u5F53\u524D\u64CD\u4F5C
EipOnlySupportBindTagConsole=\u5F39\u6027IP\u4EC5\u652F\u6301\u7ED1\u5B9Auser tag\u4E3Aconsole\u7684\u8D44\u6E90\uFF0C\u5F53\u524Duser tag\u4E3A {0}

BindWafFailed=\u7ED1\u5B9Awaf\u5931\u8D25
UnBindWafFailed=\u89E3\u7ED1waf\u5931\u8D25
EIPIsSourceIp=\u8BE5\u5F39\u6027IP\u5DF2\u7ECF\u7ED1\u5B9A\u5230WAF\u5B9E\u4F8B\u7684\u56DE\u6E90IP\u4E2D\uFF0C\u4E0D\u80FD\u540C\u65F6\u7ED1\u5B9AWAF\u5B9E\u4F8B\uFF0C\u8BF7\u5220\u9664\u56DE\u6E90IP\u540E\u91CD\u65B0\u7ED1\u5B9A
HostZoneIsReserved=\u5F53\u524D\u57DF\u540D\u662F\u4FDD\u7559\u57DF\u540D,\u65E0\u6CD5\u4F7F\u7528
BandwidthIllegal=\u8BE5\u4EA7\u54C1\u8BA1\u8D39\u65B9\u5F0F[{0}], \u94FE\u8DEF\u7C7B\u578B[{1}], \u5141\u8BB8\u8D2D\u4E70\u7684\u6700\u5C0F\u5E26\u5BBD\u503C\u4E3A[{2}], \u5F53\u524D\u5E26\u5BBD\u503C\u4E3A[{3}]
UnlimitChargeTypeBandwidthIllegal=\u8BE5\u4EA7\u54C1\u8BA1\u8D39\u65B9\u5F0F[{0}], \u5141\u8BB8\u8D2D\u4E70\u7684\u6700\u5C0F\u5E26\u5BBD\u503C\u4E3A[{1}], \u5F53\u524D\u5E26\u5BBD\u503C\u4E3A[{2}]

DhcpListIpAddressExistMustSubNetId=\u67E5\u8BE2\u5185\u7F51IP\u4FE1\u606F\uFF0C\u67E5\u8BE2\u6761\u4EF6\u4E2D\u5B58\u5728IP\u5730\u5740\uFF0C\u5B50\u7F51ID\u4E0D\u80FD\u4E3A\u7A7A
AllocationStatusSizeInvalid=\u6682\u53EA\u652F\u6301\u4E00\u79CDIP\u72B6\u6001\u7684\u67E5\u8BE2
SubnetIpStatusAndAllocateStatusInvalid=\u5B50\u7F51IP\u72B6\u6001\u548C\u5206\u914D\u72B6\u6001\u4E0D\u80FD\u540C\u65F6\u5B58\u5728,\u4E14\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
SubnetIdNotEmpty=\u5B50\u7F51ID\u4E0D\u80FD\u4E3A\u7A7A
SubnetIpStatusNotEmpty=IP\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
SubnetIdSizeInvalid=\u6682\u53EA\u652F\u6301\u5355\u5B50\u7F51IP\u67E5\u8BE2
ReserveSubnetNotSupportReserve=\u4E0D\u652F\u6301\u7EC8\u7AEF\u5B50\u7F51\u9884\u7559IP\u5730\u5740
SubnetNotExist=\u5B50\u7F51\u4E0D\u5B58\u5728
IpAddressInvalid=Ip\u5730\u5740\u4E0D\u5408\u6CD5

AccessLogsEnabledEmpty=accessLogsEnabled\u4E0D\u80FD\u4E3A\u7A7A
AccessLogsBucketNameInvalid=bucket\u5730\u5740\u975E\u6CD5
SlbNotAuthorizationKs3Log=\u5C1A\u672A\u6388\u6743\u7ED9SLB\u5B98\u65B9\u670D\u52A1\u8D26\u53F7\u8BBF\u95EEks3\u6743\u9650
OpenLogBucketNameEmpty=bucket\u5730\u5740\u4E0D\u80FD\u4E3A\u7A7A
OpenLbLogFailed=\u5F00\u542F7\u5C42\u8D1F\u8F7D\u5747\u8861\u8BBF\u95EE\u65E5\u5FD7\u5931\u8D25
CloseLbLogFailed=\u5173\u95ED7\u5C42\u8D1F\u8F7D\u5747\u8861\u8BBF\u95EE\u65E5\u5FD7\u5931\u8D25
BucketNameNullOpenLbLogFailed=\u5904\u4E8E\u65E5\u5FD7\u5F00\u542F\u72B6\u6001\u7684bucket\u4E0D\u80FD\u4E3A\u7A7A
GetTempAkSkFaild=\u83B7\u53D6\u4E34\u65F6AKSK\u5931\u8D25
AccessLogsOnlyPublicLb=\u4EC5\u652F\u6301\u516C\u7F51SLB\u5F00\u542F\u8BBF\u95EE\u65E5\u5FD7
AccessLogsInternalOnlySeventh=\u79C1\u7F51\u7C7B\u578B\u7684Slb\u4EC5HTTP/HTTPS\u7C7B\u578B\u7684\u652F\u6301\u5F00\u542F\u8BBF\u95EE\u65E5\u5FD7.
VnetNotBelongThisVpc=\u5B50\u7F51\u4E0EVPC\u4E0D\u5339\u914D
SubnetNameNotSupportExactAndFuzzyQuery=\u5B50\u7F51\u540D\u79F0\u4E0D\u80FD\u540C\u65F6\u652F\u6301\u7CBE\u786E\u67E5\u8BE2\u548C\u6A21\u7CCA\u67E5\u8BE2
EndpointVnetTypeError=\u5B50\u7F51\u5FC5\u987B\u4E3A\u7EC8\u7AEF\u5B50\u7F51
VnetVisitInternetNotMatch=\u8BE5Vif\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0E\u5176Vnet\u4E0D\u5339\u914D
VnetVisitInternetNotSupportEip=\u8BE5\u7F51\u5361\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0D\u652F\u6301\u7ED1\u5B9AEIP
VnetVisitInternetNotSupportNat=\u8BE5\u5B50\u7F51\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0D\u652F\u6301\u7ED1\u5B9ANat
VifVisitInternetNotSupportNat=\u8BE5\u7F51\u5361\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0D\u652F\u6301\u7ED1\u5B9ANat
VisitInternetEpcNotSupportLb=\u6B64EPC/KEC\u6240\u5C5E\u7684\u5B50\u7F51\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0D\u652F\u6301\u7ED1\u5B9A\u516C\u7F51\u8D1F\u8F7D\u5747\u8861
VisitInternetEpcNotSupportLbMemberGroup=\u6B64EPC/KEC\u6240\u5C5E\u7684\u5B50\u7F51\u9650\u5236\u516C\u7F51\u8BBF\u95EE\u4E0D\u652F\u6301\u7ED1\u5B9A\u670D\u52A1\u5668\u7EC4
VnetNotVisitInternetNotSupportHavip=\u8BE5\u7F51\u5361\u7684VisitInternet\u4E0D\u652F\u6301\u521B\u5EFAHavip
VnetHasVPCNat=\u6B64\u7981\u6B62\u516C\u7F51\u8BBF\u95EE\u5B50\u7F51\u521B\u5EFA\u6210\u529F,\u4F46\u5176\u6240\u5C5EVPC\u5B58\u5728VPC\u7EA7\u522BNAT<<< {0} >>>\u4E14\u81EA\u52A8\u89E3\u7ED1\u5931\u8D25,\u8BF7\u524D\u5F80Nat\u9875\u9762\u5C06\u8BE5\u5B50\u7F51\u4E8Esnat\u4E0B\u89E3\u7ED1\u518D\u8FDB\u884C\u4F7F\u7528\u8BE5\u5B50\u7F51
DcGatewayStatusInvalid=\u8FB9\u754C\u7F51\u5173 <<< {0} >>> \u975EActive\u72B6\u6001\u6216\u5DF2\u5904\u4E8E\u7ED1\u5B9A\u72B6\u6001\u6216\u7248\u672C\u975E1.0
DcRouteIdNotFound=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531 <<< {0} >>> \u627E\u4E0D\u5230\uFF0C\u8BF7\u786E\u8BA4\u540E\u64CD\u4F5C
DcRouteTypeNotDC=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u975EDirectConnect\u7C7B\u578B\uFF0C\u65E0\u6CD5\u53D1\u5E03\u5230\u4E91\u4F01\u4E1A\u7F51\uFF0C\u6216\u662F\u5DF2\u53D1\u5E03\u72B6\u6001
DcRouteMustStatic=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u5FC5\u987B\u662F\u9759\u6001
DcRouteNextHopTypeMustVpc=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u4E0B\u4E00\u8DF3\u7C7B\u578B\u5FC5\u987B\u662FVPC
BgpDcInterfaceNotSupportNqa=BGP \u8DEF\u7531\u7684\u8FDE\u63A5\u901A\u9053\u4E0D\u652F\u6301nqa\u53EF\u9760\u6027\u65B9\u6CD5
DcGatewayHaveInterfaces=\u8FB9\u754C\u7F51\u5173\u7ED1\u5B9A\u4E86\u8FDE\u63A5\u901A\u9053\uFF0C\u65E0\u6CD5\u5220\u9664
DcGatewayRouteNextHopTypeInvalid=NextHopType \u53EA\u652F\u6301 Vpc|Cen|DirectConnect
DcGatewayRoutePriorityInvalid=Priority \u53EA\u652F\u6301 20|40|60|80|100
DCGatewayRouteCidrBlocksEmpty=\u76EE\u6807\u7F51\u6BB5\u4E0D\u80FD\u4E3A\u7A7A
DCGatewayRouteTypeNotSupportIpv6=\u6B64\u4E0B\u4E00\u8DF3\u7C7B\u578B\u4E0D\u652F\u6301ipv6
DCGatewayRouteNextHopQuantityLimit=\u76EE\u6807\u7F51\u6BB5\u6570\u91CF\u8D85\u8FC7\u9650\u5236
DCGatewayRouteCidrInvalid=CIDR {0} \u683C\u5F0F\u4E0D\u5408\u6CD5
DcRouteTypeNotVpcOrCen=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u975EVpc\u6216Cen\u7C7B\u578B\uFF0C\u65E0\u6CD5\u53D1\u5E03\u5230BGP\uFF0C\u6216\u662F\u5DF2\u53D1\u5E03\u72B6\u6001
CrtInvalid=\u8BC1\u4E66\u4E0D\u5408\u6CD5
CrtKeyNotTogetherUpdate=\u516C\u94A5\u548C\u79C1\u94A5\u5FC5\u987B\u4E00\u8D77\u66F4\u65B0
CannotEnableIPv6=\u6CA1\u6709\u5F00\u542FIPv6\u7684\u6743\u9650
VpcHasDcRoute=Vpc <<< {0} >>> \u4E0B\u5B58\u5728\u8BE5\u8FB9\u754C\u7F51\u5173\u5173\u8054\u7684\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\uFF0C\u8BF7\u5148\u5220\u9664\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u518D\u8FDB\u884C\u89E3\u7ED1
VpcHasDcRouteTableRoute=Vpc <<< {0} >>> \u4E0B\u5B58\u5728\u8BE5\u8FB9\u754C\u7F51\u5173\u5173\u8054\u7684\u8DEF\u7531\u8868\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\uFF0C\u8BF7\u5148\u5220\u9664\u8DEF\u7531\u8868\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u518D\u8FDB\u884C\u89E3\u7ED1
ResourceTagNotSupport=\u8D44\u6E90\u7684\u6807\u7B7E <<< {0} >>> \u4E0D\u5141\u8BB8\u6267\u884C\u6B64\u64CD\u4F5C
ProductTypeNotSupport=\u8D44\u6E90\u7684\u5546\u54C1\u7C7B\u578B <<< {0} >>> \u4E0D\u5141\u8BB8\u6267\u884C\u6B64\u64CD\u4F5C
ProductTagNotSupport=\u7279\u6B8A\u8D44\u6E90\u7684\u4EA7\u54C1\u6807\u8BC6 <<< {0} >>> \u4E0D\u5141\u8BB8\u6267\u884C\u6B64\u64CD\u4F5C
StartTimeAndEndTimeInvalid=\u8F93\u5165\u65F6\u95F4\u4E0D\u5408\u6CD5
EndTimeLessStartTime=\u622A\u6B62\u65F6\u95F4\u4E0D\u80FD\u5C0F\u4E8E\u8D77\u59CB\u65F6\u95F4
EndTimeSubtractStartTimeLessTen=\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u8D85\u8FC710\u5206\u949F
EndTimeSubtractStartTimeLessThreeTenDay=\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u8D85\u8FC730\u5929
EndTimeLessSystemTime=\u622A\u6B62\u65F6\u95F4\u5FC5\u987B\u5C0F\u4E8E\u7CFB\u7EDF\u65F6\u95F4

VpcNotSupportIpv6=\u5B50\u7F51\u6240\u5728VPC\u4E0D\u652F\u6301IPv6
CreateRouteVpcNotSupportIpv6=\u8BE5\u7F51\u5173\u7ED1\u5B9A\u7684VPC\u4E0D\u652F\u6301IPV6
VnetTypeOrHostTypeIllegal=\u5B50\u7F51\u7C7B\u578B\u4E0D\u652F\u6301\u5F00\u901AIPv6
UserNotSupportInterface=\u5F53\u524D\u7528\u6237\u4E0D\u652F\u6301\u8BE5\u673A\u623F\u7684\u8BE5\u63A5\u53E3
DestinationCidrNotSupport=\u5F53\u524D\u673A\u623F\u4E3B\u673A\u8DEF\u7531\u6682\u4E0D\u652F\u6301\u8BE5\u7F51\u6BB5
ProductGroupNotSupport=\u6682\u4E0D\u652F\u6301\u8BE5\u4EA7\u54C1\u7EC4
InstanceTypeError=\u9519\u8BEF\u7684\u5957\u9910ID\u6216\u8BE5\u7528\u6237\u65E0\u6B64\u5957\u9910\u6743\u9650
UserNotPermissThisProduct=\u60A8\u6CA1\u6709\u8BE5\u4EA7\u54C1\u76F8\u5173\u5957\u9910\u6743\u9650
ProductTypeError=\u9519\u8BEF\u7684\u5546\u54C1\u7C7B\u578B\u6216\u8F93\u5165\u7684\u4EA7\u54C1\u7EC4\u4E0B\u65E0\u6B64\u5546\u54C1\u7C7B\u578B
MonthlyNotSupportTrial=\u5305\u5E74\u5305\u6708\u4E0D\u652F\u6301\u8BD5\u7528
SubOrderNotExist=\u5B50\u8BA2\u5355\u4E0D\u5B58\u5728
InstanceResourceNotExist=\u5B9E\u4F8B\u5E95\u5C42\u8D44\u6E90\u4E0D\u5B58\u5728
SubOrderStatusNotModify=\u5B50\u8BA2\u5355\u72B6\u6001 <<< {0} >>> \u5DF2\u662F\u6700\u7EC8\u72B6\u6001\uFF0C\u4E0D\u80FD\u4FEE\u6539
NotThisInstanceOrder=\u4E0D\u662F\u8BE5\u5B9E\u4F8B\u7684\u5B50\u8BA2\u5355\uFF0C\u8BF7\u6838\u5BF9\u540E\u518D\u8BD5
ProductNotExist=\u5546\u54C1\u4E0D\u5B58\u5728
InstanceIdEmpty=\u5B9E\u4F8BID\u4E0D\u80FD\u4E3A\u7A7A
ListenerNotTcpOrUdp=\u76D1\u542C\u5668\u534F\u8BAE\u5FC5\u987B\u662FTCP\u6216UDP\u7C7B\u578B
ListenerBindTypeIsRealServer=\u76D1\u542C\u5668\u7684\u6302\u8F7D\u7C7B\u578B\u4E3ARealServer
ListenerNotHttpOrHttps=\u76D1\u542C\u5668\u534F\u8BAE\u5FC5\u987B\u662FHTTP\u6216HTTPS\u7C7B\u578B
RealServerGroupNotMirror=\u540E\u7AEF\u670D\u52A1\u5668\u7EC4\u5FC5\u987B\u662F\u955C\u50CF\u7C7B\u578B
BackendServerGroupInvalid=\u540E\u7AEF\u670D\u52A1\u5668\u7EC4\u4E0D\u80FD\u662F\u955C\u50CF\u7C7B\u578B\u6216Alb\u7C7B\u578B
ListenerAssociatedBackendServerGroup=\u5F53\u524D\u76D1\u542C\u5668\u5DF2\u7ED1\u5B9A\u5176\u4ED6\u670D\u52A1\u5668\u7EC4
ServerGroupParamInvalid=HealthCheckState|HealthyThreshold|Interval|Timeout|UnhealthyThreshold\u4EFB\u610F\u4E00\u9879\u4E0D\u80FD\u4E3A\u7A7A
BackendServerGroupTypeInvalid=\u670D\u52A1\u7EC4\u7C7B\u578B\u5FC5\u987B\u662FServer\u6216Mirror
BackendServerGroupInstanceTypeInvalid=\u670D\u52A1\u7EC4\u5B9E\u4F8B\u7C7B\u578B\u5FC5\u987B\u662FHost\u6216Directconnect
BackendServerGroupNotAssociatedListener=\u670D\u52A1\u5668\u7EC4\u672A\u4E0E\u8BE5\u76D1\u542C\u5668\u7ED1\u5B9A\uFF0C\u8BF7\u786E\u8BA4\u540E\u518D\u64CD\u4F5C
ThisActionNotSupportDirectOpt=\u9AD8\u9632\u5305\u5546\u54C1\u7C7B\u578B\u8D44\u6E90\u4E0D\u5141\u8BB8\u6267\u884C\u8BE5\u64CD\u4F5C
CommonConfigProductTypeActionAuthConfError=CommonConfig\u4E2DProductTypeActionAuth\u914D\u7F6E\u63A5\u53E3\u62E6\u622A\u5B9E\u4F8BID\u5B57\u6BB5\u4E0D\u5B58\u5728
Ipv4VipNotSupportIpv6Rs=IPV4\u7C7B\u578B\u7684\u76D1\u542C\u5668\u65E0\u6CD5\u6DFB\u52A0IPV6\u7684\u771F\u5B9E\u670D\u52A1\u5668
DomainNotMatchError=\u672A\u627E\u5230\u76F8\u5173\u670D\u52A1\u5668\u6216\u8D1F\u8F7D\u5747\u8861\u4E0E\u670D\u52A1\u5668VPC\u4E0D\u540C
Ipv6RealServerTypeMissMatchListenerType=HTTP\u3001HTTPS\u7C7B\u578B\u7684\u76D1\u542C\u5668\u4E0D\u80FD\u7ED1\u5B9AIPV6\u7C7B\u578B\u7684\u670D\u52A1\u5668
InnerSlbHttpsInvalid=\u5185\u7F51\u8D1F\u8F7D\u5747\u8861\u4E0D\u652F\u6301HTTPS\u76D1\u542C\u5668
MemberTypeInvalid=\u771F\u5B9E\u670D\u52A1\u5668\u7C7B\u578B\u4E0E\u5DF2\u6709\u7684\u7C7B\u578B\u4E0D\u540C\uFF0C\u8BF7\u786E\u8BA4\u540E\u64CD\u4F5C
DirectConnectGatewayMemberNotSupportUdpProtocol=UDP\u7C7B\u578B\u7684\u76D1\u542C\u5668\u4E0D\u80FD\u7ED1\u5B9A\u8FB9\u754C\u7F51\u5173\u5B9E\u4F8B
VpnCiscoIkeEncryAlgorithmInvalid=IkeEncryAlgorithm\u53EA\u652F\u6301 3des|aes|des
VpnH3cIkeEncryAlgorithmInvalid=IkeEncryAlgorithm\u53EA\u652F\u6301 3des|aes|des|aes-cbc-192|aes-cbc-256|sm1-cbc-128|sm4-cbc

VpnCiscoIpsecEncryAlgorithmInvalid=IpsecEncryAlgorithm\u53EA\u652F\u6301 esp-3des|esp-aes|esp-des|esp-null|esp-seal
VpnH3cIpsecEncryAlgorithmInvalid=IpsecEncryAlgorithm\u53EA\u652F\u6301 esp-3des|esp-aes|esp-des|esp-null|aes-cbc-192|aes-cbc-256|sm1-cbc-128|sm4-cbc

VpnCiscoIpsecAuthenAlgorithmInvalid=IpsecAuthenAlgorithm\u53EA\u652F\u6301 esp-md5-hmac|esp-sha-hmac
VpnH3cIpsecAuthenAlgorithmInvalid=IpsecAuthenAlgorithm\u53EA\u652F\u6301 esp-md5-hmac|esp-sha-hmac|sha256|sha384|sha512|sm3

VpnNotFound=\u8BE5VPN\u7F51\u5173\u672A\u627E\u5230\uFF0C\u8BF7\u786E\u8BA4\u540E\u64CD\u4F5C
NetworkInterfaceNotExists=\u7F51\u5361 <<< {0} >>> \u4E0D\u5B58\u5728
NetworkInterfaceAndIpNotEq=\u5F39\u6027\u7F51\u5361<<< {0} >>>\u4E0E\u4F20\u5165\u7684ip<<< {1} >>>\u4E0D\u4E00\u81F4
NetworkInterfaceBind=\u5F53\u524D\u7F51\u5361\u5DF2\u7ED1\u5B9A\u5B9E\u4F8B\uFF0C\u8BF7\u89E3\u7ED1\u540E\u518D\u64CD\u4F5C
SecurityGroupIdsEmpty=\u5B89\u5168\u7EC4\u4E0D\u80FD\u4E3A\u7A7A
SecurityGroupIdExceedMax=\u5B89\u5168\u7EC4\u4E2A\u6570\u8D85\u8FC75\u4E2A

VipBandwidthInvalid=\u76D1\u542C\u5668\u7684\u5E26\u5BBD\u503C\u8BBE\u7F6E\u975E\u6CD5\uFF0C\u6709\u6548\u503C\u4E3A[1-10000]
RegionNotSupportInnerBandWidth=\u673A\u623F <<< {0} >>> \u4E0D\u652F\u6301\u8C03\u6574\u5185\u7F51SLB\u7684\u5E26\u5BBD
PublicVipNotSupportBandWidth=\u516C\u7F51\u8D1F\u8F7D\u5747\u8861\u4E0D\u652F\u6301\u8BE5\u65B9\u5F0F\u9650\u901F
ApigwCanNotBindVmOrEpc=\u8BE5\u5F39\u6027IP\u4E0D\u652F\u6301\u7ED1\u5B9A\u9664\u8D1F\u8F7D\u5747\u8861\u4EE5\u5916\u7684\u8D44\u6E90
EipFlowNumQuotaOut=\u6309\u91CF\u4ED8\u8D39\uFF08\u6D41\u91CF\uFF09\u548C\u6309\u91CF\u4ED8\u8D39\uFF08\u6D41\u91CF\u6708\u7ED3\uFF09\u8BA1\u8D39\u65B9\u5F0F\u7684eip\u6570\u91CF\u8D85\u8FC7\u6700\u5927\u914D\u989D\uFF0C\u6700\u5927\u914D\u989D\u4E3A {0} \u4E2A
IpVersionInvaild=IP\u7248\u672C\u652F\u6301ipv4\u6216ipv6
IpVersionNotMatch=ACL\u4E0E\u76D1\u542C\u5668\u7684IP\u7248\u672C\u4E0D\u5339\u914D, \u8BF7\u786E\u8BA4\u540E\u64CD\u4F5C
IpVersionMustSame=IP\u7248\u672C\u5FC5\u987B\u4E00\u81F4
SlbTypeInvalid=\u4EC5\u516C\u7F51LB\u53EF\u7ED1\u5B9A\u5F39\u6027IP
LbTypeInvaild=LbType \u53EA\u652F\u6301 classic|application
ApplicationSlbOnlySupportIpv4AndPublic=\u5E94\u7528\u578B\u8D1F\u8F7D\u5747\u8861\u4EC5\u652F\u6301IPV4\u548C\u516C\u7F51
VpcLbNotSupportRegion=\u673A\u623F {0} \u4E0D\u652F\u6301\u79C1\u7F51\u8D1F\u8F7D\u5747\u8861
ThisVpcNotAssociatePrivateDns=\u8BE5VPC\u672A\u5173\u8054\u6240\u5C5ERegion\u7684\u5185\u7F51DNS\u5B9E\u4F8B
ThisRegionToNeutronInnerDnsRegionNotExist=\u8BE5\u673A\u623F\u5BF9\u5E94\u7684\u5185\u7F51DNS\u673A\u623F\u4E0D\u5B58\u5728
ProjectStartSuccess=\u9879\u76EE\u542F\u52A8\u6210\u529F^_^
MinTxIntervalRangeInvalid=MinTxInterval\u8303\u56F4\u503C\u4E3A[100, 1000]
MinRxIntervalRangeInvalid=MinRxInterval\u8303\u56F4\u503C\u4E3A[100, 1000]
DetectMultiplierRangeInvalid=DetectMultiplier\u8303\u56F4\u503C\u4E3A[3, 50]
BwsMonthlyNotSupportRegion=\u5171\u4EAB\u5E26\u5BBD\u5305\u5E74\u5305\u6708\u4E0D\u652F\u6301\u8BE5\u673A\u623F
KeadEipMustBindBws=\u9AD8\u9632\u5F39\u6027IP\u5FC5\u987B\u52A0\u5165\u5171\u4EAB\u5E26\u5BBD\uFF0C\u5176\u5546\u54C1item\u5FC5\u987B\u5305\u542Bbws=1
BwsEipNetMustZero=\u79FB\u5165\u5171\u4EAB\u5E26\u5BBD\u7684\u5F39\u6027IP\u8BA2\u5355\u5E26\u5BBD\u503C\u5FC5\u987B\u4E3A0
InstanceIdNotEmptyOrOrder=\u901A\u77E5\u8BA2\u5355\u5931\u8D25\u5FC5\u987B\u4F20\u5165\u5B9E\u4F8BID\u5B57\u6BB5\uFF0C\u82E5\u8BE5\u8BA2\u5355\u4E0D\u5B58\u5728\u5B9E\u4F8BID\uFF0C\u8BF7\u76F4\u63A5\u8C03\u7528\u8BA2\u5355\u63A5\u53E3
ItemValueTypeError=\u4F20\u5165\u7684item: {0}\u53C2\u6570\u7C7B\u578B\u9519\u8BEF,\u652F\u6301\u7684\u53C2\u6570\u7C7B\u578B: {1}
RecordValueInvalid=\u8BB0\u5F55\u503C\u4E0D\u5408\u6CD5

TagProtectionModifyInvalid=\u7531\u4E8E\u8D44\u6E90 {0} \u542F\u7528\u4E86\u4FEE\u6539\u4FDD\u62A4\uFF0C\u56E0\u6B64\u65E0\u6CD5\u4FEE\u6539\u3002
TagProtectionDeleteInvalid=\u7531\u4E8E\u8D44\u6E90 {0} \u542F\u7528\u4E86\u5220\u9664\u4FDD\u62A4\uFF0C\u56E0\u6B64\u65E0\u6CD5\u5220\u9664\u3002
ProtectionStatusInvalid=\u8D44\u6E90\u4FDD\u62A4\u72B6\u6001\u503C\u4EC5\u5141\u8BB8 on \u3001 off

InstanceIdAndNetworkInterfaceIdCannotEmptyAtTheSameTime=InstanceId \u548C NetworkInterfaceId \u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
AddressAndAddressCountCannotEmptyAtTheSameTime=PrivateIpAddress \u548C SecondaryPrivateIpAddressCount \u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
PrivateIpOnlySupportVmAndEpc=PrivateIp \u4EC5\u652F\u6301 kec \u548C epc
PrivateIpOnlySupportVm=PrivateIp \u4EC5\u652F\u6301 kec
ExtensionNetworkInterfaceNeedParameterMode=NetworkInterface <<< {0} >>> \u9700\u8981\u53C2\u6570 Mode
NormalNeedParameterPrivateIpAddress=Normal \u6A21\u5F0F\u9700\u8981\u53C2\u6570 PrivateIpAddress
UserNotSupportEipBindedMode=\u7528\u6237\u4E0D\u652F\u6301eip\u53EF\u89C1\u6A21\u5F0F, \u8BF7\u8054\u7CFB\u5BA2\u670D
EipBindModeIpNumberOverQuota=NetworkInterface {0} \u7684EIP\u914D\u989D\u5DF2\u7528\u5C3D, \u8BF7\u8054\u7CFB\u5BA2\u670D
EipNotAllowChangeBindedMode=Eip \u4E0D\u5141\u8BB8\u66F4\u6539\u7ED1\u5B9A\u6A21\u5F0F
BindedNotSupportPrivateIpAddress=eip\u53EF\u89C1\u6A21\u5F0F\u4E0D\u652F\u6301\u53C2\u6570 PrivateIpAddress
PrimaryVifNotSupportParameterPrivateIpAddressAndMode=\u4E3B\u7F51\u5361\u4E0D\u652F\u6301\u53C2\u6570 PrivateIpAddress \u548C Mode 
LineIdNotSupport=\u6682\u4E0D\u652F\u6301\u8BE5\u94FE\u8DEF
MustUsePrivateLinkServerAccount=\u5220\u9664PrivateLink\u8D26\u53F7\u4E0D\u6B63\u786E
SystemRouteTableNotSupportDisassociate=\u9ED8\u8BA4\u8DEF\u7531\u8868\u4E0D\u652F\u6301\u89E3\u7ED1\u64CD\u4F5C
SubnetAndRouteTableNotMatch=\u5B50\u7F51\u4E0E\u8DEF\u7531\u8868\u672A\u7ED1\u5B9A\uFF0C\u65E0\u6CD5\u505A\u89E3\u7ED1\u64CD\u4F5C
InstanceHasOrder=\u5B9E\u4F8B <<< {0} >>> \u5DF2\u6709\u8BA2\u5355
SecurityGroupTypeInvalid=\u5B89\u5168\u7EC4\u7C7B\u578B\u4E0D\u5408\u6CD5\uFF0C\u53EA\u652F\u6301other\u6216global
MaasNotSupportGlobalSecurityGroup=\u4E92\u4FE1VPC\u4E0D\u652F\u6301\u5168\u5C40\u5B89\u5168\u7EC4
NoPermissionCreateGlobalSg=\u5F53\u524D\u7528\u6237\u8BE5\u673A\u623F\u4E0D\u652F\u6301\u521B\u5EFA\u5168\u5C40\u5B89\u5168\u7EC4
EndPointIpConfigLessTwo=\u4E3A\u4E86\u4FDD\u8BC1\u9AD8\u53EF\u7528\uFF0C\u81F3\u5C11\u6DFB\u52A0\u4E24\u4E2A IP \u5730\u5740\u3002

NotSupportBackendServerGroup=\u975ETCP\u6216UDP\u534F\u8BAE\u7C7B\u578B\u7684\u76D1\u542C\u5668\u53EA\u652F\u6301BindType\u4E3ARealServer
CanNotUpdateBindType=\u8BF7\u5148\u89E3\u7ED1\u670D\u52A1\u5668\u6216\u670D\u52A1\u5668\u7EC4\u540E\u518D\u4FEE\u6539\u6302\u8F7D\u7C7B\u578B
ProtocolNotMatch=\u76D1\u542C\u5668\u4E0E\u670D\u52A1\u5668\u7EC4\u7684Protocol\u4E0D\u5339\u914D
RuleDescriptionInvalid={0} \u6700\u5927\u662F64\u4F4D\uFF0C\u53EA\u5141\u8BB8\u5B57\u6BCD\u3001\u4E2D\u6587\u3001\u6570\u5B57\u3001-\u3001_\u3001\u3001\u3001(\u3001)
TmFilterRuleDirectionInvalid = \u7F51\u7EDC\u6D41\u91CF\u65B9\u5411\u4E0D\u5408\u6CD5, \u53EA\u652F\u6301ingress\u548Cegress
TmFilterRuleProtocolInvalid = \u534F\u8BAE\u7C7B\u578B\u4E0D\u5408\u6CD5,\u53EA\u652F\u6301IP\u3001ICMP\u3001TCP\u3001UDP\uFF0CICMPv6
TmFilterRulePolicyInvalid= \u7B56\u7565\u7C7B\u578B\u4E0D\u5408\u6CD5, \u53EA\u652F\u6301accept\u548Cdeny
TmPriorityInvalid= \u4F18\u5148\u7EA7\u4E0D\u5408\u6CD5, \u53EA\u652F\u63011-32766
TmSessionPriorityInvalid= \u4F18\u5148\u7EA7\u4E0D\u5408\u6CD5, \u53EA\u652F\u63011-65535
TmSessionPkgLenInvalid= \u6570\u636E\u5305\u957F\u5EA6\u4E0D\u5408\u6CD5, \u53D6\u503C\u8303\u56F4\u4E3A1-1450
TmSessionSourceCountPassQuota=\u5355\u955C\u50CF\u4F1A\u8BDD\u6700\u591A\u7ED1\u5B9A {0} \u4E2A\u955C\u50CF\u6E90
TmSessionVniInvalid = vni\u683C\u5F0F\u4E0D\u5408\u6CD5,\u53D6\u503C\u8303\u56F4\u4E3A0~16777215

FlowlogResourceTypeInvalid=\u8D44\u6E90\u7C7B\u578B\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u652F\u6301 NetworkInterface\u3001Subnet\u3001VPC
FlowlogTrafficTypeInvalid=\u6D41\u91CF\u7C7B\u578B\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u652F\u6301 All
FlowLogNumberPassQuota=\u8BE5\u673A\u623F\u6D41\u65E5\u5FD7\u521B\u5EFA\u6570\u91CF\u8D85\u8FC7\u914D\u7F6E\u503C {0}

ProjectOrLogPoolNotExist = KLog\u5DE5\u7A0BProject\u6216\u8005\u65E5\u5FD7\u5E93LogPool\u4E0D\u5B58\u5728

InAndOutBandwidthLimitNotExists=\u51FA\u7F51\u5E26\u5BBD\u9650\u901F\u548C\u5165\u7F51\u5E26\u5BBD\u9650\u901F\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A

CidrBlockAndAuthorizedSecurityGroupIdMustHaveOne=\u6388\u6743\u7684\u5B89\u5168\u7EC4ID\u548C\u6388\u6743\u7684CIDR\u5757\u5FC5\u987B\u81F3\u5C11\u6709\u4E00\u4E2A
VpcPeerRouteConflict=\u5373\u5C06\u521B\u5EFA\u7684\u5BF9\u7B49\u8FDE\u63A5\u8DEF\u7531\u4E0E\u5176\u4ED6\u8DEF\u7531\u51B2\u7A81\uFF0C\u8BF7\u68C0\u67E5\u540E\u518D\u8FDB\u884C\u5BF9\u7B49\u8FDE\u63A5\u521B\u5EFA

