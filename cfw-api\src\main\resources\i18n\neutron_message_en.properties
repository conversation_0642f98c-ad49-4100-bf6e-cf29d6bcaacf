neutron-PhyhostExisted=The Physical cloud host <<< %(mac)s >>> was existent
neutron-ExternalIpAddressExhausted=Unable to find any IP address on external network <<< %(net_id)s >>>.
neutron-SGFullQuotaReached=The maximum number of Vpc security group already exists.
neutron-PreexistingDeviceFailure=Creation failed. <<< %(dev_name)s >>> already exists.
neutron-VniAlreadyExist=Vni range <<< %(vni_start)d to <<< %(vni_end)d may exist already, please input right vni range
neutron-PortNotFound=Port <<< %(port_id)s >>> could not be found
neutron-VnetTypeNotLocal=Vnet type has to be local, <<< %(vnet_type)s >>> is given.
neutron-InvalidDhcpIpAllocate=The vif already has been assigned dhcp ip
neutron-InvalidAction=action must be provided and must be either accept or deny
neutron-DeleteRemotePeeringError=Can not delete peering whose type is remote
neutron-TenantNetworksDisabled=Tenant network creation is not enabled.
neutron-InvalidPortfwdInput=Invalid portfwd input. Reason <<< %(reason)s >>>
neutron-IpAddressGenerationFailure=No more IP addresses available on network <<< %(net_id)s >>>.
neutron-DelMigrationVifError=Migration in progress vif can not be deleted
neutron-VifNotAssociatedWithSg=vif <<< %(vif_id)s >>> is not associated with vpc sg <<< %(sg_uuid)s >>>
neutron-MacAlreadyUsedInThisTorFarm=The MAC <<< %(mac)s >>> reduplicate in this ksctor farm
neutron-PhyhostMACError=The cloud physical host MAC <<< %(mac)s >>> is invalid
neutron-InvalidDHCPGW=<<< %(dhcp_gw)s >>> is not a valid dhcp gw value
neutron-AclRuleIcmpRuleExists=Acl rule icmp type/code already exists
neutron-FloatingipPortfwdInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are portfwds still in use on the floatingip. can not disassociate from one igw.
neutron-IpallocationStartWrong=DHCP ip address range start address should be set
neutron-NatsubnetNotFound=Natsubnet <<< %(natsubnet_id)s >>> could not be found
neutron-UserTagAdminRequired=User tag does not have admin privileges
neutron-FailedRecycleTip=Failed to recycle tip <<< %(tip)s >>>
neutron-PhyhostIsInUse=The Physical cloud host <<< %(id)s >>> can not delete that it is in use. The ip is <<< %(ip)s >>> and the vnet vni is <<< %(vni)s >>>
neutron-RuleNotBelongToAcl=Rule <<< %(rule_id)s >>> does not belong to acl <<< %(acl_id)s >>>.
neutron-TipCidrInUse=Tip cidr <<< %(cidr)s >>> has assigned tip, can not delete it
neutron-UpdateVPNReadOnlyAttrError=VPN service <<< %(id)s >>> allow_mode is domain, can not update allow_vnets attributes
neutron-VnetPortfwdInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are portfwds still in use on the vnet. can not disassociate from one igw.
neutron-ServiceHasRoute=Unable to complete operation on service <<< %(service_id)s >>>. There still have route in this service.
neutron-RuleFullQuotaReached=The maximum number of rules in each direction already exists.
neutron-IgwInUse=Unable to complete operation on igw <<< %(igw_id)s >>>. There are portfwds or vpcvm+eip still in use on the igw.
neutron-VpcSgNotProvided=Vpc sg not provided.
neutron-AgentError=An unknown exception occurred.
neutron-RouteLimitExceeded=Maximum number of routes exceeded
neutron-NatpoolInUse=Unable to complete operation on natpool <<< %(natpool_id)s >>>. There are vnets still in use on the natpool.
neutron-RegionNotFound=No region named <<< %(name)s >>>
neutron-NoEligibleKscTorAgent=No eligible KscTor agent found
neutron-DNSNameServersExhausted=Unable to complete operation for <<< %(subnet_id)s >>>. The number of DNS nameservers exceeds the limit <<< %(quota)s >>>.
neutron-InvalidConfigurationOption=An invalid value was provided for <<< %(opt_name)s >>>
neutron-SubnetMismatchForPort=Subnet on port <<< %(port_id)s >>> does not match the requested subnet <<< %(subnet_id)s >>>
neutron-InvalidMigration=Invalid vif migration, error msg
neutron-RuleNotDict=Some rule malformed. Rules should be a dict including requested keys/values.
neutron-UserTagInconsistent=user_tag <<< %(user_tag1)s >>> and <<< %(user_tag2)s >>> are inconsistent.
neutron-PolicyCheckError=Failed to check policy <<< %(policy)s >>> because <<< %(reason)s >>>
neutron-VpcSecurityGroupTypeInvalid=user_tag <<< %(user_tag)s >>> can not create default type securitygroup.
neutron-AgentInitTimeoutError=Can not get configurations from neutron server in <<< %(timeout)s >>> seconds
neutron-PolicyFileNotFound=Policy configuration policy.json could not be found
neutron-NatipallocationInUse=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. There are another natpool use this natipallocation.
neutron-DhcpNotFound=Dhcp <<< %(dhcp_id)s >>> could not be found
neutron-VpcSecurityGroupDelError=sg with tag <<< %(tag1)s >>> can not be del by user_tag <<< %(tag2)s >>>.
neutron-DuplicateDomainCidr=<<< %(cidr)s >>> exists on another domain, it's better to use another cidr for the new domain
neutron-PortNotFoundOnNetwork=Port <<< %(port_id)s >>> could not be found on network <<< %(net_id)s >>>
neutron-OverlapCidrError=Cidr overlap error
neutron-TipOverlap=Tip overlap error
neutron-TorAgentPostmanError=<<< %(func)s >>> error in tor agent post_man function
neutron-DuplicateVPNService=Duplicate vpn service error
neutron-DelVnetDHCPGWError=Can not delete vnet dhcp gateway vif
neutron-ReplaceSGFormatIncorrect=replace vpc sg request body format incorrect. Likely no new sg is passed.
neutron-VifDhcpNotFound=Vif <<< %(vif)s >>> 's dhcp conf could not be found
neutron-VifNotFoundByVmId=Vif could not be found by vm_id <<< %(vm_id)s >>>
neutron-FloatingipInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are another igw use this floatingip. Or another lb or vm use this floatingip.
neutron-NetworkVxlanPortRangeError=Invalid network VXLAN port range
neutron-SubnetInUse=Unable to complete operation on subnet <<< %(subnet_id)s >>>. One or more ports have an IP allocation from this subnet.
neutron-NatsubnetInUse=Unable to complete operation on natsubnet <<< %(natsubnet_id)s >>>. There are ipallocations which associated still in use on the natsubnet.
neutron-VpcDomainInUseVPN=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use VPN
neutron-PrimaryVifExisted=Primary vif of the vm is existed already!
neutron-LBBindingError=Error during bing LB with new vif
neutron-VpcDomainInUseIgw=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Igw
neutron-VpcDomainInUseACL=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Acl
neutron-IllegalOperationPgwAgentCache=Illegal operation pgw agent confs cache, <<< %(msg)s >>>
neutron-ServiceUnavailable=The service is unavailable
neutron-ReplaceVifVnetError=Vif <<< %(id)s >>> still have eip, portfwd or lb, cannot replace vnet
neutron-FloatingipStatusDown=admin_state_up of floatingip <<< %(floatingip_id)s >>> is False, cannot use.
neutron-MultipleLocalRegionError=Local region already exist. Do not allow to create more then one local region.
neutron-AttrValueError=<<< %(msg)s >>>
neutron-SubnetExhausted=No available floatingip in the <<< %(subnet_id)s >>> subnet
neutron-Invalid=An unknown exception occurred.
neutron-VpnNotFound=Vpn service <<< %(id)s >>> could not be found
neutron-PortInUse=Unable to complete operation on port <<< %(port_id)s >>> for network <<< %(net_id)s >>>. Port already has an attacheddevice <<< %(device_id)s >>>.
neutron-PolicyInitError=Failed to init policy <<< %(policy)s >>> because <<< %(reason)s >>>
neutron-BridgeDoesNotExist=Bridge <<< %(bridge)s >>> does not exist.
neutron-PolicyRuleNotFound=Requested rule
neutron-NetworkInUse=Unable to complete operation on network <<< %(net_id)s >>>. There are one or more ports still in use on the network.
neutron-IPInUse=IP address <<< %(ip)s >>> has already occupied.
neutron-VnetIgwInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are another igw use this vnet.
neutron-VpcDomainInUseRoute=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Route
neutron-IgwNotFound=Igw <<< %(igw_id)s >>> could not be found
neutron-VnetRouteExists=VnetRoute vnet_id <<< %(vnet_id)s >>> type <<< %(type)s >>> already exists.
neutron-StateInvalid=Unsupported port state
neutron-VpcSecurityGroupIdInuse=securitygroup id <<< %(sg_id)s >>> is already in use.
neutron-VgwhostNotFound=Vgw host <<< %(vgw)s >>> not found in cache
neutron-IpAddressConflict=DHCP ip address range <<< %(start)s >>> and <<< %(end)s >>> conflict with Gateway ip <<< %(gw_ip)s >>>
neutron-NoPermission=This API need administrate privileges
neutron-VnetVpcvmInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are vpcvm+eip still in use on the vnet. can not disassociate from one igw.
neutron-BadRequest=Bad <<< %(resource)s >>> request
neutron-TunnelIdInUse=Unable to create the network. The tunnel ID <<< %(tunnel_id)s >>> is in use.
neutron-DuplicatePeering=Duplicate domain peering error
neutron-NatnetworkInUse=Unable to complete operation on natnetwork <<< %(natnetwork_id)s >>>. There are ipallocations which associated still in use on the natnetwork.
neutron-NotFoundTor=Can not found KscTor <<< %(id)s >>>
neutron-TooManyExternalNetworks=More than one external network exists
neutron-Dns1Null=You need to set dns1 first
neutron-GWIPInvalid=The given gw ip <<< %(ip)s >>> is invalid
neutron-TorUnuseable=The ksctor <<< %(ip)s >>> unregistered or not enabled
neutron-MissingPrimaryVifError=Can not add extension vif to vm without primary vif
neutron-NetworkVlanRangeError=Invalid network VLAN range
neutron-TorIndexError=The if_index type is <<< %(t_index)s >>> that it is invalid, We should set it to Integer
neutron-GatewayConflictWithAllocationPools=Gateway ip <<< %(ip_address)s >>> conflicts with allocation pool <<< %(pool)s >>>
neutron-QuotaResourceUnknown=Unknown quota resources <<< %(unknown)s >>>.
neutron-NatipallocationNatpoolConflict=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. The natsubnet_id of this natipallocation should be same with the other natipallocations already associated for one natpool.
neutron-ResourceDeleteError=Delete resource <<< %(resource_name)s >>> from db failed
neutron-CanNotCreateRemotePeering=Failed to create a remote peering, remote error msg
neutron-VniAllocationFailed=No valid vni in vni pools.
neutron-MissingAttrError=Request body missing <<< %(attr_name)s >>> attribute
neutron-NoVpcSgFound4Vif=No vpc sg found for vif <<< %(id)s >>>
neutron-IcmpTypeCodeRangeIncorrect=Icmp type or code should be between 0 and 99
neutron-NetworkNotFound=Network <<< %(net_id)s >>> could not be found
neutron-PhyhostMultipleNicError=Physical cloud host does not support multiple NIC yet
neutron-InUse=The resource is inuse
neutron-AgentNotSupport=This api does not support <<< %(agent_type)s >>>
neutron-SudoRequired=Sudo priviledge is required to run this command.
neutron-InvalidSharedSetting=Unable to reconfigure sharing settings for network <<< %(network)s >>>. Multiple tenants are using it
neutron-VpcSubnetNotFound=Subnet <<< %(id)s >>> is not found
neutron-TgwAgentTunnelIPError=Tgw agents have different tunnel ip
neutron-ResourceNotSyncWithRedis=Class <<< %(name)s >>> is not synced with redis
neutron-Conflict=An unknown exception occurred.
neutron-VgwRouteNotFoundByVgwAndRoute=VgwRouteNot vgw_host <<< %(vgw_host)s >>>  route_id <<< %(route_id)s >>> could not be found
neutron-VnetCidrError=Vnet <<< %(cidr)s >>> ip address number should not has less then two
neutron-HostRoutesExhausted=Unable to complete operation for <<< %(subnet_id)s >>>. The number of host routes exceeds the limit <<< %(quota)s >>>.
neutron-VniInUse=Vni still in use
neutron-VPNRemoteCidrsEmptyError=VPN remote cidr can not be empty
neutron-NoNetworkAvailable=Unable to create the network. No tenant network is available for allocation.
neutron-NetworkDhcpConflict=Network <<< %(network_id)s >>> has dhcp on subnet already
neutron-VpcDomainInUseNatPool=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Natpool
neutron-AllocateNatipallocationsError=Allocate natipallocations for natpool failed. There is no enough available nateips in one natsubnet. Please contact system administrator.
neutron-DhcpDnsDuplicateError=Duplicate nameserver
neutron-VpcDomainInUsePeering=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Peering
neutron-TorIndexUnuseable=This index <<< %(id)s >>> is not part of available range
neutron-IpAddressInUse=Unable to complete operation for network <<< %(net_id)s >>>. The IP address <<< %(ip_address)s >>> is in use.
neutron-VnetNatpoolInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are another natpool use this vnet.
neutron-InvalidContentType=Invalid content type <<< %(content_type)s >>>
neutron-PortfwdNotFound=Portfwd <<< %(portfwd_id)s >>> could not be found
neutron-DuplicateRegionNameError=Duplicate region <<< %(name)s >>>
neutron-EndpointVnetIpNumberError=Availabe ip number of endpoint vnet must in 253-1021.
neutron-NoValidIPAddressError=No valid ip address in vnet <<< %(vnet)s >>>
neutron-NatipallocationReserved=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. This natipallocation reserved.
neutron-InvalidAllocationPool=The allocation pool <<< %(pool)s >>> is not valid.
neutron-SubnetDhcpInUse=Subnet <<< %(id)s >>> still has associated dhcp ip address, disable operation is forbidden
neutron-DuplicateTorDatapathIP=The ksctor datapath ip reduplicate
neutron-EIPBindingError=Error during binding EIP with new vif
neutron-MacAddressGenerationFailure=Unable to generate unique mac on network <<< %(net_id)s >>>.
neutron-VnetNatpoolConflict=Unable to complete operation on vnet <<< %(vnet_id)s >>>. The domain of vnet and natpool <<< %(natpool_id)s >>> should be same
neutron-InvalidRemoteDomain=Domain can not create peering with itself
neutron-InvalidRestAPI=Try to invoke a hidden invalid rest api or with a wrong request body
neutron-DuplicateHwaddrError=Vif hwaddr <<< %(hwaddr)s >>> duplicate with exist one
neutron-SubnetNotFound=Subnet <<< %(subnet_id)s >>> could not be found
neutron-VnetNotAssociatedWithAcl=vnet <<< %(vnet_id)s >>> is not associated with Acl <<< %(acl_id)s >>>
neutron-RegionInUseError=Region <<< %(name)s >>> is in use, can not delete it
neutron-AclRuleNotFound=Acl_rule <<< %(id)s >>> is not found
neutron-OverlappingAllocationPools=Found overlapping allocation pools
neutron-InvalidPriority=priority must be provided and must be in the range of 1-32766
neutron-TorAlreadyExists=TOR <<< %(ip)s >>> is already exists
neutron-InvalidStartEndIPAddr=IP address <<< %(ip)s >>> not in cidr <<< %(cidr)s >>> range
neutron-NoValidVni=No valid <<< %(vni_type)s >>> vni
neutron-SubnetDhcpExhuasted=Subnet <<< %(id)s >>> is exhuasted, no more ip for dhcp
neutron-CidrSameWithRouteDestError=Cidr <<< %(cidr)s >>> same with <<< %(route_type)s >>> route error
neutron-CanNotAllocateTip=failed to allocate tip
neutron-StartBehindEnd=Start IP address <<< %(start)s >>> should not great then end ip address <<< %(end)s >>>
neutron-InvalidVPNDevice=Device <<< %(device)s >>> has already been used by another vpn service
neutron-PortStartEndMustBeGiven=both port_start and port_end must be provided for tcp/udp rules
neutron-UpdateExtenstionVifError=<<< %(id)s >>> is an extension vif
neutron-FlatNetworkInUse=Unable to create the flat network. Physical network <<< %(physical_network)s >>> is in use.
neutron-InvalidExtensionEnv=Invalid extension environment
neutron-TorInUse=The KscTor <<< %(id)s >>> can not be deleted that it is being used by Phyhost
neutron-InvalidPortrangeInput=Invalid portrange input. Reason <<< %(reason)s >>>
neutron-FloatingipVpcvmInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are vpcvm+eip still in use on the floatingip. can not disassociate from one igw.
neutron-GatewayIpInUse=Current gateway ip <<< %(ip_address)s >>> already in use by port <<< %(port_id)s >>>. Unable to update.
neutron-ServicehasRoute=There still have route in service <<< %(id)s >>>, can not delete this service.
neutron-DuplicateTipCidr=Tip cidr <<< %(cidr)s >>> has already been created
neutron-NoUpTgwAgent=No valid Tgw agent found, can not create peering
neutron-IPAddressNotFound=IP <<< %(id)s >>> not found
neutron-InvalidNatsubnetInput=Invalid natsubnet input. Reason <<< %(reason)s >>>
neutron-MultiSameVnetVifError=Vm can not own more then one vif in the same vnet
neutron-UpdateDhcpError=Create DHCP error during migration
neutron-VifFoundMultipleResultsByVmId=Vif found multiple results by vm_id <<< %(vm_id)s >>>
neutron-VnetNotFoundByVni=Vnet <<< %(vni)s >>> could not be found by vni
neutron-CanNotDeleteRemotePeering=Failed to delete a remote peering. Remote error msg
neutron-AdminRequired=User does not have admin privileges
neutron-NoValidVniForPeering=Can not find valid vni for peering service
neutron-VnetAlreadyHasAcl=vnet <<< %(vnet_id)s >>> has Acl <<< %(acl_id)s >>> associated already
neutron-VlanIdInUse=Unable to create the network. The VLAN <<< %(vlan_id)s >>> on physical network <<< %(physical_network)s >>> is in use.
neutron-VmhostRouteCannotUpdate=Vmhost route <<< %(route_id)s >>> can not update.
neutron-NeutronException=An unknown exception occurred.
neutron-InvalidActionForType=Invalid action '<<< %(action)s >>>' for object type '<<< %(object_type)s >>>'. Valid actions
neutron-NotAuthorized=Not authorized.
neutron-NotBelongToSameDomain=<<< %(id1)s >>> and <<< %(id2)s >>> does not belong to the same domain
neutron-OutOfBoundsAllocationPool=The allocation pool <<< %(pool)s >>> spans beyond the subnet cidr <<< %(subnet_cidr)s >>>.
neutron-RetryMaxTimeReached=Retry time has reached max count.
neutron-RecreateTorParamsInputError=The value of the recreate key must be setting to Ture.
neutron-UpdateExtVifError=Can not update extension vif dns attributes
neutron-InvalidDirection=direction must be provided and must be either in or out
neutron-ResourceInitError=Resource <<< %(resource_name)s >>> db initialization failed
neutron-PhyhostUpdateVifSgError=Physical cloud host does not support update sg of vif yet
neutron-VifAlreadyAssociatedWithSg=Vif is already associated with a vpc sg. Disassociate first.
neutron-RbacPolicyNotFound=RBAC policy of type <<< %(object_type)s >>> with ID <<< %(id)s >>> not found
neutron-MacAddressInUse=Unable to complete operation for network <<< %(net_id)s >>>. The mac address <<< %(mac)s >>> is in use.
neutron-ACLFullQuotaReached=The maximum number of ACL already exists.
neutron-IPInvalid=The given ip <<< %(ip)s >>> is invalid
neutron-OverQuota=Quota exceeded for resources
neutron-InvalidInput=Invalid input for operation, Reason:<<< %(reason)s >>>
neutron-NatpoolCreatedError=If there is one classic natpool already exists in a domain, Can not create any more natpool. Or if there is one custom natpool already exists in a domain, Can not create any more classtic natpool
neutron-OutofRange=Reserved range out of existing range
neutron-OperationNotAllowed=Only CREATE or DELETE operation allowed in each update operation. <<< %(op)s >>> requested.
neutron-UpdatePhyhostOpError=The operation type error of update phyhost msg
neutron-NotFound=An unknown exception occurred.
neutron-RulesShouldBeList=rules muse be a list and can not be an empty list. Invalid format.
neutron-ParamsInputError=Params input error
neutron-NatnetworkNotFound=Natnetwork <<< %(natnetwork_id)s >>> could not be found
neutron-NatipallocationNotFound=Natipallocation <<< %(natipallocation_id)s >>> could not be found
neutron-NoValidVniForVPN=Can not find valid vni for vpn service
neutron-QuotaMissingTenant=Tenant-id was missing from Quota request
neutron-NotFoundTipCidr=Can not found tipcidr <<< %(id)s >>>
neutron-VniNotFound=Vni <<< %(vni)d could not be found
neutron-NatpoolUnvalidVent=Type of the vnet must be local.
neutron-DuplicateNovaVifIDError=Nova_vif_id <<< %(nova_vif_id)s >>> should not same with Exist one
neutron-PortrangeExists=Create or update failed. another portrange be same already exists.
neutron-RbacPolicyInUse=RBAC policy on object <<< %(object_id)s >>> cannot be removed because other objects depend on it.
neutron-TorIndexAlreadyUsed=The TOR <<< %(ip)s >>> index <<< %(index)s >>> is already used
neutron-InvalidProtocol=Protocol specified in rule is none of icmp, tcp, udp or ip
neutron-TooManyVpcSgs=too many sgs provided to associate with the vif.
neutron-OneOperationAllowed=Either CREATE or DELETE operation is allowed in each update operation at one time.
neutron-PolicyNotAuthorized=Policy doesn't allow <<< %(action)s >>> to be performed.
neutron-AclRulePortRangeOverlaps=Acl rule tcp/udp port range overlaps with other acl rules
neutron-VniRangeNotFound=Vni range <<< %(id)s >>> could not be found
neutron-OneOfIcmpCodeTypeGiven=Both or neither icmp code or icmp type should be given
neutron-DefaultSGAlreadyExists=Vpc security group with default type already exists. Only one is allowed
neutron-ResourceExhausted=No available resource can be allocated
neutron-HostTypeNotMatchError=Expect host_type
neutron-GetPhyhostError=Get phyhost error when <<< %(msg)s >>>
neutron-VnetDhcpRangeWrong=Vnet dhcp range <<< %(start)s >>> to <<< %(end)s >>> should not beyond cidr <<< %(cidr)s >>>
neutron-InvalidQuotaValue=Change would make usage less than 0 for the following resources
neutron-MaskNotWithinRange=The given mask <<< %(mask)s >>> is not between 0 and 32
neutron-TipInUse=Tip <<< %(tip)s >>> in use, can not reserve it
neutron-InvalidActionForHandleBulk=Invalid action <<< %(action)s >>> for handle_vif_sg_bulk
neutron-SGBulkOperationError=Param length is exceed <<< %(length)s >>> in sys_sg bulk operation
neutron-VpcSecurityGroupIdInvalid=sg_id
neutron-QosResourceNotFound=QosResource or bw package <<< id >>> could not be found
neutron-QosBindingNotFound=QosBinding <<< %(qos_uuid)s >>> could not be found
neutron-NoQosBindingEipFound=QosBinding whose eip is <<< %(eip_id)s >>> could not be found
neutron-MultipleQosBindingEipFound=Multiple QosBinding whose eip is <<< %(eip_id)s >>> are found
neutron-QosCreatingFailed=Qos resource creating for <<< %(floatingip_id)s >>> failed
neutron-QoSIDWrongQoSIDWrong=Qos id for vpc CAN NOT be smaller than separator. CHECK Immediately!
neutron-RouteNotFound=Route could not be found. RouteId:<<< %(route_id)s >>>
neutron-InvalidVipInput=Invalid listener input. Reason <<< Vip without address cannot set admin_state_up true. >>>
neutron-NetworkExhausted=No available elastic IP in the <<< %(network_id)s >>> network
neutron-VpcDomainInUse=Vpc <<< %(domain_id)s >>> cannot be deleted since it has existing in-use network
neutron-VpcDomainInUseSecuritygroup=Vpc cannot be deleted since it has existing in-use Securitygroup whose type == other.VpcId:<<< %(domain_id)s >>> 
neutron-FloatingIPInUse=Elastic IP<<< %(id)s >>> is bonded to a lb or vm or igw cannot be deleted directly.
neutron-InvalidRemoteCidr=Cidr <<< %(cidr)s >>> should not same with vpc existing vpn and peering remote cidr
neutron-CertificateNotFound=Certificate is not found!CertificateId: <<< %(id)s >>> 
neutron-VpcNetworkInUse=Subnet cannot be deleted since it is still have network interface in use.SubnetId: <<< %(net_id)s >>>
neutron-DhcpRangeOverLimit=DHCP ip address range should not over <<< %(num)d ip
neutron-VipNotFound=listener could not be found.listenerId:<<< %(id)s >>>
neutron-VpcSgRuleNotFound=Vpc security group rule is not found. SecurityGroupEntryId:<<< %(vpc_sg_rule_id)s >>> 
neutron-VipExists=Listener with protocol port <<< %(id)s >>> and protocol <<< %(id)s >>> already exists in LoadBalancer.LoadBalancerId: <<< %(id)s >>>
neutron-IpRuleAlreadyExists=Ip rule already exists
neutron-PeeringNotFound=Peering service <<< %(id)s >>> could not be found
neutron-PortfwdExists=Create or update failed. another portfwd be same already exists.
neutron-VpcSecurityGroupInUse=Vpc security group cannot be deleted since it is still used by other network interface.SecurityGroupId\uFF1A<<< %(sg_uuid)s >>>
neutron-InvalidPoolInput=Invalid Load Balancer input. Reason <<< Pool without address cannot set admin_state_up true. >>>
neutron-VpcAclInUse=Vpc acl cannot be deleted since it is still used by subnet.AclId\uFF1A<<< %(acl_id)s >>>
neutron-VpcSecurityGroupNotFound=Vpc security group is not found.interface.SecurityGroupId\uFF1A<<< %(sg_uuid)s >>>
neutron-DhcpRangeWrong=DHCP ip address range start address <<< %(start)s >>> should small than <<< %(end)s >>>
neutron-SystemRouteCannotDelete=System route could not delete.
neutron-DomainNotFound=Vpc <<< %(domain_id)s >>> could not be found
neutron-IPMaskInvalid=The given ip <<< %(ip)s >>> and mask <<< %(mask)s >>> is invalid for a subnet
neutron-FloatingDeviceAlreadyAssociated=Cannot associate floating IP <<< %(id)s >>> (<<< %(id)s >>>) with port <<<  >>> using Device <<< %(id)s >>>, as it already has a floating IP on external network <<< %(id)s >>>.
neutron-FloatingIPNotFound=Elastic IP could not be found. AllocationId:<<< %(id)s >>>
neutron-AclNotFound=Acl is not found. AclId:<<< %(acl_id)s >>>
neutron-AllocateLocalipError=No reserve subnet ips could allocate to vip as localips, Please retry after create a reserve subnet
neutron-RuleNotBelongToSg=Rule <<< %(rule_id)s >>> does not belong to vpc Security group <<< %(sg_uuid)s >>>.
neutron-VifNotFound=Network interface <<< %(vif)s >>> could not be found
neutron-PoolNotFound=Load Balancer could not be found.LoadBalancerId\uFF1A<<< %(id)s >>>
neutron-VpcSgRuleIcmpRuleExists=Vpc security group rule icmp type/code already exists
neutron-VpcSgRulePortRangeOverlaps=Vpc security group rule tcp/udp port range overlaps with other vpc security group rule 
neutron-MalformedRequestBody=Malformed request body: <<< %(reason)s >>>
neutron-FloatingIpNotAssociated=Trying to dis-associate while it is not associated at all.
neutron-EndpointNetworkInUse=There are load balancing, cloud database or object storage resources under the terminal subnet, which cannot be deleted.
neutron-VnetNotFound=Subnet could not be found.SubnetId:<<< %(vnet_id)s >>>
neutron-PoolInUse=Load Balancer is still in use. LoadBalancerId\uFF1A<<< %(id)s >>>
neutron-NatpoolNotFound=Natpool <<< %(natpool_id)s >>> could not be found
neutron-VnetCidrWrong=subnet cidr should not beyond vpc cidr <<< %(cidr)s >>>
neutron-FailedToAllocateFlaotingIP=Falied to allocate elastic ip
neutron-DuplicateVnetCidr=<<< %(cidr)s >>> exists on another subnet, it's better to use another cidr for the new subnet.
neutron-InvalidPortRange=For TCP/UDP protocols, port_start must be &lt= port_end and in [1, 65535]
neutron-InvalidRouteInput=Invalid route input. Reason <<< %(reason)s >>>
neutron-CertificateInUsed=Certificate <<< %(id)s >>> is already in used, Do not delete it
neutron-MgroupInUse=The BackendServerGroup cannot be deleted since it is still used by listener.The BackendServerGroup Id\uFF1A<<< %(id)s >>>
neutron-VipInUse=Listener cannot be deleted since it is still used by domain name.Listener Id\uFF1A<<< %(id)s >>>.
neutron-SlbaclNotFound=Slbacl <<< %(slbacl_id)s >>> could not be found.
neutron-SlbaclruleNotFound=Slbaclrule <<< %(slbacl_rule_id)s >>> could not be found.
neutron-SlbaclruleExists=Priority is not allowed in the same ACL.
neutron-SlbaclruleCidrExists=Cidr is not allowed in the same ACL.
neutron-SlbaclInUse=Slbacl <<< %(slbacl_id)s >>> is still in use.
neutron-InvalidPoolType=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolSlbType=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolAddress=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolVNet=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolFloatingIpVersion=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidVipCertificate=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipLbKind=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipSlbAcl=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipLbMethod=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipProtocol=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipSynProxy=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorCheckDataAndCheckRet=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorHttpMethodAndUrlPath=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorVipSlbType=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorProtocol=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidServerVipSlbType=Invalid server input. Reason <<< %(reason)s >>>
neutron-InvalidServerCertificate=The empty domain name inherits the listener certificate by default, and does not support binding other certificates
neutron-InvalidServerVipAddress=Invalid server input. Reason <<< %(reason)s >>>
neutron-InvalidMemberVpcVm=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberResourceID=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberLbKindOrEmode=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberVipSlbType=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberMasterSlaveType=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidVipMetadata=The listener protocol that supports backward forwarding of the HTTP protocol version must be HTTP or HTTPS.
neutron-LocationExists=Location with path <<< %(path)s >>> already exists in server <<< %(server_id)s >>>
neutron-DedicatedResourceExhausted=VLANID is already in use
neutron-IPAlreadyReserved=IP address <<< %(ip_addresses)s >>> has already reserved.
neutron-CenNotFound=Cen <<< %(cen_id)s >>> could not be found.
neutron-AttachCenError=Attach Cen failed
neutron-InstanceNotAttachedCen=Instance <<< %(instance_id)s >>> not attached to cen <<< %(cen_id)s >>>.
neutron-CenGrantTenantShouldNotSame=Instance accountId should not be same with cen accountId.
neutron-CenGrantRuleExisted=Cen grant already existed.
neutron-CenAlreadyBindInstance=Cen already bind instance.
neutron-BandWidthExisted=Band width Mixin existed for <<< %(region_a)s >>> and <<< %(region_b)s >>>.
neutron-CenVniNotFound=Cen vni <<< %(id)s >>> could not be found.
neutron-CenVniRangeConflict=Cen Range conflict with existed.
neutron-SameRegionError=Not allowed set egress for same region.
neutron-CreateRemoteCenError=Create remote cen error, reason is: <<< %(reason)s >>>
neutron-RouteConflict=<<< %(conflict_msg)s >>>
neutron-RegionInUse=Region <<< %(region)s >>> in use.
neutron-RegionNameNotFound=Region Name <<< %(name)s >>> could not be found.
neutron-RouteConflictNotFound=Route conflict <<< %(id)s >>> could not be found.
neutron-CenBindExisted=Instance <<< %(instance_id)s >>> already bind to cen <<< %(cen)s >>>.
neutron-PublishCenRouteError=Publish cen routes failed, reason is: <<< %(reason)s >>>.
neutron-BandwidthMixinExisted=BandwidthMixin already existed for these two region
neutron-GetRemoteCenError=Get remote cen error, reason is: <<< %(reason)s >>>
neutron-GetInstanceFailed=Get Instance <<< %(id)s >>> failed, reason is: <<< %(reason)s >>>.
neutron-DirectConnectNotAttachedSwitchInterface=Direct connect <<< %(dc_id)s >>> not attached switch interface.
neutron-CenRangeInputError=Cen Range start should not bigger than end.
neutron-FailedDependency=An unknown exception occurred.
neutron-InstanceIdNotProvided=Instance id not provided.
neutron-RegionNameOrEndpointExisted=Region name or endpoint already existed.
neutron-DeleteRemoteCenError=Delete remote cen domain error, reason is: <<< %(reason)s >>>
neutron-CenFailedQueueNotFound=Cen failed queue <<< %(id)s >>> not found.
neutron-AllocateCenVniFailed=Allocate cen vni failed.
neutron-InstanceAlredyBindAttached=Attach cen failed, Instance <<< %(instance_id)s >>> already attached to cen <<< %(cen_id)s >>>.
neutron-BandwidthPackageNotFound=Bandwidth Package <<< %(id)s >>> could not be found.
neutron-CenBindingNotFound=Cen Binding <<< %(id)s >>> could not be found.
neutron-CenRegionInfoNotFound=Cen region info <<< %(id)s >>> could not be found.
neutron-DeleteCenRouteError=Publish cen routes failed, reason is: <<< %(reason)s >>>.
neutron-UpdateRemoteConnError=Update remote conn failed, reason is: <<< %(reason)s >>>.
neutron-InstanceNotFound=Instance <<< %(id)s >>> not found.
neutron-CenVniRangeNotFound=Cen vni range <<< %(id)s >>> could not be found.
neutron-CenUnSupportType=Cen not support instance type <<< %(instance_type)s >>>.
neutron-BandwidthMixinNotFound=Bandwidth Mixin <<< %(id)s >>> could not be found.
neutron-DetachCenError=Detach cen failed, reason is: <<< %(reason)s >>>.
neutron-VpcDomainInUseCen=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use cen service.
neutron-CenRouteCannotDelete=Cen route could not delete.
neutron-MissDomainIdForDirectConnVpcBind=Miss domain_id for direct connect vpc bind.
neutron-DomainIdShouldNotProvided=Args domain_id should not provided for un-bind type.
neutron-CanNotAddExtraCidrsToUnbindDc=Can not add extra cidrs to un-bind direct connect.
neutron-CanNotCreateDcBindToCen=Can not create direct connect to cen.
neutron-DirectNotAttachedVpc=Direct connect <<< %(dc_id)s >>> not attached to vpc.
neutron-DirectNotAttachedCen=Direct connect <<< %(dc_id)s >>> not attached to cen.
neutron-CanNotCreateDcToCen=Can not create a direct connect to cen that has not attached a switch interface.
neutron-DirectAlreadyAttached=Direct connect <<< %(dc_id)s >>> already attached other instance.
neutron-DirectDeleteError=Direct connect error: <<< %(msg)s >>>
neutron-DomainAlreadyAttachedCen=Can not create peering, domain <<< %(domain_id)s >>> has already attached cen.
neutron-DomainNotAttachedCen=Domain <<< %(domain_id)s >>> not attached to cen.
neutron-CenDomainNotFound=Cen domain <<< %(id)s >>> not found.
neutron-CenDomainInuse=Cen domain <<< %(id)s >>> in use.
neutron-CenRouteNotFound=Cen route <<< %(id)s >>> not found.
neutron-InvalidRouteTypeForCen=Invalid route type <<< %(type)s >>> to cen, valid types: <<< %(valid_types)s >>>.
neutron-DnsNotAllowedPublish=Dns not allowed to publish to cen or bgp direct connect.
neutron-InvalidRouteIpVersionForCen=Invalid ip version, only support ip version 4.
neutron-RouteAlreadyPublishedToCen=Route <<< %(id)s >>> has already published to cen.
neutron-RouteNotPublishedToCen=Route <<< %(id)s >>> not published to cen.
neutron-GetCenError=Get cen route failed, reason is: <<< %(reason)s >>>.
neutron-ModifyDirectConnCidrError=Modify direct conn failed, reason is: <<< %(reason)s >>>.
neutron-DirectConnRouteNotFound=Direct connect route <<< %(id)s >>> not found.
neutron-DirectConnRouteConflictNotFound=Direct connect route conflict <<< %(id)s >>> not found.
neutron-CenGrantRuleNotFound=Cen grant rule <<< %(id)s >>> not found.
neutron-CenPermissionDenied=Cen no permission to attach this instance.
neutron-DeleteGrantRuleError=Can not delete this rule, instance has already attach cen <<< %(cen_id)s >>>
neutron-DirectConnRouteNotSupportUpdate=Direct connect route type <<< %(type)s >>> not support update.
neutron-PublishDcRouteToCenError=Publish dc route to cen error: <<< %(msg)s >>>
neutron-DeleteDcRouteToCenError=Delete dc route to cen error: <<< %(msg)s >>>
neutron-PublishDcRouteToBgpError=Publish dc route to bgp error: <<< %(msg)s >>>
neutron-DeleteDcRouteToBgpError=Delete dc route to bgp error: <<< %(msg)s >>>
neutron-ModifyDirectConnError=Modify Direct connect error: <<< %(msg)s >>>.
neutron-CenRemoteRegionConnNotFound=Cen remote region <<< %(id)s >>> not found.
neutron-CenLocalInstanceConnNotFound=Cen local instance <<< %(id)s >>> not found.
neutron-CenRouteConflictNotFound=Cen Route Conflict <<< %(id)s >>> not found.
neutron-InvalidInstanceType=Invalid instance type <<< %(type)s >>>
neutron-VpcAlreadyAttachCen=Vpc <<< %(instance_id)s >>> has already attach to cen <<< %(cen_id)s >>>.
neutron-VpcAlreadyInPeering=Vpc <<< %(instance_id)s >>> has already in peering connect.
neutron-DirectConnectAlreadyAttachCen=Direct connect <<< %(instance_id)s >>> has already attach to cen <<< %(cen_id)s >>>.
neutron-DirectConnectAlreadyAttachVpc=Direct connect <<< %(instance_id)s >>> has already attach to vpc <<< %(domain_id)s >>>.
neutron-MissCenRouteAttr=Miss cen route attr <<< %(attr)s >>>.
neutron-VnetEnableIpv6Already=vnet has enable ipv6.
neutron-InvalidVipRedirectInPut=cannot set redirect to vip which had set redirect
neutron-BGPConfigError=BGP config not match previous one
neutron-AttachSwitchInterfaceERROR=Direct connect has already attached a switch interface
neutron-SwitchInterfaceInUseError=SwitchInterface <<< %(instance_id)s >>> has already been used
neutron-SwitchInterfaceNotInCorrectSwitch=The switch interface is not on the correct switch
neutron-DetachSwitchInterfaceError=Detach error. Reason: <<< %(reason)s >>>
neutron-InvalidVipMgroupInput=Listener and BackendServerGroup not in same VPC
neutron-InvalidLocationInput=Invalid parameter of Create rule, Reason <<< %(reason)s >>>
neutron-InvalidMemberType=Invalid instance input. Reason <<< The ip version of the Listener's instances should be consistent >>>
neutron-RouteTableBindError=EPC or endpoint vnet can not bind a custom route table.
neutron-VnetHasRouteTableBound=A custom route table has bound the vnet <<< %(vnet_id)s >>>, please unbound before deleting.
neutron-RouteTableNotFound=Route table <<< %(table_id)s >>> could not be found.
neutron-RouteTableNotBelongToThisDomain=Route table <<< %(table_id)s >>> not belong to this vpc.
neutron-RouteTableHasVnetBind=Route table <<< %(table_id)s >>> is in use, vnet has bound.
neutron-RouteTableHasNonSystemRoute=Route table <<< %(table_id)s >>> is in use, non-system route has bound.
neutron-CustomRouteNotFound=custom route <<< %(route_id)s >>> could not be found.
neutron-RouteTableLimitExceeded=Per-domain route table quota exceeded.
neutron-CustomRouteLimitExceeded=Per-domain custom route quota exceeded.
neutron-InvalidServerInput=Invalid server input. Reason: <<< %(reason)s >>>
neutron-InvalidLocationRedirect=Can not create location for server which listener has redirect.
neutron-RouteCannotCherryPick=Only dynamic routes can be selected to route tables
neutron-CanNotSetIpv6Rule=Can not set ipv6 rule, vpc not enable ipv6
neutron-VpcSgRuleDbConflict=rules already exist in vpc
neutron-InvalidSlbaclruleIpVersion=cidr <<< %(cidr)s >>> not match the ip_version of loadBalancer acl
neutron-VnetNotSupportIpv6=This type of subnet does not support IPV6
neutron-DirConnV2NotSupportHASwiInterface=A Direct Connect gateway in version 2.0 cannot be connected to dual Direct Connect tunnels.
neutron-SwitchInterfaceNotAssistForDirConn=The Direct Connect gateway is not connected to a Direct Connect tunnel.
neutron-BfdMetadataInUse=Bfd Config <<< %(id)s >>> is in use
neutron-LackBfdMetadataId=Bfd Config ID can't be empty
neutron-DirConnV1NotSupportPriority=Direct Connect interface bound to a 1.0 Direct Connect gateway do not support priority
neutron-DirectConnectRouteLimitExceeded=The quota of Direct Connect is insufficient
neutron-ReliabilityMethodNotAllowedOFF=nqa and bfd cannot be interconverted
neutron-ReliabilityMethodConfigError=The new direct Connect interface will be set in the same way as the previous interface reliability method
neutron-ServerExists=The domain name already exists under this listener. It is not allowed to add it again
neutron-VipRedirectInUse=The listener is used as a redirection listener and cannot be deleted
neutron-DnatRuleLimitExceeded=Per-nat dnat rule quota exceeded.
neutron-DnatruleNotFound=Dnat <<< %(dnatrule_id)s >>> could not be found
neutron-NatipHasPortMappingDnatRule=Nat IP/EIP <<< %(natip_id)s >>> already has port mapping dnat rule.
neutron-VifHasPortMappingDnatRule=Network Interface <<< %(nova_vif_id)s >>> already has port mapping dnat rule.
neutron-NatipHasIPMappingDnatRule=Nat IP/EIP <<< %(natip_id)s >>> already has ip mapping dnat rule.
neutron-VifHasIPMappingDnatRule=Network Interface <<< %(nova_vif_id)s >>> already has ip mapping dnat rule.
neutron-VifAndNatpoolConflict=Network Interface <<< %(nova_vif_id)s >>> and Nat: <<< %(natpool_id)s >>> should in same vpc.
neutron-NatipHasDuplicateDnatRule=Nat IP/EIP <<< %(natip_id)s >>> has duplicate dnat rule.
neutron-VifHasDuplicateDnatRule=Network Interface <<< %(nova_vif_id)s >>> has duplicate dnat rule.
neutron-NatipNotAssociated=Nat IP/EIP <<< %(natip_id)s >>> not associated for nat.
neutron-OnlyVMTypeVifSupportDnatrule=Network Interface <<< %(nova_vif_id)s >>>  type: <<< %(host_type)s >>>, only type vm support dnat.
neutron-VifAlreadyBindNatpool=Network Interface <<< %(nova_vif_id)s >>> already bind Nat: <<< %(natpool_id)s >>>.
neutron-VifAlreadyHasDnatRule=Network Interface <<< %(nova_vif_id)s >>> already has Dnat.
neutron-DisassociateCidrBlockError=Delete cidrblock error. Reason <<< %(reason)s >>>
neutron-InvalidCidrBlockInput=Invalid cidrblock input. Reason <<< %(reason)s >>>
neutron-FixIPFullQuotaError=PrivateIp of NetworkInterface <<< %(vif_id)s >>> quota has been exhausted.
neutron-PrivateLinkExists=PrivateLink with vip <<< %(vip_id)s >>> already exists.
neutron-PeerIPError=The customer side IP segment conflicts with the KingSoft side IP segment
neutron-RemoveNatipDnatAndSnatError=To disable, first delete the relevant 'all port' type DNAT rules or delete all SNAT rules
neutron-DisableNatipDnatAndSnatError=To disable, first delete the relevant 'all port' type DNAT rules or delete all SNAT rules
neutron-DisableNatipallocationsError=Failed to disable Nat Ip/EIP <<< %(id)s >>>. Cannot disable the last NATIP/EIP
neutron-CreateFullPortMappingDnatRuleError=This NatIP/EIP cannot be used because it is already used for SNAT
neutron-CrtOrKeyNotCorrect=private key that provided is not correct
neutron-CrtAndKeyNotMatch=Certificate and Private key not Match
neutron-SnatAndDnatruleConflict=When natpool <<< %(id)s >>> has only one natip with dnat full-port mapping, it is not allowed to add resource to natpool.
InnerDnsError-itemNotFound-InstanceNotFound=Instance <<< %(instance)s >>> cannot be found.
neutron-SystemRouteTableCannotDelete=System route table <<< %(id)s >>> not support delete
neutron-NotSupportCreateInSystemRouteTable=The interface does not support creating routes, in the system route table <<< %(id)s >>>
neutron-RouteIpVersionDomainInvalid=VPC <<< %(id)s >>> not support ipv6
neutron-VpnTunnelNotFound=VpnTunnel <<< %(id)s >>> not found
neutron-VpnVpcGwNotFound=VpnVpcGw <<< %(id)s >>> not found
neutron-LimitExceeded=Per User global securitygroup quota exceeded
neutron-InstanceNotMatchCen=The vpc <<< %(id)s >>> is not bound to the cen
neutron-MemberForbidden=Mounted as BackendServerGroup, listeners do not allow adding real servers
neutron-VipAndMgroupNotMatch=Listener and BackendServerGroup not match\uFF0CReason <<< %(reason)s >>>
neutron-TMSessionPriorityCannotBeDuplicated=The priority <<< >>> of traffic mirror session cannot be duplicated with others
neutron-TheTMSourceBoundSessionsNumExceedQuota=\u955C\u50CF\u6E90\u7F51\u5361ID<<< %(vifId)s >>>\u7ED1\u5B9A\u7684\u955C\u50CF\u4F1A\u8BDD\u6570\u91CF\u8D85\u8FC7\u914D\u989D;
neutron-TmSourceAlreadyInSession=Traffic mirror source <<< %(vifId)s >>> alreay in session
neutron-TmFilterRulePortRangeOverlaps=Traffic mirror filter rule tcp/udp port range same with other acl rules
neutron-CanNotStateUpTmSessionWithoutTarget=Cannot state up tm session %(id)s without tm target, please update session with tm target.

neutron-ModifySwitchInterfaceRouteError=Create DirectConnectGateway route failed\uFF0CReason <<< %(reason)s >>>
neutron-NatipNotBelongCurrNatpool=NatIp <<< %(id)s >>> has been not belonged to natpool <<< %(id)s >>>
neutron-FullMapDnatMutualSnatipBinding=Natip <<< %(id)s >>> already has been attached snat resource, not supported for full mapping dnat rule.
neutron-AddNatpoolResourceError=SNAT resource addition failed, please check if NatIp is being occupied by full port DNAT

neutron-CreateDcNatDcBindTypeError=Create dc_nat error,reason <<< %(reason)s >>>
neutron-CreateDcNatDcStatusError=Create dc_nat error dc status error,reason <<< %(reason)s >>>
neutron-CreateDcNatIpNatExist=Create dc_nat error IP Duplicate , Duplicate with existing LocalIp/PeerIp addresses
neutron-CreateDcNatIpNatConflictWithRemotePeerIp=Create dc_nat error, LocalIp or PeerIp conflict with  remote_peer_ip of the switch_interface
neutron-CreateDcNatIPNatSameIP=Create dc_nat error,LocalIp or PeerIp same
neutron-CreateDcNatIpNatinBlackList=Create dc_nat error,LocalIp or PeerIp in blacklist

neutron-VifFlowlogExists=Flowlog with network interface <<< %(id)s >>> already exists.

neutron-NatpoolV2EIPNetworkNotSame=Nat can only bind one type of networks line EIP
neutron-FloatingIpStatusDown=EIP <<< %(id)s >>> has expired, please renew before performing binding operation