neutron-PhyhostExisted=The Physical cloud host <<< %(mac)s >>> was existent
neutron-ExternalIpAddressExhausted=Unable to find any IP address on external network <<< %(net_id)s >>>.
neutron-SGFullQuotaReached=The maximum number of Vpc security group already exists.
neutron-PreexistingDeviceFailure=Creation failed. <<< %(dev_name)s >>> already exists.
neutron-VniAlreadyExist=Vni range <<< %(vni_start)d to <<< %(vni_end)d may exist already, please input right vni range
neutron-PortNotFound=Port <<< %(port_id)s >>> could not be found
neutron-VnetTypeNotLocal=Vnet type has to be local, <<< %(vnet_type)s >>> is given.
neutron-InvalidDhcpIpAllocate=The vif already has been assigned dhcp ip
neutron-InvalidAction=action must be provided and must be either accept or deny
neutron-DeleteRemotePeeringError=Can not delete peering whose type is remote
neutron-TenantNetworksDisabled=Tenant network creation is not enabled.
neutron-InvalidPortfwdInput=Invalid portfwd input. Reason <<< %(reason)s >>>
neutron-IpAddressGenerationFailure=No more IP addresses available on network <<< %(net_id)s >>>.
neutron-DelMigrationVifError=Migration in progress vif can not be deleted
neutron-VifNotAssociatedWithSg=vif <<< %(vif_id)s >>> is not associated with vpc sg <<< %(sg_uuid)s >>>
neutron-MacAlreadyUsedInThisTorFarm=The MAC <<< %(mac)s >>> reduplicate in this ksctor farm
neutron-PhyhostMACError=The cloud physical host MAC <<< %(mac)s >>> is invalid
neutron-InvalidDHCPGW=<<< %(dhcp_gw)s >>> is not a valid dhcp gw value
neutron-AclRuleIcmpRuleExists=Acl rule icmp type/code already exists
neutron-FloatingipPortfwdInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are portfwds still in use on the floatingip. can not disassociate from one igw.
neutron-IpallocationStartWrong=DHCP ip address range start address should be set
neutron-NatsubnetNotFound=Natsubnet <<< %(natsubnet_id)s >>> could not be found
neutron-UserTagAdminRequired=User tag does not have admin privileges
neutron-FailedRecycleTip=Failed to recycle tip <<< %(tip)s >>>
neutron-PhyhostIsInUse=The Physical cloud host <<< %(id)s >>> can not delete that it is in use. The ip is <<< %(ip)s >>> and the vnet vni is <<< %(vni)s >>>
neutron-RuleNotBelongToAcl=Rule <<< %(rule_id)s >>> does not belong to acl <<< %(acl_id)s >>>.
neutron-TipCidrInUse=Tip cidr <<< %(cidr)s >>> has assigned tip, can not delete it
neutron-UpdateVPNReadOnlyAttrError=VPN service <<< %(id)s >>> allow_mode is domain, can not update allow_vnets attributes
neutron-VnetPortfwdInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are portfwds still in use on the vnet. can not disassociate from one igw.
neutron-ServiceHasRoute=\u8BE5\u8D44\u6E90<<< %(service_id)s >>>\u5DF2\u5B58\u5728\u7ED1\u5B9A\u8DEF\u7531\uFF0C\u65E0\u6CD5\u8FDB\u884C\u8BE5\u64CD\u4F5C
neutron-RuleFullQuotaReached=The maximum number of rules in each direction already exists.
neutron-IgwInUse=Unable to complete operation on igw <<< %(igw_id)s >>>. There are portfwds or vpcvm+eip still in use on the igw.
neutron-VpcSgNotProvided=Vpc sg not provided.
neutron-AgentError=An unknown exception occurred.
neutron-RouteLimitExceeded=Maximum number of routes exceeded
neutron-NatpoolInUse=Unable to complete operation on natpool <<< %(natpool_id)s >>>. There are vnets still in use on the natpool.
neutron-RegionNotFound=No region named <<< %(name)s >>>
neutron-NoEligibleKscTorAgent=No eligible KscTor agent found
neutron-DNSNameServersExhausted=Unable to complete operation for <<< %(subnet_id)s >>>. The number of DNS nameservers exceeds the limit <<< %(quota)s >>>.
neutron-InvalidConfigurationOption=An invalid value was provided for <<< %(opt_name)s >>>
neutron-SubnetMismatchForPort=Subnet on port <<< %(port_id)s >>> does not match the requested subnet <<< %(subnet_id)s >>>
neutron-InvalidMigration=Invalid vif migration, error msg
neutron-RuleNotDict=Some rule malformed. Rules should be a dict including requested keys/values.
neutron-UserTagInconsistent=user_tag <<< %(user_tag1)s >>> and <<< %(user_tag2)s >>> are inconsistent.
neutron-PolicyCheckError=Failed to check policy <<< %(policy)s >>> because <<< %(reason)s >>>
neutron-VpcSecurityGroupTypeInvalid=user_tag <<< %(user_tag)s >>> can not create default type securitygroup.
neutron-AgentInitTimeoutError=Can not get configurations from neutron server in <<< %(timeout)s >>> seconds
neutron-PolicyFileNotFound=Policy configuration policy.json could not be found
neutron-NatipallocationInUse=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. There are another natpool use this natipallocation.
neutron-DhcpNotFound=Dhcp <<< %(dhcp_id)s >>> could not be found
neutron-VpcSecurityGroupDelError=sg with tag <<< %(tag1)s >>> can not be del by user_tag <<< %(tag2)s >>>.
neutron-DuplicateDomainCidr=<<< %(cidr)s >>> exists on another domain, it's better to use another cidr for the new domain
neutron-PortNotFoundOnNetwork=Port <<< %(port_id)s >>> could not be found on network <<< %(net_id)s >>>
neutron-OverlapCidrError=Cidr overlap error
neutron-TipOverlap=Tip overlap error
neutron-TorAgentPostmanError=<<< %(func)s >>> error in tor agent post_man function
neutron-DuplicateVPNService=Duplicate vpn service error
neutron-DelVnetDHCPGWError=Can not delete vnet dhcp gateway vif
neutron-ReplaceSGFormatIncorrect=replace vpc sg request body format incorrect. Likely no new sg is passed.
neutron-VifDhcpNotFound=Vif <<< %(vif)s >>> 's dhcp conf could not be found
neutron-VifNotFoundByVmId=Vif could not be found by vm_id <<< %(vm_id)s >>>
neutron-FloatingipInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are another igw use this floatingip. Or another lb or vm use this floatingip.
neutron-NetworkVxlanPortRangeError=Invalid network VXLAN port range
neutron-SubnetInUse=Unable to complete operation on subnet <<< %(subnet_id)s >>>. One or more ports have an IP allocation from this subnet.
neutron-NatsubnetInUse=Unable to complete operation on natsubnet <<< %(natsubnet_id)s >>>. There are ipallocations which associated still in use on the natsubnet.
neutron-VpcDomainInUseVPN=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use VPN
neutron-PrimaryVifExisted=Primary vif of the vm is existed already!
neutron-LBBindingError=Error during bing LB with new vif
neutron-VpcDomainInUseIgw=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Igw
neutron-VpcDomainInUseACL=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Acl
neutron-IllegalOperationPgwAgentCache=Illegal operation pgw agent confs cache, <<< %(msg)s >>>
neutron-ServiceUnavailable=The service is unavailable
neutron-ReplaceVifVnetError=Vif <<< %(id)s >>> still have eip, portfwd or lb, cannot replace vnet
neutron-FloatingipStatusDown=\u5F39\u6027IP\u5DF2\u88AB\u6302\u8D77\uFF0C\u8BF7\u5148\u7EED\u8D39\u540E\u518D\u505A\u64CD\u4F5C\uFF0C\u5F39\u6027IP\uFF1A <<< %(floatingip_id)s >>>
neutron-MultipleLocalRegionError=Local region already exist. Do not allow to create more then one local region.
neutron-AttrValueError=<<< %(msg)s >>>
neutron-SubnetExhausted=No available floatingip in the <<< %(subnet_id)s >>> subnet
neutron-Invalid=An unknown exception occurred.
neutron-VpnNotFound=Vpn service <<< %(id)s >>> could not be found
neutron-PortInUse=Unable to complete operation on port <<< %(port_id)s >>> for network <<< %(net_id)s >>>. Port already has an attacheddevice <<< %(device_id)s >>>.
neutron-PolicyInitError=Failed to init policy <<< %(policy)s >>> because <<< %(reason)s >>>
neutron-BridgeDoesNotExist=Bridge <<< %(bridge)s >>> does not exist.
neutron-PolicyRuleNotFound=Requested rule
neutron-NetworkInUse=Unable to complete operation on network <<< %(net_id)s >>>. There are one or more ports still in use on the network.
neutron-IPInUse=IP\u5730\u5740 <<< %(ip)s >>> \u5DF2\u88AB\u5360\u7528
neutron-VnetIgwInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are another igw use this vnet.
neutron-VpcDomainInUseRoute=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Route
neutron-IgwNotFound=Igw <<< %(igw_id)s >>> could not be found
neutron-VnetRouteExists=VnetRoute vnet_id <<< %(vnet_id)s >>> type <<< %(type)s >>> already exists.
neutron-StateInvalid=Unsupported port state
neutron-VpcSecurityGroupIdInuse=securitygroup id <<< %(sg_id)s >>> is already in use.
neutron-VgwhostNotFound=Vgw host <<< %(vgw)s >>> not found in cache
neutron-IpAddressConflict=DHCP ip address range <<< %(start)s >>> and <<< %(end)s >>> conflict with Gateway ip <<< %(gw_ip)s >>>
neutron-NoPermission=This API need administrate privileges
neutron-VnetVpcvmInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are vpcvm+eip still in use on the vnet. can not disassociate from one igw.
neutron-BadRequest=Bad <<< %(resource)s >>> request
neutron-TunnelIdInUse=Unable to create the network. The tunnel ID <<< %(tunnel_id)s >>> is in use.
neutron-DuplicatePeering=Duplicate domain peering error
neutron-NatnetworkInUse=Unable to complete operation on natnetwork <<< %(natnetwork_id)s >>>. There are ipallocations which associated still in use on the natnetwork.
neutron-NotFoundTor=Can not found KscTor <<< %(id)s >>>
neutron-TooManyExternalNetworks=More than one external network exists
neutron-Dns1Null=You need to set dns1 first
neutron-GWIPInvalid=The given gw ip <<< %(ip)s >>> is invalid
neutron-TorUnuseable=The ksctor <<< %(ip)s >>> unregistered or not enabled
neutron-MissingPrimaryVifError=Can not add extension vif to vm without primary vif
neutron-NetworkVlanRangeError=Invalid network VLAN range
neutron-TorIndexError=The if_index type is <<< %(t_index)s >>> that it is invalid, We should set it to Integer
neutron-GatewayConflictWithAllocationPools=Gateway ip <<< %(ip_address)s >>> conflicts with allocation pool <<< %(pool)s >>>
neutron-QuotaResourceUnknown=Unknown quota resources <<< %(unknown)s >>>.
neutron-NatipallocationNatpoolConflict=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. The natsubnet_id of this natipallocation should be same with the other natipallocations already associated for one natpool.
neutron-ResourceDeleteError=Delete resource <<< %(resource_name)s >>> from db failed
neutron-CanNotCreateRemotePeering=Failed to create a remote peering, remote error msg
neutron-VniAllocationFailed=No valid vni in vni pools.
neutron-MissingAttrError=Request body missing <<< %(attr_name)s >>> attribute
neutron-NoVpcSgFound4Vif=No vpc sg found for vif <<< %(id)s >>>
neutron-IcmpTypeCodeRangeIncorrect=Icmp type or code should be between 0 and 99
neutron-NetworkNotFound=Network <<< %(net_id)s >>> could not be found
neutron-PhyhostMultipleNicError=Physical cloud host does not support multiple NIC yet
neutron-InUse=\u8D44\u6E90\u6B63\u5728\u4F7F\u7528\u4E2D
neutron-AgentNotSupport=This api does not support <<< %(agent_type)s >>>
neutron-SudoRequired=Sudo priviledge is required to run this command.
neutron-InvalidSharedSetting=Unable to reconfigure sharing settings for network <<< %(network)s >>>. Multiple tenants are using it
neutron-VpcSubnetNotFound=Subnet <<< %(id)s >>> is not found
neutron-TgwAgentTunnelIPError=Tgw agents have different tunnel ip
neutron-ResourceNotSyncWithRedis=Class <<< %(name)s >>> is not synced with redis
neutron-Conflict=An unknown exception occurred.
neutron-VgwRouteNotFoundByVgwAndRoute=VgwRouteNot vgw_host <<< %(vgw_host)s >>>  route_id <<< %(route_id)s >>> could not be found
neutron-VnetCidrError=Vnet <<< %(cidr)s >>> ip address number should not has less then two
neutron-HostRoutesExhausted=Unable to complete operation for <<< %(subnet_id)s >>>. The number of host routes exceeds the limit <<< %(quota)s >>>.
neutron-VniInUse=Vni still in use
neutron-VPNRemoteCidrsEmptyError=VPN remote cidr can not be empty
neutron-NoNetworkAvailable=Unable to create the network. No tenant network is available for allocation.
neutron-NetworkDhcpConflict=Network <<< %(network_id)s >>> has dhcp on subnet already
neutron-VpcDomainInUseNatPool=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Natpool
neutron-AllocateNatipallocationsError=Allocate natipallocations for natpool failed. There is no enough available nateips in one natsubnet. Please contact system administrator.
neutron-DhcpDnsDuplicateError=Duplicate nameserver
neutron-VpcDomainInUsePeering=Vpc domain <<< %(domain_id)s >>> cannot be deleted since it has existing in-use Peering
neutron-TorIndexUnuseable=This index <<< %(id)s >>> is not part of available range
neutron-IpAddressInUse=Unable to complete operation for network <<< %(net_id)s >>>. The IP address <<< %(ip_address)s >>> is in use.
neutron-VnetNatpoolInUse=Unable to complete operation on vnet <<< %(vnet_id)s >>>. There are another natpool use this vnet.
neutron-InvalidContentType=Invalid content type <<< %(content_type)s >>>
neutron-PortfwdNotFound=Portfwd <<< %(portfwd_id)s >>> could not be found
neutron-DuplicateRegionNameError=Duplicate region <<< %(name)s >>>
neutron-EndpointVnetIpNumberError=Availabe ip number of endpoint vnet must in 253-1021.
neutron-NoValidIPAddressError=\u5B50\u7F51 <<< %(vnet)s >>> \u6CA1\u6709\u53EF\u7528IP\u8D44\u6E90
neutron-NatipallocationReserved=Unable to complete operation on natipallocation <<< %(natipallocation_id)s >>>. This natipallocation reserved.
neutron-InvalidAllocationPool=The allocation pool <<< %(pool)s >>> is not valid.
neutron-SubnetDhcpInUse=Subnet <<< %(id)s >>> still has associated dhcp ip address, disable operation is forbidden
neutron-DuplicateTorDatapathIP=The ksctor datapath ip reduplicate
neutron-EIPBindingError=Error during binding EIP with new vif
neutron-MacAddressGenerationFailure=Unable to generate unique mac on network <<< %(net_id)s >>>.
neutron-VnetNatpoolConflict=Unable to complete operation on vnet <<< %(vnet_id)s >>>. The domain of vnet and natpool <<< %(natpool_id)s >>> should be same
neutron-InvalidRemoteDomain=Domain can not create peering with itself
neutron-InvalidRestAPI=Try to invoke a hidden invalid rest api or with a wrong request body
neutron-DuplicateHwaddrError=Vif hwaddr <<< %(hwaddr)s >>> duplicate with exist one
neutron-SubnetNotFound=Subnet <<< %(subnet_id)s >>> could not be found
neutron-VnetNotAssociatedWithAcl=vnet <<< %(vnet_id)s >>> is not associated with Acl <<< %(acl_id)s >>>
neutron-RegionInUseError=Region <<< %(name)s >>> is in use, can not delete it
neutron-AclRuleNotFound=Acl_rule <<< %(id)s >>> is not found
neutron-OverlappingAllocationPools=Found overlapping allocation pools
neutron-InvalidPriority=priority must be provided and must be in the range of 1-32766
neutron-TorAlreadyExists=TOR <<< %(ip)s >>> is already exists
neutron-InvalidStartEndIPAddr=IP address <<< %(ip)s >>> not in cidr <<< %(cidr)s >>> range
neutron-NoValidVni=No valid <<< %(vni_type)s >>> vni
neutron-SubnetDhcpExhuasted=Subnet <<< %(id)s >>> is exhuasted, no more ip for dhcp
neutron-CidrSameWithRouteDestError=Cidr <<< %(cidr)s >>> same with <<< %(route_type)s >>> route error
neutron-CanNotAllocateTip=failed to allocate tip
neutron-StartBehindEnd=Start IP address <<< %(start)s >>> should not great then end ip address <<< %(end)s >>>
neutron-InvalidVPNDevice=Device <<< %(device)s >>> has already been used by another vpn service
neutron-PortStartEndMustBeGiven=both port_start and port_end must be provided for tcp/udp rules
neutron-UpdateExtenstionVifError=<<< %(id)s >>> is an extension vif
neutron-FlatNetworkInUse=Unable to create the flat network. Physical network <<< %(physical_network)s >>> is in use.
neutron-InvalidExtensionEnv=Invalid extension environment
neutron-TorInUse=The KscTor <<< %(id)s >>> can not be deleted that it is being used by Phyhost
neutron-InvalidPortrangeInput=Invalid portrange input. Reason <<< %(reason)s >>>
neutron-FloatingipVpcvmInUse=Unable to complete operation on floatingip <<< %(floatingip_id)s >>>. There are vpcvm+eip still in use on the floatingip. can not disassociate from one igw.
neutron-GatewayIpInUse=Current gateway ip <<< %(ip_address)s >>> already in use by port <<< %(port_id)s >>>. Unable to update.
neutron-ServicehasRoute=There still have route in service <<< %(id)s >>>, can not delete this service.
neutron-DuplicateTipCidr=Tip cidr <<< %(cidr)s >>> has already been created
neutron-NoUpTgwAgent=No valid Tgw agent found, can not create peering
neutron-IPAddressNotFound=IP <<< %(id)s >>> not found
neutron-InvalidNatsubnetInput=Invalid natsubnet input. Reason <<< %(reason)s >>>
neutron-MultiSameVnetVifError=Vm can not own more then one vif in the same vnet
neutron-UpdateDhcpError=Create DHCP error during migration
neutron-VifFoundMultipleResultsByVmId=Vif found multiple results by vm_id <<< %(vm_id)s >>>
neutron-VnetNotFoundByVni=Vnet <<< %(vni)s >>> could not be found by vni
neutron-CanNotDeleteRemotePeering=Failed to delete a remote peering. Remote error msg
neutron-AdminRequired=User does not have admin privileges
neutron-NoValidVniForPeering=Can not find valid vni for peering service
neutron-VnetAlreadyHasAcl=vnet <<< %(vnet_id)s >>> has Acl <<< %(acl_id)s >>> associated already
neutron-VlanIdInUse=Unable to create the network. The VLAN <<< %(vlan_id)s >>> on physical network <<< %(physical_network)s >>> is in use.
neutron-VmhostRouteCannotUpdate=Vmhost route <<< %(route_id)s >>> can not update.
neutron-NeutronException=An unknown exception occurred.
neutron-InvalidActionForType=Invalid action '<<< %(action)s >>>' for object type '<<< %(object_type)s >>>'. Valid actions
neutron-NotAuthorized=Not authorized.
neutron-NotBelongToSameDomain=<<< %(id1)s >>> and <<< %(id2)s >>> does not belong to the same domain
neutron-OutOfBoundsAllocationPool=The allocation pool <<< %(pool)s >>> spans beyond the subnet cidr <<< %(subnet_cidr)s >>>.
neutron-RetryMaxTimeReached=Retry time has reached max count.
neutron-RecreateTorParamsInputError=The value of the recreate key must be setting to Ture.
neutron-UpdateExtVifError=Can not update extension vif dns attributes
neutron-InvalidDirection=direction must be provided and must be either in or out
neutron-ResourceInitError=Resource <<< %(resource_name)s >>> db initialization failed
neutron-PhyhostUpdateVifSgError=Physical cloud host does not support update sg of vif yet
neutron-VifAlreadyAssociatedWithSg=Vif is already associated with a vpc sg. Disassociate first.
neutron-RbacPolicyNotFound=RBAC policy of type <<< %(object_type)s >>> with ID <<< %(id)s >>> not found
neutron-MacAddressInUse=Unable to complete operation for network <<< %(net_id)s >>>. The mac address <<< %(mac)s >>> is in use.
neutron-ACLFullQuotaReached=The maximum number of ACL already exists.
neutron-IPInvalid=The given ip <<< %(ip)s >>> is invalid
neutron-OverQuota=Quota exceeded for resources
neutron-InvalidInput=\u975E\u6CD5\u8F93\u5165, \u539F\u56E0:<<< %(reason)s >>>
neutron-NatpoolCreatedError=If there is one classic natpool already exists in a domain, Can not create any more natpool. Or if there is one custom natpool already exists in a domain, Can not create any more classtic natpool
neutron-OutofRange=Reserved range out of existing range
neutron-OperationNotAllowed=Only CREATE or DELETE operation allowed in each update operation. <<< %(op)s >>> requested.
neutron-UpdatePhyhostOpError=The operation type error of update phyhost msg
neutron-NotFound=An unknown exception occurred.
neutron-RulesShouldBeList=rules muse be a list and can not be an empty list. Invalid format.
neutron-ParamsInputError=Params input error
neutron-NatnetworkNotFound=Natnetwork <<< %(natnetwork_id)s >>> could not be found
neutron-NatipallocationNotFound=Natipallocation <<< %(natipallocation_id)s >>> could not be found
neutron-NoValidVniForVPN=Can not find valid vni for vpn service
neutron-QuotaMissingTenant=Tenant-id was missing from Quota request
neutron-NotFoundTipCidr=Can not found tipcidr <<< %(id)s >>>
neutron-VniNotFound=Vni <<< %(vni)d could not be found
neutron-NatpoolUnvalidVent=Type of the vnet must be local.
neutron-DuplicateNovaVifIDError=Nova_vif_id <<< %(nova_vif_id)s >>> should not same with Exist one
neutron-PortrangeExists=Create or update failed. another portrange be same already exists.
neutron-RbacPolicyInUse=RBAC policy on object <<< %(object_id)s >>> cannot be removed because other objects depend on it.
neutron-TorIndexAlreadyUsed=The TOR <<< %(ip)s >>> index <<< %(index)s >>> is already used
neutron-InvalidProtocol=Protocol specified in rule is none of icmp, tcp, udp or ip
neutron-TooManyVpcSgs=too many sgs provided to associate with the vif.
neutron-OneOperationAllowed=Either CREATE or DELETE operation is allowed in each update operation at one time.
neutron-PolicyNotAuthorized=Policy doesn't allow <<< %(action)s >>> to be performed.
neutron-AclRulePortRangeOverlaps=Acl rule tcp/udp port range overlaps with other acl rules
neutron-VniRangeNotFound=Vni range <<< %(id)s >>> could not be found
neutron-OneOfIcmpCodeTypeGiven=Both or neither icmp code or icmp type should be given
neutron-DefaultSGAlreadyExists=Vpc security group with default type already exists. Only one is allowed
neutron-ResourceExhausted=No available resource can be allocated
neutron-HostTypeNotMatchError=Expect host_type
neutron-GetPhyhostError=Get phyhost error when <<< %(msg)s >>>
neutron-VnetDhcpRangeWrong=Vnet dhcp range <<< %(start)s >>> to <<< %(end)s >>> should not beyond cidr <<< %(cidr)s >>>
neutron-InvalidQuotaValue=Change would make usage less than 0 for the following resources
neutron-MaskNotWithinRange=The given mask <<< %(mask)s >>> is not between 0 and 32
neutron-TipInUse=Tip <<< %(tip)s >>> in use, can not reserve it
neutron-InvalidActionForHandleBulk=Invalid action <<< %(action)s >>> for handle_vif_sg_bulk
neutron-SGBulkOperationError=Param length is exceed <<< %(length)s >>> in sys_sg bulk operation
neutron-VpcSecurityGroupIdInvalid=sg_id
neutron-QosResourceNotFound=QosResource or bw package <<< id >>> could not be found
neutron-QosBindingNotFound=QosBinding <<< %(qos_uuid)s >>> could not be found
neutron-NoQosBindingEipFound=QosBinding whose eip is <<< %(eip_id)s >>> could not be found
neutron-MultipleQosBindingEipFound=Multiple QosBinding whose eip is <<< %(eip_id)s >>> are found
neutron-QosCreatingFailed=Qos resource creating for <<< %(floatingip_id)s >>> failed
neutron-QoSIDWrongQoSIDWrong=Qos id for vpc CAN NOT be smaller than separator. CHECK Immediately!
neutron-RouteNotFound=\u627E\u4E0D\u5230\u8DEF\u7531\uFF0C\u8DEF\u7531ID\uFF1A<<< %(route_id)s >>>
neutron-InvalidVipInput=\u672A\u7ED1\u5B9A\u5F39\u6027IP\u7684\u8D1F\u8F7D\u5747\u8861\u65E0\u6CD5\u5F00\u542F\u76D1\u542C\u5668
neutron-NetworkExhausted=\u5F39\u6027IP\u8D44\u6E90\u4E0D\u8DB3
neutron-VpcDomainInUse=\u5220\u9664VPC\u5931\u8D25\uFF0C\u8BF7\u5148\u5220\u9664VPC\u4E0B\u8D44\u6E90\uFF0CVPC ID:<<< %(domain_id)s >>>
neutron-VpcDomainInUseSecuritygroup=\u8BF7\u5148\u5220\u9664VPC\u4E0B\u7684\u5B89\u5168\u7EC4\uFF0C\u518D\u5220\u9664VPC\u3002VPC ID\uFF1A<<< %(domain_id)s >>> 
neutron-FloatingIPInUse=\u8BF7\u5148\u89E3\u7ED1\u5F39\u6027IP\u7ED1\u5B9A\u7684\u8D44\u6E90\uFF0C\u518D\u5220\u9664\u5F39\u6027IP\u3002
neutron-InvalidRemoteCidr= \u5BF9\u7AEF\u7F51\u6BB5<<< %(cidr)s >>> \u4E0D\u80FD\u4E0E\u53D1\u8D77\u7AEF\u7684\u7F51\u6BB5\u91CD\u53E0\uFF0C\u4E0D\u80FD\u4E0E\u53D1\u8D77\u7AEFVPN\u7684\u5BF9\u7AEF\u7F51\u6BB5\u91CD\u53E0
neutron-CertificateNotFound=\u627E\u4E0D\u5230\u7684\u8BC1\u4E66,\u8BC1\u4E66ID\uFF1A<<< %(id)s >>>
neutron-VpcNetworkInUse=\u5B50\u7F51\u65E0\u6CD5\u5220\u9664\uFF0C\u8BF7\u5148\u5220\u9664\u5B50\u7F51\u4E0B\u7684\u4E91\u670D\u52A1\u5668\u3001\u7269\u7406\u673A\u6216\u8005\u9AD8\u53EF\u7528\u865A\u62DFIP\uFF0C\u5B50\u7F51ID\uFF1A<<< %(net_id)s >>>
neutron-DhcpRangeOverLimit=DHCP\u6700\u5927\u652F\u6301<<< %(num)d >>>\u4E2AIP
neutron-VipNotFound=\u627E\u4E0D\u5230\u76D1\u542C\u5668\uFF0C\u76D1\u542C\u5668ID\uFF1A<<< %(id)s >>>
neutron-VpcSgRuleNotFound=\u627E\u4E0D\u5230\u5B89\u5168\u7EC4\u89C4\u5219\uFF0C\u5B89\u5168\u7EC4\u89C4\u5219ID\uFF1A <<< %(vpc_sg_rule_id)s >>>
neutron-VipExists=\u76D1\u542C\u5668\u7AEF\u53E3\uFF08<<< %(id)s >>>\uFF09\u548C\u534F\u8BAE <<< %(id)s >>> \u5DF2\u7ECF\u88AB\u5176\u4ED6\u76D1\u542C\u5668\u5360\u7528\u3002\u8D1F\u8F7D\u5747\u8861ID\uFF1A<<< %(id)s >>>
neutron-IpRuleAlreadyExists=IP\u89C4\u5219\u5DF2\u7ECF\u5B58\u5728
neutron-PeeringNotFound=\u627E\u4E0D\u5230\u5BF9\u7B49\u8FDE\u63A5\uFF0CID\uFF1A<<< %(id)s >>> 
neutron-PortfwdExists=\u7AEF\u53E3\u6620\u5C04\u5DF2\u7ECF\u5B58\u5728
neutron-VpcSecurityGroupInUse=\u8BF7\u5148\u89E3\u7ED1\u5B89\u5168\u7EC4\u4E0B\u7684\u4E91\u670D\u52A1\u5668\u518D\u5220\u9664\u5B89\u5168\u7EC4\uFF0C\u5B89\u5168\u7EC4ID\uFF1A<<< %(sg_uuid)s >>>
neutron-InvalidPoolInput=\u672A\u7ED1\u5B9A\u5F39\u6027IP\u7684\u8D1F\u8F7D\u5747\u8861\u65E0\u6CD5\u5F00\u542F
neutron-VpcAclInUse=\u8BF7\u5148\u89E3\u7ED1ACL\u5173\u8054\u7684\u5B50\u7F51\uFF0C\u518D\u5220\u9664ACL\u3002ACL ID\uFF1A<<< %(acl_id)s >>>
neutron-VpcSecurityGroupNotFound=\u627E\u4E0D\u5230\u5B89\u5168\u7EC4\uFF0C\u5B89\u5168\u7EC4ID\uFF1A<<< %(sg_uuid)s >>>
neutron-DhcpRangeWrong=DHCP\u8303\u56F4\u5FC5\u987B\u5728<<< %(start)s >>>\u5230<<< %(end)s >>>\u4E4B\u95F4\u3002
neutron-SystemRouteCannotDelete=\u7CFB\u7EDF\u8DEF\u7531\u65E0\u6CD5\u5220\u9664\u3002
neutron-DomainNotFound=\u627E\u4E0D\u5230VPCID\uFF0CVPCID\uFF1A<<< %(domain_id)s >>>
neutron-IPMaskInvalid=\u7ED9\u5B9A\u7684IP <<< %(ip)s >>>\u548C\u63A9\u7801<<< %(mask)s >>>\u5BF9\u4E8E\u5B50\u7F51\u65E0\u6548
neutron-FloatingDeviceAlreadyAssociated=\u5B9E\u4F8B\u5DF2\u7ECF\u7ED1\u5B9A\u4E86\u5176\u4ED6\u5F39\u6027IP\uFF0C\u7ED1\u5B9A\u5F39\u6027IP\u5931\u8D25\u3002\u7ED1\u5B9A\u5931\u8D25\u7684\u5F39\u6027IP\uFF1A<<< %(id)s >>> (ID:<<< %(id)s >>>)<<< >>>\u3002\u5B9E\u4F8BID\uFF1A <<< %(id)s >>>\u3002\u5DF2\u7ED1\u5B9A\u7684\u5F39\u6027IP\uFF1A<<< %(id)s >>>
neutron-FloatingIPNotFound=\u627E\u4E0D\u5230\u7684\u5F39\u6027IP\uFF0C\u5F39\u6027IP\u7684ID\uFF1A<<< %(id)s >>>
neutron-AclNotFound=\u627E\u4E0D\u5230ACL\uFF0CACL ID\uFF1A<<< %(acl_id)s >>>
neutron-AllocateLocalipError=\u8BF7\u5148\u521B\u5EFA\u7EC8\u7AEF\u5B50\u7F51
neutron-RuleNotBelongToSg=\u5B89\u5168\u7EC4\u89C4\u5219\u4E0D\u5C5E\u4E8E\u8BE5\u5B89\u5168\u7EC4\uFF0C\u89C4\u5219ID: <<< %(rule_id)s >>>,\u5B89\u5168\u7EC4ID\uFF1A<<< %(sg_uuid)s >>>.
neutron-VifNotFound=\u627E\u4E0D\u5230\u7F51\u5361\uFF0C\u7F51\u5361ID\uFF1A<<< %(vif)s >>>
neutron-PoolNotFound=\u627E\u4E0D\u5230\u8D1F\u8F7D\u5747\u8861\u3002\u8D1F\u8F7D\u5747\u8861ID\uFF1A<<< %(id)s >>>
neutron-VpcSgRuleIcmpRuleExists=\u5B89\u5168\u7EC4\u89C4\u5219ICMP\u7C7B\u578B/\u4EE3\u7801\u5DF2\u7ECF\u5B58\u5728
neutron-VpcSgRulePortRangeOverlaps=\u5B89\u5168\u7EC4\u89C4\u5219\u7AEF\u53E3\u8303\u56F4\u4E0D\u5141\u8BB8\u91CD\u53E0
neutron-MalformedRequestBody=\u9519\u8BEF\u7684\u8BF7\u6C42\uFF0C\u56E0\u4E3A\uFF1A<<< %(reason)s >>>
neutron-FloatingIpNotAssociated=\u5F39\u6027IP\u6CA1\u6709\u7ED1\u5B9A\u4EFB\u4F55\u8D44\u6E90\uFF0C\u65E0\u9700\u89E3\u7ED1
neutron-EndpointNetworkInUse=\u8BE5\u7EC8\u7AEF\u5B50\u7F51\u4E0B\u6709\u8D1F\u8F7D\u5747\u8861\u3001\u4E91\u6570\u636E\u5E93\u6216\u5BF9\u8C61\u5B58\u50A8\u8D44\u6E90\uFF0C\u65E0\u6CD5\u5220\u9664
neutron-VnetNotFound=\u627E\u4E0D\u5230\u5B50\u7F51\uFF0C\u5B50\u7F51ID\uFF1A<<< %(vnet_id)s >>>
neutron-PoolInUse=\u8BF7\u5148\u5220\u9664\u8D1F\u8F7D\u5747\u8861\u4E0B\u7684\u76D1\u542C\u5668\uFF0C\u8D1F\u8F7D\u5747\u8861ID\uFF1A<<< %(id)s >>>
neutron-NatpoolNotFound=\u627E\u4E0D\u5230NAT\uFF0CID\uFF1A<<< %(natpool_id)s >>> \u3002
neutron-VnetCidrWrong=\u5B50\u7F51\u7684\u7F51\u6BB5\u5FC5\u987B\u5C5E\u4E8EVPC\u7F51\u6BB5\u3002VPC\u7F51\u6BB5\uFF1A<<< %(cidr)s >>>
neutron-FailedToAllocateFlaotingIP=\u521B\u5EFA\u5F39\u6027IP\u5931\u8D25
neutron-DuplicateVnetCidr=\u7F51\u6BB5<<< %(cidr)s >>> \u5DF2\u7ECF\u88AB\u5360\u7528\uFF0C\u8BF7\u4F7F\u7528\u5176\u4ED6\u7F51\u6BB5\u521B\u5EFA\u5B50\u7F51
neutron-InvalidPortRange=TCP/UDP\u534F\u8BAE\u8303\u56F4\u5FC5\u987B\u5728[1, 65535]\u4E4B\u95F4
neutron-InvalidRouteInput=\u8F93\u5165\u8DEF\u7531\u65E0\u6548\uFF0C\u56E0\u4E3A\uFF1A<<< %(reason)s >>>\u3002
neutron-CertificateInUsed=\u8BC1\u4E66\uFF0CID\uFF1A<<< %(id)s >>> \u6B63\u5728\u88AB\u4F7F\u7528, \u65E0\u6CD5\u5220\u9664\u3002
neutron-MgroupInUse=\u8BF7\u5148\u89E3\u7ED1\u670D\u52A1\u5668\u7EC4\u5173\u8054\u7684\u76D1\u542C\u5668\uFF0C\u518D\u5220\u9664\u670D\u52A1\u5668\u7EC4\u3002\u670D\u52A1\u5668\u7EC4\u0020ID\uFF1A<<< %(id)s >>>
neutron-VipInUse=\u8BF7\u5148\u5220\u9664\u76D1\u542C\u5668\u4E0B\u7684\u57DF\u540D\uFF0C\u518D\u5220\u9664\u76D1\u542C\u5668\u3002\u0020Listener ID\uFF1A<<< %(id)s >>>
neutron-SlbaclNotFound=\u627E\u4E0D\u5230LB ACL\uFF0CID\uFF1A <<< %(slbacl_id)s >>> \u4E0D\u5B58\u5728
neutron-SlbaclruleNotFound=\u627E\u4E0D\u5230LB ACL\u89C4\u5219\uFF0CID\uFF1A <<< %(slbacl_rule_id)s >>> \u4E0D\u5B58\u5728.
neutron-SlbaclruleExists=\u540C\u4E00ACL\u4E0B\u4F18\u5148\u7EA7\u4E0D\u5141\u8BB8\u91CD\u590D\u3002
neutron-SlbaclruleCidrExists=\u540C\u4E00ACL\u4E0B\u7F51\u6BB5\u4E0D\u5141\u8BB8\u91CD\u590D\u3002
neutron-SlbaclInUse=\u8BF7\u5148\u89E3\u7ED1ACL\u5173\u8054\u7684\u76D1\u542C\u5668\uFF0C\u518D\u5220\u9664ACL\u3002LB ACL ID\uFF1A<<< %(slbacl_id)s >>>
neutron-InvalidPoolType=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolSlbType=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolAddress=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolVNet=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidPoolFloatingIpVersion=Invalid pool input. Reason <<< %(reason)s >>>
neutron-InvalidVipCertificate=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipLbKind=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipSlbAcl=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipLbMethod=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipProtocol=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidVipSynProxy=Invalid vip input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorCheckDataAndCheckRet=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorHttpMethodAndUrlPath=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorVipSlbType=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidHealthMonitorProtocol=Invalid health_monitor input. Reason <<< %(reason)s >>>
neutron-InvalidServerVipSlbType=Invalid server input. Reason <<< %(reason)s >>>
neutron-InvalidServerCertificate=\u7A7A\u57DF\u540D\u9ED8\u8BA4\u7EE7\u627F\u76D1\u542C\u5668\u8BC1\u4E66\uFF0C\u4E0D\u652F\u6301\u7ED1\u5B9A\u5176\u4ED6\u8BC1\u4E66
neutron-InvalidServerVipAddress=Invalid server input. Reason <<< %(reason)s >>>
neutron-InvalidMemberVpcVm=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberResourceID=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberLbKindOrEmode=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberVipSlbType=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidMemberMasterSlaveType=Invalid member input. Reason <<< %(reason)s >>>
neutron-InvalidVipMetadata=\u652F\u6301\u5411\u540E\u8F6C\u53D1HTTP\u534F\u8BAE\u7248\u672C\u7684\u76D1\u542C\u5668\u534F\u8BAE\u5FC5\u987B\u4E3AHTTP\u6216HTTPS
neutron-LocationExists=\u8BE5\u8DEF\u5F84<<< %(path)s >>>\u5DF2\u5B58\u5728\u4E8E<<< %(server_id)s >>>\u57DF\u540D\u4E0B, \u8BF7\u786E\u8BA4\u3002
neutron-DedicatedResourceExhausted=VLANID\u5DF2\u88AB\u4F7F\u7528
neutron-IPAlreadyReserved=IP<<< %(ip_addresses)s >>>\u5DF2\u88AB\u9884\u7559\u3002
neutron-CenNotFound=\u4E91\u4F01\u4E1A\u7F51 ID: <<< %(cen_id)s >>> \u4E0D\u5B58\u5728.
neutron-AttachCenError=\u5173\u8054\u4E91\u4F01\u4E1A\u7F51\u5931\u8D25, \u539F\u56E0\uFF1AVpc\u5DF2\u5173\u8054\u5176\u4ED6\u8D44\u6E90\u3001\u6216\u8FB9\u754C\u7F51\u5173\u672A\u5173\u8054\u8FDE\u63A5\u901A\u9053\u3001\u6216\u672A\u88AB\u6388\u6743
neutron-InstanceNotAttachedCen=\u5B9E\u4F8BID\uFF1A <<< %(instance_id)s >>> \u672A\u5173\u8054\u5230\u4E91\u4F01\u4E1A\u7F51ID\uFF1A <<< %(cen_id)s >>>.
neutron-CenGrantTenantShouldNotSame=\u5B9E\u4F8B\u8D26\u53F7ID\u4E0D\u5E94\u4E0E\u4E91\u4F01\u4E1A\u7F51\u8D26\u53F7ID\u76F8\u540C\u3002
neutron-CenGrantRuleExisted=\u8BE5\u4E91\u4F01\u4E1A\u7F51\u6388\u6743\u5DF2\u7ECF\u5B58\u5728\u3002
neutron-CenAlreadyBindInstance=\u8BE5\u4E91\u4F01\u4E1A\u7F51\u5DF2\u7ED1\u5B9A\u5B9E\u4F8B, \u65E0\u6CD5\u5220\u9664
neutron-BandWidthExisted=Band width Mixin existed for <<< %(region_a)s >>> and <<< %(region_b)s >>>.
neutron-CenVniNotFound=Cen vni <<< %(id)s >>> could not be found.
neutron-CenVniRangeConflict=Cen Range conflict with existed.
neutron-SameRegionError=Not allowed set egress for same region.
neutron-CreateRemoteCenError=Create remote cen error, reason is: <<< %(reason)s >>>
neutron-RouteConflict=<<< %(conflict_msg)s >>>
neutron-RegionInUse=Region <<< %(region)s >>> in use.
neutron-RegionNameNotFound=\u673A\u623F\u540D\u79F0 <<< %(name)s >>> \u672A\u627E\u5230\u3002
neutron-RouteConflictNotFound=\u8DEF\u7531\u51B2\u7A81 <<< %(id)s >>> \u672A\u627E\u5230
neutron-CenBindExisted=\u5B9E\u4F8B <<< %(instance_id)s >>> \u5DF2\u7ED1\u5B9A\u4E91\u4F01\u4E1A\u7F51 <<< %(cen)s >>>.
neutron-PublishCenRouteError=\u53D1\u5E03\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531\u5931\u8D25\uFF0C\u539F\u56E0: <<< %(reason)s >>>.
neutron-BandwidthMixinExisted=BandwidthMixin already existed for these two region
neutron-GetRemoteCenError=Get remote cen error, reason is: <<< %(reason)s >>>
neutron-GetInstanceFailed=\u83B7\u53D6\u5B9E\u4F8B <<< %(id)s >>> \u5931\u8D25, \u539F\u56E0: <<< %(reason)s >>>.
neutron-DirectConnectNotAttachedSwitchInterface=Direct connect <<< %(dc_id)s >>> not attached switch interface.
neutron-CenRangeInputError=Cen Range start should not bigger than end.
neutron-FailedDependency=\u53D1\u751F\u672A\u77E5\u5F02\u5E38
neutron-InstanceIdNotProvided=Instance id not provided.
neutron-RegionNameOrEndpointExisted=Region name or endpoint already existed.
neutron-DeleteRemoteCenError=Delete remote cen domain error, reason is: <<< %(reason)s >>>
neutron-CenFailedQueueNotFound=Cen failed queue <<< %(id)s >>> not found.
neutron-AllocateCenVniFailed=Allocate cen vni failed.
neutron-InstanceAlredyBindAttached=\u7ED1\u5B9A\u4E91\u4F01\u4E1A\u7F51\u5931\u8D25, \u5B9E\u4F8B<<< %(instance_id)s >>> \u5DF2\u7ED1\u5B9A <<< %(cen_id)s >>>.
neutron-BandwidthPackageNotFound=\u5E26\u5BBD\u5305 <<< %(id)s >>> \u672A\u627E\u5230
neutron-CenBindingNotFound=Cen Binding <<< %(id)s >>> could not be found.
neutron-CenRegionInfoNotFound=\u4E91\u4F01\u4E1A\u7F51\u673A\u623F\u4FE1\u606F <<< %(id)s >>> \u672A\u627E\u5230
neutron-DeleteCenRouteError=\u53D1\u5E03\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531\u5931\u8D25, \u539F\u56E0: <<< %(reason)s >>>.
neutron-UpdateRemoteConnError=Update remote conn failed, reason is: <<< %(reason)s >>>.
neutron-InstanceNotFound=\u5B9E\u4F8B <<< %(id)s >>> \u672A\u627E\u5230.
neutron-CenVniRangeNotFound=Cen vni range <<< %(id)s >>> could not be found.
neutron-CenUnSupportType=\u4E91\u4F01\u4E1A\u7F51\u4E0D\u652F\u6301\u8BE5\u5B9E\u4F8B\u7C7B\u578B: <<< %(instance_type)s >>>.
neutron-BandwidthMixinNotFound=Bandwidth Mixin <<< %(id)s >>> could not be found.
neutron-DetachCenError=\u89E3\u7ED1\u4E91\u4F01\u4E1A\u7F51\u5931\u8D25, \u539F\u56E0: <<< %(reason)s >>>.
neutron-VpcDomainInUseCen=Vpc <<< %(domain_id)s >>> \u5220\u9664\u5931\u8D25\uFF0C\u5DF2\u7ED1\u5B9A\u4E91\u4F01\u4E1A\u7F51
neutron-CenRouteCannotDelete=\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531\u4E0D\u80FD\u5220\u9664
neutron-MissDomainIdForDirectConnVpcBind=\u521B\u5EFAVPC\u7C7B\u578B\u7684\u8FB9\u754C\u7F51\u5173VPC ID\u7F3A\u5931.
neutron-DomainIdShouldNotProvided=Args domain_id should not provided for un-bind type.
neutron-CanNotAddExtraCidrsToUnbindDc=Can not add extra cidrs to un-bind direct connect.
neutron-CanNotCreateDcBindToCen=Can not create direct connect to cen.
neutron-DirectNotAttachedVpc=\u8FB9\u754C\u7F51\u5173 <<< %(dc_id)s >>> \u4E0D\u80FD\u7ED1\u5B9A\u81F3vpc
neutron-DirectNotAttachedCen=\u8FB9\u754C\u7F51\u5173 <<< %(dc_id)s >>> \u4E0D\u80FD\u7ED1\u5B9A\u81F3\u4E91\u4F01\u4E1A\u7F51
neutron-CanNotCreateDcToCen=Can not create a direct connect to cen that has not attached a switch interface.
neutron-DirectAlreadyAttached=\u8FB9\u754C\u7F51\u5173 <<< %(dc_id)s >>> \u5DF2\u7ED1\u5B9A\u5176\u4ED6\u5B9E\u4F8B
neutron-DirectDeleteError=Direct connect error: <<< %(msg)s >>>
neutron-DomainAlreadyAttachedCen=Can not create peering, domain <<< %(domain_id)s >>> has already attached cen.
neutron-DomainNotAttachedCen=Domain <<< %(domain_id)s >>> not attached to cen.
neutron-CenDomainNotFound=Cen domain <<< %(id)s >>> not found.
neutron-CenDomainInuse=Cen domain <<< %(id)s >>> in use.
neutron-CenRouteNotFound=\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531 <<< %(id)s >>> \u672A\u627E\u5230
neutron-InvalidRouteTypeForCen=Invalid route type <<< %(type)s >>> to cen, valid types: <<< %(valid_types)s >>>.
neutron-DnsNotAllowedPublish=Dns not allowed to publish to cen or bgp direct connect.
neutron-InvalidRouteIpVersionForCen=\u975E\u6CD5\u7684IP\u7248\u672C, \u4EC5\u652F\u6301IPv4\u3002
neutron-RouteAlreadyPublishedToCen=\u8DEF\u7531 <<< %(id)s >>> \u5DF2\u53D1\u5E03\u81F3\u4E91\u4F01\u4E1A\u7F51
neutron-RouteNotPublishedToCen=\u8DEF\u7531 <<< %(id)s >>> \u672A\u53D1\u5E03\u81F3\u4E91\u4F01\u4E1A\u7F51
neutron-GetCenError=\u83B7\u53D6\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531\u5931\u8D25, \u539F\u56E0: <<< %(reason)s >>>.
neutron-ModifyDirectConnCidrError=Modify direct conn failed, reason is: <<< %(reason)s >>>.
neutron-DirectConnRouteNotFound=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531 <<< %(id)s >>> \u672A\u627E\u5230
neutron-DirectConnRouteConflictNotFound=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u51B2\u7A81 <<< %(id)s >>> \u672A\u627E\u5230
neutron-CenGrantRuleNotFound=\u4E91\u4F01\u4E1A\u7F51\u6388\u6743 <<< %(id)s >>> \u672A\u627E\u5230
neutron-CenPermissionDenied=\u4E91\u4F01\u4E1A\u7F51\u4E0D\u5141\u8BB8\u7ED1\u5B9A\u8BE5\u5B9E\u4F8B
neutron-DeleteGrantRuleError=\u5220\u9664\u6388\u6743\u5931\u8D25\uFF0C\u4E91\u4F01\u4E1A\u7F51 <<< %(cen_id)s >>>\u5DF2\u7ED1\u5B9A\u5B9E\u4F8B
neutron-DirectConnRouteNotSupportUpdate=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u7C7B\u578B <<< %(type)s >>> \u4E0D\u652F\u6301\u66F4\u65B0
neutron-PublishDcRouteToCenError=\u53D1\u5E03\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u81F3\u4E91\u4F01\u4E1A\u7F51\u9519\u8BEF: <<< %(msg)s >>>
neutron-DeleteDcRouteToCenError=\u5220\u9664\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u81F3\u4E91\u4F01\u4E1A\u7F51\u9519\u8BEF: <<< %(msg)s >>>
neutron-PublishDcRouteToBgpError=\u53D1\u5E03\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u81F3BGP\u9519\u8BEF: <<< %(msg)s >>>
neutron-DeleteDcRouteToBgpError=\u5220\u9664\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u81F3BGP\u9519\u8BEF: <<< %(msg)s >>>
neutron-ModifyDirectConnError=\u4FEE\u6539\u8FB9\u754C\u7F51\u5173\u9519\u8BEF: <<< %(msg)s >>>.
neutron-CenRemoteRegionConnNotFound=Cen remote region <<< %(id)s >>> not found.
neutron-CenLocalInstanceConnNotFound=Cen local instance <<< %(id)s >>> not found.
neutron-CenRouteConflictNotFound=Cen Route Conflict <<< %(id)s >>> not found.
neutron-InvalidInstanceType=Invalid instance type <<< %(type)s >>>
neutron-VpcAlreadyAttachCen=Vpc <<< %(instance_id)s >>> \u5DF2\u7ED1\u5B9A\u4E91\u4F01\u4E1A\u7F51 <<< %(cen_id)s >>>.
neutron-VpcAlreadyInPeering=Vpc <<< %(instance_id)s >>> has already in peering connect.
neutron-DirectConnectAlreadyAttachCen=\u8FB9\u754C\u7F51\u5173 <<< %(instance_id)s >>> \u5DF2\u7ED1\u5B9A\u4E91\u4F01\u4E1A\u7F51 <<< %(cen_id)s >>>.
neutron-DirectConnectAlreadyAttachVpc=\u8FB9\u754C\u7F51\u5173 <<< %(instance_id)s >>> \u5DF2\u7ED1\u5B9Avpc <<< %(domain_id)s >>>.
neutron-MissCenRouteAttr=Miss cen route attr <<< %(attr)s >>>.
neutron-VnetEnableIpv6Already=\u8BE5\u5B50\u7F51\u5DF2\u5F00\u542Fipv6
neutron-InvalidVipRedirectInPut=\u76D1\u542C\u5668\u5DF2\u5173\u8054\u540E\u7AEF\u670D\u52A1\u5668\uFF0C\u65E0\u6CD5\u5F00\u542F\u76D1\u542C\u8F6C\u53D1\u529F\u80FD\uFF0C\u8BF7\u5148\u5C06\u76D1\u542C\u5668\u4E0E\u540E\u7AEF\u670D\u52A1\u5668\u89E3\u7ED1
neutron-BGPConfigError=\u539F\u6765\u7684\u901A\u9053\u548C\u65B0\u52A0\u7684\u901A\u9053bgp\u53C2\u6570\u4E0D\u4E00\u81F4
neutron-AttachSwitchInterfaceERROR=\u8FB9\u754C\u7F51\u5173\u5F53\u524D\u5DF2\u7ED1\u5B9A\u5176\u4ED6\u8FDE\u63A5\u901A\u9053
neutron-SwitchInterfaceInUseError=\u8FDE\u63A5\u901A\u9053\u5F53\u524D\u5DF2\u7ECF\u662F\u7ED1\u5B9A\u72B6\u6001
neutron-SwitchInterfaceNotInCorrectSwitch=\u65B0\u52A0\u7684\u8FDE\u63A5\u901A\u9053\u548C\u4E4B\u524D\u7684\u8FDE\u63A5\u901A\u9053\u6CA1\u6709\u5728\u4E00\u4E2A\u4EA4\u6362\u673A\u4E0A
neutron-DetachSwitchInterfaceError=\u89E3\u7ED1\u5931\u8D25\uFF0C\u539F\u56E0 <<< %(reason)s >>>
neutron-InvalidVipMgroupInput=\u76D1\u542C\u5668\u4E0E\u670D\u52A1\u5668\u7EC4\u4E0D\u5728\u540C\u4E00\u4E2AVPC\u5185
neutron-InvalidLocationInput=\u521B\u5EFA\u89C4\u5219\u53C2\u6570\u4E0D\u5408\u6CD5, <<< %(reason)s >>>
neutron-RouteTableBindError=\u7269\u7406\u673A\u5B50\u7F51\u6216\u7EC8\u7AEF\u5B50\u7F51\u4E0D\u652F\u6301\u8DEF\u7531\u8868
neutron-VnetHasRouteTableBound=\u8DEF\u7531\u8868\u5DF2\u7ED1\u5B9A\u5B50\u7F51 <<<%\uFF08vnet_id\uFF09s>>\uFF0C\u8BF7\u5728\u5220\u9664\u524D\u89E3\u9664\u7ED1\u5B9A.
neutron-RouteTableNotFound=\u8DEF\u7531\u8868<<< %(table_id)s >>> \u6CA1\u6709\u627E\u5230.
neutron-RouteTableNotBelongToThisDomain=\u8DEF\u7531\u8868 <<< %(table_id)s >>> \u4E0D\u5C5E\u4E8E\u6B64VPC.
neutron-RouteTableHasVnetBind=\u8DEF\u7531\u8868 <<< %(table_id)s >>> \u5DF2\u7ED1\u5B9A\u5B50\u7F51
neutron-RouteTableHasNonSystemRoute=\u8DEF\u7531\u8868 <<< %(table_id)s >>> \u5DF2\u7ED1\u5B9A\u9ED8\u8BA4\u8DEF\u7531
neutron-CustomRouteNotFound=\u8DEF\u7531\u8868 <<< %(route_id)s >>> \u672A\u627E\u5230
neutron-RouteTableLimitExceeded=\u5355\u57DF\u8DEF\u7531\u8868\u8D85\u51FA\u914D\u989D.
neutron-CustomRouteLimitExceeded=\u5355\u57DF\u81EA\u5B9A\u4E49\u8DEF\u7531\u8D85\u51FA\u914D\u989D.
neutron-InvalidMemberType=\u6CE8\u518C\u7684\u771F\u5B9E\u670D\u52A1\u5668IP Version\u7C7B\u578B\u4E0E\u76D1\u542C\u5668\u7684\u5DF2\u6709\u7684\u4E0D\u540C
neutron-InvalidServerInput=\u975E\u6CD5\u7684\u5165\u53C2, Reason: <<< %(reason)s >>>
neutron-InvalidLocationRedirect=\u5904\u4E8E\u5F00\u542F\u76D1\u542C\u8F6C\u53D1\u72B6\u6001\u7684\u76D1\u542C\u5668\u4E0D\u5141\u8BB8\u6DFB\u52A0\u8F6C\u53D1\u7B56\u7565
neutron-RouteCannotCherryPick=\u53EA\u80FD\u53D1\u5E03\u52A8\u6001\u8DEF\u7531(\u4E91\u4F01\u4E1A\u7F51\u8DEF\u7531\u3001BGP\u8FB9\u754C\u7F51\u5173\u8DEF\u7531)\u5230\u8DEF\u7531\u8868
neutron-CanNotSetIpv6Rule=\u4E0D\u80FD\u8BBE\u7F6EIPV6\u89C4\u5219\uFF0C\u6240\u5C5EVPC\u672A\u5F00\u542FIPV6
neutron-VpcSgRuleDbConflict=\u5B89\u5168\u7EC4\u89C4\u5219\u91CD\u590D\u6DFB\u52A0\uFF0C\u8BF7\u786E\u8BA4\u64CD\u4F5C
neutron-InvalidSlbaclruleIpVersion=\u7F51\u6BB5 <<< %(cidr)s >>> \u4E0E\u5F53\u524D\u8BBF\u95EE\u63A7\u5236\u7684IP\u7248\u672C\u4E0D\u76F8\u7B26
neutron-VnetNotSupportIpv6=\u6B64\u7C7B\u578B\u5B50\u7F51\u4E0D\u652F\u6301IPV6
neutron-DirConnV2NotSupportHASwiInterface=2.0\u7248\u672C\u8FB9\u754C\u7F51\u5173\u4E0D\u652F\u6301\u7ED1\u5B9A\u53CC\u7EBF\u8FDE\u63A5\u901A\u9053
neutron-SwitchInterfaceNotAssistForDirConn=\u8BE5\u8FB9\u754C\u7F51\u5173\u672A\u4E0E\u8FDE\u63A5\u901A\u9053\u5173\u8054
neutron-BfdMetadataInUse=BFD\u914D\u7F6E\u5728\u4F7F\u7528\u4E2D\uFF0C\u65E0\u6CD5\u5220\u9664
neutron-LackBfdMetadataId=BFD\u914D\u7F6EID\u4E0D\u80FD\u4E3A\u7A7A
neutron-DirConnV1NotSupportPriority=\u7ED1\u5B9A1.0\u8FB9\u754C\u7F51\u5173\u7684\u901A\u9053\u4E0D\u652F\u6301\u4F18\u5148\u7EA7
neutron-DirectConnectRouteLimitExceeded=\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u5DF2\u914D\u989D\u4E0D\u8DB3
neutron-ReliabilityMethodNotAllowedOFF=\u53EF\u9760\u6027\u65B9\u6CD5BFD\u548CNQA\u4E0D\u80FD\u4E92\u76F8\u5207\u6362
neutron-ReliabilityMethodConfigError=\u65B0\u6DFB\u52A0\u7684\u8FDE\u63A5\u901A\u9053\u548C\u4E4B\u524D\u7684\u8FDE\u63A5\u901A\u9053\u53EF\u9760\u6027\u65B9\u6CD5\u8981\u8BBE\u7F6E\u4E00\u81F4
neutron-ServerExists=\u57DF\u540D\u5DF2\u5B58\u5728\u8BE5\u76D1\u542C\u5668\u4E0B\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u6DFB\u52A0
neutron-VipRedirectInUse=\u8BE5\u76D1\u542C\u5668\u88AB\u7528\u4F5C\u91CD\u5B9A\u5411\u76D1\u542C\u5668\uFF0C\u65E0\u6CD5\u5220\u9664
neutron-DnatRuleLimitExceeded=\u8D85\u8FC7\u6BCF\u4E2Anat\u5F00\u901Adnat\u9650\u989D
neutron-DnatruleNotFound=Dnat <<< %(dnatrule_id)s >>> \u627E\u4E0D\u5230
neutron-NatipHasPortMappingDnatRule=Nat IP/EIP <<< %(natip_id)s >>> \u5DF2\u7ECF\u6709\u7AEF\u53E3\u6620\u5C04dnat\u89C4\u5219
neutron-VifHasPortMappingDnatRule=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u5DF2\u7ECF\u6709\u7AEF\u53E3\u6620\u5C04dnat\u89C4\u5219
neutron-NatipHasIPMappingDnatRule=Nat IP/EIP <<< %(natip_id)s >>> \u5DF2\u6709ip\u6620\u5C04dnat\u89C4\u5219
neutron-VifHasIPMappingDnatRule=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u5DF2\u6709ip\u6620\u5C04dnat\u89C4\u5219
neutron-VifAndNatpoolConflict=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u548CNat <<< %(natpool_id)s >>> \u5E94\u8BE5\u5728\u76F8\u540C\u7684vpc
neutron-NatipHasDuplicateDnatRule=Nat IP/EIP <<< %(natip_id)s >>> \u6709\u91CD\u590D\u7684dnat\u89C4\u5219
neutron-VifHasDuplicateDnatRule=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u6709\u91CD\u590D\u7684dnat\u89C4\u5219
neutron-NatipNotAssociated=Nat IP/EIP <<< %(natip_id)s >>> \u4E0Enat\u6CA1\u6709\u5173\u8054
neutron-OnlyVMTypeVifSupportDnatrule=\u7F51\u5361ID <<< %(nova_vif_id)s >>>  \u7C7B\u578B <<< %(host_type)s >>> \u0020\u53EA\u6709\u4E91\u4E3B\u673A\u7C7B\u578B\u652F\u6301dnat
neutron-VifAlreadyBindNatpool=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u5DF2\u7ECF\u7ED1\u5B9ANat <<< %(natpool_id)s >>>
neutron-VifAlreadyHasDnatRule=\u7F51\u5361ID <<< %(nova_vif_id)s >>> \u5DF2\u7ECF\u6709Dnat
neutron-DisassociateCidrBlockError=\u5220\u9664\u9644\u52A0\u7F51\u6BB5\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A <<< %(reason)s >>>
neutron-InvalidCidrBlockInput=\u65E0\u6548\u7684cidrblock\u8F93\u5165\uFF0C\u539F\u56E0\uFF1A <<< %(reason)s >>>
neutron-FixIPFullQuotaError=NetworkInterface <<< %(vif_id)s >>> \u7684 PrivateIp \u6570\u91CF\u5DF2\u7ECF\u8D85\u8FC7\u914D\u989D
neutron-PrivateLinkExists=\u76D1\u542C\u5668 <<< %(vip_id)s >>> \u4E0B\u5DF2\u7ECF\u5B58\u5728 PrivateLink
neutron-PeerIPError=\u5BA2\u6237\u4FA7\u4E92\u8054ip\u7F51\u6BB5\u4E0E\u91D1\u5C71\u4FA7\u4E92\u8054ip\u7F51\u6BB5\u51B2\u7A81
neutron-RemoveNatipDnatAndSnatError=\u7981\u7528\u9700\u5148\u5220\u9664\u76F8\u5173\u7684\u2018\u5168\u7AEF\u53E3\u2019\u7C7B\u578B\u7684dnat\u89C4\u5219\u6216\u5220\u9664\u6240\u6709snat\u89C4\u5219
neutron-DisableNatipDnatAndSnatError=\u7981\u7528\u9700\u5148\u5220\u9664\u76F8\u5173\u7684\u2018\u5168\u7AEF\u53E3\u2019\u7C7B\u578B\u7684dnat\u89C4\u5219\u6216\u5220\u9664\u6240\u6709snat\u89C4\u5219
neutron-DisableNatipallocationsError=\u7981\u7528natip <<< %(id)s >>> \u5931\u8D25\u3002\u4E0D\u80FD\u7981\u7528\u6700\u540E\u4E00\u4E2Anatip
neutron-CreateFullPortMappingDnatRuleError=\u8BE5NAT IP/EIP\u4E0D\u80FD\u4F7F\u7528,\u56E0\u4E3A\u5DF2\u7528\u4E8ESNAT
neutron-CrtOrKeyNotCorrect=\u8BC1\u4E66\u516C\u94A5\u79C1\u94A5\u5BF9\u5E94\u9519\u8BEF
neutron-CrtAndKeyNotMatch=\u8BC1\u4E66\u548C\u79C1\u94A5\u4E0D\u5339\u914D
neutron-SnatAndDnatruleConflict=\u5F53natpool <<< %(id)s >>> \u53EA\u6709\u4E00\u4E2A\u5177\u6709dnat\u5B8C\u6574\u7AEF\u53E3\u6620\u5C04\u7684natip\u65F6\uFF0C\u4E0D\u5141\u8BB8\u5411natpool\u6DFB\u52A0\u8D44\u6E90\u3002
InnerDnsError.itemNotFound.InstanceNotFound=\u5185\u7F51DNS\u5B9E\u4F8B <<< %(instance)s >>> \u4E0D\u5B58\u5728
neutron-NotSupportCreateInSystemRouteTable=\u8BE5\u63A5\u53E3\u4E0D\u652F\u6301\u5728\u7CFB\u7EDF\u8DEF\u7531\u8868 <<< %(id)s >>> \u4E0B\u521B\u5EFA\u8DEF\u7531
neutron-SystemRouteTableCannotDelete=\u7CFB\u7EDF\u8DEF\u7531\u8868 <<< %(id)s >>> \u4E0D\u5141\u8BB8\u5220\u9664
neutron-RouteIpVersionDomainInvalid=\u8DEF\u7531\u975E\u6CD5\uFF0C\u8BE5VPC <<< %(id)s >>> \u4E0D\u652F\u6301IPV6
neutron-VpnTunnelNotFound=Vpn\u901A\u9053 <<< %(id)s >>> \u672A\u627E\u5230
neutron-VpnVpcGwNotFound=VPN\u7F51\u5173<<< %(id)s >>> \u672A\u627E\u5230
neutron-LimitExceeded=\u5168\u5C40\u5B89\u5168\u7EC4\u8D85\u51FA\u914D\u989D
neutron-InstanceNotMatchCen=VPC <<< %(id)s >>> \u672A\u7ED1\u5B9A\u6307\u5B9A\u7684\u4E91\u4F01\u4E1A\u7F51
neutron-MemberForbidden=\u6302\u8F7D\u7C7B\u578B\u4E3ABackendServerGroup\u76D1\u542C\u5668\u4E0D\u5141\u8BB8\u6DFB\u52A0\u771F\u5B9E\u670D\u52A1\u5668
neutron-VipAndMgroupNotMatch=\u76D1\u542C\u5668\u4E0E\u670D\u52A1\u5668\u7EC4\u4E0D\u5339\u914D\uFF0C\u539F\u56E0\uFF1A <<< %(reason)s >>>
neutron-ModifySwitchInterfaceRouteError=\u521B\u5EFA\u8FB9\u754C\u7F51\u5173\u8DEF\u7531\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A <<< %(reason)s >>>
neutron-TMSessionPriorityCannotBeDuplicated=\u955C\u50CF\u4F1A\u8BDD\u7684\u4F18\u5148\u7EA7<<< %(reason)s >>>\u4E0D\u5141\u8BB8\u91CD\u590D
neutron-TheTMSourceBoundSessionsNumExceedQuota=\u955C\u50CF\u6E90\u7F51\u5361ID<<< %(vifId)s >>>\u7ED1\u5B9A\u7684\u955C\u50CF\u4F1A\u8BDD\u6570\u91CF\u8D85\u8FC7\u914D\u989D;
neutron-TmSourceAlreadyInSession=\u8BE5\u5B9E\u4F8B <<< %(vifId)s >>> \u5DF2\u7ECF\u88AB\u6DFB\u52A0\u81F3\u955C\u50CF\u4F1A\u8BDD <<< %(sessions)s >>>
neutron-TmFilterRulePortRangeOverlaps=\u6D41\u91CF\u955C\u50CF\u7B5B\u9009\u5668\u89C4\u5219tcp/udp\u7AEF\u53E3\u8303\u56F4\u4E0E\u5176\u4ED6acl\u89C4\u5219\u76F8\u540C
neutron-CreateDcNatDcBindTypeError=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u8BE5\u4E13\u7EBF\u7F51\u5173\u672A\u7ED1\u5B9A\u5B9E\u4F8B
neutron-CreateDcNatDcStatusError=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u8BE5\u4E13\u7EBF\u672A\u7ED1\u5B9A\u94FE\u63A5\u901A\u9053\u6216\u94FE\u63A5\u901A\u9053\u72B6\u6001\u5F02\u5E38
neutron-CreateDcNatIpNatExist=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u6E90IP\u6216\u6620\u5C04IP\u5DF2\u5B58\u5728dcNatIp\u6620\u5C04
neutron-CreateDcNatIpNatConflictWithRemotePeerIp=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u6E90IP\u6216\u6620\u5C04IP\u5DF2\u7ED1\u5B9A\u7684\u8FDE\u63A5\u901A\u9053\u5BA2\u6237\u7AEFIP\u76F8\u540C
neutron-CreateDcNatIPNatSameIP=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u6E90IP\u4E0E\u6620\u5C04IP\u4E0D\u5F97\u76F8\u540C
neutron-CreateDcNatIpNatinBlackList=\u521B\u5EFA\u4E13\u7EBFNatIp\u6620\u5C04\u5931\u8D25\uFF0C\u6E90IP\u6216\u6620\u5C04IP\u5904\u4E8EIP\u9ED1\u540D\u5355\u4E2D
neutron-CanNotStateUpTmSessionWithoutTarget=\u955C\u50CF\u4F1A\u8BDD<<< %(id)s >>>\u6CA1\u6709\u7ED1\u5B9A\u955C\u50CF\u76EE\u7684\uFF0C\u65E0\u6CD5\u542F\u52A8,\u8BF7\u68C0\u67E5

neutron-NatipNotBelongCurrNatpool=NAT IP\u672A\u5728NAT\u5B9E\u4F8B\u4E0B
neutron-FullMapDnatMutualSnatipBinding=NatIp<<< %(reason)s >>> \u5DF2\u7ECF\u88AB\u7ED1\u5B9A\u4E0Esnat\u8D44\u6E90,\u4E0D\u53EF\u7528\u4E8E\u6784\u5EFA\u5168\u7AEF\u53E3dnat
neutron-AddNatpoolResourceError=snat\u8D44\u6E90\u6DFB\u52A0\u5931\u8D25\uFF0C\u8BF7\u68C0\u67E5NatIp\u662F\u5426\u88AB\u5168\u7AEF\u53E3dnat\u5360\u7528

neutron-VifFlowlogExists=\u6307\u5B9A\u7684\u8D44\u6E90<<< %(id)s >>>\u5DF2\u5B58\u5728flowlog\u5B9E\u4F8B

neutron-NatpoolV2EIPNetworkNotSame=Nat\u53EA\u80FD\u7ED1\u5B9A\u4E00\u79CD\u94FE\u8DEF\u7C7B\u578B\u7684EIP
neutron-FloatingIpStatusDown=EIP\u5B9E\u4F8B<<< %(id)s >>>\u5DF2\u8FC7\u671F,\u8BF7\u7EED\u8D39\u540E\u518D\u6267\u884C\u7ED1\u5B9A\u64CD\u4F5C