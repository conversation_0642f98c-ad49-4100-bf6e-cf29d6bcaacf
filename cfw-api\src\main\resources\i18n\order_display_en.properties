########################################## display\u56FD\u9645\u5316\u4E13\u5C5E\u533A\u57DF start #######################################
PackageCode=Package
LinkType=Link Type
DdosAbility=DDoS ability
BackSourceBandwidth=Bandwidth
HighestAblityBandwidth=Highest ability
CCAbility=CC ability
DomainNamePackage=Domain package
Details=Details
ServiceBandwidth=Bandwidth
DomainPackage=Domain name package
BindDomain=Primary Domain
CertificateName=Certificate Name
Year=years
Monthly=Monthly
Validity=Validity
DomainNum=Domain
WildcardNum=Wildcard
KeyType=Key type
CommonKey=Common
Configuration=Configuration
Dashboard=Dashboard
Open=Activate
Version=Version
ProfessionalEdition=Professional Edition
ThreatIntelligence=Threat intelligence
QueriesNum=Queries
BuyNum=Purchases
TenThousandTimes= Ten thousand times
HighestAblity=Highest ability
IpNum=IP
DeleteIp=Delete IP
ProductType=Product type
BandwidthShare=BWS
RegionCenter=Data center
ProtectionPackage=Package
ProtectionAbility=DDoS ability
Bandwidth=Bandwidth
Qty= Qty
Display=display_i18n
License=License
KhsVersion=Version
IpVersion=IP Version
KptType=type
LogStorageCapacity=Log Storage Capacity
LogStorageTime=Log Storage Time
Day=Day
ProtectiveBandwidth=Protective Bandwidth
ServiceSpecification=Service Specification
ServiceNum=Service Num
Tower=Tower
AssetNumber=Asset Number
Architecture=Architecture
StoreSize=Store Size
AlbNumber=Alb Number
QpsBag=Qps Bag
EIPCount = EIP Count
########################################## display\u56FD\u9645\u5316\u4E13\u5C5E\u533A\u57DF end #######################################

UserTokenEmpty = The user token cannot be empty.
UserTokenInvalid = AKSK user token error.
GetTempAKFailed = Failed to get the temporary AKSK key pair.
InvalidRegion = Invalid server room.
ProductUseRequired = Product Use cannot be empty.
ProductUseInvalid = Invalid value of Product Use.
ChargeTypeInvalid = Invalid billing mode.
ChargeTypeEmpty = The Billing mode cannot be empty.
PurchaseTimeInvalid = Duration must be between 1 and 36.
AllocationIdsRequired = Elastic IP cannot be empty.
PortfwdIdsRequired = Port mapping cannot be empty.
PublicIpRequired = Public IP cannot be empty.
RealServerTypeEmpty = Real server type cannot be empty.
RealServerTypeInvalid = Invalid real server type.
LoadBalancerIdsEmpty = SLB cannot be empty.
LoadBalancerIdEmpty = SLB cannot be empty.
LoadBalancerStateEmpty = The SLB state cannot be empty.
LoadBalancerStateInvalid = The SLB state must be either Enabled or Disabled.
Boss.GetPriceSystemFailed = Price system retrieving error. Please contact the customer service.
Boss.GetProductSimpleInfoFailed = Failed to get product brief information. Please contact the customer service.
Boss.GetRegionListFailed = Failed to get data center information. Please contact the customer service.
GetMonitorDateFalied = Failed to get monitoring data. Please contact the customer service.
Payment.CalculateProductFailed = Failed to calculate the price. Please contact the customer service.
Payment.CreateProductFailed = Failed to create the product. Please contact the customer service.
Payment.CreateOrderFailed = Failed to create the order. Please contact the customer service.
Payment.NotifySubOrderFailed = Failed to write back the order. Please contact the customer service.
Payment.ProductNotFound = No product information found. Please contact the customer service.
Payment.OrderNotFound = No order information found. Please contact the customer service.
LbMethodInvalid = The listener forwarding method must be polling or the minimum number of connections.
LoadBalancerTypeInvalid = The SLB type must be either Public or Private.
LoadBalancerNameInvalid = Invalid SLB name.
ListenerIdInvalid = Invalid listener.
ListenerIdEmpty = Listener cannot be empty.
ListenerIdsEmpty = Listener cannot be empty.
ListenerStateInvalid = The listener state must be either On or Off.
ListenerStateEmpty = The listener state cannot be empty.
HealthCheckIdEmpty = Health check cannot be empty.
HealthCheckIdInvalid = Invalid health check.
HealthyThresholdEmpty = The health check threshold cannot be empty.
HealthyThresholdInvalid = The health check threshold must be between 0 and 10.
IntervalEmpty = The health check interval cannot be empty.
IntervalInvalid = The health check interval must be between 0 and 3600.
TimeoutEmpty = The health check timeout cannot be empty.
TimeoutInvalid = The health check timeout must be between 0 and 3600.
UnhealthyThresholdEmpty = Unhealthy Threshold cannot be empty.
UnhealthyThresholdInvalid = Unhealthy Threshold must be between 0 and 10.
HealthCheckNotExistsUnderListener = This health check does not exist under the listener.
HealthCheckNotExists = Health check does not exist.
HealthCheckExists = Health check already exists.
HealthCheckHttpMethodInvalid = The HTTP request method must be either GET or HEAD.
HealthCheckStateEmpty = The health check state cannot be empty.
HealthCheckStateInvalid = The health check state must be either On or Off.
UrlPathOrHostNameEmpty = The HTTP request link and domain name cannot be empty simultaneously.
UrlPathInvalid =The HTTP request link must be an alphanumeric string of 1 to 250 characters.
HostNameInvalid = Invalid domain name.
SessionStateInvalid = The session persistence must be either turned on or off.
SessionStateEmpty = Session persistence cannot be empty.
CookieExpirationPeriodInvalid = The session persistence timeout must be between 0 and 86400.
CookieExpirationPeriodEmpty = The session persistence timeout cannot be empty.
CookieTypeInvalid = The session persistence type must be either Implant or Rewrite for cookies.
CookieTypeEmpty = The session persistence type cannot be empty.
CookieNameInvalid = Invalid session persistence name.
CookieNameEmpty = The session persistence name cannot be empty.
ListenerNameInvalid = Invalid listener name.
#internet error
InternalError = Internal access error. Please contact the customer service.
#vpc
NoTDeleteVpcsWIthHaveKECS = The VPC is already bound to a host. Unbind it before you can delete the VPC.
NoTDeleteVpcsWIthPublicNAT = The VPC has a public network type NAT. Delete the NAT before you can delete the VPC.
NoTDeleteVpcsWIthPeeringConnection = The VPC has a peering connection. Delete the peering connection before you can delete the VPC.
NoTDeleteVpcsWIthHaveSLB = This VPC has an SLB. Delete the SLB first.
HighIpExpire = KAD IP has expired.
ActionNotInWhiteList = This request is not on the whitelist and the super login access is denied.
KadNameInvalid = Invalid KAD name.
#kkms
KeyIdEmpty = The master key ID cannot be empty.
PlaintextEmpty = Data to be encrypted cannot be empty.
CiphertextBlobEmpty = Encrypted plaintext cannot be empty.
KeyStateInvalid = Invalid key state.
KeyUsageInvalid = Invalid master key purpose.
OriginInvalid = Invalid master key origin.
KeyNameInvalid = Invalid master key name.
KeyNameRepeat = The key name already exists. Use another one.

InvalidIpVersion = Invalid IP version.

ModifyDomainFail = Failed to modify the domain name.
ModifyDefenseGroupFail = Failed to modify the rule group.
ModifyDefenseRuleFail = Failed to modify the rule.
UnBindEipFail = Failed to unbind EIP.
DeleteDefenseRuleFail = Failed to delete the rule.
DeleteDefenseGroupFail = Failed to delete the rule group.

UnlimitPackageExisted = This KAD IP already has an unlimited protection package. Please change the package and try again.
LittleBandWidthPackage = This KAD IP already has a unlimited protection package. Please purchase a package with a larger bandwidth.
KadOnceInvalidCount = Failed to obtain the package number when creating the By Times product.
InvalidRenewalProPackage = Only unlimited protection packages are supported for renewal.
InvalidBuyProtectivePackageParam = Failed to get the IsBuyProtectivePackage parameter of one or more packages to be purchased.
InvalidBuyProtectivePackageProductType = Incorrect product type obtained during the purchase.
InvalidBuyProtectivePackageKadId = Failed to get the related By Times instance ID of one or more packages to be purchased.
InvalidWafVersion = Unsupported feature in this edition.

GetQuotainfoByQuotaNameFailed = Failed to query quota information by QuotaName.
IntelligenceInValid = Invalid value for intelligence querying.
OnlySupportRenewalOpt = The product only supports the renew operation.
KeadRenewalMonthlyMostBws = Renewal of the Monthly KEAD IP requires BWS to be renewed at the same time.
KeadRenewalMonthlyNumMostSameBws = Number of Monthly KEAD to be renewed must be consistent with that of BWS.
KeadRenewalDurationMustSameBws = Renewal time of Monthly KEAD must be consistent with that of BWS.
KeadUpdateOriginalKeadIpNumberNotEmpty = OriginalKeadIpNumber cannot be empty when KEAD update
KeadUpdateOriginalKeadBandWidthNotEmpty = OriginalKeadBandWidth cannot be empty when KEAD update
KeadUpdateBwsIdNotEmpty = BwsId cannot be empty when KEAD update
KeadUpdateIpLessStrNotEmpty = IP address must be specified when KEAD update
KeadUpdateIpLessNumMustSame = The number of reduced IP must be the same as the IP address specified for deletion when KEAD update
KeadUpdateIpLessError = Reduced IP does not belong to the same KEAD or no exist when KEAD update
KeadUpdateIpNumNotSupportKis = Not support kis operation when KEAD update ip number
CreateKeadIpNumLessOne = The number of IP is less than 1 when KEAD create
BuyKeadMonthlyMustDailyVolume = The monthly order must also have dailyVolume order when KEAD create or regular.
UserNotKeadLinkAuth = You do not have the link authority for KEAD IP, please contact the administrator to activate
QueryNetworkFail = Query KEAD ip link fail, reason:{0}
KeadTrialOnlySupportDay = KEAD only support daily trial order.
KeadTrialMustDayAndVolume = KEAD only support trial order(buy/renewal) based on daily and volume billing.
KeadTrialOrderErrorProductWhat = KEAD only support trial product what based on trial order.
KeadUpdateIpMoreEipProjectIdNotEmpty =The number of increase IP must establish project ID when KEAD update
GetLineFail = Get Line failed,reason:
KeadIpNotModifyProject = KEAD IP does not allow individual modify project.