########################################## display\u56FD\u9645\u5316\u4E13\u5C5E\u533A\u57DF start #######################################
PackageCode=\u5957\u9910\u540D\u79F0
LinkType=\u94FE\u8DEF\u7C7B\u578B
DdosAbility=\u9632\u62A4\u5E26\u5BBD
BackSourceBandwidth=\u56DE\u6E90\u5E26\u5BBD
HighestAblityBandwidth=\u5F39\u6027\u4E0A\u9650\u5E26\u5BBD
CCAbility=CC\u9632\u62A4
DomainNamePackage=\u7AEF\u53E3\u57DF\u540D\u5305
Details=\u8BE6\u60C5\u5C55\u793A
ServiceBandwidth=\u4E1A\u52A1\u5E26\u5BBD
DomainPackage=\u57DF\u540D\u5305
BindDomain=\u7ED1\u5B9A\u4E3B\u57DF\u540D
CertificateName=\u8BC1\u4E66\u540D\u79F0
Year=\u5E74
Monthly=\u6708
Validity=\u6709\u6548\u671F
DomainNum=\u57DF\u540D\u4E2A\u6570
WildcardNum=\u901A\u914D\u7B26\u4E2A\u6570
KeyType=\u5BC6\u94A5\u7C7B\u578B
CommonKey=\u666E\u901A\u5BC6\u94A5
Configuration=\u914D\u7F6E\u89C4\u683C
Dashboard=\u5B9E\u65F6\u5927\u5C4F
Open=\u5F00\u901A
Version=\u7248\u672C\u89C4\u683C
ProfessionalEdition=\u4E13\u4E1A\u7248
ThreatIntelligence=\u5A01\u80C1\u60C5\u62A5
QueriesNum=\u67E5\u8BE2\u6B21\u6570
BuyNum=\u8D2D\u4E70\u6B21\u6570
TenThousandTimes=\u4E07\u6B21
HighestAblity=\u5F39\u6027\u4E0A\u9650
IpNum=IP\u6570\u91CF
DeleteIp=\u5220\u9664IP
ProductType=\u4EA7\u54C1\u5C5E\u6027
BandwidthShare=\u5171\u4EAB\u5E26\u5BBD
RegionCenter=\u6570\u636E\u4E2D\u5FC3
ProtectionPackage=\u9632\u62A4\u5957\u9910
ProtectionAbility=\u9632\u62A4\u80FD\u529B
Bandwidth=\u5E26\u5BBD
Qty=\u4E2A
Display=display
License=\u6388\u6743\u6570\u91CF
KhsVersion=\u7248\u672C
IpVersion=IP\u7248\u672C
KptType=\u89C4\u6A21\u7C7B\u578B
LogStorageCapacity=\u65E5\u5FD7\u5B58\u50A8\u5BB9\u91CF
LogStorageTime=\u65E5\u5FD7\u5B58\u50A8\u65F6\u957F
Day=\u5929
ProtectiveBandwidth=\u9632\u62A4\u5E26\u5BBD
ServiceSpecification=\u670D\u52A1\u89C4\u683C
ServiceNum=\u6392\u67E5\u670D\u52A1\u5668\u53F0\u6570
Tower=\u53F0
AssetNumber=\u8D44\u4EA7\u6570
Architecture=\u5B9E\u4F8B\u7C7B\u578B
StoreSize=\u5B58\u50A8\u5927\u5C0F
AlbNumber=\u53EF\u7ED1ALB\u6570
QpsBag=QPS\u5305
EIPCount = EIP\u6570
########################################## display\u56FD\u9645\u5316\u4E13\u5C5E\u533A\u57DF end #######################################

UserTokenEmpty=\u7528\u6237token\u4E0D\u80FD\u4E3A\u7A7A
UserTokenInvalid=AKSK\u7528\u6237token\u9519\u8BEF
GetTempAKFailed=\u83B7\u53D6\u4E34\u65F6AKSK\u5BC6\u94A5\u5BF9\u5931\u8D25
InvalidRegion=\u673A\u623F\u4E0D\u5408\u6CD5
ProductUseRequired=\u5546\u54C1\u7528\u9014\u4E0D\u80FD\u4E3A\u7A7A
ProductUseInvalid=\u5546\u54C1\u7528\u9014\u4E0D\u7B26\u5408\u89C4\u8303
ChargeTypeInvalid=\u8BA1\u8D39\u7C7B\u578B\u4E0D\u7B26\u5408\u89C4\u8303
ChargeTypeEmpty=\u8BA1\u8D39\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
PurchaseTimeInvalid=\u65F6\u957F\u5FC5\u987B\u5728 1\u81F336\u4E4B\u95F4
AllocationIdsRequired=\u5F39\u6027IP\u4E0D\u80FD\u4E3A\u7A7A
PortfwdIdsRequired=\u7AEF\u53E3\u6620\u5C04\u4E0D\u80FD\u4E3A\u7A7A
PublicIpRequired=\u516C\u7F51IP\u4E0D\u80FD\u4E3A\u7A7A
RealServerTypeEmpty=\u771F\u5B9E\u670D\u52A1\u5668\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
RealServerTypeInvalid=\u771F\u5B9E\u670D\u52A1\u5668\u7C7B\u578B\u4E0D\u5408\u6CD5
LoadBalancerIdsEmpty=\u8D1F\u8F7D\u5747\u8861\u4E0D\u80FD\u4E3A\u7A7A
LoadBalancerIdEmpty=\u8D1F\u8F7D\u5747\u8861\u4E0D\u80FD\u4E3A\u7A7A
LoadBalancerStateEmpty=\u8D1F\u8F7D\u5747\u8861\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
LoadBalancerStateInvalid=\u8D1F\u8F7D\u5747\u8861\u72B6\u6001\u53EA\u80FD\u4E3A\u5F00\u542F\u6216\u5173\u95ED
Boss.GetPriceSystemFailed=\u83B7\u53D6\u4EF7\u683C\u4F53\u7CFB\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Boss.GetProductSimpleInfoFailed=\u67E5\u8BE2\u7528\u6237\u4EA7\u54C1\u7B80\u660E\u4FE1\u606F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Boss.GetRegionListFailed=\u83B7\u53D6\u673A\u623F\u4FE1\u606F\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
GetMonitorDateFalied=\u83B7\u53D6\u76D1\u63A7\u6570\u636E\u5F02\u5E38\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.CalculateProductFailed=\u5546\u54C1\u8BA1\u7B97\u4EF7\u683C\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.CreateProductFailed=\u521B\u5EFA\u5546\u54C1\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.CreateOrderFailed=\u521B\u5EFA\u8BA2\u5355\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.NotifySubOrderFailed=\u56DE\u5199\u8BA2\u5355\u5931\u8D25\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.ProductNotFound=\u6CA1\u6709\u627E\u5230\u5BF9\u5E94\u7684\u5546\u54C1\u4FE1\u606F\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
Payment.OrderNotFound=\u6CA1\u6709\u627E\u5230\u5BF9\u5E94\u7684\u8BA2\u5355\u4FE1\u606F\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
LbMethodInvalid=\u76D1\u542C\u5668\u8F6C\u53D1\u65B9\u5F0F\u5FC5\u987B\u4E3A\u8F6E\u8BE2\u6216\u6700\u5C0F\u8FDE\u63A5\u6570
LoadBalancerTypeInvalid=\u8D1F\u8F7D\u5747\u8861\u7C7B\u578B\u53EA\u80FD\u4E3A\u516C\u7F51\u6216\u79C1\u7F51
LoadBalancerNameInvalid=\u8D1F\u8F7D\u5747\u8861\u540D\u79F0\u4E0D\u5408\u6CD5
ListenerIdInvalid=\u76D1\u542C\u5668\u4E0D\u5408\u6CD5
ListenerIdEmpty=\u76D1\u542C\u5668\u4E0D\u80FD\u4E3A\u7A7A
ListenerIdsEmpty=\u76D1\u542C\u5668\u4E0D\u80FD\u4E3A\u7A7A
ListenerStateInvalid=\u76D1\u542C\u5668\u72B6\u6001\u53EA\u80FD\u4E3A\u5F00\u542F\u6216\u5173\u95ED
ListenerStateEmpty=\u76D1\u542C\u5668\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
HealthCheckIdEmpty=\u5065\u5EB7\u68C0\u67E5\u4E0D\u80FD\u4E3A\u7A7A
HealthCheckIdInvalid=\u5065\u5EB7\u68C0\u67E5\u4E0D\u5408\u6CD5
HealthyThresholdEmpty=\u5065\u5EB7\u68C0\u67E5\u9608\u503C\u4E0D\u80FD\u4E3A\u7A7A
HealthyThresholdInvalid=\u5065\u5EB7\u68C0\u67E5\u9608\u503C\u5FC5\u987B\u57280\u81F310\u4E4B\u95F4
IntervalEmpty=\u5065\u5EB7\u68C0\u67E5\u65F6\u95F4\u95F4\u9694\u4E0D\u80FD\u4E3A\u7A7A
IntervalInvalid=\u5065\u5EB7\u68C0\u67E5\u65F6\u95F4\u95F4\u9694\u5FC5\u987B\u57280\u81F33600\u4E4B\u95F4
TimeoutEmpty=\u5065\u5EB7\u68C0\u67E5\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
TimeoutInvalid=\u5065\u5EB7\u68C0\u67E5\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57280\u81F33600\u4E4B\u95F4
UnhealthyThresholdEmpty=\u4E0D\u5065\u5EB7\u68C0\u67E5\u9608\u503C\u4E0D\u80FD\u4E3A\u7A7A
UnhealthyThresholdInvalid=\u4E0D\u5065\u5EB7\u68C0\u67E5\u9608\u503C\u5FC5\u987B\u57280\u81F310\u4E4B\u95F4
HealthCheckNotExistsUnderListener=\u76D1\u542C\u5668\u4E0B\u4E0D\u5B58\u5728\u6B64\u5065\u5EB7\u68C0\u67E5
HealthCheckNotExists=\u5065\u5EB7\u68C0\u67E5\u4E0D\u5B58\u5728
HealthCheckExists=\u5065\u5EB7\u68C0\u67E5\u5DF2\u7ECF\u5B58\u5728
HealthCheckHttpMethodInvalid=HTTP\u8BF7\u6C42\u65B9\u6CD5\u53EA\u80FD\u4E3AGET\u6216HEAD
HealthCheckStateEmpty=\u5065\u5EB7\u68C0\u67E5\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A
HealthCheckStateInvalid=\u5065\u5EB7\u68C0\u67E5\u72B6\u6001\u5FC5\u987B\u4E3A\u5F00\u542F\u6216\u5173\u95ED
UrlPathOrHostNameEmpty=HTTP\u8BF7\u6C42\u94FE\u63A5\u548C\u57DF\u540D\u4E0D\u80FD\u540C\u65F6\u4E3A\u7A7A
UrlPathInvalid=HTTP\u8BF7\u6C42\u94FE\u63A5\u5FC5\u987B\u662F\u5B57\u6BCD\u3001\u6570\u5B57\u5E76\u4E14\u957F\u5EA6\u4E3A1\u81F3250
HostNameInvalid=\u57DF\u540D\u4E0D\u5408\u6CD5
SessionStateInvalid=\u4F1A\u8BDD\u4FDD\u6301\u53EA\u80FD\u5F00\u542F\u6216\u5173\u95ED
SessionStateEmpty=\u4F1A\u8BDD\u4FDD\u6301\u4E0D\u80FD\u4E3A\u7A7A
CookieExpirationPeriodInvalid=\u4F1A\u8BDD\u4FDD\u6301\u8D85\u65F6\u65F6\u95F4\u5FC5\u987B\u57280\u81F386400\u4E4B\u95F4
CookieExpirationPeriodEmpty=\u4F1A\u8BDD\u4FDD\u6301\u8D85\u65F6\u65F6\u95F4\u4E0D\u80FD\u4E3A\u7A7A
CookieTypeInvalid=\u4F1A\u8BDD\u4FDD\u6301\u7C7B\u578B\u53EA\u80FD\u4E3A\u690D\u5165cookie\u6216\u91CD\u5199cookie
CookieTypeEmpty=\u4F1A\u8BDD\u4FDD\u6301\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
CookieNameInvalid=\u4F1A\u8BDD\u4FDD\u6301\u540D\u79F0\u4E0D\u5408\u6CD5
CookieNameEmpty=\u4F1A\u8BDD\u4FDD\u6301\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
ListenerNameInvalid=\u76D1\u542C\u5668\u540D\u79F0\u4E0D\u5408\u6CD5
#internet error
InternalError=\u5185\u90E8\u8BBF\u95EE\u9519\u8BEF\uFF0C\u8BF7\u8054\u7CFB\u5BA2\u670D\u3002
#vpc
NoTDeleteVpcsWIthHaveKECS=\u8BE5vpc\u5DF2\u7ECF\u7ED1\u5B9A\u4E91\u4E3B\u673A\uFF0C\u8BF7\u89E3\u7ED1\u540E\u518D\u5220\u9664\u3002
NoTDeleteVpcsWIthPublicNAT=\u8BE5VPC\u4E0B\u62E5\u6709\u516C\u7F51\u7C7B\u578BNAT\uFF0C\u8BF7\u5220\u9664\u516C\u7F51\u7C7B\u578BNAT\u540E\u518D\u5220\u9664vpc\u3002
NoTDeleteVpcsWIthPeeringConnection=\u8BE5VPC\u4E0B\u62E5\u6709\u5BF9\u7B49\u8FDE\u63A5\uFF0C\u8BF7\u5BF9\u7B49\u8FDE\u63A5\u540E\u518D\u5220\u9664vpc\u3002
NoTDeleteVpcsWIthHaveSLB=\u8BE5VPC\u548C\u8D1F\u8F7D\u5747\u8861\u6709\u914D\u7F6E\u5173\u7CFB\uFF0C\u8BF7\u5148\u5220\u9664\u8D1F\u8F7D\u5747\u8861\u3002
HighIpExpire= \u9AD8\u9632IP\u5DF2\u5230\u671F
ActionNotInWhiteList=\u8FD9\u4E2A\u8BF7\u6C42\u4E0D\u5728\u767D\u540D\u5355\u4E2D\uFF0C\u8D85\u7BA1\u767B\u5F55\u65E0\u6CD5\u8BBF\u95EE
KadNameInvalid=\u9AD8\u9632\u540D\u79F0\u4E0D\u5408\u6CD5
#kkms
KeyIdEmpty=\u5BA2\u6237\u4E3BKEY\u7684ID\u4E0D\u80FD\u4E3A\u7A7A
PlaintextEmpty=\u5F85\u52A0\u5BC6\u6570\u636E\u4E0D\u80FD\u4E3A\u7A7A
CiphertextBlobEmpty=\u52A0\u5BC6\u660E\u6587\u4E0D\u80FD\u4E3A\u7A7A
KeyStateInvalid=KEY\u7684\u72B6\u6001\u4E0D\u7B26\u5408\u89C4\u8303
KeyUsageInvalid=\u5BC6\u94A5\u540D\u79F0\u7684\u7528\u9014\u4E0D\u7B26\u5408\u89C4\u8303
OriginInvalid=\u5BC6\u94A5\u540D\u79F0\u7684\u6765\u6E90\u4E0D\u7B26\u5408\u89C4\u8303
KeyNameInvalid=\u5BC6\u94A5\u540D\u79F0\u4E0D\u5408\u6CD5
KeyNameRepeat=\u8BE5\u5BC6\u94A5\u540D\u79F0\u5DF2\u5B58\u5728

InvalidIpVersion=IpVersion\u53C2\u6570\u503C\u975E\u6CD5

ModifyDomainFail = \u4FEE\u6539\u57DF\u540D\u5931\u8D25
ModifyDefenseGroupFail = \u4FEE\u6539\u9632\u62A4\u89C4\u5219\u7EC4\u5931\u8D25
ModifyDefenseRuleFail = \u4FEE\u6539\u9632\u62A4\u89C4\u5219\u5931\u8D25
UnBindEipFail = \u89E3\u7ED1EIP\u5931\u8D25
DeleteDefenseRuleFail = \u5220\u9664\u9632\u62A4\u89C4\u5219\u5931\u8D25
DeleteDefenseGroupFail = \u5220\u9664\u9632\u62A4\u89C4\u5219\u7EC4\u5931\u8D25

UnlimitPackageExisted = \u8BE5\u9AD8\u9632IP\u5DF2\u5B58\u5728\u65E0\u9650\u9632\u62A4\u5305\uFF0C\u8BF7\u66F4\u6362\u540E\u518D\u8BD5
LittleBandWidthPackage = \u8BE5\u9AD8\u9632IP\u5DF2\u5B58\u5728\u65E0\u9650\u9632\u62A4\u5305\uFF0C\u8BF7\u8D2D\u4E70\u66F4\u5927\u5E26\u5BBD\u503C\u7684\u6709\u9650\u9632\u62A4\u5305
KadOnceInvalidCount = \u521B\u5EFA\u5305\u5E74\u6309\u6B21\u5546\u54C1\u672A\u4F20\u5165\u9632\u62A4\u5305\u6B21\u6570
InvalidRenewalProPackage = \u7EED\u8D39\u4EC5\u652F\u6301\u65E0\u9650\u6B21\u9632\u62A4\u5305
InvalidBuyProtectivePackageParam = \u65B0\u8D2D\u9632\u62A4\u5305\u64CD\u4F5C\u6709\u9632\u62A4\u5305\u672A\u4F20\u5165IsBuyProtectivePackage\u53C2\u6570
InvalidBuyProtectivePackageProductType = \u65B0\u8D2D\u9632\u62A4\u5305\u64CD\u4F5C\u4F20\u5165\u9519\u8BEF\u7684\u5546\u54C1\u7C7B\u578B
InvalidBuyProtectivePackageKadId = \u65B0\u8D2D\u9632\u62A4\u5305\u64CD\u4F5C\u6709\u9632\u62A4\u5305\u672A\u4F20\u5165\u6240\u5C5E\u5305\u5E74\u6309\u6B21\u5B9E\u4F8B\u7684ID
InvalidWafVersion = \u5F53\u524D\u7248\u672C\u4E0D\u652F\u6301\u8BE5\u529F\u80FD

GetQuotainfoByQuotaNameFailed = \u57FA\u4E8EQuotaName\u67E5\u8BE2\u914D\u989D\u4FE1\u606F\u5931\u8D25
IntelligenceInValid = \u60C5\u62A5\u67E5\u8BE2\u503C\u4E0D\u5408\u6CD5
OnlySupportRenewalOpt=\u8BE5\u4EA7\u54C1\u4EC5\u652F\u6301\u7EED\u8D39\u64CD\u4F5C
KeadRenewalMonthlyMostBws = \u5305\u5E74\u5305\u6708\u7684\u9AD8\u9632\u5F39\u6027IP\u7EED\u8D39\u65F6\u5FC5\u987B\u540C\u65F6\u7EED\u8D39\u5171\u4EAB\u5E26\u5BBD
KeadRenewalMonthlyNumMostSameBws=\u5305\u5E74\u5305\u6708\u7684\u9AD8\u9632\u5F39\u6027IP\u7EED\u8D39\u7684\u6570\u91CF\u5E94\u4E0E\u5171\u4EAB\u5E26\u5BBD\u4FDD\u6301\u4E00\u81F4
KeadRenewalDurationMustSameBws = \u9AD8\u9632\u5F39\u6027IP\u7EED\u8D39\u65F6\u95F4\u5FC5\u987B\u4E0E\u5171\u4EAB\u5E26\u5BBD\u4FDD\u6301\u4E00\u81F4
KeadUpdateOriginalKeadIpNumberNotEmpty = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0COriginalKeadIpNumber\u4E0D\u80FD\u4E3A\u7A7A
KeadUpdateOriginalKeadBandWidthNotEmpty = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0COriginalKeadBandWidth\u4E0D\u80FD\u4E3A\u7A7A
KeadUpdateBwsIdNotEmpty = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0CBwsId\u4E0D\u80FD\u4E3A\u7A7A
KeadUpdateIpLessStrNotEmpty = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0CIP\u6570\u91CF\u51CF\u5C11\uFF0C\u5FC5\u987B\u6307\u5B9AIP\u5730\u5740
KeadUpdateIpLessNumMustSame = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0C\u51CF\u5C11\u7684IP\u6570\u91CF\u5FC5\u987B\u4E0E\u6307\u5B9A\u5220\u9664\u7684IP\u5730\u5740\u6570\u91CF\u76F8\u540C
KeadUpdateIpLessError = \u9AD8\u9632\u5F39\u6027IP\u964D\u914D\u65F6\uFF0C\u51CF\u5C11\u7684IP\u4E0D\u5C5E\u4E8E\u540C\u4E00\u4E2AKEAD\u6216IP\u4E0D\u5B58\u5728
KeadUpdateIpNumNotSupportKis = \u9AD8\u9632\u5F39\u6027IP\u4E0D\u652F\u6301KIS\u94FE\u8DEF\u66F4\u914DIP\u6570\u91CF\u64CD\u4F5C
CreateKeadIpNumLessOne = \u8D2D\u4E70\u9AD8\u9632\u5F39\u6027IP\uFF0CIP\u6570\u91CF\u4E0D\u80FD\u5C0F\u4E8E1
BuyKeadMonthlyMustDailyVolume = \u8D2D\u4E70\u3001\u8F6C\u6B63\u9AD8\u9632\u5F39\u6027IP\uFF0C\u5305\u5E74\u5305\u6708\u8BA2\u5355\u5FC5\u987B\u540C\u65F6\u5B58\u5728\u6309\u65E5\u7528\u91CF\u5B9E\u65F6\u4ED8\u8D39\u8BA2\u5355
UserNotKeadLinkAuth = \u60A8\u6CA1\u6709\u9AD8\u9632\u5F39\u6027IP\u7684\u94FE\u8DEF\u6743\u9650\uFF0C\u8BF7\u8054\u7CFB\u7BA1\u7406\u5458\u5F00\u901A
QueryNetworkFail = \u67E5\u8BE2\u9AD8\u9632\u5F39\u6027IP\u94FE\u8DEF\u5931\u8D25\uFF0C\u539F\u56E0\uFF1A{0}
KeadTrialOnlySupportDay = \u9AD8\u9632\u5F39\u6027IP\u4EC5\u652F\u6301\u6309\u65E5\u6708\u7ED3\u8BA1\u8D39\u65B9\u5F0F\u8BD5\u7528\u8BA2\u5355
KeadTrialMustDayAndVolume = \u9AD8\u9632\u5F39\u6027IP\u8BD5\u7528\u8BA2\u5355(\u8D2D\u4E70\u3001\u7EED\u8D39)\u5FC5\u987B\u540C\u65F6\u5305\u542B\u6309\u65E5\u6708\u7ED3\u53CA\u6309\u65E5\u7528\u91CF\u5B9E\u65F6\u4ED8\u8D39\u5546\u54C1
KeadTrialOrderErrorProductWhat = \u9AD8\u9632\u5F39\u6027IP\u8BD5\u7528\u8BA2\u5355\u7528\u9014\u5FC5\u987B\u4E3A\u8BD5\u7528

ModifyKeadRuleFail = \u66F4\u65B0\u9632\u62A4\u89C4\u5219\u5931\u8D25
KeadUpdateIpMoreEipProjectIdNotEmpty = \u9AD8\u9632\u5F39\u6027IP\u66F4\u914D\u65F6\uFF0CIP\u6570\u91CF\u589E\u52A0\uFF0C\u5FC5\u987B\u6307\u5B9AIP\u9879\u76EE\u5236
GetLineFail=\u83B7\u53D6\u94FE\u8DEF\u5931\u8D25,\u539F\u56E0\uFF1A
KeadIpNotModifyProject=\u9AD8\u9632\u5F39\u6027IP\u4E0D\u5141\u8BB8\u5355\u72EC\u4FEE\u6539\u9879\u76EE\u5236
InstanceOperateNotSupport = \u8BE5\u5546\u54C1\u7C7B\u578B\u4E0D\u652F\u6301{0}\u64CD\u4F5C