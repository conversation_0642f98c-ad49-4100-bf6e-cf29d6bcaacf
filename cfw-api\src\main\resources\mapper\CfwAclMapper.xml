<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.cfwapi.dao.mapper.CfwAclMapper">
    <select id="getAclCountByFwIds" resultType="com.ksyun.cfwapi.dao.entity.StatisticsDO">
		SELECT fw_id as id,count(fw_id) AS num
		FROM cfw_acl
		where fw_id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
		and delete_status = 0
		GROUP BY fw_id
	</select>
    <select id="countAclDeny" resultType="java.lang.Long">
		SELECT COALESCE(SUM(hit_count), 0)  AS num
		FROM cfw_acl
		where fw_id = #{fwId,jdbcType=VARCHAR} and policy = 'deny'
		and delete_status = 0
	</select>

    <update id="batchUpdateHitCount" parameterType="list">
		update cfw_acl
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="hit_count =case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when id=#{item.id,jdbcType=BIGINT} then #{item.hitCount,jdbcType=BIGINT}
				</foreach>
			</trim>
		</trim>
		, update_time = NOW()
		where id in
		<foreach collection="list" item="item" index="index" separator="," open="(" close=")">
			#{item.id,jdbcType=BIGINT}
		</foreach>
	</update>
</mapper>