<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.cfwapi.dao.mapper.CfwAclRelateMapper">

	<select id="getCitationCountByRelateIds" resultType="com.ksyun.cfwapi.dao.entity.CitationCountDO">
		SELECT relate_id as relateId,count(relate_id) AS citationCount
		FROM cfw_acl_relate
		where relate_id in
        <foreach collection="relateIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
		GROUP BY relate_id
	</select>
</mapper>