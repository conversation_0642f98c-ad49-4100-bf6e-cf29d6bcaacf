<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.cfwapi.dao.mapper.SubnetMapper">

    <select id="getAvailableSubNet" resultType="com.ksyun.cfwapi.dao.entity.SubnetDO">
        SELECT *
        FROM cfw_subnet
        WHERE vpc_id = (
            SELECT vpc_id
            FROM cfw_subnet_1
            WHERE status = 1
              AND region =  #{region,jdbcType=VARCHAR}
            GROUP BY vpc_id
            HAVING COUNT(*) >= #{count,jdbcType=INTEGER}
            ORDER BY vpc_id
            LIMIT 1
            )
          AND status = 1
          AND region = #{region,jdbcType=VARCHAR}
        ORDER BY create_time
            LIMIT #{count,jdbcType=INTEGER};
    </select>
</mapper>