package com.ksyun.cfwapi;


import cn.hutool.json.JSONUtil;
import com.ksyun.cfwapi.exception.CfwException;
import com.ksyun.ks3.dto.*;
import com.ksyun.ks3.service.Ks3;
import com.ksyun.ks3.service.Ks3Client;
import com.ksyun.ks3.service.Ks3ClientConfig;
import com.ksyun.ks3.service.request.GetObjectRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Base64;
import java.util.Objects;

@SpringBootTest
public class Ks3Test {
    public static void main(String[] args) throws IOException {
        // uploadFile();
        //  getDownUrl();
        getMD5();
        // yourEndpoint填写Bucket所在地域对应的Endpoint。以中国（北京）为例，Endpoint填写为ks3-cn-beijing.ksyuncs.com。如果使用自定义域名，设置endpoint为自定义域名，同时设置domainMode为true
        // extracted();
    }

    private static void getMD5() {
        // yourEndpoint填写Bucket所在地域对应的Endpoint。以中国（北京）为例，Endpoint填写为ks3-cn-beijing.ksyuncs.com。如果使用自定义域名，设置endpoint为自定义域名，同时设置domainMode为true
        String endpoint = "ks3-cn-beijing-internal.ksyuncs.com";
        // 金山云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用子账号账号进行 API 访问或日常运维，请登录 https://uc.console.ksyun.com/pro/iam/#/user/list 创建子账号。
        String accessKeyId = "AKLTx4BtKSZ3Q2LcM7LOyLMg";
        String accessKeySecret = "OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8";
        // 创建Ks3ClientConfig 实例。
        Ks3ClientConfig config = new Ks3ClientConfig();
        // 设置域名
        config.setEndpoint(endpoint);
        /**
         * false: （推荐）使用三级域名：{bucketName}.{endpoint}/{objectKey}的形式访问
         * true:  使用二级域名：{endpoint}/{bucketName}/{objectKey}的形式访问
         * 如果domainMode设置为true，则pathStyleAccess可忽略设置
         * */

        config.setPathStyleAccess(false);

        // 创建Ks3Client实例
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        HeadObjectResult headObject = client.headObject("cfw-test", "cn-qingyangtest-1_ca.pem");
        String md5 = null;
        if (Objects.nonNull(headObject.getObjectMetadata())) {
            String md5Base64 = headObject.getObjectMetadata().getContentMD5();
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] md5Bytes = decoder.decode(md5Base64);
            StringBuilder hexString = new StringBuilder();
            for (byte b : md5Bytes) {
                hexString.append(String.format("%02x", b));
            }
            md5 = hexString.toString().toUpperCase();
        }
        System.out.println(md5);
    }

    private static void extracted() {
        String endpoint = "ks3-cn-beijing.ksyuncs.com";
        // 金山云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用子账号账号进行 API 访问或日常运维，请登录 https://uc.console.ksyun.com/pro/iam/#/user/list 创建子账号。
        String accessKeyId = "AKLTx4BtKSZ3Q2LcM7LOyLMg";
        String accessKeySecret = "OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8";
        // 创建Ks3ClientConfig 实例。
        Ks3ClientConfig config = new Ks3ClientConfig();
        // 设置域名
        config.setEndpoint(endpoint);
        /**
         * false: （推荐）使用三级域名：{bucketName}.{endpoint}/{objectKey}的形式访问
         * true:  使用二级域名：{endpoint}/{bucketName}/{objectKey}的形式访问
         * 如果domainMode设置为true，则pathStyleAccess可忽略设置
         * */

        config.setPathStyleAccess(false);

        // 创建Ks3Client实例
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        HeadObjectResult headObjectResult = client.headObject("upload", "ESFile/deploy.sh");
        System.out.println("**tag**"+JSONUtil.parse(headObjectResult));
        GetObjectResult result = client.getObject("upload", "ESFile/deploy.sh");
        System.out.println("**result**"+JSONUtil.parse(result));
    }

    private static void getDownUrl() {
        // yourEndpoint填写Bucket所在地域对应的Endpoint。以中国（北京）为例，Endpoint填写为ks3-cn-beijing.ksyuncs.com。如果使用自定义域名，设置endpoint为自定义域名，同时设置domainMode为true
        String endpoint = "ks3-cn-beijing-internal.ksyuncs.com";
        // 金山云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用子账号账号进行 API 访问或日常运维，请登录 https://uc.console.ksyun.com/pro/iam/#/user/list 创建子账号。
        String accessKeyId = "AKLTx4BtKSZ3Q2LcM7LOyLMg";
        String accessKeySecret = "OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8";
        // 创建Ks3ClientConfig 实例。
        Ks3ClientConfig config = new Ks3ClientConfig();
        // 设置域名
        config.setEndpoint(endpoint);
        /**
         * false: （推荐）使用三级域名：{bucketName}.{endpoint}/{objectKey}的形式访问
         * true:  使用二级域名：{endpoint}/{bucketName}/{objectKey}的形式访问
         * 如果domainMode设置为true，则pathStyleAccess可忽略设置
         * */

        config.setPathStyleAccess(false);

        // 创建Ks3Client实例
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        String url = client.generatePresignedUrl("cfw-test", "SG6000-KCNFW-kafka-5.5R10Tx-V6-Full.qcow2", 30);
        System.out.println(url);
    }

    private static void uploadFile() {
        // yourEndpoint填写Bucket所在地域对应的Endpoint。以中国（北京）为例，Endpoint填写为ks3-cn-beijing.ksyuncs.com。如果使用自定义域名，设置endpoint为自定义域名，同时设置domainMode为true
        String endpoint = "ks3-cn-beijing.ksyuncs.com";
        String accessKeyId = "AKLTx4BtKSZ3Q2LcM7LOyLMg";
        String accessKeySecret = "OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8";
        Ks3ClientConfig config = new Ks3ClientConfig();
        config.setEndpoint(endpoint);
        /**
         * false: （推荐）使用三级域名：{bucketName}.{endpoint}/{objectKey}的形式访问
         * true:  使用二级域名：{endpoint}/{bucketName}/{objectKey}的形式访问
         * 如果domainMode设置为true，则pathStyleAccess可忽略设置
         * */

        config.setPathStyleAccess(false);
        // 创建Ks3Client实例
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        // 填写本地文件的完整路径。如果未指定本地路径，则默认从示例程序所属项目对应本地路径中上传文件流。
        File file = new File("C:\\Users\\<USER>\\Desktop\\ES (2).txt");
        // 依次填写Bucket名称（例如examplebucket）和Object完整路径（例如exampledir/exampleobject.txt）。Object完整路径中不能包含Bucket名称。
        PutObjectResult result = client.putObject("upload", "ESFile/ES.txt", file);
    }

    @Test
    public void UploadFile() throws IOException {
// yourEndpoint填写Bucket所在地域对应的Endpoint。以中国（北京）为例，Endpoint填写为ks3-cn-beijing.ksyuncs.com。如果使用自定义域名，设置endpoint为自定义域名，同时设置domainMode为true
        String endpoint = "ks3-cn-beijing.ksyuncs.com";
        // 金山云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用子账号账号进行 API 访问或日常运维，请登录 https://uc.console.ksyun.com/pro/iam/#/user/list 创建子账号。
        String accessKeyId = "AKLTx4BtKSZ3Q2LcM7LOyLMg";
        String accessKeySecret = "OGi4eYN2N3zOFP1PcjzayQgWtXDEqLNGurxZslb8";
        // 创建Ks3ClientConfig 实例。
        Ks3ClientConfig config = new Ks3ClientConfig();
        // 设置域名
        config.setEndpoint(endpoint);
        /**
         * false: （推荐）使用三级域名：{bucketName}.{endpoint}/{objectKey}的形式访问
         * true:  使用二级域名：{endpoint}/{bucketName}/{objectKey}的形式访问
         * 如果domainMode设置为true，则pathStyleAccess可忽略设置
         */
        config.setPathStyleAccess(false);

        // 创建Ks3Client实例
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        // 填写Bucket名称。
        String bucketName = "upload";
        // 填写Object的完整路径。Object完整路径中不能包含Bucket名称。
        String objectName = "ES.txt";
        // 创建getObject请求，指定
        GetObjectRequest request = new GetObjectRequest(bucketName, objectName);
        Ks3Object ks3Object = client.getObject(request).getObject();
        ObjectMetadata objectMetadata = ks3Object.getObjectMetadata();
        // 读取文件内容。
        System.out.println("Object content:");
        BufferedReader reader = new BufferedReader(new InputStreamReader(ks3Object.getObjectContent()));
        while (true) {
            String line = reader.readLine();
            if (line == null) break;
            System.out.println("\n" + line);
        }
        // 数据读取完成后，获取的流必须关闭，否则会造成连接泄漏，导致请求无连接可用，程序无法正常工作。
        reader.close();
    }
}
