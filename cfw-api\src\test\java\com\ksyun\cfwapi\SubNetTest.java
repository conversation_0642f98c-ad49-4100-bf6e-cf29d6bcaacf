package com.ksyun.cfwapi;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.ksyun.cfwcore.constants.HeaderConstant;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class SubNetTest {
    @Test
    public void testDescribeVpc() throws Exception {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("Action", Arrays.asList("DescribeVpcs"));
        parameters.put("Service", Arrays.asList("vpc"));
        parameters.put("VpcId.1", Arrays.asList("951e3604-72f7-4169-a010-f31c7332ee97"));
        Map<String, String> headers = new HashMap<>();
        headers.put("X-KSC-REQUEST-ID","a02ee874-7aed-42c3-aad4-2d0436e712d3");
        headers.put("X-KSC-ACCOUNT-ID","********");
        headers.put("X-KSC-REGION","cn-qingyangtest-1");

        //发送get请求并接收响应数据
        HttpResponse response = HttpUtil.createGet("http://*************:80").addHeaders(headers).form(parameters).execute();
        String result = response.body();
        System.out.println(result);
    }

    @Test
    public void testCreateSubNet() throws Exception {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(HeaderConstant.ACTION, Collections.singletonList("CreateSubnet"));
        parameters.put(HeaderConstant.SERVICE, Collections.singletonList("vpc"));
        parameters.put("VpcId", "0b7eed24-c2dc-4117-80fc-69616688760e");
        parameters.put("CidrBlock", "10.0.0.0/24");
        parameters.put("SubnetType", "Normal");
        parameters.put("GatewayIp", "********");
        parameters.put("ProvidedIpv6CidrBlock", "false");

        Map<String, String> headers = new HashMap<>();
        headers.put("X-KSC-REQUEST-ID", "a02ee874-7aed-42c3-aad4-2d0436e712d4");
        headers.put("X-KSC-ACCOUNT-ID", "********");
        headers.put("X-KSC-REGION", "cn-qingyangtest-1");
        //发送get请求并接收响应数据
        HttpResponse response = HttpUtil.createGet("http://*************:80").addHeaders(headers).form(parameters).timeout(6000000).execute();
        if (response.getStatus() == 200) {
            String result = response.body();
            System.out.println(result);
        }
    }
}
