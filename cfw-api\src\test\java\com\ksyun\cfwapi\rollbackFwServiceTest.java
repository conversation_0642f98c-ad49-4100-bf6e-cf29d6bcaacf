package com.ksyun.cfwapi;

import com.ksyun.cfwapi.deadletter.domain.ProcessingOrderParam;
import com.ksyun.cfwapi.service.cfwService.RollbackFwService;
import com.ksyun.cfwcore.enums.OperateTypeEnum;
import com.ksyun.cfwcore.fw.domain.RollbackFwParam;
import com.ksyun.common.proxy.ProxyAuth;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class rollbackFwServiceTest {
    @Autowired
    private RollbackFwService rollbackFwService;

    @Test
    public void rollbackTest() {
        ProxyAuth auth = new ProxyAuth();
        auth.setAccount_id("********");
        auth.setRegion("cn-qingyangtest-1");
        auth.setRequest_id("aea85bd5-c3c7-49cc-b7ca-4e04117b3b99");
        RollbackFwParam rollbackFwParam = new RollbackFwParam();
        rollbackFwParam.setFwId("ff07f667-b4ce-43d1-bbce-8874ef19008b");
        rollbackFwParam.setSubOrderId("KFW2S25041015304777570W0KJ");
        rollbackFwParam.setStatus(3);
        rollbackFwService.rollbackFwService(auth,rollbackFwParam);
    }

    @Test
    public void cancelProcessingTest() {
        ProxyAuth auth = new ProxyAuth();
        auth.setAccount_id("********");
        auth.setRegion("cn-qingyangtest-1");
        auth.setRequest_id("aea85bd5-c3c7-49cc-b7ca-4e04117b3b99");
        ProcessingOrderParam param =new ProcessingOrderParam();
        param.setFwId("ff07f667-b4ce-43d1-bbce-8874ef19008b");
        param.setSubOrderId("KFW2S250410191041834716FPA");
        param.setOperationType(OperateTypeEnum.CREATE.getType());
        rollbackFwService.cancelProcessingOrder(auth,param);
    }


}
