package com.ksyun.cfwapi;

import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


public class test {

    public static void main(String[] arg) {
        List<String> sList =new ArrayList<>();
        sList.add("【源类型：地址】");
        sList.add("【源类型：地址簿】");
        sList.add("【源类型：any】");
        List<String> dList =new ArrayList<>();
        dList.add("【目的类型：地址】");
        dList.add("【目的类型：地址簿】");
        sList.add("【目的类型：地域】");
        dList.add("【目的类型：any】");
        List<String> ser =new ArrayList<>();
        ser.add("【服务：服务】");
        ser.add("【服务：服务组】");
        ser.add("【服务：any】");
        List<String> yList =new ArrayList<>();
        yList.add("【应用：应用】");
        yList.add("【应用：any】");
        for(String s:sList){
            for(String d:dList){
                for (String se:ser){
                    for(String y:yList){
                        System.out.println(s+"+"+d+"+"+se+"+"+y);
                    }
                }
            }
        }
    }
}
