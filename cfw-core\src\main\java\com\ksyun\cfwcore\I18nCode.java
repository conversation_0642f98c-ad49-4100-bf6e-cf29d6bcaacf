package com.ksyun.cfwcore;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/10 11:28
 */
public interface I18nCode {

    String Display = "Display";                                             // display
    String Details = "Details";                                             // 详情展示
    String RegionGroup = "RegionGroup";                                     // 地域
    String BandwidthPackage = "BandwidthPackage";                           // 带宽包带宽
    String Flavor = "Flavor";// 实例规格

    String PackageCode = "PackageCode";                                     // 套餐名称
    String LinkType = "LinkType";                                           // 链路类型
    String DdosAbility = "DdosAbility";                                     // 防护带宽
    String BackSourceBandwidth = "BackSourceBandwidth";                     // 回源带宽
    String HighestAblityBandwidth = "HighestAblityBandwidth";               // 弹性上限带宽
    String CCAbility = "CCAbility";                                         // CC防护
    String DomainNamePackage = "DomainNamePackage";                         // 端口域名包
    String ServiceBandwidth = "ServiceBandwidth";                           // 业务带宽
    String DomainPackage = "DomainPackage";                                 // 域名拓展包
    String BindDomain = "BindDomain";                                       // 绑定主域名
    String CertificateName = "CertificateName";                             // 证书名称
    String Year = "Year";                                                   // 年
    String Monthly = "Monthly";                                             // 月
    String Validity = "Validity";                                           // 证书年限
    String DomainNum = "DomainNum";                                         // 域名个数
    String WildcardNum = "WildcardNum";                                     // 通配符个数
    String KeyType = "KeyType";                                             // 密钥类型
    String CommonKey = "CommonKey";                                         // 普通密钥
    String Configuration = "Configuration";                                 // 配置规格
    String Dashboard = "Dashboard";                                         // 实时大屏
    String Open = "Open";                                                   // 开通
    String Version = "Version";                                             // 版本规格
    String ProfessionalEdition = "ProfessionalEdition";                     // 专业版
    String ThreatIntelligence = "ThreatIntelligence";                       // 威胁情报
    String QueriesNum = "QueriesNum";                                       // 查询次数
    String BuyNum = "BuyNum";                                               // 购买次数
    String TenThousandTimes = "TenThousandTimes";                           // 万次
    String HighestAblity = "HighestAblity";                                 // 弹性上限
    String IpNum = "IpNum";                                                 // IP数量
    String DeleteIp = "DeleteIp";                                           // 删除IP
    String ProductType = "ProductType";                                     // 产品属性
    String BandwidthShare = "BandwidthShare";                               // 共享带宽
    String RegionCenter = "RegionCenter";                                   // 数据中心
    String ProtectionPackage = "ProtectionPackage";                         // 防护套餐
    String ProtectionAbility = "ProtectionAbility";                         // 防护能力
    String Bandwidth = "Bandwidth";                                         // 带宽
    String Qty = "Qty";                                                     // 个
    String License = "License";                                             // 授权数量
    String KhsVersion = "KhsVersion";                                       // 版本
    String LogStorageCapacity = "LogStorageCapacity";                       // 日志存储容量
    String LogStorageTime = "LogStorageTime";                               // 日志存储时长
    String Day = "Day";                                                     // 天
    String ProtectiveBandwidth = "ProtectiveBandwidth";                     // 防护带宽
    String IpVersion = "IpVersion";                                         // IP版本
    String KptType = "KptType";                                             // 规模类型
    String ServiceSpecification = "ServiceSpecification";                   // 服务规格
    String ServiceNum = "ServiceNum";                                       // 排查服务器台数
    String Tower = "Tower";                                                 // 台
    String AssetNumber = "AssetNumber";                                     // 资产数
    String Architecture = "Architecture";                                   // 实例类型
    String StoreSize = "StoreSize";                                         // 存储大小
    String AlbNumber = "AlbNumber";                                         // 可绑ALB数
    String QpsBag = "QpsBag";                                               // QPS包
    String AlbDomainBag = "AlbDomainBag";                                   // 域名包
    String EIPCount = "EIPCount";
}