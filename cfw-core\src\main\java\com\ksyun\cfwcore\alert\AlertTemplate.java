package com.ksyun.cfwcore.alert;

import com.google.gson.Gson;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.config.CommonConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * @author: hueason
 * @date: 2023/8/14 16:16
 * @description:
 */
@Slf4j
@Component
public class AlertTemplate {
    @Autowired
    private CommonConfig config;
    @Autowired
    private RestTemplate restTemplate;

    private Gson gson = new Gson();

    public void send(OnePieceAlarm alarm) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        HttpEntity<?> httpEntity = new HttpEntity<>(alarm.generate(), headers);
        log.debug("send {}  alarm {} param", config.getSendOnepieceUrl(), gson.toJson(alarm));
        ResponseEntity<Object> result = restTemplate.exchange(config.getSendOnepieceUrl(), HttpMethod.POST, httpEntity, Object.class);
        log.debug("send {} response {}", config.getSendOnepieceUrl(), gson.toJson(result.getBody()));
    }
}
