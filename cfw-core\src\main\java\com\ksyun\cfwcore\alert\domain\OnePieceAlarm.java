package com.ksyun.cfwcore.alert.domain;

import org.apache.commons.lang.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

/**
 * <AUTHOR>
 * @date 2020/6/2 8:03 下午
 */
public class OnePieceAlarm {
    private String name="kfw-commons";
    private Integer group=OnePieceAlarmGroup.SERVICE_RD.getGroup();
    private String product="kfw-commons";
    private String endpoint;
    private String time;
    private Integer priority= OnePieceAlarmPriority.P2.getPriority();
    private String content;
    private Integer no_deal= OnePieceAlarmDeal.NO_ASK.getDeal();
    private Integer status = OnePieceAlarmStatus.ALARM.getStatus();
    private String html_content="net-commons";
    private String tags;
    private String vals;
    private String pre_alarm_md5;
    private String pre_alarm_name;

    public OnePieceAlarm(String name, Integer group, String product, String endpoint, String time, Integer priority,
                         String content, Integer no_deal, Integer status, String html_content, String tags, String vals,
                         String pre_alarm_md5, String pre_alarm_name) {
        this.name = name;
        this.group = group;
        this.product = product;
        this.endpoint = endpoint;
        this.time = time;
        this.priority = priority;
        this.content = content;
        this.no_deal = no_deal;
        this.status = status;
        this.html_content = html_content;
        this.tags = tags;
        this.vals = vals;
        this.pre_alarm_md5 = pre_alarm_md5;
        this.pre_alarm_name = pre_alarm_name;
    }

    public OnePieceAlarm() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getGroup() {
        return group;
    }

    public void setGroup(Integer group) {
        this.group = group;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getNo_deal() {
        return no_deal;
    }

    public void setNo_deal(Integer no_deal) {
        this.no_deal = no_deal;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getHtml_content() {
        return html_content;
    }

    public void setHtml_content(String html_content) {
        this.html_content = html_content;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getVals() {
        return vals;
    }

    public void setVals(String vals) {
        this.vals = vals;
    }

    public String getPre_alarm_md5() {
        return pre_alarm_md5;
    }

    public void setPre_alarm_md5(String pre_alarm_md5) {
        this.pre_alarm_md5 = pre_alarm_md5;
    }

    public String getPre_alarm_name() {
        return pre_alarm_name;
    }

    public void setPre_alarm_name(String pre_alarm_name) {
        this.pre_alarm_name = pre_alarm_name;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public MultiValueMap<String, Object> generate(){
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("name",this.getName());
        postParameters.add("group",String.valueOf(this.getGroup()));
        postParameters.add("product",this.getProduct());
        postParameters.add("priority",String.valueOf(this.getPriority()));
        postParameters.add("content",this.getContent());
        postParameters.add("no_deal",String.valueOf(this.getNo_deal()));
        postParameters.add("status",String.valueOf(this.getStatus()));
        if(StringUtils.isNotBlank(this.getEndpoint())){
            postParameters.add("endpoint",this.getEndpoint());
        }
        if(StringUtils.isNotBlank(this.getHtml_content())){
            postParameters.add("html_content",this.getHtml_content());
        }
        if(StringUtils.isNotBlank(this.getTags())){
            postParameters.add("tags",this.getTags());
        }
        if(StringUtils.isNotBlank(this.getVals())){
            postParameters.add("vals",this.getVals());
        }
        if(StringUtils.isNotBlank(this.getTime())){
            postParameters.add("time",this.getTime());
        }
        if(StringUtils.isNotBlank(this.getPre_alarm_md5())){
            postParameters.add("pre_alarm_md5",this.getPre_alarm_md5());
        }
        if(StringUtils.isNotBlank(this.getPre_alarm_name())){
            postParameters.add("pre_alarm_name",this.getPre_alarm_name());
        }
        return postParameters;
    }


}
