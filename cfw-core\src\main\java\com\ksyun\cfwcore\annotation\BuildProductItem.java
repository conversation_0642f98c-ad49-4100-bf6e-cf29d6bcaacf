package com.ksyun.cfwcore.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/13 16:12
 * <p>
 * 根据入参自动构建ProductItem
 * <p>
 * 使用方法：
 * *** 仅支持包装类型字段自动生成数据，入参请不要使用基本数据类型
 * *** 暂不支持List、数组等
 * *** 当构建Item方法调用时，不写该注解的字段会被自动映射，且itemName、itemNo自动拼接字段名，unit为空（否则需要使用该注解启用自定义值）
 * *** 当构建Item方法调用时，若某些字段不需要映射，使用该注解，并标注isBuildItemField=false，该字段将被自动跳过
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD})
@Inherited
public @interface BuildProductItem {

    /**
     * 不使用该注解或者该字段为true，字段会自动映射。 当该字段为false，会被自动剔除
     */
    boolean isBuildItemField() default true;

    /**
     * 需要构建的ItemName与变量名不一致，使用该字段启用别名
     */
    String itemName() default "";

    /**
     * 需要构建的ItemNo与变量名不一致，使用该字段启用别名
     */
    String itemNo() default "";

    /**
     * item单位
     */
    String unit() default "";
}
