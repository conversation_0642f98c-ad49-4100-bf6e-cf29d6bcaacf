package com.ksyun.cfwcore.component;

import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.Validation;
import com.ksyun.cfwcore.openstack.domain.OpenStackLink;
import com.ksyun.cfwcore.openstack.domain.OpenStackToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Created by xuyaming on 2016/11/2.
 */
@Component
public class PageInfoUtils {
	/**
	 * 计算NextToken 字段转换ID或者数字输出 当只有limit的时候 只返回limit+1
	 * 当存在原始token的时候，会计算原始Token是ID还是数字 按照原始方式返回
	 * 
	 * @param marker
	 * @param sourceNextToken
	 * @param limit
	 * @return
	 */
	public String getNextToken(String marker, String sourceNextToken, Integer limit) {
		if(limit == null){
			limit = Constants.DEFAULT_MAX_RESULTS;
		}

		if (StringUtils.isBlank(marker) || (StringUtils.isBlank(sourceNextToken) && limit == null)) {
			return null;
		}

		if (StringUtils.isBlank(sourceNextToken)) {
			return String.valueOf(limit.intValue() + 1);
		} else {
			Pattern pattern = Pattern.compile(Validation.REGEX_ID);
			if (pattern.matcher(sourceNextToken).find()) {
				return marker;
			} else {
				return String.valueOf(Integer.valueOf(sourceNextToken) + limit.intValue());
			}
		}
	}

	/**
	 * 解析next和previous
	 * @param openStackLinkList
	 * @return
	 */
	public OpenStackToken getOpenStackLink(List<OpenStackLink> openStackLinkList){
		OpenStackToken openStackLink=new OpenStackToken();
		if (CollectionUtils.isNotEmpty(openStackLinkList)) {
			for (OpenStackLink links : openStackLinkList) {
				if (links.getRel().equals("next")) {
					String href = links.getHref();
					String hs = href.substring(0);
					String[] splits = hs.split("&");
					for (String split : splits) {
						if (split.startsWith("marker")) {
							String[] strings = split.split("=");
							openStackLink.setNextToken(strings[1]);
							break;
						}
					}
				} else if (links.getRel().equals("previous")) {
					String href = links.getHref();
					String hs = href.substring(0);
					String[] splits = hs.split("&");
					for (String split : splits) {
						if (split.startsWith("marker")) {
							String[] strings = split.split("=");
							openStackLink.setPreviousToken(strings[1]);
							break;
						}
					}
				}
			}
		}
		return openStackLink;
	}

}
