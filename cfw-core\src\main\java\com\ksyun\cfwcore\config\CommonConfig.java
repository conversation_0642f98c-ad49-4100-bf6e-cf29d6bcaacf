package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.google.common.base.Splitter;
import com.ksyun.cfwcore.domain.ChargeComment;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshMethod;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Data
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-core-common"})
public class CommonConfig extends LoadConfig {

    // 是否发送onepiece告警开关
    @Value("${error.log.send.onepiece:true}")
    @RefreshField("error.log.send.onepiece")
    public Boolean sendOnepieceMark;

    // 告警群配置标识
    @Value("${onepiece.alert.code:}")
    @RefreshField("onepiece.alert.code")
    public String alertCode;

    @Value("${send.onepiece.url:http://alarm.inner.sdns.ksyun.com/alarm/receptor}")
    @RefreshField("send.onepiece.url")
    public String sendOnepieceUrl;

    @Value("${charge.type.comments}")
    @RefreshField("charge.type.comments")
    public String chargeComments;

    @Value("${trade.refund.retry.code:InstanceHadInvalid}")
    @RefreshField("trade.refund.retry.code")
    public String tradeRefundRetryCode;

    @Value("${cfw.deleteInstance.now}")
    @RefreshField("cfw.deleteInstance.now")
    private boolean deleteInstanceNow;

    @Value("${create.delete.server}")
    @RefreshField("create.delete.server")
    private boolean createDeleteServer;


    private Set<String> tradeRefundRetryCodeSet = new HashSet<>();

    private Map<String, ChargeComment> chargeCommentCache = new HashMap<>();

    @RefreshMethod(field = {"charge.type.comments"})
    private void initChargeComment() throws Exception {
        synchronized (chargeCommentCache) {
            chargeCommentCache = (Map<String, ChargeComment>) loadJsonMap(chargeComments,
                    "charge.type.comments", chargeCommentCache, ChargeComment.class);
            log.info("BillType与ChargeType对应关系加载成功 [{}]", chargeCommentCache);
        }
    }

    @RefreshMethod(field = {"trade.refund.retry.code"})
    private void initTradeRefundRetryCode() {
        if (StringUtils.isEmpty(tradeRefundRetryCode)) {
            return;
        }
        synchronized (tradeRefundRetryCodeSet) {
            List<String> kvs = Splitter.on(',').splitToList(tradeRefundRetryCode);
            if (tradeRefundRetryCodeSet.size() == 0) {
                log.info("初始化tradeRefundRetryCodeSet");
            } else {
                log.info("刷新tradeRefundRetryCodeSet");
                Iterator<String> it = tradeRefundRetryCodeSet.iterator();
                while (it.hasNext()) {
                    String key = it.next();
                    if (!kvs.contains(key)) {
                        log.info("tradeRefundRetryCodeSet {} 配置将下线", key);
                        it.remove();
                    }
                }
            }
            tradeRefundRetryCodeSet.addAll(kvs);
            log.info("tradeRefundRetryCodeSet size={}", tradeRefundRetryCodeSet.size());
        }
    }

    @PostConstruct
    private void commonConfigInit() throws Exception {
        this.initChargeComment();
        this.initTradeRefundRetryCode();
    }
}
