package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Data
@EnableApolloConfig({"cfw-core-common"})
@RefreshType
@Log4j2
public class ElasticSearchConfig extends LoadConfig {
    @Value("${es_host}")
    @RefreshField("es_host")
    private String esHost;

    @Value("${es_port}")
    @RefreshField("es_port")
    private String esPort;

    @Value("${user_tag:console}")
    private String userTag;

    @Value("${support.region.convert:false}")
    @RefreshField("support.region.convert")
    private boolean supportRegionConvert;

}
