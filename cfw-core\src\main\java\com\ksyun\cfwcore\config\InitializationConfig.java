package com.ksyun.cfwcore.config;

import com.ksyun.cfwcore.annotation.DisplayAttribute;
import com.ksyun.cfwcore.trade.wapper.DisplayWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class InitializationConfig {

    public static Map<String, Method> displayAttributeMap = new HashMap<>();

    /**
     * 产品线Config文件自动注册
     *
     * @throws Exception
     */
    @PostConstruct
    public void autoScanRegisterConfigPackage() throws Exception {
        /**
         * Display注解方法扫描配对
         */
        Class<DisplayWrapper> clazz = DisplayWrapper.class;
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            DisplayAttribute displayAttribute = method.getAnnotation(DisplayAttribute.class);
            if (displayAttribute != null) {
                Class<?>[] classes = method.getParameterTypes();
                Map<String, Integer> _map = new HashMap<>();
                for (Class<?> _clazz : classes) {
                    String typeName = _clazz.getTypeName();
                    if (_map.containsKey(typeName)) {
                        _map.put(typeName, _map.get(typeName) + 1);
                    } else {
                        _map.put(typeName, 1);
                    }
                }
                displayAttributeMap.put(displayAttribute.value(), method);
            }
        }
    }
}