package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Component
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-core-common"})
public class KecConfig {

    @Value("${kec.service}")
    @RefreshField("kec.service")
    private String kecService;

    @Value("${kec.version}")
    @RefreshField("kec.version")
    private String kecVersion;

    @Value("${kec.url}")
    @RefreshField("kec.url")
    private String kecUrl;
}
