package com.ksyun.cfwcore.config;

import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.ksyun.common.network.log.utils.JsonBinder;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.util.StringUtils;

import java.util.*;

@Log4j2
public class LoadConfig {

    protected JsonBinder jsonBinder = JsonBinder.buildNormalBinder(false);

    /**
     * 以,主分隔，以:子分隔
     */
    protected Map<String, List<String>> loadConfig(String config, String configName, Map<String, ?> cache, int partSize, boolean... allowedEmpty) {
        return loadConfig(config, configName, cache, partSize, ',', ':', allowedEmpty);
    }

    /**
     * 以separator主分隔，以:子分隔
     */
    protected Map<String, List<String>> loadConfig(String config, String configName, Map<String, ?> cache, int partSize,
                                                   char separator1, char separator2, boolean... allowedEmpty) {
        Map<String, List<String>> tempMap = new HashMap<>();
        if (allowedEmpty != null && allowedEmpty.length > 0 && allowedEmpty[0]) {
            if (StringUtils.isEmpty(config)) {
                log.info("[{}] 为空，缓存配置清空...", configName);
                cache.clear();
                return tempMap;
            }
        }
        List<String> values = Splitter.on(separator1).splitToList(config);

        values.forEach(v -> {
            Optional.ofNullable(v).orElse("");
            List<String> parts = Splitter.on(separator2).splitToList(v);
            if (parts.size() != partSize) throw new IllegalArgumentException(configName + " Config is invalid.... ");
            tempMap.put(StringUtils.trimWhitespace(parts.get(0)), parts);
        });
        List<?> keyList = Lists.newArrayList(cache.keySet());
        keyList.removeAll(tempMap.keySet());
        if (CollectionUtils.isNotEmpty(keyList)) {
            log.info("{} 缓存配置KEY {} 下线....", configName, keyList);
            keyList.forEach(key -> {
                cache.remove(key);
            });
        }
        return tempMap;
    }

    /**
     * Json格式 转Map <Key, Object>
     */
    protected Map<String, ?> loadJsonMap(String config, String configName, Map<String, ?> cache, Class<?> tClass) throws Exception {
        Map<String, ?> tempMap = jsonBinder.fromJson(config, Map.class, getObjectMapType(tClass));
        if (MapUtils.isEmpty(tempMap)) {
            throw new IllegalArgumentException(configName + " Config is invalid.... ");
        }
        List<String> keyList = Lists.newArrayList(cache.keySet());
        keyList.removeAll(tempMap.keySet());
        if (CollectionUtils.isNotEmpty(keyList)) {
            log.info("{} 缓存配置KEY {} 下线....", configName, keyList);
            keyList.forEach(key -> {
                cache.remove(key);
            });
        }
        return tempMap;
    }

    /**
     * Json格式 转Map <Key, Map<String,Object>>
     */
    protected Map<String, Map<String,?>> loadJsonMapMap(String config, String configName, Map<String, ?> cache, Class<?> tClass) throws Exception {
        Map<String, Map<String,?>> tempMap = jsonBinder.fromJson(config, Map.class, getMapMapType(tClass));
        if (MapUtils.isEmpty(tempMap)) {
            throw new IllegalArgumentException(configName + " Config is invalid.... ");
        }
        List<String> keyList = Lists.newArrayList(cache.keySet());
        keyList.removeAll(tempMap.keySet());
        if (CollectionUtils.isNotEmpty(keyList)) {
            log.info("{} 缓存配置KEY {} 下线....", configName, keyList);
            keyList.forEach(key -> {
                cache.remove(key);
            });
        }
        return tempMap;
    }

    /**
     * String以,分隔
     */
    protected List<String> loadStringList(String config, String configName, Set<String> cache) {
        if (StringUtils.isEmpty(config)) return Lists.newArrayList();
        List<String> values = Splitter.on(',').splitToList(config);
        List<String> copyCache = Lists.newArrayList(cache);
        copyCache.removeAll(values);
        if (CollectionUtils.isNotEmpty(copyCache)) {
            log.info("{} 缓存配置KEY {} 下线....", configName, copyCache);
            cache.removeAll(copyCache);
        }
        return values;
    }

    /**
     * Json格式 转List<?>
     */
    protected <T> Map<String, List<T>> loadMapListJson(String config, String configName, Map<String, List<T>> cache, Class<T> tClass) throws Exception {
        if (StringUtils.isEmpty(config)) {
            return new HashMap<>();
        }
        Map<String, List<T>> tempMap = (Map<String, List<T>>) jsonBinder.fromJson(config, Map.class, getMapListType(tClass));
        List<String> removeKeys = Lists.newArrayList();
        tempMap.keySet().forEach(key -> {
            if (cache.containsKey(key)) {
                cache.remove(key);
                removeKeys.add(key);
            }
        });
        if (CollectionUtils.isNotEmpty(removeKeys)) log.info("{} 缓存配置KEY {} 下线....", configName, removeKeys);
        return tempMap;
    }

    protected MapType getObjectMapType(Class tClass) {
        return jsonBinder.getMapper().getTypeFactory().constructMapType(Map.class, String.class, tClass);
    }

    protected CollectionType getListType(Class tClass) {
        return jsonBinder.getMapper().getTypeFactory().constructCollectionType(List.class, tClass);
    }

    private MapType getMapListType(Class tClass) {
        return jsonBinder.getMapper().getTypeFactory().constructMapType(
                Map.class, jsonBinder.getMapper().getTypeFactory().constructType(String.class),
                jsonBinder.getMapper().getTypeFactory().constructCollectionType(List.class, tClass));
    }

    protected MapType getMapMapType(Class tClass) {
        return jsonBinder.getMapper().getTypeFactory().constructMapType(
                        Map.class, jsonBinder.getMapper().getTypeFactory().constructType(String.class),
                        jsonBinder.getMapper().getTypeFactory().constructMapType(Map.class,
                                jsonBinder.getMapper().getTypeFactory().constructType(String.class),
                                jsonBinder.getMapper().getTypeFactory().constructType(tClass)));
    }
}