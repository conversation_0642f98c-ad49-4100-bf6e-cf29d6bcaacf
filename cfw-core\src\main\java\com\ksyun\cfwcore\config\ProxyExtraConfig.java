package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import com.ksyun.common.proxy.ProxyConfig;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@RefreshType
@EnableApolloConfig({"cfw-core-common"})
@Data
public class ProxyExtraConfig {

/*    @Value("${admin.user:}")
    @RefreshField("admin.user")
    private String adminUser;

    @Value("${admin.token:}")
    @RefreshField("admin.token")
    private String adminToken;*/

    @Value("${proxy.host}")
    private String proxyHost;

    @Value("${proxy.port}")
    private Integer proxyPort;

    @Value("${proxy.neutron.appname}")
    private String proxyNeutronAppname;

    @Value("${proxy.neutron.token}")
    private String proxyNeutronToken;

    @Bean
    public ProxyConfig createProxyConfig() {
        return new ProxyConfig(proxyHost, proxyPort);
    }
}
