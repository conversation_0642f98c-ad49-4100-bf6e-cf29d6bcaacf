package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/18 15:10
 */
@Slf4j
@Data
@Component
@RefreshType
@EnableApolloConfig({"cfw-core-common"})
public class RabbitMqConfig {

    // rabbitmq
    @Value("${rabbitmq.addresses}")
    private String rabbitmqAddress;

    @Value("${rabbitmq.username}")
    private String rabbitmqUsername;

    @Value("${rabbitmq.password}")
    private String rabbitmqPassword;

    @Value("${rabbitmq.vhost}")
    private String rabbitmqVhost;
}
