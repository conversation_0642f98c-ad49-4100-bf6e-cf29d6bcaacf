package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.domain.Rule;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshMethod;
import com.ksyun.comm.config.annotations.RefreshType;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.*;

@Component
@Getter
@ToString
@Log4j2
@RefreshType
@EnableApolloConfig({"cfw-core-common"})
public class RegionConfig extends LoadConfig {
    @Value("${regions}")
    @RefreshField("regions")
    protected String regions;//机房Code配置

    @Value("${vpc.regions}")
    @RefreshField("vpc.regions")
    private String vpcRegions; //机房中英文名称

    private Map<String, String> regionCodeCache = new HashMap<String, String>();

    private Map<String, String> keystoneRegionCache = new HashMap<String, String>();

    @RefreshMethod(field = {"regions"})
    private void initRegionCache() {
        synchronized (regionCodeCache) {
            Map<String, List<String>> loadMap = super.loadConfig(regions, "regions", regionCodeCache, 2);
            loadMap.forEach((k, v) -> {
                regionCodeCache.put(StringUtils.trimWhitespace(v.get(0)), StringUtils.trimWhitespace(v.get(1)));
                keystoneRegionCache.put(StringUtils.trimWhitespace(v.get(1)), StringUtils.trimWhitespace(v.get(0)));
            });
            log.info("机房缓存regionCodeCache配置加载成功 [{}]", regionCodeCache);
            log.info("机房缓存keystoneRegionCache配置加载成功 [{}]", keystoneRegionCache);
        }
    }

    private Map<String, String> vpcRegionCache = new HashMap<>();

    private Map<String, String> regionEnCodeCache = new HashMap<>();

    @RefreshMethod(field = {"vpc.regions"})
    private void initVpcRegionsCache() {
        synchronized (vpcRegionCache) {
            Map<String, List<String>> loadMap = super.loadConfig(vpcRegions, "vpc.regions", vpcRegionCache, 4);
            loadMap.forEach((k, v) -> {
                vpcRegionCache.put(StringUtils.trimWhitespace(v.get(0)), StringUtils.trimWhitespace(v.get(1)));
                regionEnCodeCache.put(StringUtils.trimWhitespace(v.get(0)), StringUtils.trimWhitespace(v.get(3)));
            });
            log.info("VPC机房中文名称配置加载成功 [{}]", vpcRegionCache);
            log.info("机房Code国际化名称列表 [{}]", regionEnCodeCache);
        }
    }

    private Map<String, List<Rule>> systemSgIpv6ConfigCache = new HashMap<>();

    public String convertRegion(ProxyAuth auth) {
        if (getRegionCodeCache().containsKey(auth.getRegion())) {
            return getRegionCodeCache().get(auth.getRegion());
        } else {
            throw new OpenAPIException(ErrorCode.InvalidRegion, null, HttpStatus.BAD_REQUEST, auth.getRegion());
        }
    }

    public String getRegionName(ProxyAuth auth) {
        Locale locale = InnerAPIHolder.getLocaleHolder();
        if (!vpcRegionCache.containsKey(auth.getRegion())) {
            throw new OpenAPIException(ErrorCode.InvalidRegion, null, HttpStatus.BAD_REQUEST, auth.getRegion());
        }
        if (Objects.equals(locale, Locale.CHINESE)) {
            return vpcRegionCache.get(auth.getRegion());
        } else {
            return regionEnCodeCache.get(auth.getRegion());
        }
    }

    @PostConstruct
    private void init() throws Exception {
        initRegionCache();
        initVpcRegionsCache();
    }
}
