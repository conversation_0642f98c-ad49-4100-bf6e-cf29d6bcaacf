package com.ksyun.cfwcore.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@RefreshType
@Slf4j
@EnableApolloConfig("cfw-core-common")
public class TradeComConfig {
    @Value("${trade.product.url}")
    @RefreshField("trade.product.url")
    private String trade_product_url;

    @Value("${trade.order.url}")
    @RefreshField("trade.order.url")
    private String trade_order_url;

    @Value("${trade.instance.url}")
    @RefreshField("trade.instance.url")
    private String trade_instance_url;
}
