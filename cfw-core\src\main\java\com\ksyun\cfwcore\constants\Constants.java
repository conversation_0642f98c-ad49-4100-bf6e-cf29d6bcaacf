package com.ksyun.cfwcore.constants;

public interface Constants {

    /**
     * 控制台来源
     */
    String CONSOLE_SOURCE = "console";

    String ORDER_SOURCE = "order";
    /**
     * 运营平台来源
     */
    String OPCENTER_SOURCE = "opcenter";
    /**
     * SDK来源
     */
    String SDK_SOURCE = "user";
    String X_Auth_User_Tag="X-Auth-User-Tag";
    String DEFAULT_USER_TAG="console";

    /**
     * neutron-header
     */
    String X_OPENSTACK_REQUEST_ID = "X-Openstack-Request-Id";
    /**
     * neutron-header
     */
    String X_AUTH_SUBORDER_ID = "X-Auth-SubOrderId";
    /**
     * neutron-header
     */
    String X_AUTH_PRODUCT_GROUP = "X-Auth-ProductGroup";
    /**
     * 默认项目的编号
     */
    String DEFAULT_IAM_PROJECT_ID = "0";

    String NEUTRON_TAG = "X-Auth-User-Tag";

    String X_IAM_PROJECT_ID = "X-IamProjectId";

    String OPEN_API_SERVICE_NAME = "云防火墙OpenApi告警";

    String SCHEDULER_SERVICE_NAME = "安全Scheduler告警";

    /**
     * Neutron中根据user_tag做资源隔离，跨user_tag的资源不可做关联
     */
    String USER_TAG = "user_tag";

    String ADMIN_USER_TAG = "admin";

    /**浏览器语言类型*/
    String ACCEPT_LANGUAGE_HEADER = "Accept-Language";

    /** rabbitMq产品线标识，组件内部工厂根据产品线缓存，避免重复加载 */
    String SCURITY_RABBITMQ_FLAG = "Scurity";

    /** 不区分机房售卖价格体系创建商品机房固定值 */
    String NO_REGION_PRODUCTLINE_REGION = "Default-CN";

    Integer DEFAULT_MAX_QUERY_COUNT = 100;

    Integer DEFAULT_MAX_RESULTS = 1000;


}
