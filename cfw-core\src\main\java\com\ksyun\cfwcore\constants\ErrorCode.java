package com.ksyun.cfwcore.constants;

public interface ErrorCode extends com.ksyun.common.http.ErrorCode {
    String InvalidParameterPrefix = "InvalidParameter";
    String MissingParameterPrefix = "MissingParameter";
    String ChargeTypeInvalidPrefix = "ChargeTypeInvalid";

    String EmptyField = "EmptyField";
    String InvalidId = "InvalidId";
    String InvalidIp = "InvalidIp";
    String InvalidName = "InvalidName";
    String NotFound = "NotFound";
    String NotMatch = "NotMatch";
    String InvalidPort = "InvalidPort";
    String QuotaMaxCount = "QuotaMaxCount";
    String InvalidRegion = "InvalidRegion";
    String InvalidField = "InvalidField";
    String InvalidAccount = "InvalidAccount";
    String InvalidCidr = "InvalidCidr";

    String VpcIdInvalid = InvalidId;
    String LoadBalancerIdInvalid = InvalidId;
    String ListenerIdInvalid = InvalidId;
    String LbAclIdInvalid = InvalidId;

    String ListenerIdEmpty = EmptyField;
    String ChargeTypeEmpty = EmptyField;
    String LoadBalancerNameInvalid = InvalidName;
    String ListenerNameInvalid = InvalidName;
    String CookieNameInvalid = InvalidName;
    String HealthHostNameInvalid = InvalidName;

    String ProductTagNotSupport = "ProductTagNotSupport";
    String ListenerNotExists = NotFound;
    String AlbNotFound = NotFound;

    String ListenerPortInvalid = InvalidPort;

    String ChargeTypeInvalid = "ChargeTypeInvalid";
    String KfwChargeTypeInvalid = "KfwChargeTypeInvalid";
    String PurchaseTimeInvalid = "PurchaseTimeInvalid";
    String NextTokenInvalid = "NextTokenInvalid";
    String MaxResultsInvalid = "MaxResultsInvalid";
    String MaxResultsOneHundredInvalid = "MaxResultsOneHundredInvalid";
    String InstanceTypeInvalid = "InstanceTypeInvalid";
    String AlbTypeInvalid = "AlbTypeInvalid";
    String LoadBalancerStateInvalid = "LoadBalancerStateInvalid";
    String ListenerStateInvalid = "ListenerStateInvalid";
    String ApplicationListenerProtocolInvalid = "ApplicationListenerProtocolInvalid";
    String AlbRuleTypeInvalid = "AlbRuleTypeInvalid";
    String LbMethodInvalid = "LbMethodInvalid";
    String SessionStateInvalid = "SessionStateInvalid";
    String SessionPersistencePeriodInvalid = "SessionPersistencePeriodInvalid";
    String CookieTypeInvalid = "CookieTypeInvalid";
    String RuleUrlPathInvalid = "RuleUrlPathInvalid";
    String AlbVerionInvalid = "AlbVerionInvalid";
    String AlbIdInvalid = "AlbIdInvalid";
    String AlbIpVersionInvaild = "AlbIpVersionInvaild";
    String RedirectAlbListenerIdInvalid = InvalidId;
    String AlbRuleGroupNameInvalid = InvalidName;
    String AlbRuleGroupTypeInvalid = "AlbRuleGroupTypeInvalid";
    String AlbRuleGroupIdInvalid = InvalidId;
    String AlbListenerCertGroupIdInvalid = InvalidId;
    String AlbRuleNotExist = "AlbRuleNotExist";
    String AlbRuleGroupNotExist = "AlbRuleGroupNotExist";
    String DefaultAlbRuleGroupNotExist = "DefaultAlbRuleGroupNotExist";
    String DefaultAlbRuleModifyRefuse = "DefaultAlbRuleModifyRefuse";
    String DefaultAlbRuleDeleteRefuse = "DefaultAlbRuleDeleteRefuse";
    String VisitInternetEpcNotSupportLb = "VisitInternetEpcNotSupportLb";

    String CertMemberNotExist = "CertMemberNotExist";
    String ModifyCertificateInvalid = "ModifyCertificateInvalid";
    String RedirectHttpCodeInvalid = "RedirectHttpCodeInvalid";
    String BackendServerAndRedirectAreMutex = "BackendServerAndRedirectAreMutex";
    String BackendServerAndRedirectCannotBothEmpty = "BackendServerAndRedirectCannotBothEmpty";
    String BackendServerAndRedirectCannotSwitch = "BackendServerAndRedirectCannotSwitch";
    String RedirectNotBelongSameLb = "RedirectNotBelongSameLb";
    String RedirectListenerNoHttps = "RedirectListenerNoHttps";
    String ListenerAndRedirectNotSame = "ListenerAndRedirectNotSame";

    String HealthyThresholdInvalid = "HealthyThresholdInvalid";
    String IntervalInvalid = "IntervalInvalid";
    String TimeoutInvalid = "TimeoutInvalid";
    String UnhealthyThresholdInvalid = "UnhealthyThresholdInvalid";
    String HealthCheckStateInvalid = "HealthCheckStateInvalid";

    String OnlyPublicAlbSupportIpv6 = "OnlyPublicAlbSupportIpv6";

    // InvalidParameter
    String InvalidParameterInstanceIdMalformedIndex = "InvalidParameter.InstanceIdMalformedIndex";
    String InvalidParameterInstanceIdSameKey = "InvalidParameter.InstanceIdSameKey";
    String InvalidParameterInstanceIdMalformedPoint = "InvalidParameter.InstanceIdMalformedPoint";
    String InvalidParameterInstanceIdMaxlengthIndex = "InvalidParameter.InstanceIdMaxlengthIndex";
    String InvalidParameterInstanceIdRangeIndex = "InvalidParameter.InstanceIdRangeIndex";
    String InvalidParameterValueInstanceId = "InvalidParameterValue.InstanceId";
    String InvalidParameterFilterPrefix = "InvalidParameter.FilterPrefix";
    String InvalidParameterFilterMalformedIndex = "InvalidParameter.FilterMalformedIndex";
    String InvalidParameterFilterMaxIndexDigits = "InvalidParameter.FilterMaxIndexDigits";
    String InvalidParameterFilterIndexRange = "InvalidParameter.FilterIndexRange";
    String InvalidParameterFilterNameSameKey = "InvalidParameter.FilterNameSameKey";
    String InvalidParameterValueFilterNameSameValue = "InvalidParameterValue.FilterNameSameValue";
    String InvalidParameterFilterMalformedNameParts = "InvalidParameter.FilterMalformedNameParts";
    String InvalidParameterValueNameNotEmptyValue = "InvalidParameterValue.NameNotEmptyValue";
    String InvalidParameterFilterMalformedValueParts = "InvalidParameterFilterMalformedValueParts";
    String InvalidParameterFilterValueEndPartsMalformed = "InvalidParameter.FilterValueEndPartsMalformed";
    String InvalidParameterFilterValueEndPartsDigits = "InvalidParameter.FilterValueEndPartsDigits";
    String InvalidParameterFilterValueEndPartsRange = "InvalidParameter.FilterValueEndPartsRange";
    String InvalidParameterValueFilterValueSameValue = "InvalidParameterValue.FilterValueSameValue";
    String InvalidParameterValueFilterValueNotEmptyValue = "InvalidParameterValue.FilterValueNotEmptyValue";
    String InvalidParameterFilterValueSameKey = "InvalidParameter.FilterValueSameKey";
    String InvalidParameterFilterNameMatch = "InvalidParameter.FilterNameMatch";
    String InvalidParameterFilterValueMatch = "InvalidParameter.FilterValueMatch";

    // InvalidParameter.KeyOrValue
    String InvalidParameterValueKeySameValue = "InvalidParameterValue.KeySameValue";
    String InvalidParameterValueValueSameValue = "InvalidParameterValue.ValueSameValue";

    String MissingParameterSubnetId = "MissingParameter.SubnetId";

    String SubOrderIdEmpty = EmptyField;
    String CertificateIdInvalid = NotMatch;


    //TradeBill
    String CheckInstanceTradeStatusError = "CheckInstanceTradeStatus";
    String CheckInstanceStatusError = "CheckInstanceStatus";
    //ES
    String QueryEsResourceError = "QueryEsResourceError";
    String SubOrderIdDup = "SubOrderIdDup";

    //Project
    String ProjectMemberNotExist = "ProjectMemberNotExist";
    String NotExistProjectId = "NotExistProjectId";
    String ProjectStatusUnavaliable = "ProjectStatusUnavaliable";

    String RealServerGroupIdInvalid = InvalidId;

    String RealServerGroupIPVersionInvalid = "RealServerGroupIPVersionInvalid";

    String RuleMethodInvalid = "RuleMethodInvalid";

    String PackageNotExists = "PackageNotExists";
    String SlbPackageNotExists = "SlbPackageNotExists";
    String NatPackageNotExists = "NatPackageNotExists";
    String PeerPackageNotExists = "PeerPackageNotExists";
    String CenPkgPriceSystemNotExists = "CenPkgPriceSystemNotExists";
    String AlbPackageNotExists = "AlbPackageNotExists";

    String AlbSpecInvalid = "AlbSpecInvalid";
    String AlbSpecNotLarger = "AlbSpecNotLarger";

    //gray
    String ApiNotSupportRegion = "ApiNotSupportRegion";
    String ApiParamNotSupportRegion = "ApiParamNotSupportRegion";

    String LbListenerHttpProtocolInvalid = "LbListenerHttpProtocolInvalid";
    String LbListenerHttp2MustHttps = "LbListenerHttp2MustHttps";
    String ListenerTslPolicyInvalid = "ListenerTslPolicyInvalid";
    String LbListenerTslPolicyMustHttps = "LbListenerTslPolicyMustHttps";

    String AlbListenerPortNotSupport = "AlbListenerPortNotSupport";
    String CreateAlbListenerSucessDefaultRuleFail = "CreateAlbListenerSucessDefaultRuleFail";

    // Cen
    String CenInstanceTypeInvalid = "CenInstanceTypeInvalid";
    String NetworkInstanceIdInvalid = InvalidId;
    String CenIdInvalid = InvalidId;
    String CenGrantIdInvalid = InvalidId;
    String CenBandWidthPackageNotFound = "CenBandWidthPackageNotFound";
    String CenPriceSystemRegionGroupError = "CenPriceSystemRegionGroupError";
    String CenBandWidthPackageStatusInvalid = "CenBandWidthPackageStatusInvalid";
    String CenBandWidthPackageNameInvalid = InvalidName;
    String CenNameInvalid = InvalidName;
    String CenBandWidthPackageNotAllowReduceConfiguration = "CenBandWidthPackageNotAllowReduceConfiguration";
    String CenTrialNotSupportUpdate = "CenTrialNotSupportUpdate";
    String CenBandWidthPackageInvalid = "CenBandWidthPackageInvalid";
    String CenNetworkRoutesInvalid = "CenNetworkRoutesInvalid";
    String DirectConnectGatewayNotSupportSelfDefineCidr = "DirectConnectGatewayNotSupportSelfDefineCidr";
    String CenBandWidthPackageNotAttachCen = "CenBandWidthPackageNotAttachCen";

    String AlbTrialNotSupportUpdate = "AlbTrialNotSupportUpdate";

    String DcGatewayStatusInvalid = "DcGatewayStatusInvalid";

    String UserNotSupportInterface = "UserNotSupportInterface";

    String SubOrderNotExist = "SubOrderNotExist";

    String ProductNotExist = "ProductNotExist";

    String ThisActionNotSupportDirectOpt = "ThisActionNotSupportDirectOpt";

    String SlbUrlRuleListenerSyncInvalid = "SlbUrlRuleListenerSyncInvalid";


    String HttpHeaderNotFound = "HttpHeaderNotFound";
    String AnnotationUseError = "AnnotationUseError";

    String ContentConfigFormatError = "ContentConfigFormatError";
    String FormatError = "FormatError";
    String ParameterUnitError = "ParameterUnitError";
    String ConfigurationItemError = "ConfigurationItemError";
    String HTTPSAndTLSNotSSLEarlyData = "HTTPSAndTLSNotSSLEarlyDataInvalid";

    String RegionNotPersonalityConfig = "RegionNotPersonalityConfig";
    String HTTPNotPersonalityConfig = "HTTPNotPersonalityConfig";

    // tagV2
    String TagServiceFailed = "TagServiceFailed";
    String TagKeyFilterPassMax = "TagKeyFilterPassMax";

    // klog
    String ProjectOrLogPoolNotExist = "ProjectOrLogPoolNotExist";
    String AlbNotBindKlog = "AlbNotBindKlog";

    //profile
    String ProfileNotFound = "ProfileNotFound";

    String VnetNotBelongThisVpc = "VnetNotBelongThisVpc";
    String EndpointVnetTypeError = "EndpointVnetTypeError";
    String InvalidStartEndIPAddr = "InvalidStartEndIPAddr";
    String PrivateIpAddressOnProcess = "PrivateIpAddressOnProcess";

    String RegionNotSupport = "RegionNotSupport";

    // application
    String RealServerGroupNameInvalid = InvalidName;
    String RealServerGroupProtocolInvalid = "RealServerGroupProtocolInvalid";
    String RealServerGroupIdEmpty = EmptyField;
    String BackendServerTypeInvalid = "BackendServerTypeInvalid";
    String BackendServerGroupTypeInvalid = "BackendServerGroupTypeInvalid";
    String RealServerInstanceTypeInvalid = "RealServerInstanceTypeInvalid";
    String UpstreamKeepaliveInvalid = "UpstreamKeepaliveInvalid";

    String NetworkInterfaceNotExists = "NetworkInterfaceNotExists";
    String NetworkInterfaceAndIpNotEq = "NetworkInterfaceAndIpNotEq";

    String WeightInvalid = "WeightInvalid";

    String DirectConnectGatewayIdInvalid="DirectConnectGatewayIdInvalid";

    String WeightRangeInvalid = "WeightRangeInvalid";
    String PortRangeInvalid = "PortRangeInvalid";

    String RealServerGroupNotExists = "RealServerGroupNotExists";

    String GroupRuleDefinitionExit = "GroupRuleDefinitionExit";

    String GroupRuleCertificateExit = "GroupRuleCertificateExit";

    String AlbGroupRuleContentInvalid = "AlbGroupRuleContentInvalid";
    String AlbGroupRuleContentLengthSizeInvalid = "AlbGroupRuleContentLengthSizeInvalid";
    String AlbGroupRuleContentTypeInvalid = "AlbGroupRuleContentTypeInvalid";
    String AlbGroupRuleContentHttpCodeInvalid = "AlbGroupRuleContentHttpCodeInvalid";

    String AlbGroupRuleForwardGroupParamInvalid = "AlbGroupRuleForwardGroupParamInvalid";
    String AlbGroupRuleRedirectParamInvalid = "AlbGroupRuleRedirectParamInvalid";
    String AlbGroupRuleFixedResponseParamInvalid = "AlbGroupRuleFixedResponseParamInvalid";
    String RegionNotSupportSelfCidr = "RegionNotSupportSelfCidr";

    String AlbListenerAndBackendGroupNotMatch = "AlbListenerAndBackendGroupNotMatch";

    String EnableHpaNotFalseAndQuota = "EnableHpaNotFalseAndQuota";

    String EnableHpaNotTrueAdvanced = "EnableHpaNotTrueAdvanced";

    String EnabledQuicNotFalseAndQuota = "EnabledQuicNotFalseAndQuota";

    String QuicNotSupportTls = "QuicNotSupportTls";

    String QuicNotEnableHttp2 = "QuicNotEnableHttp2";
    String QuicNotCaEnabled = "QuicNotCaEnabled";

    String HealthCheckPortInvalid = "HealthCheckPortInvalid";
    String HealthCheckProtocolInvalid = "HealthCheckProtocolInvalid";
    String HealthCheckHttpMethodInvalid = "HealthCheckHttpMethodInvalid";

    String QueryTagProtectFailed = "QueryTagProtectFailed";
    String CreateTagFailed = "CreateTagFailed";

    String ReplaceTagsFailed = "ReplaceTagsFailed";

    String DeleteAlbDeleteProtectionStatus = "DeleteAlbDeleteProtectionStatus";
    String QuicListenerIdInvalid = InvalidId;
    String EnableQuicTrueNotQuicId = "EnableQuicTrueNotQuicId";
    String NotHttpsNoEnableQuic = "NotHttpsNoEnableQuic";

    String AlbGroupRuleHttpHostInvalid = "AlbGroupRuleHttpHostInvalid";
    String AlbGroupRuleRewriteConfigParamInvalid = "AlbGroupRuleRewriteConfigParamInvalid";
    String AlbGroupRuleRewriteConfigNotAllEmptyInvalid = "AlbGroupRuleRewriteConfigNotAllEmptyInvalid";

    String AlbListenerNotExist = "AlbListenerNotExist";
    String AlbListenerRuleGroupNotExist = "AlbListenerRuleGroupNotExist";


    String StateInvalid = "StateInvalid";

}