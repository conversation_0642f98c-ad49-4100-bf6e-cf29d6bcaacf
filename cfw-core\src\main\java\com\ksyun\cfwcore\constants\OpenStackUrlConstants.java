package com.ksyun.cfwcore.constants;

import com.ksyun.cfwcore.openstack.OpenstackConstants;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/4/27 14:48
 */
public interface OpenStackUrlConstants extends OpenstackConstants {

    String FIREWALL_LB = OpenstackConstants.PROXY_URL + "/firewalls";
    String FIREWALL_RS = OpenstackConstants.PROXY_URL + "/firewall_nodes";
    String ATTACH_EIPS_TO_FW = OpenstackConstants.PROXY_URL + "/firewalls/%S/attach_eips_to_fw";
    String DETACH_EIPS_FROM_FW = OpenstackConstants.PROXY_URL + "/firewalls/%S/detach_eips_from_fw";

    String SECURITY_GROUP_CREATE = "http://${proxy_host}:${proxy_port}/vpc/vpc_securitygroups";

    String SECURITY_GROUP_DELETE = "http://${proxy_host}:${proxy_port}/vpc/vpc_securitygroups/${security_group_id}";

    String SECURITY_GROUP_UPDATE = SECURITY_GROUP_DELETE;

    String SECURITY_GROUP_RULE_CREATE = SECURITY_GROUP_DELETE;

    String SECURITY_GROUP_RULE_DELETE = SECURITY_GROUP_DELETE;

    String SECURITY_GROUP_RULE_UPDATE = OpenstackConstants.PROXY_URL + "/vpc/vpcsgrules/${sg_rule_id}/modify_sg_rule_description";

    String QUERY_SECURITY_GROUP_RULE_BY_ID = OpenstackConstants.PROXY_URL + "/vpc/vpcsgrules/${sg_rule_id}";

    String SECURITY_GROUP_ATTACH_VIF = "http://${proxy_host}:${proxy_port}/vpc/vifs/${vif_id}/replace_vif_sg";

    String SECURITY_GROUP_DETTACH_VIF = SECURITY_GROUP_ATTACH_VIF;

    String SECURITY_GROUP_QUERY_ALL = OpenstackConstants.PROXY_URL + "/vpc/vpc_securitygroups";

    String SECURITY_GROUP_QUERY = OpenstackConstants.PROXY_URL
            + "/vpc/vpc_securitygroups/${securitygroup_id}";



    // domain
    String DOMAIN_CREATE = "http://${proxy_host}:${proxy_port}/vpc/domains";

    String DOMAIN_DELETE = "http://${proxy_host}:${proxy_port}/vpc/domains/${domain_id}";

    String DOMAIN_UPDATE = DOMAIN_DELETE;

    String DOMAIN_QUERY = DOMAIN_DELETE;

    String DOMAIN_QUERY_LIST = DOMAIN_CREATE;

    String VNET_CREATE = "http://${proxy_host}:${proxy_port}/vpc/vnets";

    String VNET_DELETE = "http://${proxy_host}:${proxy_port}/vpc/vnets/${vnet_id}";

    String VNET_UPDATE = "http://${proxy_host}:${proxy_port}/vpc/vnets/${vnet_id}";

    String VNET_QUERY_LIST = "http://${proxy_host}:${proxy_port}/vpc/vnets";

    String VNET_QUERY = "http://${proxy_host}:${proxy_port}/vpc/vnets/${vnet_id}";

    String DHCP_CREATE = OpenstackConstants.PROXY_URL + "/vpc/dhcps";

    String DHCP_DELETE = OpenstackConstants.PROXY_URL + "/vpc/dhcps/${dhcp_id}";

    String DHCP_UPDATE = DHCP_DELETE;

    String DHCP_QUERY = DHCP_DELETE;

    String DHCP_QUERY_LIST = DHCP_CREATE + "?limit=${limit}&marker=${marker}";

    String DHCP_IP_LIST = OpenstackConstants.PROXY_URL + "/vpc/dhcpips";

    String DHCP_IP_COUNT = OpenstackConstants.PROXY_URL + "/vpc/dhcpips/count";

    // 批量修改子网内IP地址预留情况
    String UPDATE_IP_ALLOCATION = OpenstackConstants.PROXY_URL + "/vpc/vpc_ipallocations/update_bulk";

    String VIF_DELETE = "http://${proxy_host}:${proxy_port}/vpc/vifs/${vif_id}";

    String IGW_CREATE = "http://${proxy_host}:${proxy_port}/vpc/igws";

    String IGW_DELETE = "http://${proxy_host}:${proxy_port}/vpc/igws/${igw_id}";

    String IGW_UPDATE = IGW_DELETE;

    String IGW_QUERY = "http://${proxy_host}:${proxy_port}/vpc/igws/${igw_id}";

    String IGW_ATTACH_EIP = OpenstackConstants.PROXY_URL + "/floatingips/${floatingip_id}";

    String IGW_DETTACH_EIP = OpenstackConstants.PROXY_URL + "/floatingips/${floatingip_id}";

    String IGW_QUERY_ALL = "http://${proxy_host}:${proxy_port}/vpc/igws";

    String ROUTE_CREATE = "http://${proxy_host}:${proxy_port}/vpc/routes";

    String ROUTE_DELETE = "http://${proxy_host}:${proxy_port}/vpc/routes/${route_id}";

    String ROUTE_QUERY = "http://${proxy_host}:${proxy_port}/vpc/routes/${route_id}";

    String ROUTE_QUERY_LIST = "http://${proxy_host}:${proxy_port}/vpc/routes";

    String NATPOOL_CREATE = OpenstackConstants.PROXY_URL + "/vpc/natpools";

    String NATPOOL_DELETE = OpenstackConstants.PROXY_URL + "/vpc/natpools/${natpool_id}";

    String NATPOOL_QUERY_BY_ID = OpenstackConstants.PROXY_URL + "/vpc/natpools/${natpool_id}";
    /**
     * 修改natpool
     */
    String NATPOOL_UPDATE = NATPOOL_QUERY_BY_ID;
    /**
     * 添加vnet
     */
    String NATPOOL_ADD_VNET = OpenstackConstants.PROXY_URL
            + "/vpc/natpools/${natpool_id}/add_natpool_vnet";
    /**
     * 删除vnet
     */
    String NATPOOL_REMOVE_VNET = OpenstackConstants.PROXY_URL
            + "/vpc/natpools/${natpool_id}/remove_natpool_vnet";
    /**
     * natpool列表
     */
    String NATPOOL_LIST = NATPOOL_CREATE;

    String ACL_CREATE = OpenstackConstants.PROXY_URL + "/vpc/acls";

    String ACL_DELETE = OpenstackConstants.PROXY_URL + "/vpc/acls/${acl_id}";

    String ACL_UPDATE = OpenstackConstants.PROXY_URL + "/vpc/acls/${acl_id}";

    String ACL_RULE_CREATE = OpenstackConstants.PROXY_URL + "/vpc/acls/${acl_id}";

    String ACL_RULE_DELETE = OpenstackConstants.PROXY_URL + "/vpc/acls/${acl_id}";

    String ACL_ATTACH_VENT = OpenstackConstants.PROXY_URL + "/vpc/vnets/${vnet_id}";

    String ACL_DETTACH_VNET = OpenstackConstants.PROXY_URL + "/vpc/vnets/${vnet_id}";

    String VPC_VIF_BY_ID = OpenstackConstants.PROXY_URL + "/vpc/vifs/${vif_id}";

    String VPC_VIF_QUERY_ALL = OpenstackConstants.PROXY_URL + "/vpc/vifs";

    String UPDATE_VIF_BY_ID = OpenstackConstants.PROXY_URL + "/vpc/vifs/${nova_vif_id}";

    String DELETE_VIF_BY_ID = UPDATE_VIF_BY_ID;

    String VPC_VIF_LIST = OpenstackConstants.PROXY_URL + "/vpc/vifs";

    String ACL_QUERY = OpenstackConstants.PROXY_URL + "/vpc/acls/${acl_id}";

    String ACL_QUERY_ALL = OpenstackConstants.PROXY_URL + "/vpc/acls";

    String ACL_RULE_UPDATE = OpenstackConstants.PROXY_URL + "/vpc/aclrules/${acl_rule_id}/modify_acl_rule_description";

    /**
     * 获取vpn列表
     */
    String VPN_LIST = OpenstackConstants.PROXY_URL + "/vpc/vpns";
    /**
     * 获取vpn信息
     */
    String VPN_QUERY_BY_ID = OpenstackConstants.PROXY_URL + "/vpc/vpns/${vpn_id}";
    /**
     * 创建vpn
     */
    String VPN_CREATE = OpenstackConstants.PROXY_URL + "/vpc/vpns";
    /**
     * 更新vpn信息
     */
    String VPN_UPDATE = OpenstackConstants.PROXY_URL + "/vpc/vpns/${vpn_id}";
    /**
     * 删除vpn信息
     */
    String VPN_DELETE = VPN_UPDATE;

    String QUERY_NETWORKS = OpenstackConstants.PROXY_URL + "/networks";

    String FLOATINGIP_CREATE = OpenstackConstants.PROXY_URL + "/floatingips";

    String FLOATINGIP_QUERY = OpenstackConstants.PROXY_URL + "/floatingips/%s";

    String FLOATINGIP_UPDATE_BANDWIDTH = OpenstackConstants.PROXY_URL
            + "/floatingips/${floatingip_id}/modify_floatingip_bandwidth";

    String FLOATINGIP_QUERY_BY_ID = OpenstackConstants.PROXY_URL + "/floatingips/${floatingip_id}";

    String FLOATINGIP_DELETE = FLOATINGIP_QUERY_BY_ID;

    String FLOATINGIP_LIST = FLOATINGIP_CREATE;

    String FLOATINGIP_ATTACH = FLOATINGIP_QUERY_BY_ID;

    String FLOATINGIP_DETACH = FLOATINGIP_QUERY_BY_ID;

    String FLOATINGIP_TAG = FLOATINGIP_QUERY_BY_ID+"/modify_user_tag";

    String PEERING_CREATE = OpenstackConstants.PROXY_URL + "/vpc/peerings";
    String PEERING_DELETE = OpenstackConstants.PROXY_URL + "/vpc/peerings/${peering_id}";

    String PEERING_QUERY = OpenstackConstants.PROXY_URL + "/vpc/peerings/${peering_id}";
    String PEERING_QUERY_LIST = OpenstackConstants.PROXY_URL + "/vpc/peerings";
    String PORTFWD_CREATE = OpenstackConstants.PROXY_URL + "/vpc/portfwds";

    String PORTFWD_DELETE = OpenstackConstants.PROXY_URL + "/vpc/portfwds/${portfwd_id}";

    String PORTFWD_UPDATE = PORTFWD_DELETE;

    String PORTFWD_QUERY = OpenstackConstants.PROXY_URL + "/vpc/portfwds/${portfwd_id}";
    String PORTFWD_QUERY_LIST = OpenstackConstants.PROXY_URL + "/vpc/portfwds";

    String LB_POOL_CREATE = OpenstackConstants.PROXY_URL + "/lb/pools";
    String LB_POOL_UPDATE = OpenstackConstants.PROXY_URL + "/lb/pools/${lb_id}";
    String LB_POOL_DETACH_FLOATINGIP = LB_POOL_UPDATE;
    String LB_POOL_ATTACH_FLOATINGIP = LB_POOL_UPDATE;
    String LB_POOL_QUERY_ID = LB_POOL_UPDATE;
    String LB_POOL_LIST = LB_POOL_CREATE;
    String LB_POOL_DELETE = LB_POOL_UPDATE;
    String LB_POOL_ACTION = OpenstackConstants.PROXY_URL + "/lb/pools/${lb_id}/pool_action";
    String LB_POOL_SET_ADMIN_STATE_UP = LB_POOL_ACTION;
    String LB_POOL_SET_KLOG_INFO = LB_POOL_ACTION;
    String LB_POOL_SET_ENABLED_LOG = LB_POOL_ACTION;
    String LB_POOL_SET_UPGRADE_DEPLOMENT = LB_POOL_ACTION;
    String DEFAULT_SLB_TYPE = "default";
    String APPLICATION_SLB_TYPE = "application";
    String LB_TYPE_CLB = "clb";
    String LB_TYPE_ALB = "alb";

    String UPLOAD_BUCKDT_INFO_SLB_CREATE = OpenstackConstants.PROXY_URL + "/addrecord";
    String UPLOAD_BUCKET_INFO_SLB_DELETE = OpenstackConstants.PROXY_URL + "/delrecord/${lb_id}";
    String UPLOAD_BUCKET_INFO_SLB_DESCRIBE_BY_ID = OpenstackConstants.PROXY_URL + "/listrecord/${lb_id}";

    String LB_MEMBER_CREATE = OpenstackConstants.PROXY_URL + "/lb/members";
    String LB_MEMBER_UPDATE = OpenstackConstants.PROXY_URL + "/lb/members/${member_id}";
    String LB_MEMBER_DELETE = OpenstackConstants.PROXY_URL + "/lb/members/${member_id}";
    String LB_MEMBER_QUERY = OpenstackConstants.PROXY_URL + "/lb/members";
    String LB_MEMBER_QUERY_ID = OpenstackConstants.PROXY_URL + "/lb/members/${member_id}";

    String HEALTH_MONITOR_CREATE = OpenstackConstants.PROXY_URL + "/lb/health_monitors";
    String HEALTH_MONITOR_DELETE = OpenstackConstants.PROXY_URL
            + "/lb/health_monitors/${health_monitor_id}";
    String HEALTH_MONITOR_UPDATE = HEALTH_MONITOR_DELETE;
    String HEALTH_MONITOR_QUERY_LIST = HEALTH_MONITOR_CREATE;
    String HEALTH_MONITOR_QUERY_ID = HEALTH_MONITOR_CREATE
            + "/${health_monitor_id}";
    String LB_VIP_CREATE = OpenstackConstants.PROXY_URL + "/lb/vips";
    String LB_VIP_UPDATE = OpenstackConstants.PROXY_URL + "/lb/vips/${vip_id}";
    String LB_VIP_DELETE = LB_VIP_UPDATE;
    String LB_VIP_QUERY_ID = LB_VIP_UPDATE;
    String LB_VIP_QUERY = LB_VIP_CREATE;
    String LB_VIP_METADATA_UPDATE = OpenstackConstants.PROXY_URL + "/lb/vips/${vip_id}/vip_metadatas/${vip_metadata_key}";
    String LB_VIP_BIND_MGROUPS = OpenstackConstants.PROXY_URL + "/lb/vips/${vip_id}/vip_bind_mgroups";
    String LB_VIP_UNBIND_MGROUPS = OpenstackConstants.PROXY_URL + "/lb/vips/${vip_id}/vip_unbind_mgroups";

    String LB_L7_POLICY_CREATE = OpenstackConstants.PROXY_URL + "/lb/l7policies";
    String LB_L7_POLICY_UPDATE = OpenstackConstants.PROXY_URL + "/lb/l7policies/${l7policy_id}";
    String LB_L7_POLICY_DELETE = LB_L7_POLICY_UPDATE;
    String LB_L7_POLICY_QUERY_ID = LB_L7_POLICY_UPDATE;
    String LB_L7_POLICY_QUERY = LB_L7_POLICY_CREATE;
    String LB_L7_POLICY_BATCH_UPDATE = OpenstackConstants.PROXY_URL + "/lb/l7policies/update_l7policy_batch";

    String LB_CERT_GROUP_CREATE = OpenstackConstants.PROXY_URL + "/lb/cert_groups";
    String LB_CERT_GROUP_DELETE = OpenstackConstants.PROXY_URL + "/lb/cert_groups/${cert_group_id}";
    String LB_CERT_GROUP_QUERY_ID = LB_CERT_GROUP_DELETE;
    String LB_CERT_GROUP_QUERY = LB_CERT_GROUP_CREATE;

    String LB_CERTIFICATE_CREATE = OpenstackConstants.PROXY_URL + "/lb/certificates";
    String LB_CERTIFICATE_UPDATE = OpenstackConstants.PROXY_URL + "/lb/certificates/${certificate_id}";
    String LB_CERTIFICATE_DELETE = LB_CERTIFICATE_UPDATE;
    String LB_CERTIFICATE_QUERY_ID = LB_CERTIFICATE_UPDATE;
    String LB_CERTIFICATE_QUERY = LB_CERTIFICATE_CREATE;

    String LB_CERT_MEMBER_CREATE = OpenstackConstants.PROXY_URL + "/lb/cert_members";
    String LB_CERT_MEMBER_UPDATE = OpenstackConstants.PROXY_URL + "/lb/cert_members/${cert_members_id}";
    String LB_CERT_MEMBER_DELETE = LB_CERT_MEMBER_UPDATE;
    String LB_CERT_MEMBER_QUERY_ID = LB_CERT_MEMBER_UPDATE;
    String LB_CERT_MEMBER_QUERY_GROUP_CERT = OpenstackConstants.PROXY_URL + "/lb/cert_members";
    String LB_CERT_MEMBER_QUERY = LB_CERT_MEMBER_CREATE;

    //创建共享带宽
    String BWS_CREATE = OpenstackConstants.PROXY_URL + "/bandwidthpackages";
    //共享带宽绑定EIP
    String BWS_ASSOCIATE = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${bwsId}/add_eip_into_bandwidthpackage";
    //共享带宽绑定EIP
    String BWS_BATCH_ASSOCIATE = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${bwsId}/add_eip_list_into_bandwidthpackage";
    //共享带宽解绑EIP
    String BWS_DISASSOCIATE = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${bwsId}/del_eip_from_bandwidthpackage";
    //查询共享带宽
    String BWS_DESCRIBE_SINGLE = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${singleId}";
    String BWS_DESCRIBE_BATCH = OpenstackConstants.PROXY_URL + "/bandwidthpackages";
    //更配共享带宽
    String BWS_MODIFY = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${uuid}";
    //删除共享带宽
    String BWS_DELETE = OpenstackConstants.PROXY_URL + "/bandwidthpackages/${uuid}";
    //创建证书
    String CERTIFICATE_CREATE= OpenstackConstants.PROXY_URL+"/lb/certificates";
    //查询证书
    String CERTIFICATE_DESCRIBE= OpenstackConstants.PROXY_URL+"/lb/certificates";
    //删除证书
    String CERTIFICATE_DELETE= OpenstackConstants.PROXY_URL+"/lb/certificates/${id}";
    //删除证书
    String CERTIFICATE_MODIFY= OpenstackConstants.PROXY_URL+"/lb/certificates/${id}";

    //CountAPI
    String COUNT_URL= OpenstackConstants.PROXY_URL+"/${machine}/${resource_class}/count";


    // DNS
    // dns - hostedzone
    String ZONE_CREATE = OpenstackConstants.PROXY_URL + "/domains";
    String ZONE_DELETE = OpenstackConstants.PROXY_URL + "/domains/${tenant_id}";
    String ZONE_QUERY_NAME = OpenstackConstants.PROXY_URL + "/domains?dn={name}";
    String ZONE_QUERY_ID = OpenstackConstants.PROXY_URL + "/domains/${id}";
    String ZONE_QUERY_LIST = OpenstackConstants.PROXY_URL + "/domains";
    String DOMAINS_COUNT = OpenstackConstants.PROXY_URL + "/domains_count";
    String QUOTA_HOSTEDZONE_DATA = OpenstackConstants.QUOTA_URL + "/quota/getQuotainfoByQuotaName";
    String QUOTA_HOSTEDZONE_INCR = OpenstackConstants.QUOTA_URL + "/quota/incrRtQuotaInfoByQuotaName";
    String CHANGE_RECORD_TYPE = OpenstackConstants.PROXY_URL+"/domains/${hosted_zone_id}/changertype";
    String ZONE_LOCK = OpenstackConstants.PROXY_URL + "/domains/${domain_id}/lock";
    String ZONE_UNLOCK = OpenstackConstants.PROXY_URL + "/domains/${domain_id}/unlock";
    String ZONE_RECORD_LOCK = OpenstackConstants.PROXY_URL + "/domains/${hostedZone_id}/records/${record_id}/lock";
    String ZONE_RECORD_UNLOCK = OpenstackConstants.PROXY_URL + "/domains/${hostedZone_id}/records/${record_id}/unlock";


    // dns - record
    String RECORD_CREATE = OpenstackConstants.PROXY_URL + "/domains/${hostedZone_id}/records";
    String RECORD_DELETE = OpenstackConstants.PROXY_URL
            + "/domains/${hostedZone_id}/records/${record_id}";
    String RECORD_QUERY = OpenstackConstants.PROXY_URL
            + "/domains/${hostedZone_id}/records/${record_id}";
    String RECORD_QUERY_List = OpenstackConstants.PROXY_URL + "/domains/${hostedZone_id}/records_data_search";
    String RECORD_UPDATE = OpenstackConstants.PROXY_URL
            + "/domains/${hostedZone_id}/records/${record_id}";
    String RECORD_GEOS = OpenstackConstants.PROXY_URL + "/geos";
    String RECORD_COUNT = OpenstackConstants.PROXY_URL + "/domains/${domain_id}/records_count";
    //TenantType
    String MANAGE_VPC_CREATE_TENANT_TYPE= OpenstackConstants.PROXY_URL+"/vpc/tenant_types";

    //Switch
    String MANAGE_SWITCH_QUERY= OpenstackConstants.PROXY_URL+"/vpc/switchs";

    //DC
    String MANAGE_DC_UPDATE= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces/${phyInterfaceId}";
    String MANAGE_DC_DELETE= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces/${phyInterfaceId}";

    //ACCOUNT->Tenant
    String ACCOUNT_TO_TENANT= OpenstackConstants.PROXY_URL+"/user?from";
    String IDENTITY_TYPE = "identity";

    String KEYSTONE_TOKEN= OpenstackConstants.KEYSTONE_URL+"/v2.0/tokens";
    String KEYSTONE_TENANT= OpenstackConstants.KEYSTONE_URL+"/v2.0/tokens";
    String KEYSTONE_ACCOUNT= OpenstackConstants.KEYSTONE_URL+"/v2.0/tenants/${tenantId}";

    String PEERING_ACCEPT = OpenstackConstants.PROXY_URL + "/vpc/peerings/${peering_id}/accept_peering";
    String PEERING_REJECT = OpenstackConstants.PROXY_URL + "/vpc/peerings/${peering_id}/reject_peering";
    String PEERING_MODIFY = OpenstackConstants.PROXY_URL + "/vpc/peerings/${peeringId}";

    String SECURITY_GROUP_RULE_COUNT = OpenstackConstants.PROXY_URL + "/vpc/vpc_securitygroups/${sg_uuid}/get_sg_rules_num";
    //product_shared
    String AUTH_USER_TAG_PRODUCT_SHARED="product_shared";
    String X_Auth_User_Tag="X-Auth-User-Tag";
    String DEFAULT_USER_TAG="console";
    String SYSTEM_SG_DEFAULT_NAME = "SystemSG";

    //az
    String AVAILABLE_ZONE= OpenstackConstants.PROXY_URL+"/os-availability-zone";

    //DirectConnect
    String VPC_DC_CREATE= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces";
    String VPC_DC_GET= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces";
    String VPC_DC_DELETE= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces/${directConnectId}/rm_phy_iface";
    String VPC_DC_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/phy_interfaces/${directConnectId}";
    String VPC_DC_INTERFACE_CREATE= OpenstackConstants.PROXY_URL+"/vpc/switch_interfaces";
    String VPC_DC_INTERFACE_GET= OpenstackConstants.PROXY_URL+"/vpc/switch_interfaces";
    String VPC_DC_INTERFACE_DELETE= OpenstackConstants.PROXY_URL+"/vpc/switch_interfaces/${directConnectInterfaceId}";
    String VPC_DC_INTERFACE_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/switch_interfaces/${directConnectInterfaceId}";
    String VPC_DC_GW_CREATE= OpenstackConstants.PROXY_URL+"/vpc/direct_connects";
    String VPC_DC_GW_GET= OpenstackConstants.PROXY_URL+"/vpc/direct_connects";
    String VPC_DC_GW_GET_ID= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}";
    String VPC_DC_GW_DELETE= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}";
    String VPC_DC_GW_ATTACH= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}/attach_switch_interface";
    String VPC_DC_GW_DETACH= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}/detach_switch_interface";
    String VPC_DC_GW_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}";
    String VPC_DC_GW_MODIFY_CIDR= OpenstackConstants.PROXY_URL + "/vpc/direct_connects/${directConnectGatewayId}/modify_direct_conn_cidr";
    String VPC_DC_GW_ADD= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}/add_switch_interface";
    String VPC_DC_GW_DEL= OpenstackConstants.PROXY_URL+"/vpc/direct_connects/${directConnectGatewayId}/del_switch_interface";
    String CREATE_BFD_METADATA = OpenstackConstants.PROXY_URL + "/vpc/bfd_metadatas";
    String GET_BFD_METADATA = CREATE_BFD_METADATA;
    String DEL_BFD_METADATA = GET_BFD_METADATA + "/${bfd_metadata_id}";
    String MODIFY_BFD_METADATA = DEL_BFD_METADATA;
    String CONFIG_DC_INTERFACE_RELIABILITY_METHOD = OpenstackConstants.PROXY_URL + "/vpc/switch_interfaces/${directConnectInterfaceId}/config_switch_interface_reliability_method";

    //dns-pl
    String DNS_PRIVATE_LINE_CREATE= OpenstackConstants.PROXY_URL+"/mylocation";
    String DNS_PRIVATE_LINE_QUERY= OpenstackConstants.PROXY_URL+"/mylocation_search";
    String DNS_PRIVATE_LINE_UPDATE= OpenstackConstants.PROXY_URL+"/mylocation/${privateLineId}";
    String DNS_PRIVATE_LINE_DELETE= OpenstackConstants.PROXY_URL+"/mylocation/${privateLineId}";
    String DNS_IP_RANGE_UPDATE= OpenstackConstants.PROXY_URL+"/mylocation/${privateLineId}/ipsegment";
    String DNS_IP_RANGE_QUERY= OpenstackConstants.PROXY_URL+"/mylocation/${privateLineId}/ipsegment_search";

    //vpn-senior
    String VPN_GW_CREATE= OpenstackConstants.PROXY_URL+"/vpc/vpn_vpc_gws";
    String VPN_GW_DELETE= OpenstackConstants.PROXY_URL+"/vpc/vpn_vpc_gws/${id}";
    String VPN_GW_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/vpn_vpc_gws/${id}";
    String VPN_GW_QUERY_ID= OpenstackConstants.PROXY_URL+"/vpc/vpn_vpc_gws/${gwId}";
    String VPN_GW_QUERY= OpenstackConstants.PROXY_URL+"/vpc/vpn_vpc_gws";

    String VPN_CUS_GW_CREATE= OpenstackConstants.PROXY_URL+"/vpc/vpn_customer_gws";
    String VPN_CUS_GW_DELETE= OpenstackConstants.PROXY_URL+"/vpc/vpn_customer_gws/${id}";
    String VPN_CUS_GW_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/vpn_customer_gws/${id}";
    String VPN_CUS_GW_QUERY= OpenstackConstants.PROXY_URL+"/vpc/vpn_customer_gws";

    String VPN_TUNNEL_CREATE= OpenstackConstants.PROXY_URL+"/vpc/vpn_tunnels";
    String VPN_TUNNEL_DELETE= OpenstackConstants.PROXY_URL+"/vpc/vpn_tunnels/${id}";
    String VPN_TUNNEL_MODIFY= OpenstackConstants.PROXY_URL+"/vpc/vpn_tunnels/${id}";
    String VPN_TUNNEL_QUERY_ID= OpenstackConstants.PROXY_URL+"/vpc/vpn_tunnels/${tunnelId}";
    String VPN_TUNNEL_QUERY= OpenstackConstants.PROXY_URL+"/vpc/vpn_tunnels";

    String MGROUP_CREATE = OpenstackConstants.PROXY_URL + "/lb/mgroups";
    String MGROUP_QUERY = MGROUP_CREATE;
    String MGROUP_DELETE = OpenstackConstants.PROXY_URL + "/lb/mgroups/${uuid}";
    String MGROUP_QUERY_BY_ID = MGROUP_DELETE;
    String MGROUP_UPDATE = MGROUP_DELETE;

    String GROUP_MEMBER_QUERY = OpenstackConstants.PROXY_URL + "/lb/group_members";
    String GROUP_MEMBER_DELETE = OpenstackConstants.PROXY_URL + "/lb/group_members/${id}";
    String GROUP_MEMBER_BATCH_DELETE = OpenstackConstants.PROXY_URL + "/lb/group_members/delete_group_members_by_id";
    String GROUP_MEMBER_CREATE = GROUP_MEMBER_QUERY;
    String GROUP_MEMBER_UPDATE = GROUP_MEMBER_DELETE;

    String HOST_HEADER_CREATE = OpenstackConstants.PROXY_URL + "/lb/servers";
    String HOST_HEADER_QUERY = OpenstackConstants.PROXY_URL + "/lb/servers";
    String HOST_HEADER_DELETE = OpenstackConstants.PROXY_URL + "/lb/servers/${id}";
    String HOST_HEADER_UPDATE = OpenstackConstants.PROXY_URL + "/lb/servers/${id}";

    String LOCATION_CREATE = OpenstackConstants.PROXY_URL + "/lb/locations";
    String LOCATION_UPDATE = OpenstackConstants.PROXY_URL + "/lb/locations/${id}";
    String LOCATION_DELETE = OpenstackConstants.PROXY_URL + "/lb/locations/${id}";
    String LOCATION_QUERY = OpenstackConstants.PROXY_URL + "/lb/locations";


    String NATPOOL_REDUCE_IP = OpenstackConstants.PROXY_URL + "/vpc/natpools/${natpool_id}/remove_natpool_natip";
    String NATPOOL_ADD_RESOURCE = OpenstackConstants.PROXY_URL + "/vpc/natpools/${natpool_id}/add_natpool_resource";
    String NATPOOL_REMOVE_RESOURCE= OpenstackConstants.PROXY_URL + "/vpc/natpools/${natpool_id}/remove_natpool_resource";

    String QUERY_NAT_NETWORK = OpenstackConstants.PROXY_URL + "/vpc/natnetworks";

    //common_id
    String COMMON_NEUTRON_QUERY= OpenstackConstants.PROXY_URL+"/${queryPrefix}/${instanceId}";
    String COMMON_NEUTRON_QUERY_BY= OpenstackConstants.PROXY_URL+"/${queryPrefix}?${by}=${instanceId}";

    // LB ACL RULE
    String LB_ACL_RULE_CREATE = OpenstackConstants.PROXY_URL + "/lb/slbaclrules";
    String BATCH_LB_ACL_RULE_DELETE = OpenstackConstants.PROXY_URL + "/lb/slbacls/${slbacl_id}";
    String LB_ACL_RULE_DELETE = OpenstackConstants.PROXY_URL + "/lb/slbaclrules/${slbacl_rule_id}";

    // LB ACL
    String LB_ACL_CREATE = OpenstackConstants.PROXY_URL + "/lb/slbacls";
    String LB_ACL_QUERY_ALL = OpenstackConstants.PROXY_URL + "/lb/slbacls";
    String LB_ACL_QUERY = OpenstackConstants.PROXY_URL + "/lb/slbacls/${slbacl_id}";
    String LB_ACL_UPDATE = OpenstackConstants.PROXY_URL + "/lb/slbacls/${slbacl_id}";
    String LB_ACL_DELETE = OpenstackConstants.PROXY_URL + "/lb/slbacls/${slbacl_id}";
    String LB_ACL_UPDATE_RULE = OpenstackConstants.PROXY_URL + "/lb/slbaclrules/${slbacl_rule_id}";

    //IPV4/6
    String IPV4="ipv4";
    String IPV6="ipv6";
    String IPALL="all";

    String NEUTRON_RESOURCE_PROCESS="NEUTRON_RESOURCE_PROCESS_NEW_";
    String NEUTRON_RESOURCE_NOTFOUND="NEUTRON_RESOURCE_NOTFOUND_";

    //IPV6_PUBLIC
    String IPV6_PUBLIC_QUERY = OpenstackConstants.PROXY_URL+"/vpc/vif_ipv6_publics";
    String IPV6_PUBLIC_QUERY_ID = OpenstackConstants.PROXY_URL+"/vpc/vif_ipv6_publics/${id}";
    String VIF_IPV6_QUERY = OpenstackConstants.PROXY_URL+"/vpc/vifs";
    String VIF_IPV6_QUERY_CONDITION= "detail=false&enabled_ipv6=true";
    String IPV6_PUBLIC_CREATE = OpenstackConstants.PROXY_URL+"/vpc/vif_ipv6_publics";

    //VPC_SUBNET
    String VPC_SUBNET = OpenstackConstants.PROXY_URL +"/vpc/vpc_subnets/${id}";
    String DHCP_AVAILABLE_COUNT = OpenstackConstants.PROXY_URL +"/vpc/dhcpips/count?vnet_id=${vnetId}&associated=false";


    //存量子网开通ipv6
    String ALLOCATE_SUBNET_IPV6 = OpenstackConstants.PROXY_URL + "/vpc/vnets/${subnetId}/vnet_enable_ipv6";

    String REGION_MAP_URL = OpenstackConstants.PROXY_URL + "/region-map";

    String SYNC_METADATA_REGION_MAP = "SYNC_METADATA_REGION_MAP";

    //route tables
    String CREATE_ROUTE_TABLE = OpenstackConstants.PROXY_URL + "/vpc/route_tables";
    String DELETE_ROUTE_TABLE = OpenstackConstants.PROXY_URL + "/vpc/route_tables/${id}";
    String UPDATE_ROUTE_TABLE = DELETE_ROUTE_TABLE;
    String QUERY_ROUTE_TABLE = CREATE_ROUTE_TABLE;
    String QUERY_ROUTE_TABLE_BY_ID = DELETE_ROUTE_TABLE;

    //custom route
    String CREATE_CUSTOM_TABLE = OpenstackConstants.PROXY_URL + "/vpc/custom_routes";
    String DELETE_CUSTOM_TABLE =  OpenstackConstants.PROXY_URL + "/vpc/custom_routes/${id}";
    String QUERY_CUSTOM_TABLE = CREATE_CUSTOM_TABLE;
    String QUERY_CUSTOM_TABLE_BY_ID = DELETE_CUSTOM_TABLE;

    //将默认路由表中的路由条目放到子路由表中
    String CHERRY_PICK_ROUTE = OpenstackConstants.PROXY_URL + "/vpc/route_tables/${id}/cherry_pick_route";

    //private_link 私网连接
    String LB_PRIVATE_LINK_CREATE = OpenstackConstants.PROXY_URL + "/lb/private_links";
    String LB_PRIVATE_LINK_QUERY_ALL = OpenstackConstants.PROXY_URL + "/lb/private_links";
    String LB_PRIVATE_LINK_QUERY = OpenstackConstants.PROXY_URL + "/lb/private_links/${id}";
    String LB_PRIVATE_LINK_UPDATE = OpenstackConstants.PROXY_URL + "/lb/private_links/${id}";
    String LB_PRIVATE_LINK_DELETE = OpenstackConstants.PROXY_URL + "/lb/private_links/${id}";

    String LB_PRIVATE_LINK_CLIENTS_CREATE = OpenstackConstants.PROXY_URL + "/lb/private_link_clients";
    String LB_PRIVATE_LINK_CLIENTS_QUERY_ALL = OpenstackConstants.PROXY_URL + "/lb/private_link_clients";
    String LB_PRIVATE_LINK_CLIENTS_QUERY = OpenstackConstants.PROXY_URL + "/lb/private_link_clients/${id}";
    String LB_PRIVATE_LINK_CLIENTS_DELETE = OpenstackConstants.PROXY_URL + "/lb/private_link_clients/${id}";

}
