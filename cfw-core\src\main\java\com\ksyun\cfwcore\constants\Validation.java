package com.ksyun.cfwcore.constants;

public interface Validation {

    // ID 36位，允许字母、数字、-
    String REGEX_ID = "^[a-zA-Z0-9-]{36}$";
    String REGEX_NUM = "^\\d{2,}$";

    // Name 128位，允许字母、中文、数字、-、_
    String REGEX_NAME = "^([\u4e00-\u9fa50-9a-zA-Z-.\\_\\\\、\\/\\(\\)]){1,128}$";
    String REGEX_NAME_64 = "^([\u4e00-\u9fa50-9a-zA-Z-.\\_\\\\、\\/\\(\\)]){1,64}$";

    // IP
    String REGEX_IP = "^(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[0-9]{1,2})(\\.(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[0-9]{1,2})){3}$";

	// 包年包月限制时长1-36
	String REGEX_MONTHLY_DURATION = "^[1-9]|[1-3][0-6]$";

    String REGEX_CHARGETYPE = "^(Monthly|Peak|Daily|Hourly|PrepaidByTime|PostpaidByTime|TrafficMonthly)$";
    String REGEX_LB_TYPE = "^(public|internal)$";
    String REGEX_ALB_VERSION = "^(standard|advanced|medium)$";
    String REGEX_ALB_IP_VERSION = "^(ipv6|ipv4)$";

    String REGEX_DESCRIPTION = "^([\u4e00-\u9fa50-9a-zA-Z-\\_\\\\、\\/\\(\\)]){0,128}$";
    // port portforward(1-65535)、其他port（0-65535）
    long PORT_MIN = 1;
    long ALB_PORT_MAX = 65500;
    // eip
    long PURCHASE_TIME_MIN = 1;

    //eip
    String REGEX_EIP_STATE = "^(start|stop)$";
    String REGEX_LB_STATE = "^(start|stop)$";
    String REGEX_LB_URL_RULE_LISTENER_SYNC = "^(on|off)$";
    String REGEX_LB_LISTENER_STATE = "^(start|stop)$";
    String REGEX_ALB_METHOD = "^(RoundRobin|LeastConnections)$";
    String REGEX_LB_SESSION_STATE = "^(start|stop)$";
    String REGEX_LB_COOKIE_TYPE = "^(ImplantCookie|RewriteCookie)$";
    String REGEX_LB_TSL_POLICY = "^(TlsCipherPolicy1.0|TlsCipherPolicy1.1|TlsCipherPolicy1.2|TlsCipherPolicy1.2-strict|TlsCipherPolicy1.2-most-strict-with1.3)$";
    String REGEX_ALB_ALB_RULE_TYPE = "^(domain|url|method|sourceIp)$";
    String REGEX_ALB_ALB_RULE_OLD_TYPE = "^(domain|url)$";

    String REGEX_KFW_CHARGETYPE = "^(Monthly|Daily)$";

    long HealthyThreshold_Min = 1;
    long HealthyThreshold_Max = 10;
    long SessionPersistencePeriod_Min = 1;
    long SessionPersistencePeriod_Max = 86400;
    long UnhealthyThreshold_Min = 1;
    long UnhealthyThreshold_Max = 10;
    long Timeout_Min = 1;
    long Timeout_Max = 3600;
    long Interval_Min = 1;
    long Interval_Max=3600;
    long Interval_Delay_Max = 1000;

    //page
    String REGEX_NEXT_TOKEN = "^(" + REGEX_ID + "|^[1-9]\\d*$)$";

    String REGEX_APPLICATION_PROTOCOL_TYPE = "^(HTTP|HTTPS|QUIC)$";

    String REGEX_ALB_RULE_GROUP_TYPE = "^(ForwardGroup|Redirect|FixedResponse|Rewrite)$";

    String REGEX_HOST_NAME = "^[_.a-zA-Z0-9-]{0,64}$";

    String REGEX_RS_GROUP_PROTOCOL = "^(HTTP|gRPC|HTTPS)$";

    String PATTERN_CEN_INSTANCE_TYPE = "^(Vpc|DirectConnectGateway)$";

    String REGEX_RULE_PATH = "^/[a-zA-Z0-9_.\\-/=\\?]{0,79}$";

    String REGEX_REDIRECT_STATUS_CODE = "^(301|302|303|307|308)$";

    String REGEX_SERVERGROUP_INSTANCE_TYPE = "^(Host|DirectConnect)$";

    String REGEX_SERVERGROUP_UPSTREAMKEEPALIVE = "^(adaptation|keepalive|shortconnection)$";

    String REGEX_MEMBER_GROUP_IP_VERSION = "^(ipv6|ipv4)$";

    long PORTFORWARD_MIN = 1;

    long PORTFORWARD_MAX = 65535;

    String FIXED_RESPONSE_CONFIG_CONTENT = "[^\u4E00-\u9FA5]+";

    String FIXED_RESPONSE_CONFIG_CONTENT_TYPE = "^(text/plain|text/css|text/html|application/javascript|application/json)";

    String FIXED_RESPONSE_CONFIG_Http_Code = "^[0-9]*$";

    long HEALTH_CHECK_CONNECT_PORT_MIN = 1;
    long HEALTH_CHECK_CONNECT_PORT_MAX = 65535;
    String HEALTH_CHECK_PROTOCOL = "^(TCP|HTTP)$";
    String REGEX_LB_HEALTH_HHTP_METHOD = "^(GET|HEAD)$";

    String REWRITE_CONFIG_CONTENT = "^.{1,64}$";

    String REGEX_STATUS = "^(start|stop)$";
}
