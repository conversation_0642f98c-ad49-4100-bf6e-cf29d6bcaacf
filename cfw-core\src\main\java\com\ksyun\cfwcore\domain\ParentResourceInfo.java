package com.ksyun.cfwcore.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: hueason
 * @date: 2021/10/26 15:16
 * @description:
 */
@Data
@EqualsAndHashCode
public class ParentResourceInfo implements Cloneable {
    private String resourceName;
    private String queryPrefix;
    private String endpointType;
    private String by;
    private String instanceId;
    private String resourceType;
    private String parentIdName;

    @Override
    public Object clone() {
        ParentResourceInfo info = null;
        try {
            info = (ParentResourceInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return info;
    }
}
