package com.ksyun.cfwcore.domain;

import com.ksyun.cfwcore.openstack.OpenstackConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by xuyaming on 2016/10/8.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryCountParam {
    private String              machine;
    private String              resource_class;
    private Map<String, String> param;
    private String              keystoneType = OpenstackConstants.NEUTRON_ENDPOINT_TYPE;

    public QueryCountParam(String machine, String resource_class, Map<String, String> param) {
        this.machine = machine;
        this.resource_class = resource_class;
        this.param = param;
    }
}
