package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum ChargeType {
    /**
     * 此处计费方式为最初定义模式，之后定义与当前的映射关系在Apollo中配置，配置项为charge.type.mapping
     */
    Monthly(1, "Monthly"), Hourly(2, "Hourly"), Peak(3,
            "Peak"), Amount(4, "Amount"), Daily(5, "Daily"), PayByPeakPerDay(
            6, "DailyPeak"), BandwidthHourly(80, "BandwidthHourly"), TrafficHourly(8, "TrafficHourly"),
    TrafficMonthly(704, "TrafficMonthly"), RegionPeak(83, "RegionPeak"), PrepaidByTime(801, "PrepaidByTime"),
    PostpaidByTime(805, "PostpaidByTime"), HourlySettlement(84, "HourlySettlement"),
    HourlyInstantSettlement(87, "HourlyInstantSettlement"), PostPaidByAdvanced95Peak(807, "PostPaidByAdvanced95Peak"),
    DailyPaidByTransfer(86, "DailyPaidByTransfer"), PrePaidByHourUsage(809, "PrePaidByHourUsage");

    private final Integer value;

    private final String name;

    ChargeType(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getName(Integer value) {
        return Arrays.stream(ChargeType.values())
                .filter(chargeType -> chargeType.getValue().equals(value))
                .findFirst()
                .map(ChargeType::getName)
                .orElse(null);
    }

    public static Integer getValue(String name) {
        return Arrays.stream(ChargeType.values())
                .filter(chargeType -> chargeType.getName().equals(name))
                .findFirst()
                .map(ChargeType::getValue)
                .orElse(null);
    }

}