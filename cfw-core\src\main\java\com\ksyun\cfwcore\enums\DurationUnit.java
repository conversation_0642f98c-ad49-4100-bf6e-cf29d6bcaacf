package com.ksyun.cfwcore.enums;

import lombok.Getter;

@Getter
public enum DurationUnit {
	
	Monthly(1, "月"), Hourly(2, "小时"), Peak(3, "月"), Daily(5, "日"),DailyPeak(6,"日");
	
	private final Integer billType;
	private final String unit;

	DurationUnit(Integer billType, String unit) {
		this.billType = billType;
		this.unit = unit;
	}

	public static String getUnitByBillType(Integer billType) {
		switch (billType) {
			case 1 :
				return DurationUnit.Monthly.unit;
			case 3 :
				return DurationUnit.Peak.unit;
			case 5 :
				return DurationUnit.Daily.unit;
			case 6 :
				return DurationUnit.DailyPeak.unit;
			default :
				return DurationUnit.Monthly.unit;
		}
	}
}
