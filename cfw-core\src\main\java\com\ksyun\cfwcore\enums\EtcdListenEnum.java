package com.ksyun.cfwcore.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum EtcdListenEnum {
    /**
     * 防火墙启动
     */
    FIREWALL_START_CALLBACK("/firewall/callback/start/"),
    /**
     * 变更操作
     */
    FIREWALL_CLUSTER_CALLBACK("/firewall/cluster/callback"),
    /**
     * 集群操作
     */
    FIREWALL_COMMAND_CALLBACK("/firewall/command/callback"),
    /**

    /**
     * 集群状态上报
     */
    FIREWALL_UPLOAD_STATUS("/firewall/upload");
    private String key;

    EtcdListenEnum(String key) {
        this.key = key;
    }
}
