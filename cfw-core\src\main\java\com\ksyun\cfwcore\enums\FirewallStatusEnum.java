package com.ksyun.cfwcore.enums;

import lombok.Getter;

@Getter
public enum FirewallStatusEnum {
    CREATEING(1, "创建中"),
    RUNNING(2, "运行中"),
    RECONFIGURING(3, "更配中"),
    STOPED(4, "关停"),
    ERROR(5, "异常"),
    UNSUBSCRIBING(6, "退订中");
    private Integer status;
    private String desc;

    FirewallStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
