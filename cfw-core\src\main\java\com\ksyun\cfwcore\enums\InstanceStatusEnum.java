package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum InstanceStatusEnum {
    CREATING(1, "创建中", false),
    RUNNING(2, "已开通", false),
    FAILED(3, "开通失败", true),
    EXPIRED(4, "已过期(到期关停)", false),
    REMOVED(5, "已回收(到期回收)", true),
    REFUNDED(6, "已退订", true),
    DELETED(7, "已删除", true),
    ARREARS(8, "已欠费（欠费关停）", false),
    ARREARS_REMOVED(9, "欠费回收", true),
    ONE_KEY_EXPIRED(10, "一键关停", false),
    ONE_KEY_REMOVED(11, "一键回收", true),
    REFUNDING(12, "退订中", false);

    /**
     * 实例状态
     */
    private final Integer status;
    /**
     * 状态说明
     */
    private final String comment;
    /**
     * 是否是订单最终状态
     */
    private final boolean isFinal;

    /**
     * wiki地址：http://wiki.op.ksyun.com/pages/viewpage.action?pageId=18537488
     */
    InstanceStatusEnum(Integer status, String comment, boolean isFinal) {
        this.status = status;
        this.comment = comment;
        this.isFinal = isFinal;
    }

    /**
     * 校验实例状态是否是最终状态，若是则不需要再次退订
     */
    public static Boolean checkIsFinal(Integer status) {
        return Arrays.stream(InstanceStatusEnum.values())
                .filter(instanceStatus -> instanceStatus.getStatus().intValue() == status.intValue())
                .findFirst()
                .map(InstanceStatusEnum::isFinal)
                .orElse(false);
    }
}