package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/7 17:25
 */
@Getter
public enum LocalLanguageEnum {

    ZH("zh", Locale.CHINESE, true),
    EN("en", Locale.ENGLISH, false),
    KO("ko", Locale.KOREA, false);

    private final String code;

    private final Locale name;

    private final boolean isInUse;// 国际化是否已开始使用

    LocalLanguageEnum(String code, Locale name, boolean isInUse) {
        this.code = code;
        this.name = name;
        this.isInUse = isInUse;
    }

    /**
     * 根据前端提供Cookie：ksc_lang获取国际化语种，默认返回中文
     */
    public static Locale getLanguageByCode(String code) {
        return Arrays.stream(LocalLanguageEnum.values())
                .filter(l -> l.getCode().equals(code))
                .findFirst()
                .map(LocalLanguageEnum::getName)
                .orElse(Locale.CHINESE);
    }

    public static String getLanguageByName(Locale name) {
        return Arrays.stream(LocalLanguageEnum.values())
                .filter(l -> l.getName().equals(name))
                .findFirst()
                .map(LocalLanguageEnum::getCode)
                .orElse("zh");
    }

    /**
     * 获取所有国际化使用的语言Locale
     *
     * @return
     */
    public static List<Locale> getIsInUseLocal() {
        List<Locale> localeList = new ArrayList<>();
        Arrays.stream(LocalLanguageEnum.values())
                .filter(_l -> _l.isInUse)
                .forEach(_l -> localeList.add(_l.getName()));
        return localeList;
    }
}
