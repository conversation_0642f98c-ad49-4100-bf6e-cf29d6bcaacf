package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.Arrays;

@Getter
public enum NotifySubOrderResult {

    SUCCESS(1, "成功"), FAIL(2, "失败"), UPDATE(3, "更新实例");

    private final int value;
    private final String name;

    NotifySubOrderResult(int value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据status的大写匹配返回value
     *
     * @param status status支持 success/fail/update大小写
     * @return
     */
    public static Integer getStatusValueByString(String status) {
        return Arrays.stream(NotifySubOrderResult.values())
                .filter(notfySubOrderResult -> notfySubOrderResult.toString().equals(status.toUpperCase()))
                .findFirst()
                .map(NotifySubOrderResult::getValue)
                .orElse(null);
    }
}
