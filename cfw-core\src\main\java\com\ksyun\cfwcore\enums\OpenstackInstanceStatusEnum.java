package com.ksyun.cfwcore.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * @author: hueason
 * @date: 2021/7/27 17:18
 * @description:
 */
@Getter
public enum OpenstackInstanceStatusEnum {

    PDNS(ResourceType.PDNS.getProductLine(), "engine_cluster_status", "ACTIVE", "ERROR", Lists.newArrayList("BUILDING", "DELETING")),
    SLB(ResourceType.SLB.getProductLine(), "status", "active", "error", Lists.newArrayList("building", "deleting"));

    private String resourceType;
    private String statusName;
    private String succ;
    private String error;
    private List<String> middleState;

    OpenstackInstanceStatusEnum(String resourceType, String statusName, String succ, String error, List<String> middleState) {
        this.resourceType = resourceType;
        this.statusName = statusName;
        this.succ = succ;
        this.error = error;
        this.middleState = middleState;
    }

    public static String getStatusName(String resourceType) {
        for (OpenstackInstanceStatusEnum statusEnum : OpenstackInstanceStatusEnum.values()) {
            if (statusEnum.getResourceType().equals(resourceType)) {
                return statusEnum.getStatusName();
            }
        }
        return null;
    }

    public static String getSuccStr(String resourceType) {
        for (OpenstackInstanceStatusEnum statusEnum : OpenstackInstanceStatusEnum.values()) {
            if (statusEnum.getResourceType().equals(resourceType)) {
                return statusEnum.getSucc();
            }
        }
        return null;
    }

    public static String getErrorStr(String resourceType) {
        for (OpenstackInstanceStatusEnum statusEnum : OpenstackInstanceStatusEnum.values()) {
            if (statusEnum.getResourceType().equals(resourceType)) {
                return statusEnum.getError();
            }
        }
        return null;
    }

}
