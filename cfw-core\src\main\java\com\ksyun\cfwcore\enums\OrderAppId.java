package com.ksyun.cfwcore.enums;

import lombok.Getter;

@Getter
public enum OrderAppId {
    EIP("102.openapi", "弹性IP"),
    SLB("105.openapi", "负载均衡"),
    BWS("113.openapi", "共享带宽"),
    PEERING("130.openapi", "对等连接"),
    VPN_GATEWAY("144.openapi", "VPN网关"),
    NAT("112.openapi", "NAT"),
    APPLICATION_SLB("105.openapi", "应用型负载均衡"),
    PDNS("251.openapi", "内网DNS"),
    CEN("273.openapi", "云企业网"),
    KFW("317.openapi", "云防火墙"),
    ;

    private final String value;

    private final String name;

    OrderAppId(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
