package com.ksyun.cfwcore.enums;

import com.ksyun.cfwcore.constants.Constants;
import lombok.Getter;

@Getter
public enum OrderSource {

    UN_KNOW(0, "未知"),
    CONSOLE(1, Constants.CONSOLE_SOURCE),
    OPCENTER(2, Constants.OPCENTER_SOURCE),
    OPENAPI(3, Constants.SDK_SOURCE);

    private final int value;
    private final String name;

    OrderSource(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Integer getValue(String name) {
        switch (name) {
            case Constants.OPCENTER_SOURCE:
                return OrderSource.OPCENTER.value;
            case Constants.SDK_SOURCE:
                return OrderSource.OPENAPI.value;
            default:
                return OrderSource.CONSOLE.value;
        }
    }
}
