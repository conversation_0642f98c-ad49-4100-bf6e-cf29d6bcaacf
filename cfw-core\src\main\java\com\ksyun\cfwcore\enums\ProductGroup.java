package com.ksyun.cfwcore.enums;

import lombok.Getter;

@Getter
public enum ProductGroup {
	VM(100, "云主机"),
	EIP(102,"弹性IP"),
	<PERSON><PERSON>(109,"Kad组"),
	<PERSON>(112, "网关"),
	WAF(118,"WAF防火墙"),
	KCM(120,"证书"),
    KKMS(159, "密钥管理服务"),
	KSM(196, "云安全管理中心"),
	BRI(204, "业务风险情报"),
	KEAD(208, "高防弹性IP(防护包)"),
	KPT(258, "渗透测试"),
	SLB(105, "负载均衡"),
	BWS(113, "共享带宽"),
	VPN(144, "隧道网关"),
	PEERING(130, "对等连接"),
	KHS(245, "服务器安全"),
	ERS(261, "应急响应"),
	KBH(302, "云堡垒机"),
	KFW(317, "云防火墙"),

	;

	private int value;
	private String name;

	ProductGroup(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public static String getNameByValue(Integer value) {
		for (ProductGroup productGroup : ProductGroup.values()) {
			if (productGroup.getValue() == value) {
				return productGroup.getName();
			}
		}
		return null;
	}

	/**
	 * 根据枚举名称获取ProductGroupId
	 * */
	public static Integer getValueByName(String name) {
		for (ProductGroup productGroup : ProductGroup.values()) {
			if (productGroup.name().equalsIgnoreCase(name)) {
				return productGroup.getValue();
			}
		}
		return null;
	}

	/**
	 * 判断是否为安全的产品组
	 * */
	public static boolean isSecProductGroup(int productGroup) {
		if (KKMS.getValue() == productGroup || Kad.getValue() == productGroup || WAF.getValue() == productGroup
				|| KCM.getValue() == productGroup) {
			return true;
		}
		return false;
	}
}
