package com.ksyun.cfwcore.enums;

import lombok.Getter;

/**
 * 商品类型
 *
 * <AUTHOR>
 */
@Getter
public enum ProductType {

    SLB(5, "负载均衡"),
    EIP(9, "弹性IP"),
    NAT(49, "NAT"),
    BWS(50, "共享带宽"),
    PEERING(51, "对等连接"),
    VPN_GATEWAY(106, "VPN网关"),
    NONE(-1, "NONE"),
    IN_EIP(10, "弹性IP"),
    APPLICATION_SLB(117, "应用型负载均衡"),
    APPLICATION_SLB_NEW(636, "应用型负载均衡(新)"),
    KCE_EIP(130, "弹性IP"),
    KCE_SLB(131, "负载均衡"),
    IPV6_EIP(170, "弹性IP"),
    IPV6PUB(177, "弹性IP"),
    PDNS(476, "内网DNS"),
    CEN(567, "云企业网"),
    KFW(1147,"云防火墙"),
    ;

    private final int value;
    private final String name;

    ProductType(int value, String name) {
        this.value = value;
        this.name = name;
    }

}
