package com.ksyun.cfwcore.enums;

import lombok.Getter;

@Getter
public enum ResourceType {
    Default("", "", false),
    EIP("EIP", "floatingips", true),
    BATCH_EIP("EIP", "floatingips", true),
    BWS("BWS", "bandwidthpackages", true),
    SLB("SLB", "lb/pools", true),
    VIF("VIF", "vpc/vifs", false),
    Peering("PEERING", "vpc/peerings", true),
    NAT("NAT", "vpc/natpools", true),
    VPN_GATEWAY("VPN_GATEWAY", "vpc/vpn_vpc_gws", true),
    VNET("VNET", "vpc/vnets", false),
    VPN_TUNNEL("VPN_TUNNEL", "vpc/vpn_tunnels", false),
    DIRECT_CONNECT_GATEWAY("DIRECT_CONNECT_GATEWAY", "vpc/direct_connects", false),
    DIRECT_CONNECT("DIRECT_CONNECT","vpc/direct_connects",false),
    SWITCH_INTERFACE("SWITCH_INTERFACE","vpc/switch_interfaces",false),
    DIRECT_CONN_ROUTE("DIRECT_CONN_ROUTE", "vpc/direct_conn_routes", false),
    SWITCH_INTERFACE_ROUTE("SWITCH_INTERFACE_ROUTE","hc/switch_interface_routes",false),
    BATCH_SWITCH_INTERFACE_ROUTE("SWITCH_INTERFACE_ROUTE", "hc/switch_interface_routes", false),
    VPC("VPC", "vpc/domains", false),
    ROUTE("ROUTE", "vpc/routes", false),
    VPC_SG("VPC_SG", "vpc/vpc_securitygroups", false),
    VIF_IPV6("VIF_IPV6", "vpc/vif_ipv6_publics", true),
    PDNS("PDNS", "v1.0/instances", true),
    PDNS_ZONE("PDNS_NAME", "v1.0/zones", true),
    CEN_BANDWIDTH_PACKAGE("CEN_PKG", "v2.0/cen/cen_bandwidth_packages", true),
    SLB_PLS("SLB_PLS", "lb/private_links", true),
    SLB_PLC("SLB_PLC", "lb/private_link_clients", true),
    KFW("KFW", "lb/private_link_clients", true);

    private String productLine;

    private String type;

    private boolean hasOrder;

    ResourceType(String productLine, String type, boolean hasOrder) {
        this.productLine = productLine;
        this.type = type;
        this.hasOrder = hasOrder;
    }

    /**
     * 根据产品线判断是否是计费资源
     */
    public static boolean hasOrderByProductLine(String productLine) {
        if (productLine != null) {
            for (ResourceType resourceType : ResourceType.values()) {
                if (resourceType.getProductLine().equals(productLine)) {
                    return resourceType.isHasOrder();
                }
            }
        }
        return true; // true则不同步自搭建的ES
    }
}
