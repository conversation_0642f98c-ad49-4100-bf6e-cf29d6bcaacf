package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * Created by xuyaming on 2018/8/21.
 */
@Getter
public enum ResourceTypePrefix {

    VIF(ResourceType.VIF, "vif-");

    private final ResourceType type;
    private final String prefix;

    ResourceTypePrefix(ResourceType type, String prefix) {
        this.type = type;
        this.prefix = prefix;
    }

    /**
     * 根据资源类型返回前缀，如无返回""
     */
    public static String getPrefix(ResourceType resourceType) {
        return Arrays.stream(ResourceTypePrefix.values())
                .filter(resourceTypePrefix -> resourceTypePrefix.getType().equals(resourceType))
                .findFirst()
                .map(ResourceTypePrefix::getPrefix)
                .orElse("");
    }
}
