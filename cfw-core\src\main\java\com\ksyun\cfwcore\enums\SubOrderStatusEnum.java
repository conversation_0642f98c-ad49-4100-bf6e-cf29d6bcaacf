package com.ksyun.cfwcore.enums;

import lombok.Getter;

/**
 * @author: hueason
 * @date: 2021/7/28 10:57
 * @description:
 */
@Getter
public enum SubOrderStatusEnum {
    /**
     * https://wiki.op.ksyun.com/pages/viewpage.action?pageId=18537488#id-%E3%80%90%E9%87%8D%E8%A6%81%E3%80%91%E5%AE%9E%E4%BD%93%E7%BB%93%E6%9E%84-SubOrder.status%EF%BC%9A%E5%AD%90%E8%AE%A2%E5%8D%95%E7%8A%B6%E6%80%81
     */

    CREATING(1, "处理中"), SUCC(2, "成功"), FAIL(3, "失败");

    private Integer status;
    private String comment;

    SubOrderStatusEnum(Integer status, String comment) {
        this.status = status;
        this.comment = comment;
    }
}
