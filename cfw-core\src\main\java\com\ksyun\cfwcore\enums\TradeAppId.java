package com.ksyun.cfwcore.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * Created by xuyaming on 18/1/13.
 */
@Getter
public enum TradeAppId {

    SLB(1001, "负载均衡"),
    EIP(1002, "弹性IP"),
    Nat(1003, "网关"),
    BWS(1004, "共享带宽"),
    PEERING(1005, "对等连接"),
    VPN(1006, "隧道网关"),
    IPV6PUB(1007, "网卡IPV6"),
    PDNS(6002, "内网DNS"),
    CEN(1009, "云企业网"),
    ALB(1013, "应用型负载均衡"),
    KFW(4014,"云防火墙"),
    ;

    private final Integer value;

    private final String name;

    TradeAppId(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static String getNameByValue(Integer value) {
        return Arrays.stream(TradeAppId.values())
                .filter(tradeAppId -> tradeAppId.getValue().equals(value))
                .findFirst()
                .map(TradeAppId::getName)
                .orElse(null);
    }

    public static Integer getValueByName(String name) {
        return Arrays.stream(TradeAppId.values())
                .filter(tradeAppId -> tradeAppId.getName().equals(name))
                .findFirst()
                .map(TradeAppId::getValue)
                .orElse(null);
    }

    public static String getStrByValue(Integer value) {
        return Arrays.stream(TradeAppId.values())
                .filter(tradeAppId -> tradeAppId.getValue().equals(value))
                .findFirst()
                .map(TradeAppId::toString)
                .orElse(null);
    }
}