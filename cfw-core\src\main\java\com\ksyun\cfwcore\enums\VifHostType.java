package com.ksyun.cfwcore.enums;

/**
 * Created by xuyaming on 2017/3/13.
 */
public enum VifHostType {
    vm("vm", "Kec"), physical("physical", "Epc"), docker("docker", "Kci"),smartnic("smartnic", "Kec");

    private String name;
    private String alias;

    VifHostType(String name, String alias) {
        this.name = name;
        this.alias = alias;
    }

    public String getName() {
        return name;
    }

    public String getAlias() {
        return alias;
    }

    public static String getAlias(String name) {
        for (VifHostType hostType : VifHostType.values()) {
            if (hostType.getName().equals(name)) {
                return hostType.getAlias();
            }
        }
        return null;
    }
}
