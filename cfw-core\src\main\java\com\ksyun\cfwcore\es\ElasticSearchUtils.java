package com.ksyun.cfwcore.es;

import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.config.ElasticSearchConfig;
import com.ksyun.cfwcore.config.RegionConfig;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.domain.ChargeComment;
import com.ksyun.cfwcore.enums.ChargeType;
import com.ksyun.cfwcore.es.domain.*;
import com.ksyun.cfwcore.es.enums.EsOperation;
import com.ksyun.cfwcore.es.handle.AfterEsQueryHandle;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.openstack.domain.OpenStackLink;
import com.ksyun.cfwcore.openstack.domain.ProjectResource;
import com.ksyun.comm.component.GsonUtils;
import com.ksyun.comm.util.JsonBinder;
import com.ksyun.common.http.ObjectPlaceholderResolver;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Log4j2
@Component
public class ElasticSearchUtils {

    /**
     * 接口文档：https://wiki.op.ksyun.com/pages/viewpage.action?pageId=65426878
     */

    private ElasticSearchConfig elasticSearchConfig;

    protected PropertyPlaceholderHelper placeholderHelper;

    protected RestTemplate restTemplate;

    private GsonUtils gsonUtils;

    private RegionConfig regionConfig;

    private CommonConfig commonConfig;

    private JsonBinder jsonBinder = JsonBinder.buildNormalBinder(false);

    @Autowired
    public void setElasticSearchConfig(ElasticSearchConfig elasticSearchConfig) {
        this.elasticSearchConfig = elasticSearchConfig;
    }

    @Autowired
    public void setPlaceholderHelper(PropertyPlaceholderHelper placeholderHelper) {
        this.placeholderHelper = placeholderHelper;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Autowired
    public void setGsonUtils(GsonUtils gsonUtils) {
        this.gsonUtils = gsonUtils;
    }

    @Autowired
    public void setRegionConfig(RegionConfig regionConfig) {
        this.regionConfig = regionConfig;
    }

    @Autowired
    public void setCommonConfig(CommonConfig commonConfig) {
        this.commonConfig = commonConfig;
    }


    /**
     * ES 计数查询
     */
    public ElasticSearchCountQueryResponse count(ElasticSearchQueryParam param) {
        String url = placeholderHelper.replacePlaceholders(EsConstants.ES_COUNT,
                new ObjectPlaceholderResolver(elasticSearchConfig, InnerAPIHolder.getProxyAuth()));
        HttpEntity<?> entity = generateHttpEntity(param);
        log.info("[POST] {} with param {}", url, gsonUtils.getCommonGson().toJson(param));
        ResponseEntity<ElasticSearchCountQueryResponse> result = restTemplate.
                exchange(url, HttpMethod.POST, entity, ElasticSearchCountQueryResponse.class);
        log.info("[POST] {} response {}", url, result);
        return result.getBody();
    }

    /**
     * 聚合查询
     */
    public ElasticSearchAggregationCountQueryResponse aggregationCount(ElasticSearchQueryParam param, String groupBy,
                                                                       Integer size) {
        String url = placeholderHelper.replacePlaceholders(EsConstants.ES_AGG_COUNT,
                new ObjectPlaceholderResolver(elasticSearchConfig, InnerAPIHolder.getProxyAuth()));
        ElasticSearchQueryParam elasticSearchQueryParam = (ElasticSearchQueryParam) param.clone();
        elasticSearchQueryParam.setGroupByFiled(ElasticSearchQueryParam.Field.builder().type(EsConstants.TYPE_EXTENSION).size(size).field(groupBy).build());
        elasticSearchQueryParam.setFrom(null);
        elasticSearchQueryParam.setSize(null);
        elasticSearchQueryParam.setSort(null);
        HttpEntity<?> entity = generateHttpEntity(elasticSearchQueryParam);
        log.info("[POST] {} with param {}", url, gsonUtils.getCommonGson().toJson(elasticSearchQueryParam));
        ResponseEntity<ElasticSearchAggregationCountQueryResponse> result = restTemplate.
                exchange(url, HttpMethod.POST, entity, ElasticSearchAggregationCountQueryResponse.class);
        log.info("[POST] {} response {}", url, result);
        return result.getBody();
    }

    /**
     * 查询ES
     */
    @SafeVarargs
    @SuppressWarnings("unchecked")
    public final <T> ElasticSearchQueryResponse<T> query(ElasticSearchQueryParam param, Class<T> clazz,
                                                         AfterEsQueryHandle<T>... handle) {
        try {
            if (InnerAPIHolder.getCountFlag() != null && InnerAPIHolder.getCountFlag()) {
                //计数器查询
                ElasticSearchCountQueryResponse elasticSearchCountQueryResponse = this.count(param);
                ElasticSearchQueryResponse<T> response = new ElasticSearchQueryResponse<>(elasticSearchCountQueryResponse);
                InnerAPIHolder.addResourceCount(elasticSearchCountQueryResponse.getData().getTotal());
                return response;
            } else if (StringUtils.isNotBlank(InnerAPIHolder.getAggFlag())) {
                //聚合查询
                ElasticSearchAggregationCountQueryResponse elasticSearchAggregationCountQueryResponse =
                        this.aggregationCount(param, InnerAPIHolder.getAggFlag(), InnerAPIHolder.getAggSize());
                ElasticSearchQueryResponse<T> response =
                        new ElasticSearchQueryResponse<>(elasticSearchAggregationCountQueryResponse);
                InnerAPIHolder.addAggList(elasticSearchAggregationCountQueryResponse.getData());
                return response;
            } else {
                String url = placeholderHelper.replacePlaceholders(EsConstants.ES_QUERY,
                        new ObjectPlaceholderResolver(elasticSearchConfig, InnerAPIHolder.getProxyAuth()));
                HttpEntity<?> entity = generateHttpEntity(param);
                log.info("[POST] {} with param {}", url, gsonUtils.getCommonGson().toJson(param));
                ResponseEntity<String> result = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
                log.info("[POST] {} response", url, result);
                ElasticSearchQueryResponse<T> response = jsonBinder.fromJson(result.getBody(),
                        ElasticSearchQueryResponse.class,
                        jsonBinder.getCollectionType(ElasticSearchQueryResponse.class, clazz));
                if (CollectionUtils.isNotEmpty(response.getData().getItems())) {
                    for (ElasticSearchQueryResponse.Item<T> item : response.getData().getItems()) {
                        T t = item.getExtension();
                        if (t instanceof ProjectResource) {
                            ((ProjectResource) t).processAfterQuery(param);
                        }
                    }
                }
                if (handle != null && handle.length == 1) {
                    handle[0].process(this, elasticSearchConfig, param, response);
                }
                return response;
            }

        } catch (Exception e) {
            log.error("ES查询失败" + e.getMessage(), e);
            throw new OpenAPIException(ErrorCode.QueryEsResourceError, ErrorCode.QueryEsResourceError,
                    HttpStatus.BAD_REQUEST);
        }

    }


    /**
     * 通用查询
     */
    public ElasticSearchQueryResponse<Map> commonQuery(ProxyAuth auth, CommonQueryParam param) {
        ElasticSearchQueryParam elasticSearchQueryParam = ElasticSearchQueryParam.builder()
                .from(param.getFrom()).size(param.getSize()).productLine(param.getProductLine())
                .build();
        List<ElasticSearchQueryParam.Query> queries = new ArrayList<>();
        List<ElasticSearchQueryParam.Sort> sorts = Lists.newArrayList();
        queries.add(ElasticSearchQueryParam.Query.builder().field("userId").type(EsConstants.TYPE_BASE)
                .op(EsOperation.EQ.getOp()).value(auth.getAccount_id()).build());
        /**默认查询，按照机房查询，如果false，则可以支持多机房查询*/
        if (param.getUseDefault() == null || param.getUseDefault()) {
            String region = auth.getRegion();
            List<String> regions = Lists.newArrayList(region);
            if (region != null && StringUtils.isNotEmpty(regionConfig.getRegionCodeCache().get(region))) {
                regions.add(regionConfig.getRegionCodeCache().get(region));
            }
            queries.add(ElasticSearchQueryParam.Query.builder().field("region").type(EsConstants.TYPE_BASE)
                    .op(EsOperation.IN.getOp()).value(regions).build());
            queries.add(ElasticSearchQueryParam.Query.builder().field("deleteStatus").type(EsConstants.TYPE_BASE)
                    .op(EsOperation.EQ.getOp()).value(0).build());
        }
        if (MapUtils.isNotEmpty(param.getQueryMap())) {
            for (Map.Entry<String, Object> entry : param.getQueryMap().entrySet()) {
                ElasticSearchQueryParam.Query rangeQueryParam = null;
                List<ElasticSearchQueryParam.Query> rangeQueryParamList = null;
                ElasticSearchQueryParam.Query _query = null;
                ElasticSearchQueryParam.Sort sort = null;
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value instanceof String) {
                    if (key.startsWith("Ex_Range.") || key.startsWith("Ba_Range.")) {
                        try {
                            rangeQueryParam = jsonBinder.fromJson((String) value, ElasticSearchQueryParam.Query.class);
                        } catch (Exception e) {
                            try {
                                rangeQueryParamList = (List<ElasticSearchQueryParam.Query>) jsonBinder.fromJson((String) value, ElasticSearchQueryParam.Query.class);
                            } catch (Exception exception) {
                                log.error("ES查询Range操作参数格式错误，json转换失败");
                                throw new OpenAPIException("ES查询Range操作参数格式错误，json转换失败", exception.getMessage(), HttpStatus.BAD_REQUEST);
                            }
                        }
                    }

                    //暂时只支持in、not_in、eq、not_eq、range
                    if (key.startsWith("Ex")) {
                        if (key.startsWith("Ex.")) {
                            _query = ElasticSearchQueryParam.Query.builder().op(EsOperation.EQ.getOp()).type(EsConstants.TYPE_EXTENSION)
                                    .field(key.replace("Ex.", "")).value(value).build();
                        } else if (key.startsWith("Ex_Not.")) {
                            _query = ElasticSearchQueryParam.Query.builder().op(EsOperation.NOT_EQ.getOp()).type(EsConstants.TYPE_EXTENSION)
                                    .field(key.replace("Ex_Not.", "")).value(value).build();
                        } else if (key.startsWith("Ex_Range.")) {
                            if (rangeQueryParam != null) {
                                _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ex_Range.", "")).type(EsConstants.TYPE_EXTENSION)
                                        .op(EsOperation.RANGE.getOp()).from(rangeQueryParam.getFrom()).to(rangeQueryParam.getTo())
                                        .includeLower(rangeQueryParam.getIncludeLower() == null ? true : rangeQueryParam.getIncludeLower())
                                        .includeUpper(rangeQueryParam.getIncludeUpper() == null ? true : rangeQueryParam.getIncludeUpper()).build();
                            } else if (CollectionUtils.isNotEmpty(rangeQueryParamList)) {
                                for (ElasticSearchQueryParam.Query _rangeQuery : rangeQueryParamList) {
                                    _rangeQuery.setOp(EsOperation.RANGE.getOp());
                                    _rangeQuery.setType(EsConstants.TYPE_EXTENSION);
                                    _rangeQuery.setField(key.replace("Ex_Range.", ""));
                                    _rangeQuery.setIncludeLower(_rangeQuery.getIncludeLower() == null ? true : _rangeQuery.getIncludeLower());
                                    _rangeQuery.setIncludeUpper(_rangeQuery.getIncludeUpper() == null ? true : _rangeQuery.getIncludeUpper());
                                }
                            }
                        } else if (key.startsWith("Ex_Sort.")) {
                            sort = ElasticSearchQueryParam.Sort.builder().order("asc".equals(value) ? "asc" : "desc")
                                    .type(EsConstants.TYPE_EXTENSION).field(key.replace("Ex_Sort.", "")).build();
                        }
                    } else if (key.startsWith("Ba")) {
                        if (key.startsWith("Ba.")) {
                            _query = ElasticSearchQueryParam.Query.builder().op(EsOperation.EQ.getOp()).type(EsConstants.TYPE_BASE)
                                    .field(key.replace("Ba.", "")).value(value).build();
                        } else if (key.startsWith("Ba_Not.")) {
                            _query = ElasticSearchQueryParam.Query.builder().op(EsOperation.NOT_EQ.getOp()).type(EsConstants.TYPE_BASE)
                                    .field(key.replace("Ba_Not.", "")).value(value).build();
                        } else if (key.startsWith("Ba_Range.")) {
                            if (rangeQueryParam != null) {
                                _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ba_Range.", "")).type(EsConstants.TYPE_BASE)
                                        .op(EsOperation.RANGE.getOp()).from(rangeQueryParam.getFrom()).to(rangeQueryParam.getTo())
                                        .includeLower(rangeQueryParam.getIncludeLower() == null ? true : rangeQueryParam.getIncludeLower())
                                        .includeUpper(rangeQueryParam.getIncludeUpper() == null ? true : rangeQueryParam.getIncludeUpper()).build();
                            } else if (CollectionUtils.isNotEmpty(rangeQueryParamList)) {
                                for (ElasticSearchQueryParam.Query _rangeQuery : rangeQueryParamList) {
                                    _rangeQuery.setOp(EsOperation.RANGE.getOp());
                                    _rangeQuery.setType(EsConstants.TYPE_BASE);
                                    _rangeQuery.setField(key.replace("Ba_Range.", ""));
                                    _rangeQuery.setIncludeLower(_rangeQuery.getIncludeLower() == null ? true : _rangeQuery.getIncludeLower());
                                    _rangeQuery.setIncludeUpper(_rangeQuery.getIncludeUpper() == null ? true : _rangeQuery.getIncludeUpper());
                                }
                            }
                        } else if (key.startsWith("Ba_Sort.")) {
                            sort = ElasticSearchQueryParam.Sort.builder().order("asc".equals(value) ? "asc" : "desc")
                                    .type(EsConstants.TYPE_BASE).field(key.replace("Ba_Sort.", "")).build();
                        }
                    }
                } else if (value instanceof List) {
                    if (key.startsWith("Ex")) {
                        if (key.startsWith("Ex.")) {
                            _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ex.", ""))
                                    .op(EsOperation.IN.getOp()).type(EsConstants.TYPE_EXTENSION).value(value).build();
                        } else if (key.startsWith("Ex_Not.")) {
                            _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ex_Not.", "")).
                                    op(EsOperation.NOT_IN.getOp()).type(EsConstants.TYPE_EXTENSION).value(value).build();
                        }
                    } else {
                        if (key.startsWith("Ba.")) {
                            _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ba.", ""))
                                    .op(EsOperation.IN.getOp()).type(EsConstants.TYPE_BASE).value(value).build();
                        } else if (key.startsWith("Ba_Not.")) {
                            _query = ElasticSearchQueryParam.Query.builder().field(key.replace("Ba_Not.", ""))
                                    .op(EsOperation.NOT_IN.getOp()).type(EsConstants.TYPE_BASE).value(value).build();
                        }
                    }
                }
                if (_query != null) {
                    queries.add(_query);
                }
                if (sort != null) {
                    sorts.add(sort);
                }
                if (CollectionUtils.isNotEmpty(rangeQueryParamList)) {
                    queries.addAll(rangeQueryParamList);
                }
            }
        }
        if (StringUtils.isNotBlank(param.getFields())) {
            elasticSearchQueryParam.setField(Lists.newArrayList(Splitter.on(CommonConstant.COMMA).split(param.getFields())));
        }
        elasticSearchQueryParam.setQuery(queries);
        elasticSearchQueryParam.setSort(sorts);
        return query(elasticSearchQueryParam, Map.class);
    }

    public ElasticSearchQueryResponse.ResponseData<Map> commonQueryResponseData(ProxyAuth proxy_auth, CommonQueryParam param) {
        if (param.getIsCount() != null && param.getIsCount()) {
            InnerAPIHolder.setCountFlag(true);
            InnerAPIHolder.setResourceCount(new AtomicInteger(0));
        }
        if (!org.springframework.util.StringUtils.isEmpty(param.getAgg())) {
            InnerAPIHolder.setAggFlag(param.getAgg());
            InnerAPIHolder.intAggList();
            InnerAPIHolder.setAggSize(param.getAggSize());
        }
        return commonQuery(proxy_auth, param).getData();
    }
    /**
     * 模拟Neutron分页
     */
    public <T extends ProjectResource> List<OpenStackLink> simulationNetronPage(ElasticSearchQueryResponse.ResponseData<T> data,
                                                                                List<T> _list) {
        int count = data.getFrom() + data.getSize();
        List<OpenStackLink> linksList = new ArrayList<>();
        /**判断是否有下一页*/
        if (count < data.getTotal() && CollectionUtils.isNotEmpty(_list)) {
            StringBuilder sb = new StringBuilder()
                    .append("limit=").append(data.getSize())
                    .append("&marker=").append(_list.get(_list.size() - 1).getResourceId());
            linksList.add(OpenStackLink.builder().rel("next").href(sb.toString()).build());
        }
        /**判断是否有上一页*/
        if (data.getFrom() > 0 && CollectionUtils.isNotEmpty(_list)) {
            StringBuilder sb = new StringBuilder()
                    .append("limit=").append(data.getSize())
                    .append("&marker=").append(_list.get(0).getResourceId());
            linksList.add(OpenStackLink.builder().rel("previous").href(sb.toString()).build());
        }
        return linksList;
    }



    /**
     * 合并项目制相关信息
     */
    public <T extends ProjectResource> List<T> mergeResponseData(ElasticSearchQueryResponse.ResponseData<T> data) {
        List<T> _list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(data.getItems())) {
            for (ElasticSearchQueryResponse.Item<T> item : data.getItems()) {
                Object obj = item.getBase().get("iamProjectId");
                if (obj != null) {
                    item.getExtension().setIamProjectId(item.getBase().get("iamProjectId").toString());
                }
                Object billType = item.getBase().get("billType");
                Object productType = item.getBase().get("productType");
                Object serviceEndTime = item.getBase().get("serviceEndTime");
                Object instanceType = item.getBase().get("instanceType");
                item.getExtension().setProductType(String.valueOf(productType));
                if (billType instanceof Integer) {
                    item.getExtension().setBillType((Integer) billType);
                    ChargeComment comment = commonConfig.getChargeCommentCache().get(String.valueOf(billType));
                    item.getExtension().setChargeType(comment != null ? comment.getCode() : ChargeType.getName((Integer) billType));
                }
                if (serviceEndTime instanceof String) {
                    item.getExtension().setServiceEndTime((String) serviceEndTime);
                }
                if (instanceType instanceof Integer) {
                    item.getExtension().setInstanceType((Integer) instanceType);
                }
                _list.add(item.getExtension());
            }
        }
        return _list;
    }

    /**
     * 设置访问的头
     */
    private HttpEntity<?> generateHttpEntity(Object param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return new HttpEntity<>(param, headers);
    }
}
