package com.ksyun.cfwcore.es;


import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.enums.ResourceType;
import com.ksyun.cfwcore.enums.ResourceTypePrefix;
import com.ksyun.cfwcore.utils.DateUtils;

import java.util.Date;

/**
 * Created by xuyaming on 2018/8/9.
 */
public class EsUtils {
    public static String checkInstanceIdNeedPrefix(ResourceType resourceType) {
        String prefix = ResourceTypePrefix.getPrefix(resourceType);
        if (prefix == null) {
            prefix = "";
        }
        return prefix;
    }

    public static String getIndexByDay(String topicIndex, Date time) {
        return topicIndex + CommonConstant.UNDERLINE + DateUtils.getDate(time, DateUtils.DATETIME_FORMAT).substring(0, 10);
    }

    public static String getIndexByMonth(String topicIndex, String time) {
        return topicIndex + CommonConstant.UNDERLINE + time.substring(0, 7);
    }

    public static String getIndexByMonth(String topicIndex, Date time) {
        return topicIndex + CommonConstant.UNDERLINE +  DateUtils.getDate(time, DateUtils.DATETIME_FORMAT).substring(0, 7);
    }
}
