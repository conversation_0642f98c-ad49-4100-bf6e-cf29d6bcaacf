package com.ksyun.cfwcore.es.annotation;

import java.lang.annotation.*;

/**
 * 本注解用来表示ES查询的分页选项的偏移量
 * 支持Neutron的Id方式的偏移量
 * Created by xuyaming on 2017/11/8.
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface EsFrom {
    String indexName() default ""; //映射Id方式分页的时候 的起始查询条件ESfield名称映射 如资源的创建时间
    String idName() default "id";//映射Id方式分页的时候 过滤调的当前ID 当前ID的ESfield名称映射
    boolean reserved() default false; //是否是保留字
}
