package com.ksyun.cfwcore.es.annotation;


import com.ksyun.cfwcore.es.enums.EsOperation;

import java.lang.annotation.*;

/**
 * 标示字段为查询条件字段
 * Created by xuyaming on 2017/11/8.
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
public @interface EsQueryFiled {
    String mapping() default ""; //映射的EsField 默认为字段名称

    boolean reserved() default false;//是否保留字

    boolean nullOrNotNull() default false;//标示是否是按照值的null或者notnull的二意性比较 这个使用需要field为Boolean型 field值true代表使用EQ NULL查询 false代表NOT_EQ NULL查询

    EsOperation op() default EsOperation.EQ;//操作的方式eq in ....
}
