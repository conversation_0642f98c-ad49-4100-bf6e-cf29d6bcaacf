package com.ksyun.cfwcore.es.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.ksyun.cfwcore.es.EsConstants;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * ES查询实例
 * Created by xuyaming on 2017/11/6.
 */
@Data
@Builder
public class ElasticSearchQueryParam implements Cloneable {

    @Expose
    private String productLine;
    @Expose
    private Integer from;
    @Expose
    private Integer size;

    @Expose
    @SerializedName("groupBy")
    private Field groupByFiled;
    @Expose
    @Builder.Default
    private List<Sort> sort = new ArrayList<>();
    @Expose
    @Builder.Default
    private List<Query> query = new ArrayList<>();

    @Expose
    private List<String> field;//支持过滤字段

    @JsonIgnore
    private ProxyAuth proxyAuth;

    @Data
    @Builder
    public static class Sort {
        @Expose
        private String field;
        @Builder.Default
        @Expose
        private String type = EsConstants.TYPE_EXTENSION;
        @Expose
        private String order;
    }

    @Data
    @Builder
    public static class Query {
        @Expose
        private String field;
        @Expose
        @Builder.Default
        private String type = EsConstants.TYPE_EXTENSION;
        @Expose
        private String op;
        @Expose
        private Object value;
        @Expose
        private Object from;
        @Expose
        private Object to;
        @Expose
        private Boolean includeLower;
        @Expose
        private Boolean includeUpper;

        /*public Query(String field, String type, String op, Object value, Object to, Boolean includeUpper) {
            this.field = field;
            this.type = type;
            this.op = op;
            this.value = value;
            this.to = to;
            this.includeUpper = includeUpper;
        }

        public Query(String field, String type, String op, Object from, Object to, Boolean includeLower, Boolean includeUpper) {
            this.field = field;
            this.type = type;
            this.op = op;
            this.from = from;
            this.to = to;
            this.includeLower = includeLower;
            this.includeUpper = includeUpper;
        }*/
    }

    @Data
    @Builder
    public static class Field {
        @Expose
        private String field;
        @Expose
        @Builder.Default
        private String type = EsConstants.TYPE_EXTENSION;
        @Expose
        private Integer size;
    }

    public Object clone() {
        ElasticSearchQueryParam elasticSearchQueryParam = null;
        try {
            elasticSearchQueryParam = (ElasticSearchQueryParam) super.clone();
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return elasticSearchQueryParam;
    }
}
