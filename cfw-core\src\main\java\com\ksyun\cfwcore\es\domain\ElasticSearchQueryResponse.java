package com.ksyun.cfwcore.es.domain;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 查询的通用返回类
 * Created by xuyaming on 2017/11/6.
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ElasticSearchQueryResponse<T> {
    @Expose
    private String error_code;
    @Expose
    private String error_msg;
    @Expose
    private ResponseData<T> data;

    public ElasticSearchQueryResponse(ElasticSearchCountQueryResponse response) {
        this.error_code = response.getError_code();
        this.error_msg = response.getError_msg();
        this.data = new ResponseData<>();
        this.data.setTotal(response.getData().getTotal());
        this.data.setFrom(0);
        this.data.setSize(0);
    }

    public ElasticSearchQueryResponse(ElasticSearchAggregationCountQueryResponse response) {
        this.error_code = response.getError_code();
        this.error_msg = response.getError_msg();
        this.data = new ResponseData<>();
        if (response.getData() != null) {
            this.data.setTotal(response.getData().size());
        } else {
            this.data.setTotal(0);
        }
        this.data.setFrom(0);
        this.data.setSize(0);
    }


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class ResponseData<T> {
        @Expose
        private Integer total;
        @Expose
        private Integer from;
        @Expose
        private Integer size;
        @Expose
        private List<Item<T>> items = new ArrayList<>();
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Item<T> {
        @Expose
        private Map<String, Object> base;
        @Expose
        private T extension;
    }
}
