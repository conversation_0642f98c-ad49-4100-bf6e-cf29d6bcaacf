package com.ksyun.cfwcore.es.handle;
import com.ksyun.cfwcore.config.ElasticSearchConfig;
import com.ksyun.cfwcore.es.ElasticSearchUtils;
import com.ksyun.cfwcore.es.domain.ElasticSearchQueryParam;
import com.ksyun.cfwcore.es.domain.ElasticSearchQueryResponse;
/**
 * 查询结果后置器
 * Created by xuyaming on 2018/6/29.
 */
public interface AfterEsQueryHandle<T> {
    void process(ElasticSearchUtils elasticSearchUtils, ElasticSearchConfig config, ElasticSearchQueryParam param,
                 ElasticSearchQueryResponse<T> response) throws Exception;
}
