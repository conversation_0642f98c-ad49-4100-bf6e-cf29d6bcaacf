package com.ksyun.cfwcore.etcd;

import com.ksyun.cfwcore.constants.CommonConstant;
import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Configuration
@Log4j2
public class EtcdConfig {

    @Value("${etcd.endpoints}")
    private String etcdEndpoints;

    @Value("${etcd.saslEnable}")
    private boolean saslEnable;

    @Value("${etcd.username}")
    private String username;

    @Value("${etcd.password}")
    private String password;

    private final ExecutorService executorServiceEtcd = new ThreadPoolExecutor(100, 100,
            1000, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.CallerRunsPolicy());
    @Bean
    public Client etcdClient() {
        if(saslEnable){
            log.info("saslEnable:{},username:{}",saslEnable,username);
            return Client.builder().loadBalancerPolicy("round_robin").endpoints(etcdEndpoints.split(CommonConstant.COMMA)).user(ByteSequence.from(username, StandardCharsets.UTF_8))
                    .password(ByteSequence.from(password, StandardCharsets.UTF_8)).executorService(executorServiceEtcd).build();
        }else{
            return Client.builder().loadBalancerPolicy("round_robin").endpoints(etcdEndpoints.split(CommonConstant.COMMA)).executorService(executorServiceEtcd).build();
        }

    }
}
