package com.ksyun.cfwcore.etcd;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.kv.DeleteResponse;
import io.etcd.jetcd.KeyValue;
import io.etcd.jetcd.kv.GetResponse;
import io.etcd.jetcd.kv.PutResponse;
import io.etcd.jetcd.options.DeleteOption;
import lombok.extern.log4j.Log4j2;
import io.etcd.jetcd.options.GetOption;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
@Component
@Log4j2
public class EtcdService {

    @Autowired
    private Client etcdClient;

    public CompletableFuture<PutResponse> putValue(String key, String value) {
        try {
            ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
            ByteSequence valueByte = ByteSequence.from(value, StandardCharsets.UTF_8);
            return etcdClient.getKVClient().put(keyByte, valueByte);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public CompletableFuture<String> getValue(String key) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        CompletableFuture<GetResponse> getFuture = etcdClient.getKVClient().get(keyByte);
        return getFuture.thenApply(getResponse -> {
            if (getResponse.getKvs().isEmpty()) {
                return null;
            }
            return getResponse.getKvs().get(0).getValue().toString(StandardCharsets.UTF_8);
        });
    }

    public CompletableFuture<Void> deleteValue(String key) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        return etcdClient.getKVClient().delete(keyByte).thenAccept(deleteResponse -> {
        });
    }

    public long[] deletePrefixes(List<String> prefixes) {
        try {
            return prefixes.stream()
                    .parallel()
                    .mapToLong(this::deleteSinglePrefix).toArray();
        }catch (Exception e){
            log.error("删除");
        }
        return null;
    }

    /**
     * 删除单个前缀下的所有键值对
     *
     * @param prefix 键前缀
     * @return 删除的键数量
     */
    public long deleteSinglePrefix(String prefix){
        try {
            ByteSequence bsPrefix = ByteSequence.from(prefix.getBytes());
            DeleteOption option = DeleteOption.newBuilder()
                    .withPrefix(bsPrefix)
                    .build();
            DeleteResponse response = etcdClient.getKVClient().delete(bsPrefix, option).get();
            return response.getDeleted();
        } catch (Exception e) {
            log.error("Error deleting prefix'" + prefix + "': " + e.getMessage());
            return 0;
        }
    }

    public List<KeyValue> getValueByPrefix(String keyPrefix) {
        try {
            ByteSequence prefixByte = ByteSequence.from(keyPrefix.getBytes());
            GetOption getOption = GetOption.newBuilder().withPrefix(prefixByte).build();
            // 异步查询
            CompletableFuture<GetResponse> future = etcdClient.getKVClient().get(prefixByte, getOption);
            GetResponse response = future.get();
            return response.getKvs();
        } catch (Exception e) {
            log.error("etcd批量查询错误:{}",e.getMessage());
        }
        return null;
    }

}