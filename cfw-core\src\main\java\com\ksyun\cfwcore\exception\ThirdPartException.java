package com.ksyun.cfwcore.exception;

import com.ksyun.common.http.Response;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class ThirdPartException extends RuntimeException {

    private static final long serialVersionUID = 4988549158498271515L;

    private Response response;

    private HttpStatus http_status;

    private Object[] arguments;

    public ThirdPartException(String code, String message, HttpStatus http_status) {
        super(code + ":" + message);
        this.response = new Response(code, message);
        this.http_status = http_status;
    }

    public ThirdPartException(String code, String message,
                              HttpStatus http_status, Object... arguments) {
        super(code + ":" + message);
        this.response = new Response(code, message);
        this.http_status = http_status;
        this.arguments = arguments;
    }

    public ThirdPartException(String code, String message,
                              HttpStatus http_status, Throwable e) {
        super(code + ":" + message, e);
        this.response = new Response(code, message);
        this.http_status = http_status;
    }

    public ThirdPartException(String code, String message,
                              HttpStatus http_status, Throwable e, Object... arguments) {
        super(code + ":" + message, e);
        this.response = new Response(code, message);
        this.http_status = http_status;
        this.arguments = arguments;
    }
}
