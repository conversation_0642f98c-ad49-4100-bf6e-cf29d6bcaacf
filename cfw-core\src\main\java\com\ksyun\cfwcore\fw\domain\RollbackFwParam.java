package com.ksyun.cfwcore.fw.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class RollbackFwParam implements Serializable {
	private static final long serialVersionUID = 2029115307527033500L;
	private String fwId;
	private String subOrderId;
	private int status;
}
