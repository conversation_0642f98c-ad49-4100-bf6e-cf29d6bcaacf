package com.ksyun.cfwcore.holder;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.ksyun.cfwcore.domain.QueryCountParam;
import com.ksyun.cfwcore.es.domain.ElasticSearchAggregationCountQueryResponse;
import com.ksyun.comm.util.HostUtils;
import com.ksyun.common.network.log.plugin.OnePiecePlugin;
import com.ksyun.common.network.log.utils.NetWorkLogUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpStatus;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

@Log4j2
public class InnerAPIHolder {
    private static final ThreadLocal<String>       subOderIdHolder      = new TransmittableThreadLocal<String>();
    private static final ThreadLocal<String>       instanceIdHolder     = new ThreadLocal<String>();
    private static final ThreadLocal<ProxyAuth>    proxyAuthHolder      = new TransmittableThreadLocal<ProxyAuth>();
    private static final ThreadLocal<String>       sourceHolder         = new ThreadLocal<String>();
    private static final ThreadLocal<Set<String>>  aclRuleNumHolder     = new ThreadLocal<>();
    private static final ThreadLocal<Boolean>      needNotifyFail       = new ThreadLocal<>();
    private static final ThreadLocal<Set<String>>  subOrderIdSetHolder  = new ThreadLocal<>();
    private static final ThreadLocal<Long>         timestampHolder      = new TransmittableThreadLocal<>();
    private static final ThreadLocal<List<String>> iamProjectIdHolder   = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      esUpdateErrorHolder  = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      tenantNotFoundHolder = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      countFlag            = new TransmittableThreadLocal<>();
    private static final ThreadLocal<String>       aggFlag              = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Integer>      aggSize              = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      regionRollbackFlag   = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      needCheckQuota       = new TransmittableThreadLocal<>();
    private static final ThreadLocal<Boolean>      needCheckNonKaQuota  = new TransmittableThreadLocal<>();
    private static final ThreadLocal<String>       service              = new TransmittableThreadLocal<>();

    private static final ThreadLocal<AtomicInteger>                                                    resourceCount    = new TransmittableThreadLocal<>();
    private static final ThreadLocal<List<ElasticSearchAggregationCountQueryResponse.AggregationData>> aggList          = new TransmittableThreadLocal<>();
    /**
     * 由于计费方式定义迭代多次，新老对应关系在Apollo配置，在内部流转时，会转为老模式，因此需将最初的计费方式存储在ThreadLocal中，用于错误提示、返回值
     */
    private static final ThreadLocal<String>                                                           chargeTypeHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Boolean> synchronization = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Boolean> existEs = new TransmittableThreadLocal<>();

    private static final ThreadLocal<ConcurrentHashMap<String, String>> instanceIdTagsHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Type> responseTypeHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Integer> billTypeHolder = new TransmittableThreadLocal<>();

    /**
     * Action
     */
    private static final ThreadLocal<String> actionHolder = new TransmittableThreadLocal<>();

    // 某些action特殊处理标识，若该值为true，则action正常执行不做拦截，且在该接口存在订单操作情况下 根据commonfig中isCheckNotifySubOrder参数判断是否修改子订单状态
    private static final ThreadLocal<String> actionCheckSignHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Boolean> actionQueryNeutronHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<QueryCountParam> queryCountParamHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Boolean> searchChargeType = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Locale> localeHolder = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Integer> productTypeHolder = new TransmittableThreadLocal<>(); // 弹性IP区分商品类型来通知用户邮件

    private static final ThreadLocal<Integer> existResourceNum = new TransmittableThreadLocal<>();

    private static final TransmittableThreadLocal<HttpStatus> httpStatusHolder = new TransmittableThreadLocal<>();

    public static void cleanAllHolder() {

        subOderIdHolder.remove();
        proxyAuthHolder.remove();
        aclRuleNumHolder.remove();
        instanceIdHolder.remove();
        needNotifyFail.remove();
        subOrderIdSetHolder.remove();
        timestampHolder.remove();
        iamProjectIdHolder.remove();
        esUpdateErrorHolder.remove();
        tenantNotFoundHolder.remove();
        countFlag.remove();
        resourceCount.remove();
        aggFlag.remove();
        aggSize.remove();
        aggList.remove();
        chargeTypeHolder.remove();
        regionRollbackFlag.remove();
        needCheckQuota.remove();
        service.remove();
        synchronization.remove();
        existEs.remove();
        responseTypeHolder.remove();
        instanceIdTagsHolder.remove();
        billTypeHolder.remove();
        actionHolder.remove();
        actionCheckSignHolder.remove();
        actionQueryNeutronHolder.remove();
        needCheckNonKaQuota.remove();
        queryCountParamHolder.remove();
        searchChargeType.remove();
        localeHolder.remove();
        productTypeHolder.remove();
        existResourceNum.remove();
        httpStatusHolder.remove();
        NetWorkLogUtils.clearNetWorkLogCommonInfo();
    }

    public static void setLocaleHolder(Locale locale) {
        localeHolder.set(locale);
    }

    public static Locale getLocaleHolder() {
        return localeHolder.get();
    }

    public static void setSubOderId(String subOrderId) {
        subOderIdHolder.set(subOrderId);
    }

    public static void setInstanceId(String instanceId) {
        instanceIdHolder.set(instanceId);
    }

    public static void setSource(String source) {
        sourceHolder.set(source);
    }

    public static void setNeedNotifyFail(Boolean need) {
        if (needNotifyFail.get() == null) {
            needNotifyFail.set(need);
        }
    }

    public static void forceSetNeedNotifyFail(Boolean need) {
        needNotifyFail.set(need);
    }

    public static void addSubOrderId(String subOrderId) {
        if (subOrderIdSetHolder.get() == null) {
            subOrderIdSetHolder.set(new HashSet<String>());
        }
        subOrderIdSetHolder.get().add(subOrderId);
    }

    public static boolean addAclRuleNum(String num) {
        if (aclRuleNumHolder.get() == null) {
            aclRuleNumHolder.set(new HashSet<String>());
        }
        return aclRuleNumHolder.get().add(num);
    }

    public static void setProxyAuth(ProxyAuth auth, final String action, final String service, final OnePiecePlugin plugin) throws Exception {
        proxyAuthHolder.set(auth);
        final ProxyAuth proxyAuth = (ProxyAuth) auth.clone();
        NetWorkLogUtils.setNetWorkLogCommonInfo(auth.getRequest_id(), new HashMap<String, String>() {
            private static final long serialVersionUID = 8461131185247550408L;

            {
                put("Address", HostUtils.getLocalHosts().get(0));
                put("AccountId", proxyAuth.getAccount_id());
                put("Action", action);
                put("Region", proxyAuth.getRegion());
                put("UserId", proxyAuth.getUser_id());
                if (StringUtils.isNotEmpty(service)) {
                    put("Service", service);
                }
            }

        }, plugin);
    }

    public static String getSubOderId() {
        return subOderIdHolder.get();
    }

    public static String getInstanceId() {
        return instanceIdHolder.get();
    }

    public static String getSource() {
        return sourceHolder.get();
    }

    public static Boolean getNeedNotifyFail() {
        return needNotifyFail.get();
    }

    public static ProxyAuth getProxyAuth() {
        return proxyAuthHolder.get();
    }

    public static Set<String> getAclRuleNum() {
        return aclRuleNumHolder.get();
    }

    public static Set<String> getSubOrderIdSet() {
        return subOrderIdSetHolder.get();
    }

    public static Long getTimestamp() {
        return timestampHolder.get();
    }

    public static void setTimestamp(Long timestamp) {
        timestampHolder.set(timestamp);
    }

    public static List<String> getIamProjectId() {
        return iamProjectIdHolder.get();
    }

    public static void addIamProjectId(String iamProjectId) {
        if (iamProjectIdHolder.get() == null) {
            iamProjectIdHolder.set(new ArrayList<String>());
        }
        iamProjectIdHolder.get().add(iamProjectId);
    }

    public static void setIamProjectId(List<String> iamProjectIds) {
        iamProjectIdHolder.set(iamProjectIds);
    }

    public static Boolean getEsUpdateError() {
        return esUpdateErrorHolder.get();
    }

    public static void setEsUpdateError(Boolean esUpdateError) {
        esUpdateErrorHolder.set(esUpdateError);
    }

    public static void setTenantNotFound() {
        tenantNotFoundHolder.set(true);
    }

    public static void setTenantNotFoundNull() {
        tenantNotFoundHolder.set(null);
    }

    public static void setCountFlag(Boolean v) {
        countFlag.set(v);
    }

    public static Boolean getCountFlag() {
        return countFlag.get();
    }

    public static void setAggFlag(String v) {
        aggFlag.set(v);
    }

    public static String getAggFlag() {
        return aggFlag.get();
    }

    public static void setAggSize(Integer size) {
        aggSize.set(size);
    }

    public static Integer getAggSize() {
        return aggSize.get();
    }

    public static Boolean getTenantNotFound() {
        return tenantNotFoundHolder.get();
    }

    public static AtomicInteger getResourceCount() {
        return resourceCount.get();
    }

    public static void setResourceCount(AtomicInteger count) {
        resourceCount.set(count);
    }

    public static void addResourceCount(int add) {
        resourceCount.get().addAndGet(add);
    }

    public static List<ElasticSearchAggregationCountQueryResponse.AggregationData> getAggList() {
        return aggList.get();
    }

    public static void intAggList() {
        aggList.set(new CopyOnWriteArrayList<ElasticSearchAggregationCountQueryResponse.AggregationData>());
    }

    public static void addAggList(List<ElasticSearchAggregationCountQueryResponse.AggregationData> list) {
        aggList.get().addAll(list);
    }

    public static void setChargeTypeHolder(String chargeType) {
        chargeTypeHolder.set(chargeType);
    }

    public static String getChargeTypeHolder() {
        return chargeTypeHolder.get();
    }

    public static void setRegionRollbackFlag(Boolean flag) {
        regionRollbackFlag.set(flag);
    }

    public static Boolean getRegionRollbackFlag() {
        return regionRollbackFlag.get();
    }

    public static void setNeedCheckQuota(Boolean flag) {
        needCheckQuota.set(flag);
    }

    public static Boolean getNeedCheckQuota() {
        return needCheckQuota.get();
    }

    public static void setService(String svc) {
        service.set(svc);
    }

    public static String getService() {
        return service.get();
    }

    public static Boolean getSynchronization() {
        return synchronization.get();
    }

    public static void setSynchronization(boolean b) {
        synchronization.set(b);
    }

    public static Boolean getExistEs() {
        return existEs.get();
    }

    public static void setExistEs(boolean b) {
        existEs.set(b);
    }

    public static Map<String, String> getInstanceIdTags() {
        return instanceIdTagsHolder.get();
    }

    public static void addInstanceIdTags(String id, String tag) {
        instanceIdTagsHolder.get().put(id, tag);
    }

    public static void initInstanceIdTags() {
        instanceIdTagsHolder.set(new ConcurrentHashMap<String, String>());
    }

    public static Type getResponseTypeHolder() {
        return responseTypeHolder.get();
    }

    public static void setResponseTypeHolder(Type t) {
        responseTypeHolder.set(t);
    }

    public static void setBillTypeHolder(Integer billType) {
        billTypeHolder.set(billType);
    }

    public static Integer getBillTypeHolder() {
        return billTypeHolder.get();
    }

    public static String getActionHolder() {
        return actionHolder.get();
    }

    public static void setActionHolder(String action) {
        actionHolder.set(action);
    }

    public static String getActionCheckSignHolder() {
        return actionCheckSignHolder.get();
    }

    public static void setActionCheckSignHolder(String checkSign) {
        actionCheckSignHolder.set(checkSign);
    }

    public static Boolean getActionQueryNeutronHolder() {
        return actionQueryNeutronHolder.get();
    }

    public static void setActionQueryNeutronHolder(Boolean actionQueryNeutron) {
        actionQueryNeutronHolder.set(actionQueryNeutron);
    }

    public static void setNeedCheckNonKaQuota(Boolean flag) {
        needCheckNonKaQuota.set(flag);
    }

    public static Boolean getNeedCheckNonKaQuota() {
        return needCheckNonKaQuota.get();
    }

    public static void setQueryCountParamHolder(QueryCountParam param) {
        queryCountParamHolder.remove();
        queryCountParamHolder.set(param);
    }

    public static QueryCountParam getQueryCountParamHolder() {
        return queryCountParamHolder.get();
    }

    public static void setSearchChargeType(Boolean flag) {
        searchChargeType.set(flag);
    }

    public static Boolean getSearchChargeType() {
        return searchChargeType.get();
    }

    public static void setProductTypeHolder(Integer billType) {
        productTypeHolder.set(billType);
    }

    public static Integer getProductTypeHolder() {
        return productTypeHolder.get();
    }

    public static void setExistResourceNum(Integer num) {
        existResourceNum.set(num);
    }

    public static Integer getExistResourceNum() {
        return existResourceNum.get();
    }

    public static void setHttpStatusHolder(HttpStatus status) {
        httpStatusHolder.set(status);
    }
    public static HttpStatus getHttpStatusHolder() {
        return httpStatusHolder.get();
    }
}
