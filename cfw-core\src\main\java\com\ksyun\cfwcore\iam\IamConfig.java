package com.ksyun.cfwcore.iam;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.google.common.collect.Lists;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Component
@Data
@EnableApolloConfig({"cfw-core-common"})
@RefreshType
public class IamConfig{

    private final String HEADER_X_KSC_VERSION="X-KSC-VERSION";
    private final String HEADER_X_KSC_SERVICE="X-KSC-SERVICE";
    private final String HEADER_X_KSC_USER_ID="X-KSC-USER-ID";
    private final String HEADER_X_KSC_API="X-KSC-API";
    private final String SERVICE="iam";

    @Value("${iam.url:}")
    @RefreshField("iam.url")
    private String iamUrl;

    @Value("${iam.port:}")
    @RefreshField("iam.port")
    private String iamPort;

    @Value("${iam.version:}")
    @RefreshField("iam.version")
    private String iamVersion;

    @Value("${iam.version}")
    @RefreshField("iam.version")
    private String version;

    @Value("${iam.region:cn-beijing-6}")
    @RefreshField("iam.region")
    private String region;

    public HttpEntity<?> generateIamHttpEntity(ProxyAuth auth, String iamAction, Object param){
        HttpHeaders headers = new HttpHeaders();
        // assert
        Assert.hasText(auth.getRequest_id(),
                String.format("%s is empty", HttpEntityWapper.HEADER_X_KSC_REQUEST_ID));

        Assert.hasText(auth.getAccount_id(),
                String.format("%s is empty", HttpEntityWapper.HEADER_X_KSC_ACCOUNT_ID));

        Assert.hasText(auth.getRegion(),
                String.format("%s is empty", HttpEntityWapper.HEADER_X_KSC_REGION));

        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Lists.newArrayList(MediaType.APPLICATION_JSON));
        headers.set(HttpEntityWapper.HEADER_X_KSC_REQUEST_ID, auth.getRequest_id());
        headers.set(HttpEntityWapper.HEADER_X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.set(HttpEntityWapper.HEADER_X_KSC_REGION, auth.getRegion());
        headers.set(HEADER_X_KSC_VERSION,iamVersion);
        headers.set(HEADER_X_KSC_SERVICE,SERVICE);
        headers.set(HEADER_X_KSC_API,iamAction);
        if(!StringUtils.isEmpty(auth.getUser_id())){
            headers.set(HEADER_X_KSC_USER_ID,auth.getUser_id());
        }
        return new HttpEntity<Object>(param, headers);
    }
}
