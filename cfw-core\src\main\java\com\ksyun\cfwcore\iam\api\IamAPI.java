package com.ksyun.cfwcore.iam.api;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.exception.ThirdPartException;
import com.ksyun.cfwcore.iam.IamConfig;
import com.ksyun.cfwcore.iam.api.domain.IamErrorInfo;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfo;
import com.ksyun.cfwcore.iam.api.domain.IamProjectInfos;
import com.ksyun.common.http.ObjectPlaceholderResolver;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

@Component
@Slf4j
public class IamAPI {
	private final String IAM_URL = "${iamUrl}:${iamPort}";
	private final String GET_BY_ID = IAM_URL + "/?Action=GetAccountProjectInfo&Version=${iamVersion}&ProjectId=";
	private final String GET_ALL = IAM_URL + "/?Action=GetAccountAllProjectList&Version=${iamVersion}";
	private final String VALID_PROJECT_ID = IAM_URL + "/?Action=ValidProjectId&Version=${iamVersion}&ProjectId=";
	@Autowired
	protected RestTemplate rest_template;
	@Autowired
	protected IamConfig iamConfig;
	@Autowired
	protected PropertyPlaceholderHelper placeholder_helper;

	private Gson gson = new GsonBuilder().create();


	/**
	 * 验证项目ID是否有效
	 * @param auth
	 * @param projectId
	 * @return
	 */
	public IamProjectInfo validIamProjectInfo(ProxyAuth auth, String projectId) {
		StringBuilder _url = new StringBuilder(
				placeholder_helper.replacePlaceholders(VALID_PROJECT_ID, new ObjectPlaceholderResolver(iamConfig)));
		_url.append(projectId);
		String url = _url.toString();
		if (log.isDebugEnabled()) {
			log.debug("[GET] ValidProjectId url {} with param {}", url, projectId);
		}
		StopWatch stop_watch = new StopWatch();
		stop_watch.start();
		IamProjectInfo response;
		try {
			HttpEntity<?> entity = iamConfig.generateIamHttpEntity(auth, "ValidProjectId", null);
			ResponseEntity<IamProjectInfo> r = rest_template.exchange(url, HttpMethod.GET, entity,
					IamProjectInfo.class);
			response = r.getBody();
			stop_watch.stop();
			if (log.isDebugEnabled()) {
				log.debug("[GET] ValidProjectId time {} url {} response {}", stop_watch.getTime(), url,
						response);
			}
			if (response == null) {
				throw new OpenAPIException(ErrorCode.InternalError, "ValidProjectId response is empty",
						HttpStatus.BAD_REQUEST);
			}
			return response;
		} catch (HttpStatusCodeException e) {
			if(e.getStatusCode().is4xxClientError()){
				String body_message = e.getResponseBodyAsString();
				IamErrorInfo error_data = converData(body_message);
				log.error("ValidProjectId failed. [" + body_message + "]", e);
				if("NotExistProjectId".equals(error_data.getError().getCode())){
					throw new OpenAPIException(ErrorCode.NotExistProjectId, ErrorCode.NotExistProjectId,
							HttpStatus.BAD_REQUEST,projectId);
				}else if("ProjectStatusUnavaliable".equals(error_data.getError().getCode())){
					throw new OpenAPIException(ErrorCode.ProjectStatusUnavaliable, ErrorCode.ProjectStatusUnavaliable,
							HttpStatus.BAD_REQUEST,projectId);
				}else if("ProjectMemberNotExist".equals(error_data.getError().getCode())){
					throw new OpenAPIException(ErrorCode.ProjectMemberNotExist, ErrorCode.ProjectMemberNotExist,
							HttpStatus.BAD_REQUEST,projectId);
				} else{
					throw new ThirdPartException(error_data.getError().getCode(), error_data.getError().getMessage(),
							HttpStatus.BAD_REQUEST);
				}

			}else {
				throw e;
			}

		} catch (Exception e) {
			log.error("ValidProjectId failed.", e);
			log.error("ValidProjectId failed.", e);
			throw new OpenAPIException(ErrorCode.InternalError, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * 按照项目ID查询
	 * @param auth
	 * @param projectId
	 * @return
	 */
	public IamProjectInfo getIamProjectInfo(ProxyAuth auth, String projectId) {
		StringBuilder _url = new StringBuilder(
				placeholder_helper.replacePlaceholders(GET_BY_ID, new ObjectPlaceholderResolver(iamConfig)));
		_url.append(projectId);
		String url = _url.toString();
		if (log.isDebugEnabled()) {
			log.debug("[GET] GetAccountProjectInfo url {} with param {}", url, projectId);
		}
		StopWatch stop_watch = new StopWatch();
		stop_watch.start();
		IamProjectInfo response;
		try {
			HttpEntity<?> entity = iamConfig.generateIamHttpEntity(auth, "GetAccountProjectInfo", null);
			ResponseEntity<IamProjectInfo> r = rest_template.exchange(url, HttpMethod.GET, entity,
					IamProjectInfo.class);
			response = r.getBody();
			stop_watch.stop();
			if (log.isDebugEnabled()) {
				log.debug("[GET] GetAccountProjectInfo time {} url {} response {}", stop_watch.getTime(), url,
						response);
			}
			if (response == null) {
				throw new OpenAPIException(ErrorCode.InternalError, "GetAccountProjectInfo response is empty",
						HttpStatus.BAD_REQUEST);
			}
			return response;
		} catch (HttpStatusCodeException e) {
			if(e.getStatusCode().is4xxClientError()){
				String body_message = e.getResponseBodyAsString();
				IamErrorInfo error_data = converData(body_message);
				log.error("GetAccountProjectInfo failed. [" + body_message + "]", e);
				if("ProjectMemberNotExist".equals(error_data.getError().getCode())){
					throw new OpenAPIException(ErrorCode.ProjectMemberNotExist, ErrorCode.ProjectMemberNotExist,
							HttpStatus.BAD_REQUEST,projectId);
				}else if("NotExistProjectId".equals(error_data.getError().getCode())){
					throw new OpenAPIException(ErrorCode.NotExistProjectId, ErrorCode.NotExistProjectId,
							HttpStatus.BAD_REQUEST,projectId);
				}else{
					throw new ThirdPartException(error_data.getError().getCode(), error_data.getError().getMessage(),
							HttpStatus.BAD_REQUEST);
				}

			}else {
				throw e;
			}

		} catch (Exception e) {
			log.error("GetAccountProjectInfo failed.", e);
			log.error("GetAccountProjectInfo failed.", e);
			throw new OpenAPIException(ErrorCode.InternalError, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * 查询所有项目
	 * @param auth
	 * @return
	 */
	public IamProjectInfos getIamProjectInfos(ProxyAuth auth){
		StringBuilder _url = new StringBuilder(
				placeholder_helper.replacePlaceholders(GET_ALL, new ObjectPlaceholderResolver(iamConfig)));
		String url = _url.toString();
		if (log.isDebugEnabled()) {
			log.debug("[GET] GetAccountAllProjectList url {} ", url);
		}
		StopWatch stop_watch = new StopWatch();
		stop_watch.start();
		IamProjectInfos response;
		try {
			HttpEntity<?> entity = iamConfig.generateIamHttpEntity(auth, "GetAccountAllProjectList", null);
			ResponseEntity<IamProjectInfos> r = rest_template.exchange(url, HttpMethod.GET, entity,
					IamProjectInfos.class);
			response = r.getBody();
			stop_watch.stop();
			if (log.isDebugEnabled()) {
				log.debug("[GET] GetAccountAllProjectList time {} url {} response {}", stop_watch.getTime(), url);
			}
			if (response == null) {
				throw new OpenAPIException(ErrorCode.InternalError, "GetAccountAllProjectList response is empty",
						HttpStatus.BAD_REQUEST);
			}
			return response;
		} catch (HttpStatusCodeException e) {
			if(e.getStatusCode().is4xxClientError()){
				String body_message = e.getResponseBodyAsString();
				IamErrorInfo error_data = converData(body_message);
				log.error("GetAccountAllProjectList failed. [" + body_message + "]", e);
				throw new ThirdPartException(error_data.getError().getCode(), error_data.getError().getMessage(),
						HttpStatus.BAD_REQUEST);
			}else {
				throw e;
			}
		} catch (Exception e) {
			log.error("GetAccountAllProjectList failed.", e);
			log.error("GetAccountAllProjectList failed.", e);
			throw new OpenAPIException(ErrorCode.InternalError, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	private IamErrorInfo converData(String error_message) {
		IamErrorInfo error_data = gson.fromJson(error_message, IamErrorInfo.class);
		return error_data;
	}

	public IamProjectInfos notify(ProxyAuth auth){
		StringBuilder _url = new StringBuilder(
				placeholder_helper.replacePlaceholders(GET_ALL, new ObjectPlaceholderResolver(iamConfig)));
		String url = _url.toString();
		if (log.isDebugEnabled()) {
			log.debug("[GET] GetAccountAllProjectList url {} ", url);
		}
		StopWatch stop_watch = new StopWatch();
		stop_watch.start();
		IamProjectInfos response;
		try {
			HttpEntity<?> entity = iamConfig.generateIamHttpEntity(auth, "GetAccountAllProjectList", null);
			ResponseEntity<IamProjectInfos> r = rest_template.exchange(url, HttpMethod.GET, entity,
					IamProjectInfos.class);
			response = r.getBody();
			stop_watch.stop();
			if (log.isDebugEnabled()) {
				log.debug("[GET] GetAccountAllProjectList time {} url {} response {}", stop_watch.getTime(), url);
			}
			if (response == null) {
				throw new OpenAPIException(ErrorCode.InternalError, "GetAccountAllProjectList response is empty",
						HttpStatus.BAD_REQUEST);
			}
			return response;
		} catch (HttpStatusCodeException e) {
			if(e.getStatusCode().is4xxClientError()){
				String body_message = e.getResponseBodyAsString();
				IamErrorInfo error_data = converData(body_message);
				log.error("GetAccountAllProjectList failed. [" + body_message + "]", e);
				throw new ThirdPartException(error_data.getError().getCode(), error_data.getError().getMessage(),
						HttpStatus.BAD_REQUEST);
			}else {
				throw e;
			}
		} catch (Exception e) {
			log.error("GetAccountAllProjectList failed.", e);
			log.error("GetAccountAllProjectList failed.", e);
			throw new OpenAPIException(ErrorCode.InternalError, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
