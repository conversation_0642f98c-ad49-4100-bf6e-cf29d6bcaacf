package com.ksyun.cfwcore.iam.api.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IamErrorInfo {

    @Expose
    @SerializedName("RequestId")
    private String requestId;

    @Expose
    @SerializedName("Error")
    private Error Error;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Error{

        @Expose
        @SerializedName("Type")
        private String type;

        @Expose
        @SerializedName("Code")
        private String code;

        @Expose
        @SerializedName("Message")
        private String message;
    }
}
