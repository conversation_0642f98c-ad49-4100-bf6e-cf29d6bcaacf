package com.ksyun.cfwcore.iam.api.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
public class IamProjectInfos {

    @Expose
    @SerializedName("ListProjectResult")
    private Infos infos;

    @Expose
    @SerializedName("RequestId")
    private String requestId;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Infos{

        @Expose
        @SerializedName("Total")
        private Integer total;

        @Expose
        @SerializedName("ProjectList")
        private List<IamProjectResult> projectList;
    }
}
