package com.ksyun.cfwcore.iam.api.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IamProjectResult {

    @Expose
    @SerializedName("ProjectId")
    private String projectId;

    @Expose
    @SerializedName("AccountId")
    private String accountId;

    @Expose
    @SerializedName("ProjectName")
    private String projectName;

    @Expose
    @SerializedName("ProjectDesc")
    private String ProjectDesc;

    @Expose
    @SerializedName("Status")
    private Integer status;

    @Expose
    @SerializedName("Krn")
    private String krn;

    @Expose
    @SerializedName("CreateTime")
    private String createTime;
}
