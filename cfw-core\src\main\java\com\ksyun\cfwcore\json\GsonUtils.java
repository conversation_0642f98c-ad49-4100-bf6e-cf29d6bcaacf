package com.ksyun.cfwcore.json;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ksyun.comm.util.JsonBinder;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

/**
 * Created by xuyaming on 2017/7/11.
 */
public class GsonUtils {
    private static Gson noNullGson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
    private static Gson NullGson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().serializeNulls().create();
    private static JsonBinder jsonBinder = JsonBinder.buildNonNullBinder(false);


    /**
     * 转换对象为非空的map
     *
     * @param obj
     * @return
     */
    @SuppressWarnings("unchecked")
    public static Map<String, ?> transferObj2NoNullMap(Object obj) {
        String json = noNullGson.toJson(obj);
        return (Map<String, ?>) jsonBinder.fromJson(json, Map.class);
    }

    public static Gson getGson() {
        return noNullGson;
    }

    public static Gson getNullGson() {
        return NullGson;
    }

    /**
     * Json字符串转换为实体
     *
     * @param json
     * @param t
     * @param <T>
     * @return
     */
    public static <T> T getObjectFromJson(String json, Class<T> t) {
        return jsonBinder.fromJson(json, t);
    }


    /**
     * Json字符串转换为List
     *
     * @param json
     * @param t
     * @param <T>
     * @return
     */
    public static <T> List<T> getListFromJson(String json, Class<T> t) {
        Type type = new ParameterizedTypeImpl(t);
        return NullGson.fromJson(json, type);
    }
}

