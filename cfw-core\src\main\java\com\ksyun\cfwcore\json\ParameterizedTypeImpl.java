package com.ksyun.cfwcore.json;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2019-04-03 18:03
 */
public class ParameterizedTypeImpl implements ParameterizedType {

    private Class clazz;

    public ParameterizedTypeImpl(Class clazz) {
        this.clazz = clazz;
    }

    @Override
    public Type[] getActualTypeArguments() {
        return new Type[]{clazz};
    }

    @Override
    public Type getRawType() {
        return List.class;
    }

    @Override
    public Type getOwnerType() {
        return null;
    }
}
