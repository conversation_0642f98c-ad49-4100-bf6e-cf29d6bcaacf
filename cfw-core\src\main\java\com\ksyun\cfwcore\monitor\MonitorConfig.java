package com.ksyun.cfwcore.monitor;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@EnableApolloConfig({"cfw-core-common"})
@RefreshType
public class MonitorConfig {

    @Value("${monitor.host}")
    @RefreshField("monitor.host")
    private String monitorHost;
}
