package com.ksyun.cfwcore.monitor.api;

import com.amazonaws.util.json.Jackson;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.monitor.MonitorConfig;
import com.ksyun.cfwcore.monitor.api.domain.*;
import com.ksyun.comm.util.Commons;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.ObjectPlaceholderResolver;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
@Component
public class MonitorAPI {

	private final String CREATE_MONITOR_URL = "${monitorHost}/monitorapi/host/create";
	private final String DELETE_MONITOR_URL = "${monitorHost}/monitorapi/host/delete";
	private final String UPDATE_MONITOR_URL = "${monitorHost}/monitorapi/host/updateHostInstance";
	private final String CHECK_MONITOR_URL = "${monitorHost}/monitorapi/host/checkInstanceStatus";


	@Autowired
	protected MonitorConfig monitorConfig;

	@Autowired
	protected PropertyPlaceholderHelper placeholderHelper;

	@Autowired
	protected RestTemplate  restTemplate;

	/**
	 * 创建云监听实例
	 * @return
	 */
	public MonitorResponse createMonitor(ProxyAuth auth, MonitorCreateParam param) {
		return postForObject(auth, CREATE_MONITOR_URL, param, MonitorResponse.class, HttpMethod.POST);
	}

	/**
	 * 删除云监听实例
	 * @return
	 */
	public MonitorResponse deleteMonitor(ProxyAuth auth, MonitorDeleteParam param) {
		return postForObject(auth, DELETE_MONITOR_URL, param, MonitorResponse.class, HttpMethod.POST);
	}

	/**
	 * 更新云监控实例
	 * @param auth
	 * @param param
	 * @return
	 */
	public MonitorResponse updateMonitor(ProxyAuth auth, MonitorUpdateParam param){
		return postForObject(auth, UPDATE_MONITOR_URL, param, MonitorResponse.class, HttpMethod.PUT);
	}

	/**
	 * Post 请求封装
	 *
	 * @param auth
	 *            auth封装对象
	 * @param pattern_url
	 *            占位符url
	 * @param param
	 *            入参对象， 对象属性要和pattern_url中的占位符字段相同
	 * @param T
	 *
	 * @Param httpMethod
	 *            返回结果对象类
	 * @return
	 */
	private <T> T postForObject(ProxyAuth auth, String pattern_url,
								Object param, Class<T> T, HttpMethod httpMethod) {
		StopWatch watch = new StopWatch();
		watch.start();
		// 访问url
		String url = placeholderHelper.replacePlaceholders(pattern_url,
				new ObjectPlaceholderResolver(monitorConfig));

		HttpEntity<?> entity = generateMonitorHttpEntity(auth,
				new Gson().toJson(param));
		log.info("[POST] {} with param {}", url, entity);

		ResponseEntity<T> r = restTemplate.exchange(url,httpMethod,
				entity, T);
		T t = r.getBody();
		watch.stop();
		Long tempTime = watch.getTime();
		if (log.isDebugEnabled()) {
			log.debug("[POST] {} response {} and need time {} ms", url, t,
					tempTime);
		} else {
			if (tempTime > 100) {
				log.info("[POST] {} response {} and need time {} ms", url, t,
						tempTime);
			}
		}
		Assert.notNull(t, "getLines response is null");
		return t;
	}

	private HttpEntity<?> generateMonitorHttpEntity(ProxyAuth auth, Object param) {
		HttpHeaders headers = new HttpHeaders();
		// assert
		Assert.hasText(auth.getRequest_id(), String.format("%s is empty",
				HttpEntityWapper.HEADER_X_KSC_REQUEST_ID));

		Assert.hasText(auth.getAccount_id(), String.format("%s is empty",
				HttpEntityWapper.HEADER_X_KSC_ACCOUNT_ID));

		Assert.hasText(auth.getRegion(), String.format("%s is empty",
				HttpEntityWapper.HEADER_X_KSC_REGION));

		// header
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON));
		headers.set(HttpEntityWapper.HEADER_X_KSC_REQUEST_ID,
				auth.getRequest_id());
		headers.set(HttpEntityWapper.HEADER_X_KSC_ACCOUNT_ID,
				auth.getAccount_id());
		// region 转换
//		String region = monitor_config.getRegion_cache().get(auth.getRegion());
		headers.set(HttpEntityWapper.HEADER_X_KSC_REGION, auth.getRegion());
		return new HttpEntity<Object>(param, headers);
	}
}
