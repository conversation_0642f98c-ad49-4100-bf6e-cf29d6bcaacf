package com.ksyun.cfwcore.monitor.api.domain;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorCreateParam  {
	@Expose
	String regionKey;// 机房
	@Expose
	int productType;//产品类型：0：kec 1：ks3 2：rds 3：kts 4：eip 5:kmr 6：kcs 7：slb 8：listener 10：nat 11：bws     999：表所有 (必填参数) 
	@Expose
	String instanceId;// 要加入监控的实例id，多个id之间以逗号分隔
	@Expose
	String ip;// 云主机所在物理机ip，主机业务线这个参数必填，其他业务线这个参数选填
	@Expose
	String hostName;// 实例名
	@Expose
	String guestIp;// 云主机虚机ip
	@Expose
	String osType;// 操作系统类型
	@Expose
	String[] template;// 模板

    /**
     * 属性
     */
    @Expose
    Map<String,Object> extProperty;
}
