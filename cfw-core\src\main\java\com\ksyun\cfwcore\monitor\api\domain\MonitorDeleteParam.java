package com.ksyun.cfwcore.monitor.api.domain;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorDeleteParam {

	@Expose
	String[] instanceIdList;// 实例id

	@Expose
	int productType;// 产品类型(0表示云主机，1表示KS3，2表示RDS，3表示KTS，4表示EIP，5表示kmr，6表示kcs，9表示概要监空)
}
