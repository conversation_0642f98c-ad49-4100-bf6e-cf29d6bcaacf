package com.ksyun.cfwcore.monitor.api.domain;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorUpdateParam {
    @Expose
    private String instanceId;// 要加入监控的实例id，多个id之间以逗号分隔
    @Expose
    private String ip;// 云主机所在物理机ip，主机业务线这个参数必填，其他业务线这个参数选填
    @Expose
    private String instanceName;// 实例名
    @Expose
    private Integer productType;//产品类型
}
