package com.ksyun.cfwcore.monitor.enums;

import org.apache.commons.lang.StringUtils;

public enum MonitorProductTypeEnum {
    KEC(0), // 云主机
    KS3(1), // KS3
    RDS(2), // RDS
    KTS(3), // KTS
    EIP(4), // EIP
    KMR(5), // KMR
    KCS(6), // KCS
    SLB(7), // SLB
    LISTENER(8),//listener
    OM(9),// 概要监控
    NAT(10),//NAT
    BWS(11), //bws
    PEERING(16),//对等连接
    DC_GW(17),//边界网关
    VPN_TUNNEL_GW(18),//VPNTunnel
    LISTENER_HTTP(24),
    LISTENER_TCP(25),
    LISTENER_HTTPS(26),
    LISTENER_UDP(27),
    CEN_BANDWIDTH_PACKAGE(126),
    LISTENER_QUIC(135),
    ALB_LISTENER_HTTP(139),
    ALB_LISTENER_HTTPS(140),
    ALB_LISTENER_QUIC(141),
    <PERSON><PERSON>(131),
    <PERSON><PERSON><PERSON>(153),;

    MonitorProductTypeEnum(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    private int type;

    public static MonitorProductTypeEnum getListenerProductType(String protocol) {
        if(StringUtils.isNotEmpty(protocol)) {
            if ("HTTP".equals(protocol.toUpperCase())) {
                return ALB_LISTENER_HTTP;
            } else if ("HTTPS".equals(protocol.toUpperCase())) {
                return ALB_LISTENER_HTTPS;
            } else if ("TCP".equals(protocol.toUpperCase())) {
                return LISTENER_TCP;
            } else if ("UDP".equals(protocol.toUpperCase())) {
                return LISTENER_UDP;
            } else if ("QUIC".equals(protocol.toUpperCase())) {
                return ALB_LISTENER_QUIC;
            }
        }
        return LISTENER;
    }
}
