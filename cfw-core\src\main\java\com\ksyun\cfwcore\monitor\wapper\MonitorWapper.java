package com.ksyun.cfwcore.monitor.wapper;

import com.ksyun.cfwcore.monitor.api.MonitorAPI;
import com.ksyun.cfwcore.monitor.api.domain.*;
import com.ksyun.common.proxy.ProxyAuth;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MonitorWapper {

	@Autowired
	private MonitorAPI monitorApi;
	
	/**
	 * 创建云监听实例
	 * @return
	 */
	public MonitorResponse createMonitor(ProxyAuth auth, MonitorCreateParam param) {
		return monitorApi.createMonitor(auth, param);
	}
	
	/**
	 * 删除云监听实例
	 * @return
	 */
	public MonitorResponse deleteMonitor(ProxyAuth auth, MonitorDeleteParam param) {
		return monitorApi.deleteMonitor(auth, param);
	}

	/**
	 * 更新云监控实例
	 * @param auth
	 * @param param
	 * @return
	 */
	public MonitorResponse updateMonitor(ProxyAuth auth, MonitorUpdateParam param){
		return monitorApi.updateMonitor(auth,param);
	}

}
