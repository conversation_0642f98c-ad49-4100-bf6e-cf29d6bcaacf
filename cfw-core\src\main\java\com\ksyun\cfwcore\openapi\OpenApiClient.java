package com.ksyun.cfwcore.openapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ksyun.cfwcore.openapi.kec.domain.RequestParam;
import com.ksyun.cfwcore.openapi.kec.domain.RunInstancesRequest;
import com.ksyun.cfwcore.openapi.kec.domain.RunInstancesResponse;
import common.BaseClient;
import common.Credential;
import common.aws.AWS4EncryptionFactory;
import common.utils.HttpClientUtils;
import common.utils.SignUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Classname RunInstancesClient
 * @Description 创建实例
 */
@Log4j2
@Component
public class OpenApiClient extends BaseClient {

    /**
     * post请求
     *
     * @param path
     * @param requestObj
     * @param head
     * @return
     * @throws Exception
     */
    public <T, R> R  doPost(String path, T requestObj, Map<String, String> head,RequestParam param,Class<R> clazz) throws Exception {
        if (head == null) {
            head = new HashMap<>();
        }
        //参数配置
        JSONObject requestParams = null;
        if (head.get("Content-Type").equalsIgnoreCase("application/json")){
            requestParams = getPostRawRequestParams(requestObj,param);
        }else {
            requestParams = getSimpleRequestParams(requestObj,param);
        }
        String response = HttpClientUtils.httpPost(path, requestParams, head);
        log.info("doPost end,path:{},params:{},head:{},response:{}", path, requestParams, head,response);
        return JSON.parseObject(response, clazz);
    }

    /**
     * get 请求
     *
     * @param path
     * @param requestObj
     * @return
     * @throws Exception
     */
    public RunInstancesResponse doGet(String path, RunInstancesRequest requestObj) throws Exception {
        return doGet(path, requestObj);
    }

    /**
     * doDelete 请求
     *
     * @param path
     * @param requestObj
     * @return
     * @throws Exception
     */
    public RunInstancesResponse doDelete(String path, RunInstancesRequest requestObj,Credential credential) throws Exception {
        return doDelete(path, requestObj, credential);
    }

    /**
     * doDelete 请求
     *
     * @param path
     * @param requestObj
     * @param head
     * @return
     * @throws Exception
     */
    public RunInstancesResponse doDelete(String path, RunInstancesRequest requestObj, Map<String, String> head,Credential credential,RequestParam param) throws Exception {
        JSONObject requestParams = getRequestParams(requestObj,credential,param);
        String response = HttpClientUtils.httpDelete(path, requestParams, head);
        log.info("doDelete end,path:{},params:{},head:{}", path, requestParams, head);
        RunInstancesResponse RunInstancesResponse = JSON.parseObject(response, RunInstancesResponse.class);
        return RunInstancesResponse;
    }


    /**
     * doPut 请求
     *
     * @param path
     * @param requestObj
     * @return
     * @throws Exception
     */
    public RunInstancesResponse doPut(String path, RunInstancesRequest requestObj,Credential credential) throws Exception {
        return doPut(path, requestObj, credential);
    }

    /**
     * doPut 请求
     *
     * @param path
     * @param requestObj
     * @param head
     * @return
     * @throws Exception
     */
    public RunInstancesResponse doPut(String path, RunInstancesRequest requestObj, Map<String, String> head,Credential credential,RequestParam param) throws Exception {
        JSONObject requestParams = getRequestParams(requestObj,credential,param);
        String response = HttpClientUtils.httpPut(path, requestParams, head);
        log.info("httpPut end,path:{},params:{},head:{}", path, requestParams, head);
        RunInstancesResponse RunInstancesResponse = JSON.parseObject(response, RunInstancesResponse.class);
        return RunInstancesResponse;
    }

    /**
     * get 请求
     *
     * @param path
     * @param requestObj
     * @param head
     * @return
     * @throws Exception
     */
    public <T, R> R doGet(String path, T requestObj, Map<String, String> head,RequestParam param,Class<R> clazz) throws Exception {
        if (head == null) {
            head = new HashMap<>();
        }

        //参数配置
        JSONObject requestParams = getSimpleRequestParams(requestObj,param);

        String response = HttpClientUtils.httpGet(path, requestParams, head);
        log.info("doGet end,path:{},params:{},head:{}", path, requestParams, head);
        return JSON.parseObject(response, clazz);
    }


    /**
     * 构造请求参数
     *
     * @param requestObj
     * @return
     */
    private JSONObject getRequestParams(RunInstancesRequest requestObj, Credential credential, RequestParam param) throws Exception {
        JSONObject requestParams = new JSONObject();
        //设置证书
        getCommonParams(credential, requestParams);
        //设置接口属性
        requestParams.put("Service", param.getService());
        requestParams.put("Action", param.getAction());
        requestParams.put("Version", param.getVersion());

        //设置请求体请求参数
        setRequestField(requestObj,requestParams);

        //签名
        String signature = SignUtils.signature(requestParams, credential.getSignStr());
        requestParams.put("Signature", signature);
        return requestParams;
    }

    private static void enhanceAws4Signature(Map<String, String> head, Map<String, Object> params, Credential credential, String requestMethod, RequestParam param) {
        AWS4EncryptionFactory aws4EncryptionFactory = new AWS4EncryptionFactory(credential.getSecretKey(), credential.getSignStr(), param.getService(), credential.getRegion());

        //设置请求参数
        if (params != null) {
            params.entrySet().forEach(entry -> {
                aws4EncryptionFactory.setParamMap(entry.getKey(), entry.getValue());
            });
        }

        //设置请求头
        if (head != null) {
            head.entrySet().forEach(entry -> {
                aws4EncryptionFactory.setHeadMap(entry.getKey(), entry.getValue());
            });
        }

        //aws 加密
        aws4EncryptionFactory.generateSignature(requestMethod);

        //回填aws4 签名
        String authorization = aws4EncryptionFactory.getHead().get(AWS4EncryptionFactory.X_Authorization);
        String xAmzDate = aws4EncryptionFactory.getHead().get(AWS4EncryptionFactory.X_AMZ_DATA);
        head.put(AWS4EncryptionFactory.X_Authorization, authorization);
        head.put(AWS4EncryptionFactory.X_AMZ_DATA, xAmzDate);
    }

    private <T> JSONObject getSimpleRequestParams(T requestObj,RequestParam param) throws Exception {
        JSONObject requestParams = new JSONObject();
        //设置接口属性
        requestParams.put("Action", param.getAction());
        requestParams.put("Version", param.getVersion());

        //设置请求体请求参数
        setRequestField(requestObj, requestParams);
        return requestParams;
    }

    private <T>  JSONObject getPostRawRequestParams(T requestObj,RequestParam param) throws Exception {
        JSONObject requestParams = new JSONObject();
        //设置接口属性
        requestParams.put("Action", param.getAction());
        requestParams.put("Version", param.getVersion());

        //设置请求体请求参数
        setRequestFieldForPostRaw(requestObj, requestParams);
        return requestParams;
    }
}
