package com.ksyun.cfwcore.openapi.kec;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.HeaderConstant;
import com.ksyun.cfwcore.openapi.OpenApiClient;
import com.ksyun.cfwcore.openapi.kec.domain.*;
import com.ksyun.cfwcore.utils.GUIDGeneratorUtil;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ksyun.cfwcore.config.KecConfig;


import java.util.*;

@Log4j2
@Component
public class RunInstancesAPI {
    @Autowired
    private OpenApiClient openApiClient;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private KecConfig kecConfig;

    public Set<RunInstancesResponse.InstanceResponse> createRunInstances(RunInstancesRequest requestObj, ProxyAuth auth) throws Exception {
        log.info("开始创建云服务器，auth:{},param:{},kecConfig{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(requestObj),JSONUtil.toJsonStr(kecConfig));
        String action = "RunInstances";
        RequestParam requestParam = new RequestParam(kecConfig.getKecService(), action, kecConfig.getKecVersion());
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderConstant.X_KSC_REQUEST_ID, auth.getRequest_id() + "-" + GUIDGeneratorUtil.generateGUID());
        headers.put(HeaderConstant.X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.put(HeaderConstant.X_KSC_REGION, auth.getRegion());
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        RunInstancesResponse response = openApiClient.doPost(kecConfig.getKecUrl(), requestObj, headers, requestParam, RunInstancesResponse.class);
        log.info("创建云服务器结束，auth:{},response:{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && CollectionUtil.isNotEmpty(response.getInstancesSet())) {
            return response.getInstancesSet();
        }
        return null;
    }

    public Set<DescribeInstancesResponse.InstanceResponse> describeInstances(DescribeInstancesRequest requestObj, ProxyAuth auth) throws Exception {
        log.info("开始查询云服务器，auth:{},param:{},kecConfig{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(requestObj),JSONUtil.toJsonStr(kecConfig));
        String action = "DescribeInstances";
        RequestParam requestParam = new RequestParam(kecConfig.getKecService(), action, kecConfig.getKecVersion());
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderConstant.X_KSC_REQUEST_ID, auth.getRequest_id());
        headers.put(HeaderConstant.X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.put(HeaderConstant.X_KSC_REGION, auth.getRegion());
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        DescribeInstancesResponse response = openApiClient.doPost(kecConfig.getKecUrl(), requestObj, headers, requestParam, DescribeInstancesResponse.class);
        log.info("查询云服务器结束，auth:{},response:{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && CollectionUtil.isNotEmpty(response.getInstancesSet())) {
            return response.getInstancesSet();
        }
        return null;
    }

    public Set<TerminateInstancesResponse.InstanceResponse> deleteInstances(TerminateInstancesRequest requestObj, ProxyAuth auth) throws Exception {
        String action = "TerminateInstances";
        RequestParam requestParam = new RequestParam(kecConfig.getKecService(), action, kecConfig.getKecVersion());
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderConstant.X_KSC_REQUEST_ID, auth.getRequest_id());
        headers.put(HeaderConstant.X_KSC_ACCOUNT_ID, auth.getAccount_id());
        headers.put(HeaderConstant.X_KSC_REGION, auth.getRegion());
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        requestObj.setForceDelete(commonConfig.isDeleteInstanceNow());
        log.info("开始删除云服务器，auth:{},param:{},kecConfig{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(requestObj),JSONUtil.toJsonStr(kecConfig));
        TerminateInstancesResponse response = openApiClient.doPost(kecConfig.getKecUrl(), requestObj, headers, requestParam, TerminateInstancesResponse.class);
        log.info("删除云服务器结束，auth:{},response:{}",JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(response));
        if (Objects.nonNull(response) && CollectionUtil.isNotEmpty(response.getInstancesSet())) {
            return response.getInstancesSet();
        }
        return null;
    }


}
