package com.ksyun.cfwcore.openapi.kec.domain;

import common.annotation.KsYunField;
import lombok.Data;

import java.util.List;

/**
* @Classname DescribeInstancesRequest
* @Description 请求参数
*/
@Data
public class DescribeInstancesRequest{
    /**实例Id。*/
    @KsYunField(name="InstanceId")
    private List<String> instanceId;

    /**项目Id,多项目id使用‘,’ 分隔。*/
    @KsYunField(name="ProjectId")
    private List<String> projectId;

    @KsYunField(name="MaxResults")
    private int maxResults = 100;
}