package com.ksyun.cfwcore.openapi.kec.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import common.BaseResponseModel;
import lombok.Data;
import lombok.ToString;

import java.util.Set;

/**
 * @Classname DescribeInstancesResponse
 * @Description DescribeInstances 返回体
 */
@Data
@ToString
public class DescribeInstancesResponse extends BaseResponseModel {
    /**
     * 分页数据
     */
    @Expose
    @JsonProperty("Marker")
    private Integer marker;

    /**
     * 实例总数
     */
    @Expose
    @JsonProperty("InstanceCount")
    private Integer instanceCount;

    /**
     * 实例集合
     */
    @Expose
    @JsonProperty("InstancesSet")
    private Set<DescribeInstancesResponse.InstanceResponse> instancesSet;

    @Data
    public static class InstanceResponse {

        @JsonProperty("InstanceId")
        private String instanceId;
        @JsonProperty("ProjectId")
        private int projectId;
        @JsonProperty("InstanceName")
        private String instanceName;
        @JsonProperty("InstanceType")
        private String instanceType;
        @JsonProperty("IsDistributeIpv6")
        private boolean isDistributeIpv6;
        @JsonProperty("InstanceConfigure")
        private InstanceConfigure instanceConfigure;
        @JsonProperty("ImageId")
        private String imageId;
        @JsonProperty("SubnetId")
        private String subnetId;
        @JsonProperty("PrivateIpAddress")
        private String privateIpAddress;
        @JsonProperty("InstanceState")
        private InstanceState instanceState;
        @JsonProperty("Monitoring")
        private Monitoring monitoring;
        @Expose
        @JsonProperty("NetworkInterfaceSet")
        private Set<NetworkInterfaceSet> networkInterfaceSet;
        @JsonProperty("SriovNetSupport")
        private String sriovNetSupport;
        @JsonProperty("IsShowSriovNetSupport")
        private boolean isShowSriovNetSupport;
        @JsonProperty("CreationDate")
        private String creationDate;
        @JsonProperty("AvailabilityZone")
        private String availabilityZone;
        @JsonProperty("AvailabilityZoneName")
        private String availabilityZoneName;
        @JsonProperty("DedicatedUuid")
        private String dedicatedUuid;
        @JsonProperty("ProductType")
        private int productType;
        @JsonProperty("ProductWhat")
        private int productWhat;
        @JsonProperty("LiveUpgradeSupport")
        private boolean liveUpgradeSupport;
        @JsonProperty("FailureAutoDelete")
        private boolean failureAutoDelete;
        @JsonProperty("SyncTag")
        private boolean syncTag;
        @JsonProperty("ChargeType")
        private String chargeType;
        @JsonProperty("SystemDisk")
        private SystemDisk systemDisk;
        @JsonProperty("HostName")
        private String hostName;
        @JsonProperty("Migration")
        private int migration;
        @JsonProperty("CabinetName")
        private String cabinetName;
        @JsonProperty("RackName")
        private String rackName;
        @JsonProperty("VncSupport")
        private boolean vncSupport;
        @JsonProperty("IsProtect")
        private boolean isProtect;
        @JsonProperty("BootMode")
        private String bootMode;
    }

    @Data
    public static class InstanceConfigure {
        @JsonProperty("VCPU")
        private int vvpu;
        @JsonProperty("GPU")
        private int gpu;
        @JsonProperty("MemoryGb")
        private int memoryGb;
        @JsonProperty("DataDiskGb")
        private int dataDiskGb;
        @JsonProperty("RootDiskGb")
        private int rootDiskGb;
        @JsonProperty("DataDiskType")
        private String dataDiskType;
        @JsonProperty("VGPU")
        private String vgpu;
    }
    @Data
    public static class InstanceState {
        @JsonProperty("Name")
        private String name;
        @JsonProperty("OnMigrate")
        private boolean onMigrate;
        @JsonProperty("CostTime")
        private String costTime;
        @JsonProperty("TimeStamp")
        private String timeStamp;
    }
    @Data
    public static class Monitoring {
        @JsonProperty("State")
        private String state;
    }
    @Data
    public static class NetworkInterfaceSet {
        @JsonProperty("NetworkInterfaceId")
        private String networkInterfaceId;
        @JsonProperty("NetworkInterfaceType")
        private String networkInterfaceType;
        @JsonProperty("VpcId")
        private String vpcId;
        @JsonProperty("SubnetId")
        private String subnetId;
        @JsonProperty("MacAddress")
        private String macAddress;
        @JsonProperty("PrivateIpAddress")
        private String privateIpAddress;
        @JsonProperty("GroupSet")
        private Set<GroupSet> groupSet;
        @JsonProperty("SecurityGroupSet")
        private Set<SecurityGroupSet> securityGroupSet;
        @JsonProperty("NetworkInterfaceName")
        private String networkInterfaceName;
    }
    @Data
    public static class GroupSet {
        @JsonProperty("GroupId")
        private String groupId;
    }
    @Data
    public static class SecurityGroupSet {
        @JsonProperty("SecurityGroupId")
        private String securityGroupId;
    }
    @Data
    public static class SystemDisk {
        @JsonProperty("DiskType")
        private String diskType;
        @JsonProperty("DiskSize")
        private int diskSize;
    }

}
