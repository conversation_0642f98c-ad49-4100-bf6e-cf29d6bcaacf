package com.ksyun.cfwcore.openapi.kec.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import common.BaseResponseModel;
import lombok.Data;
import lombok.ToString;

import java.util.Set;

/**
* @Classname RunInstancesResponse
* @Description RunInstances 返回体
*/
@Data
@ToString
public class RunInstancesResponse extends BaseResponseModel {
    @Expose
    @JsonProperty("InstancesSet")
    private Set<RunInstancesResponse.InstanceResponse> instancesSet;
    @Data
    @ToString
    public static class InstanceResponse {
        @Expose
        @JsonProperty("InstanceId")
        private String instanceId;
        @Expose
        @JsonProperty("InstanceName")
        private String instanceName;
    }
}
