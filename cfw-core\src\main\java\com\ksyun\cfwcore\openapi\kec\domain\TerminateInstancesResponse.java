package com.ksyun.cfwcore.openapi.kec.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import common.BaseResponseModel;
import lombok.Data;
import lombok.ToString;

import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@ToString
public class TerminateInstancesResponse extends BaseResponseModel {

    @Expose
    @JsonProperty("InstancesSet")
    private Set<TerminateInstancesResponse.InstanceResponse> instancesSet;
    @Data
    @ToString
    public static class InstanceResponse {
        @Expose
        @JsonProperty("InstanceId")
        private String instanceId;

        @Expose
        @JsonProperty("Return")
        private boolean Return;
    }
}
