package com.ksyun.cfwcore.openapi.network.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import common.annotation.KsYunField;
import lombok.Data;

import java.util.Map;

@Data
public class CommonQueryParam {
    @Expose
    @SerializedName("productLine")
    private String productLine;
    @Expose
    @SerializedName("fields")
    private String fields;
    @Expose
    @SerializedName("from")
    private Integer from=0;
    @Expose
    @SerializedName("size")
    private Integer size=100;
    @Expose
    @SerializedName("queryMap")
    private Map<String,Object> queryMap;
    @Expose
    @SerializedName("isCount")
    private Boolean isCount=Boolean.FALSE;
    @Expose
    @SerializedName("Agg")
    private String Agg;
    @Expose
    @SerializedName("AggSize")
    private Integer AggSize=100;
    @Expose
    @SerializedName("useDefault")
    private Boolean useDefault=true;// 默认当前机房及deleteStatus=0
    @Expose
    @SerializedName("checkCurrentUser")
    private Boolean checkCurrentUser = true;// 默认查询当前账号
}
