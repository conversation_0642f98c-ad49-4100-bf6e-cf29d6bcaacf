package com.ksyun.cfwcore.openapi.network.domain;

import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommonQueryResponse<T> {
    @Expose
    private Integer total;
    @Expose
    private Integer from;
    @Expose
    private Integer size;
    @Expose
    private List<Item<T>> items=new ArrayList<>();

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class Item<T>{
        @Expose
        private Map<String,Object> base;
        @Expose
        private T extension;
    }
}


