package com.ksyun.cfwcore.openstack;

public interface OpenstackConstants {

    /**
     * Proxy代理地址
     */
    String PROXY_URL = "http://${proxy_host}:${proxy_port}";

    /**
     * 配额地址
     */
    String QUOTA_URL = "http://${quota_url}";
    /**
     * KeyStone地址 获取用户与TenantId对应关系
     */
    String KEYSTONE_URL = "http://${keystoneHost}:${keystonePort}";
    /**
     * Neutron Endpoint
     */
    String NEUTRON_ENDPOINT_TYPE = "network";
    /**
     * Nova Endpoint
     */
    String COMPUTE_ENDPOINT_TYPE = "compute";
    /**
     * 公网Dns Endpoint
     */
    String DNS_ENDPOINT_TYPE = "designate";
    /**
     * CDN 专用公网DNS Endpoint
     */
    String DNS_CDN_ENDPOINT_TYPE = "designate-cdn";

    /**
     * 云企业网 Endpoint
     */
    String CEN_ENDPOINT_TYPE = "cen";

    String LIMIT = "limit";
    String MARKER = "marker";
}
