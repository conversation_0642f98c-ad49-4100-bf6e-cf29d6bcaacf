package com.ksyun.cfwcore.openstack.cfw.eip;

import com.google.common.collect.Lists;
import com.ksyun.cfwcore.config.ProxyExtraConfig;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.OpenStackUrlConstants;
import com.ksyun.cfwcore.openstack.OpenstackConstants;
import com.ksyun.cfwcore.openstack.cfw.eip.domain.OpenstackFloatingip;
import com.ksyun.cfwcore.proxy.ProxyAPI;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.*;

@Component
@Slf4j
public class EipAPI extends ProxyAPI {
    @Autowired
    private ProxyExtraConfig proxyExtraConfig;
    private final ExecutorService executorCommonService = new ThreadPoolExecutor(10, 10,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

    public void synFloatingipList(ProxyAuth auth, List<String> eipIdList) throws Exception {
        setExtraHeaders(auth);
        List<List<String>> lists = Lists.partition(eipIdList, CommonConstant.BATCH_HANDLE_SIZE_FIVE);
        for (List<String> list : lists) {
            final CountDownLatch latch = new CountDownLatch(list.size());
            for (String eipId : list) {
                executorCommonService.execute(() -> {
                    try {
                        queryFloatingip(auth, eipId);
                    } catch (Exception e) {
                        log.error("创建云服务器:{}失败 error:{}", eipId, e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        }
    }

    public OpenstackFloatingip queryFloatingip(ProxyAuth auth, String eipId) {
        String url = String.format(getUrl(OpenStackUrlConstants.DETACH_EIPS_FROM_FW), eipId);
        OpenstackFloatingip openstackFloatingip = query(auth, url, OpenstackFloatingip.class);
        return openstackFloatingip;
    }

    public void setExtraHeaders(ProxyAuth auth) throws Exception {
        auth.setType(OpenstackConstants.NEUTRON_ENDPOINT_TYPE);
        auth.getExtraHeaders().put("Content-Type", "application/json");
        auth.getExtraHeaders().put("X-KSC-APPLICATION-TOKEN", proxyExtraConfig.getProxyNeutronToken());
        auth.getExtraHeaders().put("X-KSC-APPLICATION-NAME", proxyExtraConfig.getProxyNeutronAppname());
    }
}
