package com.ksyun.cfwcore.openstack.cfw.eip.domain;

import com.google.gson.annotations.Expose;
import com.ksyun.cfwcore.openstack.domain.OpenStackLink;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
public class OpenStackFloatingipsLinks {
	@Expose
	private String href;
	@Expose
	private String rel;

	public OpenStackFloatingipsLinks(OpenStackLink openStackLink){
		this.href=openStackLink.getHref();
		this.rel=openStackLink.getRel();
	}
}
