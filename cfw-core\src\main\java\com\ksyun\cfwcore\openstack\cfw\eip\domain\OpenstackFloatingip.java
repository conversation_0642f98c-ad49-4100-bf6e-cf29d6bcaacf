package com.ksyun.cfwcore.openstack.cfw.eip.domain;

import com.google.gson.annotations.Expose;
import com.ksyun.cfwcore.enums.ResourceType;
import com.ksyun.cfwcore.openstack.domain.ProjectResource;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class OpenstackFloatingip extends ProjectResource {
	@Expose
	private String id;
	@Expose
	private String tenant_id;
	@Expose
	private String floating_network_id;
	@Expose
	private String type;
	@Expose
	private String usage_type;
	@Expose
	private String floating_ip_address;
	@Expose
	private Integer egress;
	@Expose
	private Integer ingress;
	@Expose
	private String igw_id;
	@Expose
	private String router_id;
	@Expose
	private String lb_pool_id;
	@Expose
	private String natpool_id;
	@Expose
	private String device_id;
	@Expose
	private String fixed_ip_address;
	@Expose
	private String port_id;
	@Expose
	private String created_at;
	@Expose
	private String bwp_id; // 共享带宽id
	@Expose
	private Boolean admin_state_up;
	@Expose
	private String user_tag;
	@Expose
	private String ip_version;
	@Expose
	private String binding_type;
	@Expose
	private String eip_pool_id;

	@Expose
	private String firewall_id;

	public String getFirewall_id() {
		return firewall_id;
	}

	public void setFirewall_id(String firewall_id) {
		this.firewall_id = firewall_id;
	}

	public String getId() {
		return id;
	}

	public String getTenant_id() {
		return tenant_id;
	}

	public String getFloating_network_id() {
		return floating_network_id;
	}

	public String getType() {
		return type;
	}

	public String getUsage_type() {
		return usage_type;
	}

	public String getFloating_ip_address() {
		return floating_ip_address;
	}

	public Integer getEgress() {
		return egress;
	}

	public Integer getIngress() {
		return ingress;
	}

	public String getIgw_id() {
		return igw_id;
	}

	public String getRouter_id() {
		return router_id;
	}

	public String getLb_pool_id() {
		return lb_pool_id;
	}

	public String getNatpool_id() {
		return natpool_id;
	}

	public String getDevice_id() {
		return device_id;
	}

	public String getFixed_ip_address() {
		return fixed_ip_address;
	}

	public String getPort_id() {
		return port_id;
	}

	public String getCreated_at() {
		if(StringUtils.isNotEmpty(created_at)) {
			if (created_at.lastIndexOf(".") > 0) {
				created_at = created_at.substring(0, created_at.lastIndexOf("."));
			}
			created_at = created_at.replaceAll("T", " ");
		}
		return created_at;
	}

	public void setId(String id) {
		this.id = id;
	}

	public void setTenant_id(String tenant_id) {
		this.tenant_id = tenant_id;
	}

	public void setFloating_network_id(String floating_network_id) {
		this.floating_network_id = floating_network_id;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setUsage_type(String usage_type) {
		this.usage_type = usage_type;
	}

	public void setFloating_ip_address(String floating_ip_address) {
		this.floating_ip_address = floating_ip_address;
	}

	public void setEgress(Integer egress) {
		this.egress = egress;
	}

	public void setIngress(Integer ingress) {
		this.ingress = ingress;
	}

	public void setIgw_id(String igw_id) {
		this.igw_id = igw_id;
	}

	public void setRouter_id(String router_id) {
		this.router_id = router_id;
	}

	public void setLb_pool_id(String lb_pool_id) {
		this.lb_pool_id = lb_pool_id;
	}

	public void setNatpool_id(String natpool_id) {
		this.natpool_id = natpool_id;
	}

	public void setDevice_id(String device_id) {
		this.device_id = device_id;
	}

	public void setFixed_ip_address(String fixed_ip_address) {
		this.fixed_ip_address = fixed_ip_address;
	}

	public void setPort_id(String port_id) {
		this.port_id = port_id;
	}

	public void setCreated_at(String created_at) {
		if(StringUtils.isNotEmpty(created_at)) {
			if (created_at.lastIndexOf(".") > 0) {
				created_at = created_at.substring(0, created_at.lastIndexOf("."));
			}
			created_at = created_at.replaceAll("T", " ");
		}
		this.created_at = created_at;
	}

	public String getBwp_id() {
		return bwp_id;
	}

	public void setBwp_id(String bwp_id) {
		this.bwp_id = bwp_id;
	}

	public Boolean getAdmin_state_up() {
		return admin_state_up;
	}

	public void setAdmin_state_up(Boolean admin_state_up) {
		this.admin_state_up = admin_state_up;
	}


	public String getUser_tag() {
		return user_tag;
	}

	public void setUser_tag(String user_tag) {
		this.user_tag = user_tag;
	}

	@Override
	public String getResourceId(){
		return id;
	}

	@Override
	public ResourceType getResourceType() {
		return ResourceType.EIP;
	}

	public String getIp_version() {
//		if(StringUtils.isNotBlank(ip_version)){
//			return  ip_version;
//		}
//		if(floating_ip_address.contains(":")){
//			return Constants.IPV6;
//		}else{
//			return Constants.IPV4;
//		}
		if(StringUtils.isBlank(this.ip_version)){
			this.ip_version="4";
		}
		return this.ip_version;
	}

	public void setIp_version(String ip_version) {
		this.ip_version=ip_version;
	}

	public String getBinding_type() {
		return binding_type;
	}

	public void setBinding_type(String binding_type) {
		this.binding_type = binding_type;
	}

	public String getEip_pool_id() {
		return eip_pool_id;
	}

	public void setEip_pool_id(String eip_pool_id) {
		this.eip_pool_id = eip_pool_id;
	}
}
