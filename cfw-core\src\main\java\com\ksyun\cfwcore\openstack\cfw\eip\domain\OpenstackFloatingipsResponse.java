package com.ksyun.cfwcore.openstack.cfw.eip.domain;

import com.google.gson.annotations.Expose;
import com.ksyun.cfwcore.openstack.domain.OpenStackLink;
import com.ksyun.cfwcore.utils.Stringable;
import lombok.Getter;
import lombok.Setter;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OpenstackFloatingipsResponse extends Stringable {
	@Expose
	public List<OpenstackFloatingip> floatingips;
	@Expose
	public List<OpenStackFloatingipsLinks> floatingips_links;
	@Expose
	private Integer totalCount;

	public void setFloatingips_links(List<OpenStackLink> openStackLinks){
		if(!CollectionUtils.isEmpty(openStackLinks)){
			if(floatingips_links==null){
				floatingips_links=new ArrayList<>();
			}else{
				floatingips_links.clear();
			}
			for(OpenStackLink link:openStackLinks){
				floatingips_links.add(new OpenStackFloatingipsLinks(link));
			}
		}
	}

}
