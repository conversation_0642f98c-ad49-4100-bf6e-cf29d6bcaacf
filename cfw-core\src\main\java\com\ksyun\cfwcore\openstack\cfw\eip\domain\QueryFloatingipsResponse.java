package com.ksyun.cfwcore.openstack.cfw.eip.domain;

import com.ksyun.cfwcore.openstack.wapper.domain.Floatingip;
import com.ksyun.cfwcore.utils.Stringable;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class QueryFloatingipsResponse extends Stringable {
    private List<Floatingip> floatingips;
    private String next_token;
    private String pre_token;
    private Integer totalCount;

    public QueryFloatingipsResponse(OpenstackFloatingipsResponse response) {
        if (response.getFloatingips() != null
                && !response.getFloatingips().isEmpty()) {
            floatingips = new ArrayList<>();
            this.totalCount = response.getTotalCount();
            for (OpenstackFloatingip ip : response.getFloatingips()) {
                Floatingip floatingip = new Floatingip();
                floatingip.setCreated_at(ip.getCreated_at());
                floatingip.setDevice_uuid(ip.getDevice_id());
                floatingip.setEgress(ip.getEgress());
                floatingip.setFixed_ip_address(ip.getFixed_ip_address());
                floatingip.setFloating_ip_address(ip.getFloating_ip_address());
                floatingip.setFloating_network_uuid(ip.getFloating_network_id());
                floatingip.setIgw_uuid(ip.getIgw_id());
                floatingip.setIngress(ip.getIngress());
                floatingip.setLb_pool_uuid(ip.getLb_pool_id());
                floatingip.setPort_uuid(ip.getPort_id());
                floatingip.setRouter_uuid(ip.getRouter_id());
                floatingip.setType(ip.getType());
                floatingip.setUsage_type(ip.getUsage_type());
                floatingip.setUuid(ip.getId());
                floatingip.setBwp_id(ip.getBwp_id());
                floatingip.setIamProjectId(ip.getIamProjectId());
                floatingip.setIpVersion("ipv"+ip.getIp_version());
                floatingip.setUser_tag(ip.getUser_tag());
                floatingip.setChargeType(ip.getChargeType());
                floatingip.setBillType(ip.getBillType());
                floatingip.setProductType(ip.getProductType());
                floatingip.setServiceEndTime(ip.getServiceEndTime());
                floatingip.setBinding_type(ip.getBinding_type());
                floatingip.setAdmin_state_up(ip.getAdmin_state_up());
                floatingip.setNatpool_id(ip.getNatpool_id());
                floatingip.setProductWhat(ip.getProductWhat());
                floatingip.setEip_pool_id(ip.getEip_pool_id());
                floatingip.setFirewall_id(ip.getFirewall_id());
                floatingips.add(floatingip);
            }
        }
    }

}
