package com.ksyun.cfwcore.openstack.cfw.firewall;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.ProxyExtraConfig;
import com.ksyun.cfwcore.constants.OpenStackUrlConstants;
import com.ksyun.cfwcore.openstack.OpenstackConstants;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CfwLbOtResponse;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.EipsToFwParam;
import com.ksyun.cfwcore.proxy.ProxyAPI;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FirewallEipAPI extends ProxyAPI {
    @Autowired
    private ProxyExtraConfig proxyExtraConfig;
    public CfwLbOtResponse.FirewallResponse attachEipsToFw(ProxyAuth auth, EipsToFwParam param,String firewallLbId) throws Exception {
        setExtraHeaders(auth);
        log.info("开始attachEipsToFw，auth:{},param:{},firewallLbId:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(param),firewallLbId);
        CfwLbOtResponse result = modify(auth, param, String.format(getUrl(OpenStackUrlConstants.ATTACH_EIPS_TO_FW),firewallLbId), CfwLbOtResponse.class);
        log.info("attachEipsToFw结束，auth:{},result:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(result));
        if (Objects.nonNull(result) && Objects.nonNull(result.getFirewall())) {
            return result.getFirewall();
        }
        return null;
    }


    public CfwLbOtResponse.FirewallResponse detachEipsToFw(ProxyAuth auth, EipsToFwParam param,String firewallLbId) throws Exception {
        setExtraHeaders(auth);
        log.info("开始detachEipsToFw，auth:{},param:{},firewallLbId:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(param),firewallLbId);
        CfwLbOtResponse result = modify(auth, param, String.format(getUrl(OpenStackUrlConstants.DETACH_EIPS_FROM_FW),firewallLbId), CfwLbOtResponse.class);
        log.info("detachEipsToFw结束，auth:{},result:{},firewallLbId:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(result),firewallLbId);
        if (Objects.nonNull(result) && Objects.nonNull(result.getFirewall())) {
            return result.getFirewall();
        }
        return null;
    }

    public void setExtraHeaders(ProxyAuth auth) throws Exception {
        auth.setType(OpenstackConstants.NEUTRON_ENDPOINT_TYPE);
        auth.getExtraHeaders().put("Content-Type", "application/json");
        auth.getExtraHeaders().put("X-KSC-APPLICATION-TOKEN", proxyExtraConfig.getProxyNeutronToken());
        auth.getExtraHeaders().put("X-KSC-APPLICATION-NAME", proxyExtraConfig.getProxyNeutronAppname());
    }
}
