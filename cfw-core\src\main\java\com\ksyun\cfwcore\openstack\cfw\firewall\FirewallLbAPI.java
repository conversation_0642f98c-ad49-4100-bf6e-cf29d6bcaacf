package com.ksyun.cfwcore.openstack.cfw.firewall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.ProxyExtraConfig;
import com.ksyun.cfwcore.constants.OpenStackUrlConstants;
import com.ksyun.cfwcore.openstack.OpenstackConstants;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CreateCfwLbOtParam;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CfwLbOtResponse;
import com.ksyun.cfwcore.proxy.ProxyAPI;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FirewallLbAPI extends ProxyAPI {
    @Autowired
    protected ProxyExtraConfig proxyExtraConfig;

    public CfwLbOtResponse.FirewallResponse createFirewallLb(ProxyAuth auth, CreateCfwLbOtParam param) throws Exception {
        setExtraHeaders(auth);
        log.info("开始创建lb，auth:{},param:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(param));
        CfwLbOtResponse result = create(auth, param, getUrl(OpenStackUrlConstants.FIREWALL_LB), CfwLbOtResponse.class);
        log.info("创建lb结束，auth:{},result:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(result));
        if (Objects.nonNull(result) && Objects.nonNull(result.getFirewall())) {
            return result.getFirewall();
        }
        return null;
    }

    public void deleteFirewallLb(ProxyAuth auth, List<String> lbs) throws Exception {
        setExtraHeaders(auth);
        log.info("开始删除lb，auth:{},param:{}", JSONUtil.toJsonStr(auth),lbs);
        List<List<String>> lists = ListUtil.split(lbs, 5);
        for (List<String> list : lists) {
            CountDownLatch countDownLatch = new CountDownLatch(list.size());
            for (String lbId : list) {
                try {
                    String url = getUrl(OpenStackUrlConstants.FIREWALL_LB) + "/" + lbId;
                    delete(auth, url);
                }catch (Exception e){
                    log.error("删除lb失败，auth:{},lbId:{},errMsg:{}", JSONUtil.toJsonStr(auth),lbId,e.getMessage());
                }finally{
                    countDownLatch.countDown();
                }
            }
            countDownLatch.await();
        }
    }

    public CfwLbOtResponse.FirewallResponse updateFirewallLb(ProxyAuth auth, CreateCfwLbOtParam param,String firewallLbId) throws Exception {
        setExtraHeaders(auth);
        log.info("开始修改lb，auth:{},param:{},firewallLbId:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(param),firewallLbId);
        CfwLbOtResponse result = modify(auth, param, getUrl(OpenStackUrlConstants.FIREWALL_LB)+"/"+firewallLbId, CfwLbOtResponse.class);
        log.info("修改lb结束，auth:{},result:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(result));
        setExtraHeaders(auth);
        if (Objects.nonNull(result) && Objects.nonNull(result.getFirewall())) {
            return result.getFirewall();
        }
        return null;
    }

    public void setExtraHeaders(ProxyAuth auth) throws Exception {
        auth.setType(OpenstackConstants.NEUTRON_ENDPOINT_TYPE);
        auth.getExtraHeaders().put("Content-Type", "application/json");
        auth.getExtraHeaders().put("X-KSC-APPLICATION-TOKEN", proxyExtraConfig.getProxyNeutronToken());
        auth.getExtraHeaders().put("X-KSC-APPLICATION-NAME", proxyExtraConfig.getProxyNeutronAppname());
    }

}
