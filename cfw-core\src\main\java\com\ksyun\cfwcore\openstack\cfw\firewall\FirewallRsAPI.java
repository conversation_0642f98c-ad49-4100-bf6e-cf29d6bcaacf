package com.ksyun.cfwcore.openstack.cfw.firewall;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.ProxyExtraConfig;
import com.ksyun.cfwcore.constants.OpenStackUrlConstants;
import com.ksyun.cfwcore.openstack.OpenstackConstants;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CreateCfwRsOtParam;
import com.ksyun.cfwcore.openstack.cfw.firewall.domain.CreateCfwRsOtResponse;
import com.ksyun.cfwcore.proxy.ProxyAPI;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FirewallRsAPI extends ProxyAPI {
    @Autowired
    protected ProxyExtraConfig proxyExtraConfig;
    public CreateCfwRsOtResponse.FirewallNodeResponse createFirewallRs(ProxyAuth auth, CreateCfwRsOtParam param) throws Exception {
        setExtraHeaders(auth);
        log.info("开始创建Rs node，auth:{},param:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(param));
        CreateCfwRsOtResponse result = create(auth, param, getUrl(OpenStackUrlConstants.FIREWALL_RS), CreateCfwRsOtResponse.class);
        log.info("创建Rs node 结束，auth:{},result:{}", JSONUtil.toJsonStr(auth),JSONUtil.toJsonStr(result));
        if(Objects.nonNull(result)&&Objects.nonNull(result.getFirewall_node())){
            return result.getFirewall_node();
        }
        return null;
    }

    public void deleteFirewallRs(ProxyAuth auth, List<String> rsList) throws Exception {
        setExtraHeaders(auth);
        log.info("开始删除Rs node，auth:{},param:{}", JSONUtil.toJsonStr(auth),rsList);
        List<List<String>> lists = ListUtil.split(rsList, 5);
        for (List<String> list : lists) {
            CountDownLatch countDownLatch = new CountDownLatch(list.size());
            for (String rsId : list) {
                try {
                    String url = getUrl(OpenStackUrlConstants.FIREWALL_RS)  + "/" + rsId;
                    delete(auth, url);
                } finally {
                    countDownLatch.countDown();
                }
            }
            countDownLatch.await();
        }
    }

    public void setExtraHeaders(ProxyAuth auth) throws Exception {
        auth.setType(OpenstackConstants.NEUTRON_ENDPOINT_TYPE);
        auth.getExtraHeaders().put("Content-Type", "application/json");
        auth.getExtraHeaders().put("X-KSC-APPLICATION-TOKEN", proxyExtraConfig.getProxyNeutronToken());
        auth.getExtraHeaders().put("X-KSC-APPLICATION-NAME", proxyExtraConfig.getProxyNeutronAppname());
    }

}
