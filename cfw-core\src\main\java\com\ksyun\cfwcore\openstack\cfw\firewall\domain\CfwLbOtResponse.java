package com.ksyun.cfwcore.openstack.cfw.firewall.domain;

import com.google.gson.annotations.Expose;
import lombok.Data;

@Data
public class CfwLbOtResponse {
    @Expose
    private CfwLbOtResponse.FirewallResponse firewall;
    @Data
    public static class FirewallResponse {
        @Expose
        private String id;
        @Expose
        private String name;
        @Expose
        private String description;
        @Expose
        private String tenant_id;
        @Expose
        private String user_tag;
        @Expose
        private Integer firewall_vni;
        @Expose
        private Integer rate_in;
        @Expose
        private Integer rate_out;
        @Expose
        private Boolean admin_state_up;
        @Expose
        private String project_id;
        @Expose
        private String created_at;
        @Expose
        private String updated_at;
    }
}
