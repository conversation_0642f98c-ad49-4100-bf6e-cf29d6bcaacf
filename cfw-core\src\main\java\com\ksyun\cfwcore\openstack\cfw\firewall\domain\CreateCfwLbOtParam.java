package com.ksyun.cfwcore.openstack.cfw.firewall.domain;

import com.google.gson.annotations.Expose;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class CreateCfwLbOtParam {
    @Expose
    private FirewallParam firewall;
    @Data
    public static class FirewallParam {
        /**
         * 防火墙lb名称（必填项，不可为空）
         */
        @Expose
        private String name;

        /**
         * 防火墙lb描述
         */
        @Expose
        private String description;

        /**
         * 防护墙开关
         */
        @Expose
        private Boolean admin_state_up = null;

        /**
         * 防火墙限速入速率（单位：Mbps）
         */
        @Expose
        private Integer rate_in;

        /**
         * 防火墙限速出速率（单位：Mbps）
         */
        @Expose
        private Integer rate_out;

    }

}
