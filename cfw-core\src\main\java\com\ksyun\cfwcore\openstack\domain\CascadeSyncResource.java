package com.ksyun.cfwcore.openstack.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ksyun.cfwcore.enums.ResourceType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by xuyaming on 2019-03-25.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CascadeSyncResource {
    /**
     * 级联的表类型
     */
    private ResourceType resourceType;
    /**
     * 级联表查询使用的外键的key
     */
    private String foreignKey;

    @JsonIgnore
    private Class<? extends ProjectResource> clazz;

    /**
     * 级联数据是否支持替换外键（举例vnet可以更换natpool 但是rs不能更换vip）
     */
    private Boolean supportChangeFk;
}
