package com.ksyun.cfwcore.openstack.domain;

import com.ksyun.cfwcore.enums.ResourceStatus;
import com.ksyun.cfwcore.enums.ResourceType;
import com.ksyun.cfwcore.es.domain.ElasticSearchIdQueryResponse;
import com.ksyun.cfwcore.es.domain.ElasticSearchQueryParam;
import com.ksyun.cfwcore.utils.Stringable;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.Data;

/**
 * Created by xuyaming on 2017/11/28.
 */
@Data
public abstract class ProjectResource extends Stringable {

    protected String iamProjectId;

    protected Integer billType;

    protected String chargeType;

    protected String productType;

    protected String serviceEndTime;

    protected String productWhat;


    protected Integer instanceType;

    public abstract String getResourceId();
    //特殊的 因为创建时间字段不统一 这块会根据实现类是否有这个属性来判断是否需要忽略
    public abstract String getCreated_at();
    public abstract String getTenant_id();

    public abstract ResourceType getResourceType();



    public ElasticSearchIdQueryResponse getDataCache() {
        return null;
    }

    public void setDataCache(ElasticSearchIdQueryResponse dateCache) {
    }

    //额外需要执行的参数 在执行索引更新之前进行 比如做一些特殊的补充数据的操作
    public void processBeforeSync(){
    }

    //额外需要执行的参数 在查询进行 比如做一些特殊的补充数据的操作
    public void processAfterQuery(ElasticSearchQueryParam param){
    }

    protected Integer resourceStatus= ResourceStatus.EXIST.getStatus();//0 正常状态（开通/挂起） 1 删除状态

    protected Long resourceCreateTime;


    public ProxyAuth getProxyAuth() {
        return null;
    }
}
