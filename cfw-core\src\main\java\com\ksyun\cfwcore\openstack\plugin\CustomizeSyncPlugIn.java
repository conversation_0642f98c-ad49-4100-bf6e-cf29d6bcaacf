package com.ksyun.cfwcore.openstack.plugin;

import com.ksyun.cfwcore.openstack.domain.ProjectResource;
import com.ksyun.common.proxy.ProxyAuth;

/**
 * 自定义同步处理器插件
 * Created by xuyaming on 2019-08-15.
 */
public interface CustomizeSyncPlugIn<T,S extends ProjectResource> {
    /**
     * 获取实际要同步资源的ID
     * @param t
     * @return
     */
    ActualInfo actualInfo(ProxyAuth auth, T t)throws Exception;

    /**
     * 资源数据整合
     * @param s
     */
    void mergeData(ProxyAuth auth, ActualInfo actualInfo, S s)throws Exception;
}
