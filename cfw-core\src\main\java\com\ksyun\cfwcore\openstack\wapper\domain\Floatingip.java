package com.ksyun.cfwcore.openstack.wapper.domain;

import com.google.gson.annotations.Expose;
import com.ksyun.cfwcore.utils.Stringable;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Floatingip extends Stringable {
    protected String instance_id;
    protected String instanceName;
    protected String uuid;
    protected String floating_network_uuid;
    protected String floating_network_name;
    protected String type;
    protected String usage_type;
    protected String floating_ip_address;
    protected Integer egress;
    protected Integer ingress;
    protected String igw_uuid;
    protected String router_uuid;
    protected String lb_pool_uuid;
    protected String natpool_id;
    protected String device_uuid;
    protected String fixed_ip_address;
    protected String port_uuid;
    protected String created_at;
    protected String bwp_id;
    protected String iamProjectId;
    protected String projectName;
    protected String ipVersion;
    protected String user_tag;
    protected String vif_type;
    protected String ip;
    protected String binding_type;
    protected Integer billType;
    protected String chargeType;
    protected String productType;
    protected String productWhat;
    protected String serviceEndTime;
    protected String hostType;
    protected Boolean admin_state_up;
    protected String eip_pool_id;
    protected String firewall_id;
}
