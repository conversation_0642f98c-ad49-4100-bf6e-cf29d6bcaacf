package com.ksyun.cfwcore.proxy;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.ProxyExtraConfig;
import com.ksyun.comm.component.GsonUtils;
import com.ksyun.common.http.HttpEntityWapper;
import com.ksyun.common.http.ObjectPlaceholderResolver;
import com.ksyun.common.proxy.ProxyAuth;
import com.ksyun.common.proxy.ProxyConfig;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.web.client.RestTemplate;

@Log4j2
@Component
public class ProxyAPI {

    private GsonUtils gsonUtils;

    protected ProxyConfig proxyConfig;

    protected ProxyExtraConfig proxyExtraConfig;

    protected RestTemplate restTemplate;

    protected PropertyPlaceholderHelper placeholderHelper;

    @Autowired
    public void setProxyConfig(ProxyConfig proxyConfig) {
        this.proxyConfig = proxyConfig;
    }

    @Autowired
    public void setProxyExtraConfig(ProxyExtraConfig proxyExtraConfig) {
        this.proxyExtraConfig = proxyExtraConfig;
    }

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Autowired
    public void setPlaceholderHelper(PropertyPlaceholderHelper placeholderHelper) {
        this.placeholderHelper = placeholderHelper;
    }

    @Autowired
    public void setGsonUtils(GsonUtils gsonUtils) {
        this.gsonUtils = gsonUtils;
    }



    /**
     * 新建资源
     */
    public <T> T create(ProxyAuth auth, Object param, String url, Class<T> response, boolean... isNeedNullParam) {
        StopWatch watch = new StopWatch();
        watch.start();
        HttpEntity<?> entity = (isNeedNullParam.length > 0 && isNeedNullParam[0])
                ? HttpEntityWapper.generateHttpEntity(auth, param)
                : HttpEntityWapper.generateHttpEntity(auth, gsonUtils.getCommonGson().toJson(param));
        log.info("[POST] {} with param {}", url, gsonUtils.getCommonGson().toJson(param));
        ResponseEntity<T> r = restTemplate.exchange(url, HttpMethod.POST, entity, response);
        T t = r.getBody();
        watch.stop();
        Long tempTime = watch.getTime();
        log.debug("[POST] {} response {} and need time {} ms", url, t, tempTime);
        return t;
    }

    public <T> T modify(ProxyAuth auth, Object param, String url, Class<T> response, boolean... isNeedNullParam) {
        return modify(false, auth, param, url, response, isNeedNullParam);
    }

    /**
     * 更新资源
     * @param isAdminUser neutron需要admin权限时传true
     * @param auth
     * @param param
     * @param url
     * @param response
     * @param isNeedNullParam
     * @param <T>
     * @return
     */
    public <T> T modify(boolean isAdminUser, ProxyAuth auth, Object param, String url, Class<T> response, boolean... isNeedNullParam) {
        StopWatch watch = new StopWatch();
        watch.start();
        HttpEntity<?> entity = (isNeedNullParam.length > 0 && isNeedNullParam[0])
                ? HttpEntityWapper.generateHttpEntity(auth, param)
                : HttpEntityWapper.generateHttpEntity(auth, param == null ? null : gsonUtils.getCommonGson().toJson(param));

        if (isAdminUser) {
            entity = new HttpEntity<>(entity.getBody(), getHeaders(entity));
        }
        log.info("[PUT] {} with param {}", url, gsonUtils.getCommonGson().toJson(param));
        ResponseEntity<T> r = restTemplate.exchange(url, HttpMethod.PUT, entity, response);
        T t = r.getBody();
        watch.stop();
        Long tempTime = watch.getTime();
        log.debug("[PUT] {} response {} and need time {} ms", url, t, tempTime);
        return t;
    }

    /**
     * 删除资源
     */
    public void delete(ProxyAuth auth, String url) {
        StopWatch watch = new StopWatch();
        watch.start();
        HttpEntity<?> entity = HttpEntityWapper.generateHttpEntity(auth, null);
        log.info("[DELETE] {} with param {}", url);
        ResponseEntity<Object> r = restTemplate.exchange(url, HttpMethod.DELETE, entity, Object.class);
        //Object t = r.getBody();
        watch.stop();
        Long tempTime = watch.getTime();
        log.debug("[DELETE] {} response {} and need time {} ms", url, JSONUtil.toJsonStr(r), tempTime);
    }

    public <T> T query(ProxyAuth auth, String url, Class<T> response) {
        return query(auth, url, response, false);
    }

    /**
     * 查询资源
     *
     * @param auth
     * @param url
     * @param response
     * @param isAdminUser neutron需要admin权限时传true
     * @param <T>
     * @return
     */
    public <T> T query(ProxyAuth auth, String url, Class<T> response, boolean isAdminUser) {
        StopWatch watch = new StopWatch();
        watch.start();
        HttpEntity<?> entity = HttpEntityWapper.generateHttpEntity(auth, null);
        if (isAdminUser) {
            entity = new HttpEntity<>(getHeaders(entity));
        }
        log.info("[GET] {} with param {}", url, entity);
        ResponseEntity<T> result = restTemplate.exchange(url, HttpMethod.GET, entity, response);
        log.info("[GET] {} response", url);
        T t = result.getBody();
        watch.stop();
        Long tempTime = watch.getTime();
        log.debug("[GET] {} response and need time {} ms", url, tempTime);
        return t;
    }

    /**
     * 获取请求Url
     */
    protected String getUrl(String replaceUrl, Object param) {
        return placeholderHelper.replacePlaceholders(replaceUrl, new ObjectPlaceholderResolver(proxyConfig, param));
    }

    protected String getUrl(String replaceUrl) {
        return placeholderHelper.replacePlaceholders(replaceUrl, new ObjectPlaceholderResolver(proxyConfig));
    }

    private HttpHeaders getHeaders(HttpEntity<?> entity) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAll(entity.getHeaders().toSingleValueMap());
        headers.set("X-Auth-User", "admin");
        return headers;
    }


}
