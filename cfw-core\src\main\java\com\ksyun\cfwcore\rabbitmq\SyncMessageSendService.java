package com.ksyun.cfwcore.rabbitmq;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.enums.InstanceStatusEnum;
import com.ksyun.cfwcore.enums.NotifySubOrderResult;
import com.ksyun.cfwcore.fw.domain.RollbackFwParam;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.monitor.api.domain.MonitorCreateParam;
import com.ksyun.cfwcore.monitor.api.domain.MonitorDeleteParam;
import com.ksyun.cfwcore.monitor.api.domain.MonitorUpdateParam;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.cfwcore.rabbitmq.enums.ApiMessageTypeEnum;
import com.ksyun.cfwcore.rabbitmq.enums.SchedulerMessageTypeEnum;
import com.ksyun.cfwcore.rabbitmq.handle.ToSchedulerSendMqHandler;
import com.ksyun.cfwcore.rabbitmq.handle.ToApiSendMqHandler;
import com.ksyun.comm.thirdpart.trade.api.domain.DeleteInstanceParam;
import com.ksyun.comm.thirdpart.trade.api.domain.InstanceSubOrder;
import com.ksyun.comm.thirdpart.trade.api.domain.NotifySubOrderParam;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Log4j2
@Service
public class SyncMessageSendService {

    @Autowired
    private ToSchedulerSendMqHandler toSchedulerSendMqHandler;

    @Autowired
    private ToApiSendMqHandler toApiSendMqHandler;

    @Autowired
    private TradeUtils tradeUtils;

    /**
     * 通知订单
     * <p>
     * 统一处理异常代码中，调用此方法通知订单失败
     *
     * @param auth
     * @param sub_order_id
     * @param status
     * @param instance_id
     * @param reason
     */
    public void notifySubOrder(ProxyAuth auth, String sub_order_id, Integer status, String instance_id, String reason) {
        log.debug("notifySubOrder with reason begin");
        log.debug("subOrderId : {} and instance_id : {} and reason [{}] ", sub_order_id,
                instance_id, reason);

        InnerAPIHolder.setInstanceId(instance_id);
        NotifySubOrderParam notifySuborderParam = new NotifySubOrderParam(
                sub_order_id, status, instance_id, reason);
        MessageInfo<NotifySubOrderParam> messageInfo = new MessageInfo<>(
                SchedulerMessageTypeEnum.NOTIFY_SUBORDER_STATUS.getType(),
                auth,
                notifySuborderParam,instance_id
        );
        try {
            toSchedulerSendMqHandler.sendMq(new SendMessageInfo<>(
                    messageInfo, RabbitMQConfiguration.API_TO_SCHEDULER_NEW_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
            log.debug("notifySubOrder with reason successfully");
        } catch (Exception e) {
            log.error("写订单通知消息队列失败 订单id=[" + sub_order_id + "] 实例ID=[" + instance_id + "]");
            ScheduleWarnLog.getLog().error(new NetworkLogMsg().putMsg(
                    "写订单通知消息队列失败: 消息 [" + notifySuborderParam + "], Message: " + e.getMessage()), e);
            throw new OpenAPIException("message", e.getMessage(), HttpStatus.BAD_REQUEST);
        }finally {
            //代表通知一次已经成功，不会在后面在做处理 消息队列出现问题也不需要通知失败 防止出现问题
            InnerAPIHolder.setNeedNotifyFail(false);
        }
    }

    /**
     * 删除资源成功后，通知订单服务
     *
     * @param auth   auth對象
     * @param id     資源id
     * @param source 订单来源： 见com.ksyun.trade.enums.OrderSource
     */
    public void notifyOrder2DeleteInstance(ProxyAuth auth, String id, Integer source) {
        Integer status = null;
        try {
            InstanceSubOrder instanceSubOrder = tradeUtils.queryInstanceByInstanceId(id);
            status = instanceSubOrder.getStatus();
            /***若实例是创建中删除，则通知子订单失败*/
            if (status != null && status.intValue() == InstanceStatusEnum.CREATING.getStatus()) {
                notifySubOrder(auth, instanceSubOrder.getOrderId(), NotifySubOrderResult.FAIL.getValue(), id,null);
                return;
            }
        } catch (Exception e) {
            log.error("查询需要删除的实例出现问题 [{}]", e.getMessage(), e);
        }

        if (status == null || !InstanceStatusEnum.checkIsFinal(status)) {
            log.info("notifyOrder2DeleteInstance, auth : [{}], id : [{}], source : [{}]", JSONUtil.toJsonStr(auth), id, source);
            DeleteInstanceParam deleteInstanceParam = new DeleteInstanceParam(id,
                    Long.valueOf(auth.getAccount_id()), source);
            MessageInfo<DeleteInstanceParam> messageInfo = new MessageInfo<>(
                    SchedulerMessageTypeEnum.ORDER_DELETE_INSTANCE.getType(),
                    auth,
                    deleteInstanceParam,id
            );
            try {
                toSchedulerSendMqHandler.sendMq(new SendMessageInfo<>(
                        messageInfo, RabbitMQConfiguration.API_TO_SCHEDULER_NEW_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
            } catch (Exception e) {
                log.error("写实例ID删除消息队列异常 auth=[{}],id=[{}],source=[{}]", auth, id, source);
                ScheduleWarnLog.getLog().error(new NetworkLogMsg().putMsg(
                        "写实例ID删除消息队列失败: 消息 [" + deleteInstanceParam + "], Message: " + e.getMessage()), e);
                throw new OpenAPIException("message", e.getMessage(), HttpStatus.BAD_REQUEST);
            }
            log.debug("notifyOrder2DeleteInstance successfully");
        }
    }

    /**
     * 创建资源，通知云监控
     *
     * @param auth  auth對象
     * @param param 入参
     */
    public void notifyMonitorCreate(ProxyAuth auth, MonitorCreateParam param) {
        log.debug("notifyMonitorCreate begin");
        log.info(" notifyMonitorCreate, auth : [{}], param : [{}]", auth, param);
        MessageInfo<MonitorCreateParam> messageInfo = new MessageInfo<>(
                SchedulerMessageTypeEnum.CREATE_MONITOR.getType(),
                auth,
                param,param.getInstanceId()
        );
        try {
            toSchedulerSendMqHandler.sendMq(new SendMessageInfo<>(
                    messageInfo, RabbitMQConfiguration.API_TO_SCHEDULER_NEW_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
        } catch (Exception e) {
            log.error("写通知云监控创建消息队列异常 auth=[{}],param=[{}]", auth, param, e);
        }
        log.debug("notifyMonitorCreate successfully");
    }

    /**
     * 资源更新通知云监控
     *
     * @param auth
     * @param param
     */
    public void notifyMonitorUpdate(ProxyAuth auth, MonitorUpdateParam param) {
        log.debug("notifyMonitorUpdate begin");
        log.info(" notifyMonitorUpdate, auth : [{}], param : [{}]", auth,
                param);
        MessageInfo<MonitorUpdateParam> messageInfo = new MessageInfo<>(
                SchedulerMessageTypeEnum.UPDATE_MONITOR.getType(),
                auth,
                param,param.getInstanceId()
        );
        try {
            toSchedulerSendMqHandler.sendMq(new SendMessageInfo<>(
                    messageInfo, RabbitMQConfiguration.API_TO_SCHEDULER_NEW_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
        } catch (Exception e) {
            log.error("写通知云监控更配消息队列异常 auth=[{}],param=[{}]",
                    auth, param, e);
        }
        log.debug("notifyMonitorUpdate successfully");
    }

    /**
     * 删除资源，通知云监控
     *
     * @param auth  auth對象
     * @param param 入参
     */
    public void notifyMonitorDelete(ProxyAuth auth, MonitorDeleteParam param) {
        log.debug("notifyMonitorDelete begin");
        log.info(" notifyMonitorDelete, auth : [{}], param : [{}]", JSONUtil.toJsonStr(auth), param);
        MessageInfo<MonitorDeleteParam> messageInfo = new MessageInfo<>(SchedulerMessageTypeEnum.DELETE_MONITOR.getType(),auth, param,JSONUtil.toJsonStr(param.getInstanceIdList()));
        try {
            toSchedulerSendMqHandler.sendMq(new SendMessageInfo<>(messageInfo, RabbitMQConfiguration.API_TO_SCHEDULER_NEW_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
        } catch (Exception e) {
            log.error("写通知云监控删除消息队列异常 auth=[{}],param=[{}]",
                    auth, param, e);
        }
        log.debug("notifyMonitorDelete successfully");
    }

    /**
     * 更配失败，通知恢复防火墙原先配置
     *
     * @param auth  auth對象
     * @param param 入参
     */
    public void notifyRollbackFw(ProxyAuth auth, RollbackFwParam param) {
        log.debug("rollbackFw begin");
        log.info(" notifyMonitorDelete, auth : [{}], param : [{}]", auth, param);
        MessageInfo<RollbackFwParam> messageInfo = new MessageInfo<>(ApiMessageTypeEnum.NOTIFY_ROLLBACK_FW.getType(),auth, param,JSONUtil.toJsonStr(param.getFwId()));
        try {
            toApiSendMqHandler.sendMq(new SendMessageInfo<>(messageInfo, RabbitMQConfiguration.TO_API_ROUTINGKEY), Constants.SCURITY_RABBITMQ_FLAG);
        } catch (Exception e) {
            log.error("写通知回滚防火墙异常 auth=[{}],param=[{}]",
                    auth, param, e);
        }
        log.debug("notifyRollbackFw successfully");
    }

}
