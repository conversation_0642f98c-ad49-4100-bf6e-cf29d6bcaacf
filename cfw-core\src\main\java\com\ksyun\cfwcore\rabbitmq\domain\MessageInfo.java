package com.ksyun.cfwcore.rabbitmq.domain;

import com.ksyun.common.proxy.ProxyAuth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageInfo<T> {

    private String message_id;

    private Integer message_type;

    private Long send_time;

    private AtomicInteger retry_times = new AtomicInteger();

    private ProxyAuth auth;

    private String instanceId;

    private T message;

    public MessageInfo(Integer message_type, ProxyAuth auth, T message,String instanceId) {
        this.message_id = UUID.randomUUID().toString();
        this.message_type = message_type;
        this.send_time = new Date().getTime();
        this.auth = auth;
        this.message = message;
        this.instanceId = instanceId;
    }
}