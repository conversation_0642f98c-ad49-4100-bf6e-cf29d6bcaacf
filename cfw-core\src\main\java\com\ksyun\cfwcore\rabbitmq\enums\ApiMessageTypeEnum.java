package com.ksyun.cfwcore.rabbitmq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ApiMessageTypeEnum {

    NOTIFY_ROLLBACK_FW(1);

    private Integer type;

    public static String getMessageTypeStr(Integer type) {
        for (SchedulerMessageTypeEnum schedulerMessageTypeEnum : SchedulerMessageTypeEnum.values()) {
            if (schedulerMessageTypeEnum.getType().equals(type)) {
                return schedulerMessageTypeEnum.name();
            }
        }
        return null;
    }
}
