package com.ksyun.cfwcore.rabbitmq.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum SchedulerMessageTypeEnum {

    NOTIFY_SUBORDER_STATUS(1),
    CREATE_MONITOR(2),
    UPDATE_MONITOR(3),
    DELETE_MONITOR(4),
    ORDER_DELETE_INSTANCE(5),;

    private Integer type;

    public static String getMessageTypeStr(Integer type) {
        for (SchedulerMessageTypeEnum schedulerMessageTypeEnum : SchedulerMessageTypeEnum.values()) {
            if (schedulerMessageTypeEnum.getType().equals(type)) {
                return schedulerMessageTypeEnum.name();
            }
        }
        return null;
    }
}