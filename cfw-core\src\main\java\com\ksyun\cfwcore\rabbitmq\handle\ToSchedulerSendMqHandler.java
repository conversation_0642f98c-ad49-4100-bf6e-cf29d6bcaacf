package com.ksyun.cfwcore.rabbitmq.handle;

import com.ksyun.cfwcore.rabbitmq.RabbitMqService;
import com.ksyun.cfwcore.rabbitmq.SendMessageInfo;
import com.ksyun.comm.rabbitmq.RabbitMQConstants;
import com.ksyun.comm.rabbitmq.RabbitMQUtils;
import com.ksyun.cfwcore.config.RabbitMqConfig;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ToSchedulerSendMqHandler implements RabbitMqService<MessageInfo<?>> {

    @Autowired
    private RabbitMqConfig rabbitMqConfig;

    @Override
    public void sendMq(SendMessageInfo<MessageInfo<?>> messageInfo, String id) throws Exception {
        RabbitMQUtils.sendAck(
                id,
                rabbitMqConfig.getRabbitmqAddress(),
                null,
                0,
                rabbitMqConfig.getRabbitmqUsername(),
                rabbitMqConfig.getRabbitmqPassword(),
                RabbitMQConfiguration.API_TO_SCHEDULER_NEW_EXCHANGE,
                messageInfo.getRouteKey(),
                messageInfo.getMessage(),
                false,
                true,
                RabbitMQConstants.ExChangeType.Direct.getValue(),
                rabbitMqConfig.getRabbitmqVhost()
        );
        log.info("send event to routingKey {} , message {}", messageInfo.getRouteKey(), messageInfo.getMessage());
    }
}