package com.ksyun.cfwcore.trade.wapper;

import com.google.common.collect.Lists;
import com.ksyun.cfwcore.I18nCode;
import com.ksyun.cfwcore.annotation.DisplayAttribute;
import com.ksyun.cfwcore.config.RegionConfig;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwcore.trade.wapper.domain.DisplayInfo;
import com.ksyun.cfwcore.trade.wapper.domain.KfwProductInfo;
import com.ksyun.comm.thirdpart.trade.api.domain.ProductItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/7 17:35
 */
@Slf4j
@Component
public class DisplayWrapper {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private RegionConfig regionConfig;
    @DisplayAttribute("KFW")
    private DisplayWrapperProxy.Handler<KfwProductInfo> getKfwDisplay() {
        return (productParam, auth, locale) -> {
            List<DisplayInfo> infos = Lists.newArrayList();
            if (auth != null && !StringUtils.isEmpty(auth.getRegion())) {
                infos.add(buildProductItemInfo(I18nCode.RegionCenter, regionConfig.getRegionName(auth), locale));
            }
            if (productParam != null) {
                if(InstanceTypeEnum.ENTERPRISE.getType().equals(productParam.getInstanceType())){
                    infos.add(buildProductItemInfo(I18nCode.ProtectionPackage, "企业版" , locale));
                }else{
                    infos.add(buildProductItemInfo(I18nCode.ProtectionPackage, "高级版" , locale));
                }

                infos.add(buildProductItemInfo(I18nCode.ProtectiveBandwidth, productParam.getBand_width() + " Mbps", locale));
                infos.add(buildProductItemInfo(I18nCode.EIPCount, productParam.getTotalEipNum(), locale));
            }
            return infos;
        };
    }

    public String getLocalMessage(String message, Locale locale) {
        return messageSource.getMessage(message, new Object[]{}, locale);
    }

    private DisplayInfo buildProductItemInfo(String keyCode, Object value, Locale locale) {
        return buildProductItemInfo(keyCode, value, null, locale);
    }

    /**
     * 拼接商品display item参数
     *
     * @param internationalizationCode display key国际化code
     * @param value                    display value
     * @param valueUnitCode            display value单位国际化code
     * @param locale
     * @return
     */
    private DisplayInfo buildProductItemInfo(String internationalizationCode, Object value, String valueUnitCode, Locale locale) {
        if (valueUnitCode == null) {
            return new DisplayInfo(this.getLocalMessage(internationalizationCode, locale), "" + value);
        } else {
            return new DisplayInfo(
                    this.getLocalMessage(internationalizationCode, locale),
                    value + this.getLocalMessage(valueUnitCode, locale)
            );
        }
    }
}
