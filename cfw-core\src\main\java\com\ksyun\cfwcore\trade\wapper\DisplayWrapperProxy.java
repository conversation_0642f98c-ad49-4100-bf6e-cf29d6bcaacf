package com.ksyun.cfwcore.trade.wapper;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ksyun.cfwcore.I18nCode;
import com.ksyun.cfwcore.config.InitializationConfig;
import com.ksyun.cfwcore.enums.LocalLanguageEnum;
import com.ksyun.cfwcore.trade.wapper.base.OpenApiProductInfo;
import com.ksyun.cfwcore.trade.wapper.domain.DisplayInfo;
import com.ksyun.comm.thirdpart.trade.api.domain.ProductItem;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/7 17:24
 */
@Slf4j
@Component
public class DisplayWrapperProxy {

    private static Gson gson = (new GsonBuilder().serializeNulls().create());

    @Autowired
    private DisplayWrapper displayWrapper;

    /**
     * 获取订单详情display代码方法，productLine不能为空，需与DisplayAttribute注解值拼写一致，不区分大小写
     *
     * @param info
     * @param auth
     * @param productLine
     * @param <T>
     * @return
     */
    @SuppressWarnings("unchecked")
    public <T extends OpenApiProductInfo> List<ProductItem> getProductDisplay(T info, ProxyAuth auth, String productLine) {
        List<ProductItem> productItemList = new ArrayList<>();
        try {
            String serviceUpperCase = productLine.toUpperCase();
            if (InitializationConfig.displayAttributeMap.containsKey(serviceUpperCase)) {
                for (Locale locale : LocalLanguageEnum.getIsInUseLocal()) {
                    Method method = InitializationConfig.displayAttributeMap.get(serviceUpperCase);
                    method.setAccessible(true);
                    Handler<T> displayWrapperHandler = (Handler<T>) method.invoke(displayWrapper);
                    List<DisplayInfo> infos = displayWrapperHandler.getProductDisplayInfo(info, auth, locale);
                    ProductItem item = new ProductItem(displayWrapper.getLocalMessage(I18nCode.Details, locale),
                            displayWrapper.getLocalMessage(I18nCode.Display, locale), gson.toJson(infos), null);
                    productItemList.add(item);
                }
            }
        } catch (Exception e) {
            log.error("获取订单配置详情错误，auth:{},param:{},errorMessage:{}", auth, info, e.getMessage(), e);
        }
        return productItemList;
    }

    public interface Handler<T extends OpenApiProductInfo> {
        List<DisplayInfo> getProductDisplayInfo(T info, ProxyAuth auth, Locale locale);
    }
}