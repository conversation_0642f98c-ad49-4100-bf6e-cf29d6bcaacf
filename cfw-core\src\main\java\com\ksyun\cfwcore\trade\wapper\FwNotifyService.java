package com.ksyun.cfwcore.trade.wapper;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.alert.AlertTemplate;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmPriority;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.enums.InstanceTypeEnum;
import com.ksyun.cfwcore.enums.NotifySubOrderResult;
import com.ksyun.cfwcore.enums.SubOrderStatusEnum;
import com.ksyun.cfwcore.fw.domain.RollbackFwParam;
import com.ksyun.cfwcore.rabbitmq.SyncMessageSendService;
import com.ksyun.comm.thirdpart.trade.api.domain.SubOrder;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmDeal;
import com.ksyun.cfwcore.constants.Constants;
@Slf4j
@Component
public class FwNotifyService {
    @Autowired
    private SyncMessageSendService syncMessageSendService;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private AlertTemplate alertTemplate;

    @Autowired
    private TradeUtils tradeUtils;

    public void notifySubOrder(String subOrderId,int status,String instanceId, ProxyAuth auth,String reason) {
        if (checkAlreadyNotify(subOrderId)) {
            log.info("FwNotifyService.notifySubOrder 已经通知过,subOrderId:{},status:{},instanceId:{},auth:{},", subOrderId, status, instanceId, JSONUtil.toJsonStr(auth));
            return ;
        }

        log.info("FwNotifyService.notifySubOrder 开始订单回调:subOrderId:{},status:{},instanceId:{},auth:{},", subOrderId,status,instanceId,JSONUtil.toJsonStr(auth));
        syncMessageSendService.notifySubOrder(auth, subOrderId, status, instanceId,reason);
        log.info("FwNotifyService.notifySubOrder 结束订单回调:subOrderId:{},status:{},instanceId:{},auth:{},", subOrderId,status,instanceId,JSONUtil.toJsonStr(auth));
        //回滚防火墙
        RollbackFwParam param = new RollbackFwParam().setFwId(instanceId).setSubOrderId(subOrderId);
        if(NotifySubOrderResult.FAIL.getValue()==status){
            syncMessageSendService.notifyRollbackFw(auth, param);
            log.info("FwNotifyService.notifySubOrder 更新状态回调:param:{},auth:{},", JSONUtil.toJsonStr(param),JSONUtil.toJsonStr(auth));
            try{
                //添加告警
                OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
                onePieceAlarm.setName(Constants.OPEN_API_SERVICE_NAME);
                onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
                onePieceAlarm.setProduct(commonConfig.getAlertCode());
                onePieceAlarm.setContent("notifySubOrder 订单创建失败，id："+instanceId+"，subOrderId：{}" +subOrderId);
                onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
                onePieceAlarm.setHtml_content("notifySubOrder 订单创建失败，id："+instanceId+"，subOrderId：{}" +subOrderId+"，reason:" + "[" + reason + "]");
                alertTemplate.send(onePieceAlarm);
            }catch(Exception e){
                log.error("告警发送失败，notifySubOrder 订单创建失败，id：{}，subOrderId：{},error:{}",instanceId,subOrderId,e.getMessage());
            }
        }
    }

    private boolean checkAlreadyNotify(String subOrderId) {
        SubOrder subOrder = tradeUtils.querySubOrderBySubOrderId(subOrderId);
        log.info("[NotifySubOrder] checkAlreadyNotify subOrder {}", JSONUtil.toJsonStr(subOrder));
        if (subOrder != null && subOrder.getStatus() != null) {
            if (subOrder.getStatus().equals(SubOrderStatusEnum.CREATING.getStatus())) {
                return false;
            } else {
                //完结状态
                return true;
            }
        }
        return false;
    }

    public int getRsNum(String instanceType,Integer bandwidth){
        if(InstanceTypeEnum.ADVANCED.getType().equals(instanceType)){
            if(bandwidth>0 && bandwidth<=1000){
                return 2;
            }else if(bandwidth>1000 && bandwidth<=3000){
                return 3;
            }else if(bandwidth>3000 && bandwidth<=4500){
                return 4;
            }else{
                return 5;
            }
        }else {
            if(bandwidth>0 && bandwidth<=500){
                return 3;
            }else if(bandwidth>500 && bandwidth<=1500){
                return 4;
            }else if(bandwidth>1500 && bandwidth<=2000){
                return 6;
            }else if(bandwidth>2000 && bandwidth<=3000){
                return 8;
            }else if(bandwidth>3000 && bandwidth<=4000){
                return 10;
            }else{
                return 12;
            }
        }
    }


}
