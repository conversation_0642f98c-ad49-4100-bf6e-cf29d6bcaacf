package com.ksyun.cfwcore.trade.wapper;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ksyun.cfwcore.annotation.BuildProductItem;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.config.TradeComConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.TradeConstants;
import com.ksyun.cfwcore.domain.ChargeComment;
import com.ksyun.cfwcore.enums.ChargeType;
import com.ksyun.cfwcore.enums.DurationUnit;
import com.ksyun.cfwcore.enums.TradeAppId;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.openstack.domain.ProjectResource;
import com.ksyun.cfwcore.trade.wapper.base.OpenApiProductInfo;
import com.ksyun.cfwcore.trade.wapper.domain.TradeErrorResponse;
import com.ksyun.cfwcore.trade.wapper.domain.UpdateProductParam;
import com.ksyun.cfwcore.utils.CommonUtils;
import com.ksyun.comm.ErrorCode;
import com.ksyun.comm.thirdpart.trade.api.TradeAPI;
import com.ksyun.comm.thirdpart.trade.api.domain.*;
import com.ksyun.comm.thirdpart.trade.enums.ProductUse;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.comm.thread.ThreadPool;
import com.ksyun.common.http.ObjectPlaceholderResolver;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.StopWatch;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.PropertyPlaceholderHelper;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CountDownLatch;


@Slf4j
@Component
public class TradeWapper {

    @Autowired
    private TradeUtils tradeUtils;
    @Autowired
    private ThreadPool threadPool;
    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private DisplayWrapperProxy displayWrapperProxy;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private PropertyPlaceholderHelper placeholderHelper;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private TradeAPI tradeApi;
    @Autowired
    private TradeComConfig tradeConfig;

    /**
     * 创建商品
     *
     * @param param
     * @return product_id
     */
    public String createProduct(com.ksyun.cfwcore.trade.wapper.domain.CreateProductParam param) {
        CreateProductParam create_product_param = new CreateProductParam();
        create_product_param.setUserId(param.getUser_id());
        create_product_param.setRegion(param.getRegion());
        // default
        create_product_param.setProductType(param.getProduct_type());
        create_product_param.setBillType(param.getBill_type());
        create_product_param.setProductWhat(param.getProduct_what());
        create_product_param.setProductUse(param.getProduct_use());
        create_product_param.setDuration(param.getDuration());
        create_product_param.setDurationUnit(DurationUnit.getUnitByBillType(param.getBill_type()));
        create_product_param.setSource(param.getSource());
        // 升级续费
        create_product_param.setInstanceId(param.getInstance_id());
        create_product_param.setProductId(param.getProduct_id());
        create_product_param.setPStartTime(param.getStart_time());
        create_product_param.setPEndTime(param.getEnd_time());

        create_product_param.setItems(param.getItems());
        return tradeUtils.createProduct(create_product_param);
    }

    /**
     * 拼接productItem通用方法【传入泛型T内部字段需配合BuildProductItem注解使用，详细可参照注解说明】
     *
     * @param productInfo
     * @param auth
     * @param <T>
     * @return
     */
    public <T extends OpenApiProductInfo> List<ProductItem> buildProductItemList(T productInfo, ProxyAuth auth) {
        List<ProductItem> items = new ArrayList<>();
        Class<T> clazz = (Class<T>) productInfo.getClass();
        List<Field> fields = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));
        fields.addAll(Arrays.asList(clazz.getSuperclass().getDeclaredFields()));
        List<String> fieldNameList = new ArrayList<>();
        for (Field field : fields) {
            if (!CommonUtils.isPackageClass(field.getType())) {
                log.debug("仅支持包装类型字段，该字段跳过，fieldName {}", field.getName());
                break;
            }
            if (fieldNameList.contains(field.getName())) {
                continue;
            }
            fieldNameList.add(field.getName());
            BuildProductItem buildProductItem = field.getAnnotation(BuildProductItem.class);
            if (buildProductItem == null || buildProductItem.isBuildItemField()) {
                try {
                    PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                    Object value = pd.getReadMethod().invoke(productInfo);
                    if (value != null) {
                        String itemName = (buildProductItem == null || StringUtils.isEmpty(buildProductItem.itemName())) ? field.getName() : buildProductItem.itemName();
                        String itemNo = (buildProductItem == null || StringUtils.isEmpty(buildProductItem.itemNo())) ? field.getName() : buildProductItem.itemNo();
                        String unit = (buildProductItem == null || StringUtils.isEmpty(buildProductItem.unit())) ? null : buildProductItem.unit();
                        items.add(new ProductItem(itemName, itemNo, value, unit));
                    }
                } catch (Exception e) {
                    log.info("自动拼接Item失败，fieldName {},原因 {}", field.getName(), e.getMessage());
                }
            }
        }
        String productLine = TradeAppId.getStrByValue(productInfo.getTradeAppId());
        items.addAll(displayWrapperProxy.getProductDisplay(productInfo, auth, productLine));
        return items;
    }

    /**
     * 更配创建商品
     *
     * @param param
     * @return product_id
     */
    public String updateProduct(UpdateProductParam param) {
        CreateProductParam create_product_param = new CreateProductParam();
        create_product_param.setUserId(param.getUser_id());
        create_product_param.setRegion(param.getRegion());
        // default
        create_product_param
                .setProductType(param.getProduct().getProductType());
        create_product_param.setBillType(param.getProduct().getBillType());
        create_product_param
                .setProductWhat(param.getProduct().getProductWhat());
        create_product_param.setProductUse(ProductUse.UPDATE.getValue());
        create_product_param.setDuration(param.getProduct().getDuration());
        create_product_param.setDurationUnit(param.getProduct()
                .getDurationUnit());
        create_product_param.setSource(param.getSource());
        // 升级续费
        create_product_param.setInstanceId(param.getInstance_id());
        create_product_param.setProductId(param.getProduct().getProductId());
        create_product_param.setPStartTime(param.getProduct().getpStartTime());
        create_product_param.setPEndTime(param.getProduct().getpEndTime());

        create_product_param.setItems(param.getItems());
        return tradeUtils.createProduct(create_product_param);
    }

    /**
     * 创建订单
     *
     * @param param
     * @return sub_order_id
     */
    public String createOrder(com.ksyun.cfwcore.trade.wapper.domain.CreateOrderParam param) {
        CreateOrderParam create_order_param = new CreateOrderParam();
        create_order_param.setUserId(param.getUser_id());
        create_order_param.setOrderUse(param.getOrder_use());
        create_order_param.setOrderType(param.getOrder_type());
        create_order_param.setSource(param.getSource());
        CreateOrderParam.SourceExtend sourceExtend = new CreateOrderParam.SourceExtend();
        sourceExtend.setAppId(param.getAppId());
        create_order_param.setSourceExtend(sourceExtend);
        OrderProductItem orderItem = new OrderProductItem();
        orderItem.setProductId(param.getProduct_id());
        orderItem.setNum(param.getNum());
        List<OrderProductItem> order_product_items = new ArrayList<>();
        order_product_items.add(orderItem);
        create_order_param.setOrderProductItems(order_product_items);
        if (!StringUtils.isEmpty(InnerAPIHolder.getProxyAuth().getUser_id())) {
            create_order_param.setSubUserId(InnerAPIHolder.getProxyAuth().getUser_id());
        }
        String subOrderId = tradeUtils.createOrder(create_order_param).getSubOrderIds().get(0);
        InnerAPIHolder.setSubOderId(subOrderId);
        ProxyAuth proxy_auth = InnerAPIHolder.getProxyAuth();
        proxy_auth.getExtraHeaders().put(Constants.X_AUTH_SUBORDER_ID, subOrderId);
        return subOrderId;
    }

    /**
     * 查询实例信息，获取实际的计费类型
     */
    public void queryInstanceInfos(List<String> instanceIds, final Map<String, ProjectResource> resourceMap) {
        if (CollectionUtils.isNotEmpty(instanceIds)) {
            List<List<String>> subInstanceIds = Lists.partition(instanceIds, 1000);
            final CountDownLatch countDownLatch = new CountDownLatch(subInstanceIds.size());
            final Map<String, String> contextMap = MDC.getCopyOfContextMap();
            for (final List<String> subIds : subInstanceIds) {
                threadPool.getThreadPool().execute(() -> {
                    try {
                        MDC.setContextMap(contextMap);
                        List<InstanceSubOrder> response = tradeUtils.queryInstancesByInstanceId(subIds);
                        if (CollectionUtils.isNotEmpty(response)) {
                            for (InstanceSubOrder instanceInfo : response) {
                                if (instanceInfo.getBillType() != null) {
                                    ChargeComment comment = commonConfig.getChargeCommentCache().get(String.valueOf(instanceInfo.getBillType()));
                                    if (resourceMap.containsKey(instanceInfo.getInstanceId())) {
                                        ProjectResource resource = resourceMap.get(instanceInfo.getInstanceId());
                                        resource.setBillType(instanceInfo.getBillType());
                                        resource.setChargeType(comment != null ? comment.getCode() : ChargeType.getName(instanceInfo.getBillType()));
                                        resource.setProductType(String.valueOf(instanceInfo.getProductType()));
                                        resource.setServiceEndTime(instanceInfo.getServiceEndTime());
                                    }

                                }
                            }
                        }
                    } catch (Exception e) {
                        log.info("批量获取实例信息失败Message [{}]", e.getMessage());
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("批量获取实例信息CountDownLatch.await异常 [{}]", e.getMessage(), e);
            }

        }
    }


    /**
     * 查询实例信息，获取实际的计费类型
     */
    public List<SubOrder> querySubOrderByInstanceIds(List<String> instanceIds) {
        try {
            QuerySubOrdersByInstanceIdsParam param = new QuerySubOrdersByInstanceIdsParam();
            param.setInstanceIds(instanceIds);
            return tradeApi.querySubOrderByInstanceIds(param);
        } catch (Exception e) {
            log.error("查询子订单失败", e);
            return null;
        }

    }


    private static TradeErrorResponse converData(String error_message) {
        Gson gson = new GsonBuilder().create();
        TradeErrorResponse error_data = gson.fromJson(error_message, TradeErrorResponse.class);
        return error_data;
    }

    /**
     * 退订实例
     *
     * @param auth
     * @param param
     * @return
     */
    public RefundInstanceResponse refundInstance(ProxyAuth auth, RefundInstanceParam param) {
        log.debug("[POST] 推定实例 refundInstance with auth [{}] and  param {}", JSONUtil.toJsonStr(auth), JSONUtil.toJsonStr(param));
        StopWatch stop_watch = new StopWatch();
        stop_watch.start();
        RefundInstanceResponse response;
        try {
            response = tradeUtils.refundInstance(param);
            log.debug("[POST] RefundInstance time {} response {}", stop_watch.getTime(), response);
            if (response == null) {
                throw new OpenAPIException(ErrorCode.PaymentFailure, "RefundInstance response is empty",
                        HttpStatus.BAD_REQUEST);
            }
            if (HttpStatus.OK.value() != response.getStatus()) {
                throw new OpenAPIException(ErrorCode.PaymentFailure,
                        "RefundInstance response status is " + response.getStatus()
                                + " message is " + response.getMessage(), HttpStatus.BAD_REQUEST);
            }
            return response;
        } catch (HttpStatusCodeException e) {
            String body_message = e.getResponseBodyAsString();
            TradeErrorResponse error_data = converData(body_message);
            if (commonConfig.getTradeRefundRetryCodeSet().contains(error_data.getErrorCode())) {
                //实例找不到或者已经退订的情况
                log.warn("实例找不到或者已经退订的情况，RefundInstance failed. [" + body_message + "]");
                RefundInstanceResponse refundInstanceResponse = new RefundInstanceResponse();
                refundInstanceResponse.setMessage(error_data.getErrorCode());
                refundInstanceResponse.setStatus(200);
                return refundInstanceResponse;
            } else {
                log.error("RefundInstance failed. [" + body_message + "]", e);
                String message = error_data.getMessage();
                throw new OpenAPIException(ErrorCode.PaymentFailure, message, HttpStatus.BAD_REQUEST);
            }
        } catch (Exception e) {
            log.error("RefundInstance failed.", e);
            throw new OpenAPIException(ErrorCode.PaymentFailure, e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 批量查询实例
     *
     * @param param
     * @return
     */
    public List<InstanceSubOrder> querySubOrderByInstanceIdsWithInstance(QuerySubOrdersByInstanceIdsParam param) {
        String url = placeholderHelper.replacePlaceholders(TradeConstants.QUERY_BATCH_INSTANCE,
                new ObjectPlaceholderResolver(tradeConfig));

        HttpEntity<?> entity = new HttpEntity<Object>(param.getInstanceIdList());
        log.debug("[POST] querySubOrderByInstanceIdsWithInstance url {}", url);
        StopWatch stop_watch = new StopWatch();
        stop_watch.start();
        QueryInstances queryInstances;
        try {
            ResponseEntity<QueryInstances> r = restTemplate.exchange(url, HttpMethod.POST, entity,
                    QueryInstances.class);
            log.debug("[POST] querySubOrderByInstanceIdsWithInstance response ");
            stop_watch.stop();
            queryInstances = r.getBody();
            return queryInstances.getData();
        } catch (Exception e) {
            log.error("querySubOrderByInstanceIdsWithInstance failed url {} param {}", url, param, e);
            return new ArrayList<>();
        }
    }
}
