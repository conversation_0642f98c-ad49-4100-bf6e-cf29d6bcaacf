package com.ksyun.cfwcore.trade.wapper.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/8 15:17
 * <p>
 * OpenAPI层处理商品的基类
 * 用来往商品的item中存放一些固定属性
 * 来方便后期的代码处理
 * 主要是为了订单支付权限上线之后可以异步处理openapi创建的订单
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class OpenApiProductInfo {

    //用来描述控制台的产品操作
    protected String consoleNetworkProductUse;

    //用来描述资源的上级ID
    protected String consoleNetworkParentInstanceId;

    //埋点数据
    protected Integer tradeAppId;

    public OpenApiProductInfo(Integer tradeAppId) {
        this.tradeAppId = tradeAppId;
    }
}
