package com.ksyun.cfwcore.trade.wapper.base;

import lombok.Getter;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/7 17:52
 */
@Getter
public enum OpenApiProductUse {
    Create("Create", 1),
    Update("Update", 3),
    <PERSON><PERSON>("Renewal", 2),
    Delete("Delete", -1),
    Bind("Bind", 3),
    UnBind("UnBind", 3);

    private String use;

    private Integer productUse;

    public String getUse() {
        return use;
    }

    public Integer getProductUse() {
        return productUse;
    }

    OpenApiProductUse(String use, Integer productUse) {
        this.use = use;
        this.productUse = productUse;
    }

    public static Integer getProductUseByUse(String use) {
        for (OpenApiProductUse consoleProductUse : OpenApiProductUse.values()) {
            if (consoleProductUse.getUse().equals(use)) {
                return consoleProductUse.productUse;
            }
        }
        return -1;
    }

    public static String getUseByProductUse(Integer productUse) {
        for (OpenApiProductUse consoleProductUse : OpenApiProductUse.values()) {
            if (consoleProductUse.getProductUse().equals(productUse)) {
                return consoleProductUse.use;
            }
        }
        return null;
    }
}
