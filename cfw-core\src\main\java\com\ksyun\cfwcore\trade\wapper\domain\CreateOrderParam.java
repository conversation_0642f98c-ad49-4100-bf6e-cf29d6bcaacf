package com.ksyun.cfwcore.trade.wapper.domain;

import lombok.Data;

@Data
public class CreateOrderParam {
	private String user_id;
	private String product_id;
	private Integer order_use;
	private Integer order_type;
	private Integer source;
	private Integer num;
	private String appId;
	
	public CreateOrderParam(String user_id, String product_id,
			Integer order_use, Integer order_type, Integer source,
			Integer num,String appId) {
		super();
		this.user_id = user_id;
		this.product_id = product_id;
		this.order_use = order_use;
		this.order_type = order_type;
		this.source = source;
		this.num = num;
		this.appId=appId;
	}

	public CreateOrderParam(String user_id, String product_id,
							Integer order_use, Integer order_type, Integer source,
							Integer num) {
		super();
		this.user_id = user_id;
		this.product_id = product_id;
		this.order_use = order_use;
		this.order_type = order_type;
		this.source = source;
		this.num = num;
	}

	public CreateOrderParam() {
		super();
	}
}
