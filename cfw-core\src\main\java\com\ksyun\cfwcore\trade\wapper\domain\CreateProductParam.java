package com.ksyun.cfwcore.trade.wapper.domain;

import com.ksyun.comm.thirdpart.trade.api.domain.ProductItem;
import lombok.Data;

import java.util.List;

@Data
public class CreateProductParam {
	private String user_id;
	private String region;
	private Integer product_type;
	private Integer bill_type;
	private Integer product_what;
	private Integer product_use;
	private String instance_id;
	private String product_id;
	private Integer duration;
	private String start_time;
	private String end_time;
	List<ProductItem> items;
	private Integer source;

	public CreateProductParam() {
		super();
	}

	public CreateProductParam(String user_id, String region,
							  Integer product_type, Integer bill_type, Integer product_what,
							  Integer product_use, Integer duration, List<ProductItem> items, Integer source) {
		super();
		this.user_id = user_id;
		this.region = region;
		this.product_type = product_type;
		this.bill_type = bill_type;
		this.product_what = product_what;
		this.product_use = product_use;
		this.duration = duration;
		this.items = items;
		this.source=source;
	}

	public CreateProductParam(String user_id, String region,
							  Integer product_type, Integer bill_type, Integer product_what,
							  Integer product_use, Integer duration, String start_time, String end_time, List<ProductItem> items, Integer source, String instance_id) {
		super();
		this.user_id = user_id;
		this.region = region;
		this.product_type = product_type;
		this.bill_type = bill_type;
		this.product_what = product_what;
		this.product_use = product_use;
		this.duration = duration;
		this.start_time = start_time;
		this.end_time = end_time;
		this.items = items;
		this.source=source;
		this.instance_id = instance_id;
	}
}
