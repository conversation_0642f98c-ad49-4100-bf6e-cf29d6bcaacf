package com.ksyun.cfwcore.trade.wapper.domain;

import com.ksyun.cfwcore.enums.TradeAppId;
import com.ksyun.cfwcore.trade.wapper.base.OpenApiProductInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by xuyaming on 2018/11/26.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Ipv6PublicProductInfo extends OpenApiProductInfo {
    private Integer egress;
    private String networkInterfaceId;
    private String ipv6PublicId;
    private String instanceType;
    private String provider="BGP-V6";
    private String ipv6PublicIpAddress;
    private Integer tradeAppId= TradeAppId.IPV6PUB.getValue();

}
