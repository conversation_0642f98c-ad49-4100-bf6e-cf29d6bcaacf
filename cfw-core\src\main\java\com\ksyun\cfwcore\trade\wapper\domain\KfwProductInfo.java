package com.ksyun.cfwcore.trade.wapper.domain;

import com.ksyun.cfwcore.annotation.BuildProductItem;
import com.ksyun.cfwcore.enums.TradeAppId;
import com.ksyun.cfwcore.trade.wapper.base.OpenApiProductInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/11/16 17:17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class KfwProductInfo extends OpenApiProductInfo {

    private String instanceId;

    private String instanceName;

    private String instanceType;

    private String instanceDesc;

    private Integer totalEipNum;

    private Integer band_width;

    private String chargeType;

    private String projectId;

    private Integer purchaseTime;

    private Integer duration;


    public KfwProductInfo() {
        super(TradeAppId.KFW.getValue());
    }
}
