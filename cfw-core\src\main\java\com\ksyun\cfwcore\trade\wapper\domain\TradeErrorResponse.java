package com.ksyun.cfwcore.trade.wapper.domain;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 订单错误封装类
 * 
 * <AUTHOR>
 *
 * @Date 2016年9月23日 下午3:11:04
 */
@Data
public class TradeErrorResponse {
	@Expose
	@SerializedName("errorCode")
	private String errorCode;

	@Expose
	@SerializedName("status")
	private String status;

	@Expose
	@SerializedName("message")
	private String message;

	@Expose
	@SerializedName("orderId")
	private String orderId;
}
