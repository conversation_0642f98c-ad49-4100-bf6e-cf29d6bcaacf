package com.ksyun.cfwcore.trade.wapper.domain;

import com.ksyun.comm.thirdpart.trade.api.domain.Product;
import com.ksyun.comm.thirdpart.trade.api.domain.ProductItem;
import lombok.Data;

import java.util.List;

@Data
public class UpdateProductParam {
	private String user_id;
	private String region;
	private String instance_id;
	private Integer egress;
	private Product product;
	private List<ProductItem> items;
	private Integer source;
	
	public UpdateProductParam() {
		super();
	}

	public UpdateProductParam(String user_id, String region, String instance_id,
			Integer egress, Product product, List<ProductItem> items,Integer source) {
		super();
		this.user_id = user_id;
		this.region = region;
		this.instance_id = instance_id;
		this.egress = egress;
		this.product = product;
		this.items = items;
		this.source=source;
	}

}
