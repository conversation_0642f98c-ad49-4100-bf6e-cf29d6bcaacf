package com.ksyun.cfwcore.utils;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * <AUTHOR> by FENGJINGJU
 * @Date 2021/5/14 10:48
 */
public class CommonUtils {

    public static boolean isPackageClass(Class<?> classType) {
        return classType.equals(String.class)
                || classType.equals(Boolean.class)
                || classType.equals(Integer.class)
                || classType.equals(Short.class)
                || classType.equals(Double.class)
                || classType.equals(Long.class)
                || classType.equals(Float.class)
                || classType.equals(Character.class)
                || classType.equals(Byte.class);
    }

    /**
     * 正则校验
     *
     * @param regex
     * @return
     */
    public static boolean isMatchPattern(String regex, String input) {
        if (StringUtils.isEmpty(regex)) {
            return false;
        }
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(input).matches();
    }

    /**
     * 递归获取某实体除特殊字段外，其他字段是否全部为null
     * <p>
     * 注意：1、不支持实体内含有基本数据类型的情况；2、不支持实体内部含有非静态内部类的情况；3、暂不支持除特殊字段外的其他字段为List的情况
     *
     * @param object          需要判断的实体
     * @param specialFieldSet 特殊字段（可以为null的字段）
     * @return 除specialFieldSet字段外全部字段为null，则返回true；否则返回false
     */
    public static boolean getOtherFieldAllNullExceptSpecial(Object object, Set<String> specialFieldSet) throws Exception {
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (CollectionUtils.isEmpty(specialFieldSet) || !specialFieldSet.contains(field.getName())) {
                field.setAccessible(true);
                Object obj = field.get(object);
                if (obj != null) {
                    if (obj instanceof Number || obj instanceof Boolean || obj instanceof String || obj instanceof Character) {
                        return false;
                    } else {
                        // 递归
                        if (!getOtherFieldAllNullExceptSpecial(obj, specialFieldSet)) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }
}
