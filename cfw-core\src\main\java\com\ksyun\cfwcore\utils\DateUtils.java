package com.ksyun.cfwcore.utils;

import cn.hutool.core.collection.ListUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * 时间日期工具类
 *
 * <AUTHOR>
 * @createTime 2018/1/23 10:45
 */

@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    /**
     * 仅显示年月，例如 2024-05
     */
    public static final String DATE_MONTH_FORMAT = "yyyy-MM";

    /**
     * 仅显示年月日，例如 2015-08-11.
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 显示年月日时分秒，例如 2015-08-11 09:51:53.
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 仅显示时分秒，例如 09:51:53.
     */
    public static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * 每天的毫秒数 8640000.
     */
    public static final long MILLISECONDS_PER_DAY = 86400000L;

    /**
     * 每周的天数.
     */
    public static final long DAYS_PER_WEEK = 7L;

    /**
     * 每小时毫秒数.
     */
    public static final long MILLISECONDS_PER_HOUR = 3600000L;

    /**
     * 每分钟秒数.
     */
    public static final long SECONDS_PER_MINUTE = 60L;

    /**
     * 每小时秒数.
     */
    public static final long SECONDS_PER_HOUR = 3600L;

    /**
     * 每天秒数.
     */
    public static final long SECONDS_PER_DAY = 86400L;

    /**
     * 每个月秒数，默认每月30天.
     */
    public static final long SECONDS_PER_MONTH = 2592000L;

    /**
     * 每年秒数，默认每年365天.
     */
    public static final long SECONDS_PER_YEAR = 31536000L;

    /**
     * 常用的时间格式.
     */
    private static String[] parsePatterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm"};

    /**
     * 得到当前日期字符串.
     *
     * @return String 日期字符串，例如2015-08-11
     * @since 1.0
     */
    public static String getDate() {
        return getDate(DateUtils.DATE_FORMAT);
    }

    /**
     * 得到当前时间字符串.
     *
     * @return String 时间字符串，例如 09:51:53
     * @since 1.0
     */
    public static String getTime() {
        return formatDate(new Date(), DateUtils.TIME_FORMAT);
    }

    /**
     * 得到当前日期和时间字符串.
     *
     * @return String 日期和时间字符串，例如 2015-08-11 09:51:53
     * @since 1.0
     */
    public static String getDateTime() {
        return formatDate(new Date(), DateUtils.DATETIME_FORMAT);
    }

    /**
     * 获取当前时间指定格式下的字符串.
     *
     * @param pattern 转化后时间展示的格式，例如"yyyy-MM-dd"，"yyyy-MM-dd HH:mm:ss"等
     * @return String 格式转换之后的时间字符串.
     * @since 1.0
     */
    public static String getDate(String pattern) {
        return DateFormatUtils.format(new Date(), pattern);
    }

    /**
     * 字符串转时间
     *
     * @param date    格式转换之后的时间字符串
     * @param pattern 日期格式
     * @return
     * @throws ParseException
     */
    public static Date getDate(String date, String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.parse(date);
    }

    /**
     * 获取指定日期的字符串格式.
     *
     * @param date    需要格式化的时间，不能为空
     * @param pattern 时间格式，例如"yyyy-MM-dd"，"yyyy-MM-dd HH:mm:ss"等
     * @return String 格式转换之后的时间字符串.
     * @since 1.0
     */
    public static String getDate(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 获取日期时间字符串，默认格式为（yyyy-MM-dd）.
     *
     * @param date    需要转化的日期时间
     * @param pattern 时间格式，例如"yyyy-MM-dd" "HH:mm:ss" "E"等
     * @return String 格式转换后的时间字符串
     * @since 1.0
     */
    public static String formatDate(Date date, Object... pattern) {
        String formatDate = null;
        if (pattern != null && pattern.length > 0) {
            formatDate = DateFormatUtils.format(date, pattern[0].toString());
        } else {
            formatDate = DateFormatUtils.format(date, DateUtils.DATE_FORMAT);
        }
        return formatDate;
    }

    /**
     * 获取当前年份字符串.
     *
     * @return String 当前年份字符串，例如 2015
     * @since 1.0
     */
    public static String getYear() {
        return formatDate(new Date(), "yyyy");
    }

    /**
     * 获取当前月份字符串.
     *
     * @return String 当前月份字符串，例如 08
     * @since 1.0
     */
    public static String getMonth() {
        return formatDate(new Date(), "MM");
    }

    /**
     * 获取当前天数字符串.
     *
     * @return String 当前天数字符串，例如 11
     * @since 1.0
     */
    public static String getDay() {
        return formatDate(new Date(), "dd");
    }

    /**
     * 获取当前星期字符串.
     *
     * @return String 当前星期字符串，例如星期二
     * @since 1.0
     */
    public static String getWeek() {
        return formatDate(new Date(), "E");
    }

    /**
     * 将日期型字符串转换为日期格式.
     * <p>
     * 支持的日期字符串格式包括"yyyy-MM-dd","yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm"
     * </p>
     *
     * @param str
     * @return Date
     * @since 1.0
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 将yyyy-MM-dd格式的日期解析为Date，格式不正确返回null
     * 当传入一个类似2月31号这样不存在的日期同样会返回null
     * @param dateString
     * @return
     */
    public static Date parseDateyyyyMMdd(String dateString) {
        if (StringUtils.isBlank(dateString)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT);
            LocalDate parseDate = LocalDate.parse(dateString, formatter);
            if(parseDate != null && dateString.equals(parseDate.format(formatter))){
                return Date.from(parseDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
            }
        } catch (DateTimeParseException e) {
            log.info("DateUtils.parseDateyyyyMMdd方法日期解析错误：%s", dateString);
        }
        return null;
    }

    /**
     * 获取当前日期与指定日期相隔的天数.
     *
     * @param date 给定的日期
     * @return long 日期间隔天数，正数表示给定日期在当前日期之前，负数表示在当前日期之后
     * @since 1.0
     */
    public static long pastDays(Date date) {
        // 将指定日期转换为yyyy-MM-dd格式
        date = DateUtils.parseDate(DateUtils.formatDate(date, DateUtils.DATE_FORMAT));
        // 当前日期转换为yyyy-MM-dd格式
        Date currentDate = DateUtils.parseDate(DateUtils.formatDate(new Date(), DateUtils.DATE_FORMAT));
        long t = (currentDate.getTime() - date.getTime()) / DateUtils.MILLISECONDS_PER_DAY;
        return t;
    }

    /**
     * 获取当前日期指定天数之后的日期.
     *
     * @param num 相隔天数
     * @return Date 日期
     * @since 1.0
     */
    public static Date nextDay(int num) {
        Calendar curr = Calendar.getInstance();
        curr.set(Calendar.DAY_OF_MONTH, curr.get(Calendar.DAY_OF_MONTH) + num);
        return curr.getTime();
    }

    /**
     * 获取给定日期指定天数之后的日期
     * @param date
     * @param plusDays
     * @return
     */
    public static Date addDays(Date date, int plusDays) {
        if(null == date) {
            throw new IllegalArgumentException("The date must not be null");
        }
        Calendar curr = Calendar.getInstance();
        curr.setTime(date);
        curr.add(Calendar.DAY_OF_MONTH, plusDays);
        return curr.getTime();
    }

    /**
     * 获取当前日期指定月数之后的日期.
     *
     * @param num 间隔月数
     * @return Date 日期
     * @since 1.0
     */
    public static Date nextMonth(int num) {
        Calendar curr = Calendar.getInstance();
        curr.add(Calendar.MONTH, num);// 月份减1  
        return curr.getTime();
    }

    /**
     * 获取给定日期指定月数之后的日期
     * @param date
     * @param plusMonths
     * @return
     */
    public static Date addMonths(Date date, int plusMonths) {
        if(null == date) {
            throw new IllegalArgumentException("The date must not be null");
        }
        Calendar curr = Calendar.getInstance();
        curr.setTime(date);
        curr.add(Calendar.MONTH, plusMonths);
        return curr.getTime();
    }

    /**
     * 获取当前日期指定周数之后的日期.
     *
     * @param num 间隔月数
     * @return Date 日期
     * @since 1.0
     */
    public static Date nextWeek(int num) {
        Calendar curr = Calendar.getInstance();
        curr.add(Calendar.WEEK_OF_YEAR, num);// 月份减1  
        return curr.getTime();
    }

    /**
     * 获取时间段内时间列表
     *
     * @param start_date
     * @param end_date
     * @return
     * <AUTHOR>
     */
    public static List<String> getDateList(Date start_date, Date end_date) {
        Calendar start_cal = Calendar.getInstance();
        Calendar end_cal = Calendar.getInstance();
        start_cal.setTime(start_date);
        end_cal.setTime(end_date);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        List<String> dateList = new ArrayList<String>();
        for (; start_cal.compareTo(end_cal) <= 0; ) {
            String date = sdf.format(start_cal.getTime());
            dateList.add(date);
            start_cal.add(Calendar.DATE, 1);
        }
        return dateList;
    }

    /**
     * 获取时间段内年月列表
     *
     * @param start_date
     * @param end_date
     * @return
     * <AUTHOR>
     */
    public static List<String> getYearMonthList(Date start_date, Date end_date) {
        Calendar start_cal = Calendar.getInstance();
        Calendar end_cal = Calendar.getInstance();
        start_cal.setTime(start_date);
        end_cal.setTime(end_date);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Set<String> dateSet = new HashSet<>();
        dateSet.add(sdf.format(start_date));
        dateSet.add(sdf.format(end_date));

        for (; start_cal.compareTo(end_cal) <= 0; ) {
            String date = sdf.format(start_cal.getTime());
            dateSet.add(date);
            start_cal.add(Calendar.MONTH, 1);
        }

        return ListUtil.toList(dateSet);
    }

    /**
     * 获取当前时期的周日日期
     *
     * @param date
     * @return
     * <AUTHOR>
     */
    public static String getLastDayOfWeek(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 设置该周第一天为星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int day_of_week = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        cal.add(Calendar.DATE, 7 - day_of_week);
        return sdf.format(cal.getTime());
    }

    /**
     * 获取时间段内每个周日的日期
     *
     * @param start_date
     * @param end_date
     * @return
     * <AUTHOR>
     */
    public static ArrayList<String> getLastDayOfWeeks(Date start_date, Date end_date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<String> result = new ArrayList<>();
        Calendar begin_cal = Calendar.getInstance();
        Calendar end_cal = Calendar.getInstance();
        begin_cal.setTime(start_date);
        try {
            Date enddate = sdf.parse(getFirstDayOfWeek(end_date));
        } catch (ParseException e) {
            log.error("解析时间异常：{}", e.getMessage());
        }
        end_cal.setTime(end_date);
        while (end_cal.after(begin_cal) || (end_cal.get(Calendar.DATE) == begin_cal.get(Calendar.DATE) && end_cal.get(Calendar.DAY_OF_WEEK) == 1)) {
            String day = getLastDayOfWeek(begin_cal.getTime());
            result.add(day);
            begin_cal.add(Calendar.DATE, 7);
        }
        return result;
    }

    /**
     * 获取当前时期的周一日期
     *
     * @param date
     * @return
     * <AUTHOR>
     */
    public static String getFirstDayOfWeek(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 设置该周第一天为星期一
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int day_of_week = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        cal.add(Calendar.DATE, -day_of_week + 1);
        return sdf.format(cal.getTime());
    }

    /**
     * 获取时间段内每个月的最后一天
     *
     * @param endTime
     * @param beginTime
     * @return
     * <AUTHOR>
     */
    public static ArrayList<String> getLastDayOfMonths(Date beginTime, Date endTime) {
        ArrayList<String> result = new ArrayList<String>();
        Calendar begin_cal = Calendar.getInstance();
        Calendar end_cal = Calendar.getInstance();
        begin_cal.setTime(beginTime);
        end_cal.setTime(endTime);
        end_cal.set(end_cal.get(Calendar.YEAR),
                end_cal.get(Calendar.MONTH), 1);
        while (end_cal.after(begin_cal)) {
            int year = begin_cal.get(Calendar.YEAR);
            int month = begin_cal.get(Calendar.MONTH) + 1;
            String day = getLastDayOfMonth(year, month);
            result.add(day);
            begin_cal.set(Calendar.MONTH, month);
        }
        return result;
    }

    /**
     * 获取年月，格式为yyyy-MM
     *
     * @param endTime
     * @param beginTime
     * @return
     * <AUTHOR>
     */
    public static ArrayList<String> getYearMonth(Date beginTime, Date endTime) {
        ArrayList<String> result = new ArrayList<String>();
        Calendar begin_cal = Calendar.getInstance();
        Calendar end_cal = Calendar.getInstance();
        begin_cal.setTime(beginTime);
        end_cal.setTime(endTime);
        while (end_cal.after(begin_cal)) {
            int year = begin_cal.get(Calendar.YEAR);
            int month = begin_cal.get(Calendar.MONTH) + 1;
            String day = "";
            if (month > 9) {
                day = year + "-" + month;
            } else {
                day = year + "-0" + month;
            }
            result.add(day);
            begin_cal.set(Calendar.MONTH, month);
        }
        return result;
    }

    /**
     * 获取当前日期指定年数之后的日期.
     *
     * @param num 间隔年数
     * @return Date 日期
     * @since 1.0
     */
    public static Date nextYear(int num) {
        Calendar curr = Calendar.getInstance();
        curr.set(Calendar.YEAR, curr.get(Calendar.YEAR) + num);
        return curr.getTime();
    }

    /**
     * 将 Date 日期转化为 Calendar 类型日期.
     *
     * @param date 给定的时间，若为null，则默认为当前时间
     * @return Calendar Calendar对象
     * @since 1.0
     */
    public static Calendar getCalendar(Date date) {
        Calendar calendar = Calendar.getInstance();
        // calendar.setFirstDayOfWeek(Calendar.SUNDAY);//每周从周日开始
        // calendar.setMinimalDaysInFirstWeek(1); // 设置每周最少为1天
        if (date != null) {
            calendar.setTime(date);
        }
        return calendar;
    }

    /**
     * 计算两个日期之间相差天数.
     *
     * @param start 计算开始日期
     * @param end   计算结束日期
     * @return long 相隔天数
     * @since 1.0
     */
    public static long getDaysBetween(Date start, Date end) {
        // 将指定日期转换为yyyy-MM-dd格式
        start = DateUtils.parseDate(DateUtils.formatDate(start, DateUtils.DATE_FORMAT));
        // 当前日期转换为yyyy-MM-dd格式
        end = DateUtils.parseDate(DateUtils.formatDate(end, DateUtils.DATE_FORMAT));
        long diff = (end.getTime() - start.getTime()) / DateUtils.MILLISECONDS_PER_DAY;
        return diff;
    }

    /**
     * 计算两个日期之前相隔多少周.
     *
     * @param start 计算开始时间
     * @param end   计算结束时间
     * @return long 相隔周数，向下取整
     * @since 1.0
     */
    public static long getWeeksBetween(Date start, Date end) {
        return getDaysBetween(start, end) / DateUtils.DAYS_PER_WEEK;
    }

    /**
     * 获取与指定日期间隔给定天数的日期.
     *
     * @param specifiedDay 给定的字符串格式日期，支持的日期字符串格式包括"yyyy-MM-dd","yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss",
     *                     "yyyy/MM/dd HH:mm"
     * @param num          间隔天数
     * @return String 间隔指定天数之后的日期
     * @since 1.0
     */
    public static String getSpecifiedDayAfter(String specifiedDay, int num) {
        Date specifiedDate = parseDate(specifiedDay);
        Calendar c = Calendar.getInstance();
        c.setTime(specifiedDate);
        int day = c.get(Calendar.DATE);
        c.set(Calendar.DATE, day + num);
        String dayAfter = formatDate(c.getTime(), DateUtils.DATE_FORMAT);
        return dayAfter;
    }

    /**
     * 计算两个日期之前间隔的小时数.
     *
     * @param date1 结束时间
     * @param date2 开始时间
     * @return String 相差的小时数，保留一位小数
     * @since 1.0
     */
    public static String dateMinus(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return "0";
        }
        Long r = date1.getTime() - date2.getTime();
        DecimalFormat df = new DecimalFormat("#.0");
        double result = r * 1.0 / DateUtils.MILLISECONDS_PER_HOUR;
        return df.format(result);
    }

    /**
     * 获取当前季度 .
     *
     * @return Integer 当前季度数
     * @since 1.0
     */
    public static Integer getCurrentSeason() {
        Calendar calendar = Calendar.getInstance();
        Integer month = calendar.get(Calendar.MONTH) + 1;
        int season = 0;
        if (month >= 1 && month <= 3) {
            season = 1;
        } else if (month >= 4 && month <= 6) {
            season = 2;
        } else if (month >= 7 && month <= 9) {
            season = 3;
        } else if (month >= 10 && month <= 12) {
            season = 4;
        }
        return season;
    }

    /**
     * 将以秒为单位的时间转换为其他单位.
     *
     * @param seconds 秒数
     * @return String 例如 16分钟前、2小时前、3天前、4月前、5年前等
     * @since 1.0
     */
    public static String getIntervalBySeconds(long seconds) {
        StringBuilder buffer = new StringBuilder();
        if (seconds < SECONDS_PER_MINUTE) {
            buffer.append(seconds).append("秒前");
        } else if (seconds < SECONDS_PER_HOUR) {
            buffer.append(seconds / SECONDS_PER_MINUTE).append("分钟前");
        } else if (seconds < SECONDS_PER_DAY) {
            buffer.append(seconds / SECONDS_PER_HOUR).append("小时前");
        } else if (seconds < SECONDS_PER_MONTH) {
            buffer.append(seconds / SECONDS_PER_DAY).append("天前");
        } else if (seconds < SECONDS_PER_YEAR) {
            buffer.append(seconds / SECONDS_PER_MONTH).append("月前");
        } else {
            buffer.append(seconds / DateUtils.SECONDS_PER_YEAR).append("年前");
        }
        return buffer.toString();
    }

    /**
     * getNowTimeBefore(记录时间相当于目前多久之前)
     *
     * @param seconds 秒
     * @return
     * @throws @since 1.0
     * <AUTHOR>
     */
    public static String getNowTimeBefore(long seconds) {
        StringBuilder buffer = new StringBuilder();
        buffer.append("上传于");
        if (seconds < 3600) {
            buffer.append((long) Math.floor(seconds / 60)).append("分钟前");
        } else if (seconds < 86400) {
            buffer.append((long) Math.floor(seconds / 3600)).append("小时前");
        } else if (seconds < 604800) {
            buffer.append((long) Math.floor(seconds / 86400)).append("天前");
        } else if (seconds < 2592000) {
            buffer.append((long) Math.floor(seconds / 604800)).append("周前");
        } else if (seconds < 31104000) {
            buffer.append((long) Math.floor(seconds / 2592000)).append("月前");
        } else {
            buffer.append((long) Math.floor(seconds / 31104000)).append("年前");
        }
        return buffer.toString();
    }

    /**
     * getMonthsBetween(查询两个日期相隔的月份)
     *
     * @param startDate 日期1 (格式yyyy-MM-dd)
     * @param endDate   日期2 (格式yyyy-MM-dd)
     * @return
     * <AUTHOR>
     * @since 3.3
     */
    public static int getMonthsBetween(String startDate, String endDate) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(DateUtils.parseDate(startDate));
        c2.setTime(DateUtils.parseDate(endDate));
        int year = c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR);
        int month = c2.get(Calendar.MONTH) - c1.get(Calendar.MONTH);
        return Math.abs(year * 12 + month);
    }

    /**
     * getDayOfWeek(获取当前日期是星期几)
     *
     * @param dateStr 日期
     * @return 星期几
     * @throws @since 3.3
     * <AUTHOR>
     */
    public static String getDayOfWeek(String dateStr) {
        String[] weekOfDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Date date = parseDate(dateStr);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int num = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        return weekOfDays[num];
    }

    /**
     * getTimesWeekmorning(这里用一句话描述这个方法的作用)
     *
     * @return
     * @throws @since 3.3
     * <AUTHOR>
     */
    public static Date getTimesWeekmorning(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONDAY), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return cal.getTime();
    }

    /**
     * getTimesWeeknight(这里用一句话描述这个方法的作用)
     *
     * @return
     * @throws @since 3.3
     * <AUTHOR>
     */
    public static Date getTimesWeeknight(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(getTimesWeekmorning(date));
        cal.add(Calendar.DAY_OF_WEEK, 7);
        return cal.getTime();
    }

    /**
     * 获取某年某月的最后一天
     *
     * <AUTHOR>
     * @createTime 2017/3/9 22:05
     */
    public static String getLastDayOfMonth(String key, String month) {
        //Calendar cal = Calendar.getInstance();
        ////设置年份
        //cal.set(Calendar.YEAR, year);
        ////设置月份
        //cal.set(Calendar.MONTH, month);
        ////获取某月最大天数
        //int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        ////设置日历中月份的最大天数
        //cal.set(Calendar.DAY_OF_MONTH, lastDay);
        ////格式化日期
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        //String lastDayOfMonth = sdf.format(cal.getTime());
        String lastDay;
        switch (month) {
            case "1":
                lastDay = "31";
                break;
            case "2":
                int year = Integer.parseInt(key.substring(0, 3));
                if (String.valueOf(year).endsWith("00")) {
                    if (year % 400 == 0) {
                        lastDay = "29";
                    } else {
                        lastDay = "28";
                    }
                } else if (year % 4 == 0) {
                    lastDay = "29";
                } else {
                    lastDay = "28";
                }
                break;
            case "3":
                lastDay = "31";
                break;
            case "4":
                lastDay = "30";
                break;
            case "5":
                lastDay = "31";
                break;
            case "6":
                lastDay = "30";
                break;
            case "7":
                lastDay = "31";
                break;
            case "8":
                lastDay = "31";
                break;
            case "9":
                lastDay = "30";
                break;
            case "10":
                lastDay = "31";
                break;
            case "11":
                lastDay = "30";
                break;
            case "12":
                lastDay = "31";
                break;
            default:
                lastDay = "30";

        }
        return key.substring(0, 8) + lastDay;
    }

    /**
     * 获取指定时间的年月日
     *
     * <AUTHOR>
     * @createTime 2017/3/9 22:03
     */
    public static Map<String, Integer> getNYR(Date date) {
        Map<String, Integer> map = new HashMap<>();
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1; // 0-based!
        int day = now.get(Calendar.DAY_OF_MONTH);
        map.put("year", year);
        map.put("month", month);
        map.put("day", day);
        return map;
    }

    /**
     * 获取昨天的时间
     */
    public static String getYesterday() {
//        Date data = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String yesterDay = sdf.format(calendar.getTime());
        return yesterDay;
    }


    /**
     * 根据时间段，获取所有月份的最后一天
     */
    public static List<String> getPerMonthLastDay(String startDateStr, String endDateStr) throws ParseException {
        Date startDate = null;
        Date endDate = null;
        List<String> list = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (StringUtils.isNotBlank(startDateStr)) {
            startDate = sdf.parse(startDateStr);
        }
        if (StringUtils.isNotBlank(endDateStr)) {
            endDate = sdf.parse(endDateStr);
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);//设置日期起始时间
        while (cal.getTime().before(endDate)) {//判断是否到结束日期
            cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
            String perMonthLastDay = sdf.format(cal.getTime());
            list.add(perMonthLastDay);
            cal.add(Calendar.MONTH, 1);//进行当前日期月份加1
        }
        return list;
    }


    /**
     * 获得该月最后一天
     *
     * @param year
     * @param month
     * @return
     * <AUTHOR>
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month);
        //设为本月第一天
        cal.set(Calendar.DAY_OF_MONTH, 1);
        //天数减一
        cal.add(Calendar.DATE, -1);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

    public static Date timeStamp2Date(Long timestamp) {
        return new Date(timestamp);
    }

    /**
     * date 转 LocalDateTime
     * @param date
     * @return
     */
    public static LocalDateTime asLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 指定时间的当月1号0点0分0秒
     * @param localDate
     * @return
     */
    public static LocalDateTime firstDayOfMonth(LocalDate localDate) {
        return localDate.with(TemporalAdjusters.firstDayOfMonth()).atTime(0, 0, 0);
    }

    /**
     * 几个月之前时间
     * @param monthsToSubtract 月份间隔
     * @return
     */
    public static LocalDateTime minusMonths(long monthsToSubtract) {
        return firstDayOfMonth(LocalDate.now().minusMonths(monthsToSubtract));
    }

    /**
     * 格式化日期
     *
     * @param localDateTime 时间
     * @param pattern pattern
     * @return
     */
    public static String parseDate(LocalDateTime localDateTime,String pattern) {
        DateTimeFormatter formatters = DateTimeFormatter.ofPattern(pattern);
        return localDateTime.format(formatters);
    }

    /**
     * 获取当前时间索引
     * answer_record_yyyy_MM
     *
     * @return es 索引
     */
    public static String parseDate(Date date,String pattern) {
        //使用Date和SimpleDateFormat
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        return simpleDateFormat.format(date);
    }

    public static String dateFormatToUnderline(String date) {
        return date.replaceAll("-", "_");
    }


    /**
     * @Description: 获取昨天结束时间
     * @Param: []
     * @Return: java.util.Date
     * @Author: szhu
     * @Date: 2020/4/17
     */
    public static Date getLastDayEndTime(){
        ZoneId zone = ZoneId.systemDefault();
        Instant zonedDateTime =LocalDate.now().minusDays(1).atTime(23,59,59).atZone(zone).toInstant();
        return Date.from(zonedDateTime);
    }

    /**
     * @Description: 获取几天前开始时间
     * @Param: [daysToSubtract]
     * @Return: java.util.Date
     * @Author: szhu
     * @Date: 2020/5/30
     */
    public static Date getMinusDayStartTime(LocalDate localDate,long daysToSubtract){
        ZoneId zone = ZoneId.systemDefault();
        Instant zonedDateTime =localDate.minusDays(daysToSubtract).atTime(0,0,0).atZone(zone).toInstant();
        return Date.from(zonedDateTime);
    }

    /**
     * 获取指定日期减去若干个月后的起始时间（00:00:00）
     *
     * @param localDate      基准日期
     * @param monthsToSubtract 要减去的月数
     * @return 减去月份后的起始时间（Date 类型）
     */
    public static Date getMinusMonthStartTime(LocalDate localDate, long monthsToSubtract) {
        ZoneId zone = ZoneId.systemDefault();
        LocalDate newDate = localDate.minusMonths(monthsToSubtract); // 减去月份
        Instant instant = newDate.atTime(0, 0, 0).atZone(zone).toInstant(); // 转换为当天的 00:00:00
        return Date.from(instant);
    }


    /**
     * @Description: 获取几天后结束时间
     * @Param: [daysToSubtract]
     * @Return: java.util.Date
     * @Author: szhu
     * @Date: 2020/5/30
     */
    public static Date getPlusDayEndTime(LocalDate localDate,long daysToAdd){
        ZoneId zone = ZoneId.systemDefault();
        Instant zonedDateTime =localDate.plusDays(daysToAdd).atTime(23,59,59).atZone(zone).toInstant();
        return Date.from(zonedDateTime);
    }

    /**
     * 获取当前日期所在自然周的星期天，结果转成对应的格式
     * @param date
     * @param pattern
     * @return
     */
    public static String getNaturalWeekSundayString(Date date, String pattern){
        if(date == null || StringUtils.isBlank(pattern)){
            return null;
        }
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.with(DayOfWeek.SUNDAY).format(DateTimeFormatter.ofPattern(pattern));
    }

    /**
     * 转化当前时间戳
     *
     * @param date
     * @return
     */
    public static Date stringToDate(String date) {
        try {
            return org.apache.commons.lang3.time.DateUtils.parseDate(date, DATETIME_FORMAT);
        } catch (Exception e) {
            log.error("e->", e);
        }
        return new Date();
    }

    public static LocalDateTime getPastStartTime(Integer num) {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 获取n天前的日期
        LocalDate nDaysAgo = now.toLocalDate().minusDays(num);
        // 将日期转换为当天的0点时间
        return nDaysAgo.atStartOfDay();
    }

    public static Date convertLocalDateTime(LocalDateTime localDateTime) {
        // 获取系统默认时区
        ZoneId defaultZoneId = ZoneId.systemDefault();
        // 将LocalDateTime转换为ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(defaultZoneId);
        // 从ZonedDateTime获取Instant
        return Date.from(zonedDateTime.toInstant());
    }


    public static Date getPastStartDate(Integer num) {
        LocalDateTime localDateTime = getPastStartTime(num);
        return convertLocalDateTime(localDateTime);
    }
}
