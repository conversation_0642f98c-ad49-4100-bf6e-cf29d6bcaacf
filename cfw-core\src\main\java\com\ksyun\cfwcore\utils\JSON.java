package com.ksyun.cfwcore.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.google.common.collect.Maps;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public interface JSON {

    String PATTERN_DATETIME = "yyyy-MM-dd HH:mm:ss";

    String PATTERN_DATE = "yyyy-MM-dd";

    String PATTERN_TIME = "HH:mm:ss";

    /**
     * @return 是否为合法的 json 对象字符串
     */
    static boolean isValidObject(String json) {
        if (json == null || json.length() == 0) {
            return false;
        }
        try {
            JsonNode node = Holder.MAPPER.readTree(json);
            return node.isObject();
        } catch (IOException e) {
            // ignored
        }
        return false;
    }

    /**
     * @return null for invalid json
     */
    static JsonNodeType getJsonType(String json) {
        if (json == null || json.length() == 0) {
            return null;
        }
        try {
            JsonNode node = Holder.MAPPER.readTree(json);
            return node.getNodeType();
        } catch (IOException e) {
            // ignored
        }
        return null;
    }

    static Map<String, Object> toMap(String json) throws IOException {
        return Holder.MAPPER.readValue(json, Holder.TYPE_REF_MAP_STRING_OBJECT);
    }

    @SuppressWarnings("unchecked")
    static Map<String, Object> objToMap(Object obj) throws IOException {
        if (Objects.isNull(obj)) {
            return Maps.newHashMap();
        }
        if (obj instanceof Map) {
            return (Map<String, Object>) obj;
        }
        return Holder.MAPPER.readValue(stringify(obj), Holder.TYPE_REF_MAP_STRING_OBJECT);
    }

    static Map<String, String> toMapStr(String json) throws IOException {
        return Holder.MAPPER.readValue(json, Holder.TYPE_REF_MAP_STRING_STRING);
    }

    static String stringify(Object o) {
        return stringify(o, false);
    }

    static String stringify(Object o, boolean pretty) {
        try {
            if (pretty) {
                return Holder.MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(o);
            } else {
                return Holder.MAPPER.writeValueAsString(o);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    static <T> T parse(String json, Class<T> type) {
        try {
            return Holder.MAPPER.readValue(json, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    static Map<String, Object> stringParseMap(String str) {
        Map<String, Object> map = new HashMap<>();
        String[] entries = str.split(",\\s*");
        for (String entry : entries) {
            String[] keyValue = entry.split("=");
            if (keyValue.length == 2) {
                map.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        return map;
    }

    /**
     * If {@link ObjectMapper} is not required, use {@link #reader()} or {@link #writer()}
     * instead.
     *
     * @return The ObjectMapper used by {@link JSON}, the returned mapper is
     * immutable,{@link ObjectMapper#copy()} first if you wanner change.
     * @see <a href=http://stackoverflow.com/a/3909846/1870054>How to share
     * ObjectMapper</a>
     */
    static ObjectMapper mapper() {
        return Holder.MAPPER;
    }

    /**
     * @return Shared writer, this is immutable
     */
    static ObjectWriter writer() {
        return Holder.MAPPER.writer();
    }

    /**
     * @return Shared reader, this is immutable
     */
    static ObjectReader reader() {
        return Holder.MAPPER.reader();
    }

    static void initTimeJd8(ObjectMapper objectMapper) {
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        // 将 Long 转换为 String
        javaTimeModule.addSerializer(new ToStringSerializer(Long.TYPE));
        javaTimeModule.addSerializer(new ToStringSerializer(Long.class));
        // JDK8 时间处理
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        javaTimeModule.addSerializer(LocalDateTime.class,
                new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(PATTERN_DATETIME)));
        javaTimeModule.addSerializer(LocalDate.class,
                new LocalDateSerializer(DateTimeFormatter.ofPattern(PATTERN_DATE)));
        javaTimeModule.addSerializer(LocalTime.class,
                new LocalTimeSerializer(DateTimeFormatter.ofPattern(PATTERN_TIME)));
        javaTimeModule.addDeserializer(LocalDateTime.class,
                new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(PATTERN_DATETIME)));
        javaTimeModule.addDeserializer(LocalDate.class,
                new LocalDateDeserializer(DateTimeFormatter.ofPattern(PATTERN_DATE)));
        javaTimeModule.addDeserializer(LocalTime.class,
                new LocalTimeDeserializer(DateTimeFormatter.ofPattern(PATTERN_TIME)));
        objectMapper.registerModule(javaTimeModule).registerModule(new ParameterNamesModule());
    }

    class Holder {

        private static final ObjectMapper MAPPER;

        private static final TypeReference<Map<String, Object>> TYPE_REF_MAP_STRING_OBJECT =
                new TypeReference<Map<String, Object>>() {
        };

        private static final TypeReference<Map<String, String>> TYPE_REF_MAP_STRING_STRING =
                new TypeReference<Map<String, String>>() {
        };

        static {
            MAPPER = new ObjectMapper().findAndRegisterModules().setSerializationInclusion(JsonInclude.Include.NON_NULL)
                    .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                    .configure(SerializationFeature.WRITE_ENUMS_USING_INDEX, true);
            JSON.initTimeJd8(MAPPER);
        }

    }

    /**
     * 将节点保存为字符串, {@link com.fasterxml.jackson.annotation.JsonRawValue} 只作用于序列化,
     * 该反序列化器相当于该操作的反向操作. 使用注解
     * {@code @JsonDeserialize(using = JSON.KeepAsJsonDeserializer.class)}
     */
    class JsonRawValueDeserializer extends JsonDeserializer<String> {

        @Override
        public String deserialize(JsonParser jp, DeserializationContext ctxt) throws IOException {
            TreeNode tree = jp.getCodec().readTree(jp);
            return tree.toString();
        }

    }

}
