package com.ksyun.cfwcore.utils;


import com.alibaba.fastjson.JSONObject;
import com.ksyun.cfwcore.openapi.kec.domain.RunInstancesRequest;
import common.BaseClient;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;

public class KsyunIamClient {
    private static final String SERVIER = "kec"; //访问的服务
    private static final String REGION = "cn-qingyangtest-1"; // 访问的区域

    private static final String API_URL = "http://iam.api.ksyun.com"; // 接口地址




    private static String sign(Map<String, Object> params, String secretKey) throws Exception {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder paramStr = new StringBuilder();
        for (String key : keys) {
            paramStr.append(URLEncoder.encode(key, "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(String.valueOf(params.get(key)), "UTF-8"))
                    .append("&");
        }
        paramStr.deleteCharAt(paramStr.length() - 1);

        Mac hmac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        hmac.init(secretKeySpec);
        byte[] hash = hmac.doFinal(paramStr.toString().getBytes(StandardCharsets.UTF_8));
        return Hex.encodeHexString(hash);
    }

    private static String getUTCTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(new Date());
    }
    private static JSONObject getSimpleRequestParams(RunInstancesRequest requestObj) throws Exception {
        JSONObject requestParams = new JSONObject();
        //设置接口属性
        requestParams.put("Action", "RunInstances");
        requestParams.put("Version", "2016-03-04");

        BaseClient baseClient =new BaseClient();
        //设置请求体请求参数
        baseClient.setRequestField(requestObj, requestParams);
        return requestParams;
    }
    
    public static void main(String[] args) throws Exception {
        String action = "RunInstances"; // 接口名称
        String version = "2016-03-04"; // 接口版本

        Map<String, Object> params;

        // 接口参数
        RunInstancesRequest requestObj = getData();
        params = getSimpleRequestParams(requestObj);

        // 固定参数
        params.put("Service", SERVIER);
        params.put("Region", REGION);
        params.put("Action", action);
        params.put("Version", version);
        params.put("Timestamp", getUTCTime());
        params.put("SignatureVersion", "1.0");
        params.put("SignatureMethod", "HMAC-SHA256");


        StringBuilder requestURL = new StringBuilder(API_URL + "?");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            requestURL.append(URLEncoder.encode(entry.getKey(), "UTF-8"))
                    .append("=")
                    .append(URLEncoder.encode(String.valueOf(entry.getValue()), "UTF-8"))
                    .append("&");
        }
        HttpClient client = HttpClients.createDefault();
        HttpGet request = new HttpGet(requestURL.toString());

        // 设置请求头
        request.setHeader(HttpHeaders.ACCEPT, "application/json");

        // 发送请求并处理响应
        HttpResponse response = client.execute(request);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String responseBody = EntityUtils.toString(entity);
            System.out.println("Response Status: " + response.getStatusLine());
            System.out.println("Response Body: " + responseBody);
        }
    }

    public static RunInstancesRequest getData(){
        RunInstancesRequest request = new RunInstancesRequest();
        // 设置基本属性
        request.setImageId("f6cad92f-830f-4403-8104-762938f14cc9");
        request.setInstanceType("SE9.12C");
        request.setDataDiskGb(100);
        request.setMaxCount(1);
        request.setMinCount(1);
        request.setSubnetId("2fdfd6cc-c0f1-4e0a-8fe9-71a797bba8f3");
        request.setChargeType("Daily");
        request.setPurchaseTime(1);
        request.setSecurityGroupId("b6191a72-1e69-4c62-9635-1dae9ea0ea90");
        request.setProjectId(0);
        request.setInstanceName("cmy-test2");
        // request.setUserData("user data example");

        // 设置 NetworkInterfaceList
        List<RunInstancesRequest.NetworkInterfaceDto> networkInterfaceList = new ArrayList<>();
        RunInstancesRequest.NetworkInterfaceDto networkInterface1 = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface1.setSubnetId("2fdfd6cc-c0f1-4e0a-8fe9-71a797bba8f3");
        networkInterface1.setSecurityGroupId("b6191a72-1e69-4c62-9635-1dae9ea0ea90");
        RunInstancesRequest.NetworkInterfaceDto networkInterface2 = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface2.setSubnetId("2fdfd6cc-c0f1-4e0a-8fe9-71a797bba8f3");
        networkInterface2.setSecurityGroupId("b6191a72-1e69-4c62-9635-1dae9ea0ea90");
        RunInstancesRequest.NetworkInterfaceDto networkInterface3 = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface3.setSubnetId("2fdfd6cc-c0f1-4e0a-8fe9-71a797bba8f3");
        networkInterface3.setSecurityGroupId("b6191a72-1e69-4c62-9635-1dae9ea0ea90");
        networkInterfaceList.add(networkInterface1);
        networkInterfaceList.add(networkInterface2);
        networkInterfaceList.add(networkInterface3);
        request.setNetworkInterfaceList(networkInterfaceList);
        return request;
    }


}