package com.ksyun.cfwcore.utils;

import com.alibaba.fastjson.JSONObject;
import com.ksyun.cfwcore.openapi.kec.domain.RunInstancesRequest;
import common.BaseClient;
import common.Credential;
import common.aws.AWS4EncryptionFactory;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class KsyunIamClientAws4 {

    private static final String AK = "AKLTwMXRuxtNQGi4Ed3zmdeiA"; // 请替换为您的AccessKey
    private static final String SK = "Your SK"; // 请替换为您的SecretKey
    private static final String SERVIER = "iam"; //访问的服务
    private static final String REGION = "cn-beijing-6"; //访问的区域

    private static final String API_URL = "http://iam.api.ksyun.com"; //服务地址

    private static final String HOST = "iam.api.ksyun.com"; // 请求头Host

    /**
     *
     * @param head 请求头
     * @param params 请求参数
     * @param requestMethod get|post
     */
    public static void enhanceAws4Signature(Map<String, String> head, Map<String, Object> params, String requestMethod) {
        Credential credential = new Credential(AK, SK, REGION);
        AWS4EncryptionFactory aws4EncryptionFactory = new AWS4EncryptionFactory(credential.getSecretKey(), credential.getSignStr(), SERVIER, credential.getRegion());


        //设置请求参数
        if (params != null) {
            params.entrySet().forEach(entry -> {
                aws4EncryptionFactory.setParamMap(entry.getKey(), entry.getValue());
            });
        }

        //设置请求头
        if (head != null) {
            head.entrySet().forEach(entry -> {
                aws4EncryptionFactory.setHeadMap(entry.getKey(), entry.getValue());
            });
        }

        //AWS 加密
        aws4EncryptionFactory.generateSignature(requestMethod);

        //回填AWS4 签名
        String authorization = aws4EncryptionFactory.getHead().get(AWS4EncryptionFactory.X_Authorization);
        String xAmzDate = aws4EncryptionFactory.getHead().get(AWS4EncryptionFactory.X_AMZ_DATA);
        head.put(AWS4EncryptionFactory.X_Authorization, authorization);
        head.put(AWS4EncryptionFactory.X_AMZ_DATA, xAmzDate);
    }

    public static JSONObject getSimpleRequestParams(RunInstancesRequest requestObj,String action,String version) throws Exception {
        JSONObject requestParams = new JSONObject();
        //设置接口属性
        requestParams.put("Action", action);
        requestParams.put("Version", version);

        BaseClient baseClient =new BaseClient();
        //设置请求体请求参数
        baseClient.setRequestField(requestObj, requestParams);
        return requestParams;
    }

    public static void main(String[] args) throws Exception {
        //接口参数
        Map<String, Object> params;
        RunInstancesRequest requestParam = getData();
        params = getSimpleRequestParams(requestParam,"Action","Version");
        create(params);
    }

    public static void create(Map<String, Object> params) throws Exception {

        //接口参数

        Map<String, String> header = new HashMap<>();
        header.put("Accept", "application/json");
        header.put("Host", HOST);

        //AWS4
        enhanceAws4Signature(header, params, "get");

        // 拼接请求URL
        String queryString = params.entrySet().stream()
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        URI requestURL = new URI(API_URL + "?" + queryString);


        // 发送请求并处理响应
        HttpClient client = HttpClients.createDefault();
        HttpGet request = new HttpGet(requestURL.toString());

        //设置请求头
        header.entrySet().forEach(entry -> {
            request.setHeader(entry.getKey(), entry.getValue());
        });

        HttpResponse response = client.execute(request);
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            String responseBody = EntityUtils.toString(entity);
            System.out.println("Response Status: " + response.getStatusLine());
            System.out.println("Response Body: " + responseBody);
        }
    }

    public static RunInstancesRequest getData(){
        RunInstancesRequest request = new RunInstancesRequest();

        // 设置基本属性
        request.setImageId("ami-123456");
        request.setDedicatedHostId("dh-123456");
        request.setInstanceConfigureVCPU("4");
        request.setInstanceConfigureMemoryGb("16");
        request.setInstanceType("ecs.g5.large");
        request.setDataDiskGb(100);
        request.setMaxCount(2);
        request.setMinCount(1);
        request.setSubnetId("subnet-123456");
        request.setInstancePassword("password123");
        request.setChargeType("PostPaidByHour");
        request.setPurchaseTime(1);
        request.setSecurityGroupId("sg-123456");
        request.setProjectId(123456);
        request.setInstanceName("MyInstance");
        request.setInstanceNameSuffix("001");
        request.setUserData("user data example");
        request.setSystemDiskDiskType("SSD");
        request.setSystemDiskDiskSize(50);
        request.setModelId("model-123456");
        request.setModelVersion(1);
        request.setAssembledImageDataDiskType("HDD");
        request.setAutoCreateEbs(true);
        request.setLineId("line-123456");
        request.setAddressBandWidth(10);
        request.setAddressChargeType("PostPaidByPeak");
        request.setAddressProjectId("123456");
        request.setAddressPurchaseTime(12);
        request.setKeepImageLogin(false);
        request.setHostName("hostname");
        request.setHostNameSuffix(1);
        request.setPassword("password123");
        request.setFailureAutoDelete(false);
        request.setDataGuardId("dg-123456");

        // 设置 DataDiskList
        List<RunInstancesRequest.DataDiskDto> dataDiskList = new ArrayList<>();
        RunInstancesRequest.DataDiskDto dataDisk = new RunInstancesRequest.DataDiskDto();
        dataDisk.setDeleteWithInstance(true);
        dataDisk.setType("SSD");
        dataDisk.setSize(50);
        dataDiskList.add(dataDisk);
        request.setDataDiskList(dataDiskList);

        // 设置 NetworkInterfaceList
        List<RunInstancesRequest.NetworkInterfaceDto> networkInterfaceList = new ArrayList<>();
        RunInstancesRequest.NetworkInterfaceDto networkInterface = new RunInstancesRequest.NetworkInterfaceDto();
        networkInterface.setSubnetId("subnet-123456");
        networkInterface.setSecurityGroupId("sg-123456");
        networkInterface.setPrivateIpAddress("*************");
        networkInterfaceList.add(networkInterface);
        request.setNetworkInterfaceList(networkInterfaceList);

        // 设置 TagList
        List<RunInstancesRequest.TagDto> tagList = new ArrayList<>();
        RunInstancesRequest.TagDto tag = new RunInstancesRequest.TagDto();
        tag.setKey("Environment");
        tag.setValue("Production");
        tagList.add(tag);
        request.setTagList(tagList);
        return request;
    }
}