package com.ksyun.cfwmessage;

import lombok.extern.log4j.Log4j2;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * <AUTHOR>
 */
@Log4j2
@SpringBootApplication(scanBasePackages = {"com.ksyun"})
@MapperScan("com.ksyun.cfwmessage.dao.mapper")
public class CfwMessageApplication {

    public static void main(String[] args) {
        SpringApplication.run(CfwMessageApplication.class, args);
        log.info("cfw-Message服务组件启动成功");
    }

}
