package com.ksyun.cfwmessage.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import com.ksyun.comm.config.apollo.ApolloRefreshConfigProvider;
import com.ksyun.comm.config.apollo.ApolloUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * Created by xuyaming on 2018/2/9.
 */
@Component
@Slf4j
public class ApolloConfigUtils implements ApplicationContextAware {
    private ApolloRefreshConfigProvider provider;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        provider= ApolloUtils.init(applicationContext,"com.ksyun");

    }

    @ApolloConfigChangeListener({"cfw-scheduler-common","cfw-message","cfw-core-common","cfw-api-common"})
    protected void onChange(ConfigChangeEvent changeEvent) {
        provider.onChange(changeEvent);
    }
}
