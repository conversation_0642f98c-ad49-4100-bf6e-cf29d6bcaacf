package com.ksyun.cfwmessage.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.cfwcore.config.LoadConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshMethod;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2025/7/11
 */

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Component
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-message"})
public class CommonMessageConfig extends LoadConfig {
    /**
     * 流量日志保留时长
     */
    @Value("${log.save.map}")
    @RefreshField("log.save.map")
    private String logSaveDayMap;

    private Map<String, Integer> logSaveDayMapCache = new HashMap<>();
    @RefreshMethod(field = {"log.save.map"})
    private void initLogSaveDayMapCache() throws Exception {
        if (StringUtils.isBlank(logSaveDayMap)) {
            return;
        }
        synchronized (logSaveDayMapCache) {
            logSaveDayMapCache = (Map<String, Integer>) loadJsonMap(logSaveDayMap, "log.save.day.map", logSaveDayMapCache, Integer.class);
            log.info("日志保留时间map [{}]", logSaveDayMapCache);
        }
    }

    @PostConstruct
    private void commonConfigInit() throws Exception {
        this.initLogSaveDayMapCache();
    }
}
