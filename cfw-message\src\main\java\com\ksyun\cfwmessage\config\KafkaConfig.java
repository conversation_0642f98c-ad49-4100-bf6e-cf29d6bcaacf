package com.ksyun.cfwmessage.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Configuration
@RefreshType
@Slf4j
@EnableApolloConfig({"cfw-message","cfw-core-common"})
public class KafkaConfig {

    @Value("${kafka.consumer.servers}")
    private String kafkaServers;
    @Value("${kafka.consumer.enable.auto.commit}")
    private boolean enableAutoCommit;
    @Value("${kafka.consumer.properties.group.id}")
    public String groupId;
    @Value("${kafka.consumer.auto.offset.reset}")
    public String autoOffsetReset;
    @Value("${kafka.consumer.max.poll.records}")
    public String maxPollRecords;
    @Value("${kafka.consumer.session.timeout}")
    public String sessionTimeout;
    @Value("${kafka.consumer.properties.request.timeout}")
    public String requestTimeout;
    @Value("${kafka.consumer.auto.commit.interval}")
    public String autoCommitInterval;
    @Value("${kafka.consumer.concurrency}")
    public Integer consumerConcurrency;
    @Value("${kafka.user}")
    public String kafkaUser;
    @Value("${kafka.password}")
    public String kafkaPassword;
    @Value("${kafka.security.protocol}")
    public String securityProtocol;
    @Value("${kafka.sasl.mechanism}")
    public String saslMechanism;
    @Value("${kafka.ssl.truststore.location}")
    public String sslTruststoreLocation;
    @Value("${kafka.ssl.truststore.password}")
    public String sslTruststorePassword;

    @Value("${kafka.saslEnable}")
    public Integer saslEnable;




    @Bean
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<String, String>> batchFactory() {

        ConcurrentKafkaListenerContainerFactory<String, String> factory = new ConcurrentKafkaListenerContainerFactory<>();

        factory.setConsumerFactory(consumerFactory());

        //消费并发数，不大于partition数量
        factory.setConcurrency(consumerConcurrency);

        //开启批量消费
        factory.setBatchListener(true);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);

        return factory;

    }


    public ConsumerFactory<String, String> consumerFactory() {

        return new DefaultKafkaConsumerFactory<>(consumerConfigs());

    }

    public Map<String, Object> consumerConfigs() {

        Map<String, Object> propsMap = new HashMap<>(16);

        propsMap.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaServers);

        propsMap.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, enableAutoCommit);

        propsMap.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, autoCommitInterval);

        propsMap.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, sessionTimeout);

        propsMap.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, requestTimeout);

        propsMap.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        propsMap.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        propsMap.put(ConsumerConfig.GROUP_ID_CONFIG, groupId);

        propsMap.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);

        propsMap.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 10);

        if (saslEnable == 1) {
            // SASL 认证配置
            propsMap.put("security.protocol", securityProtocol);
            propsMap.put("sasl.mechanism", saslMechanism);
            propsMap.put("ssl.truststore.location", sslTruststoreLocation);
            propsMap.put("ssl.truststore.password", sslTruststorePassword);
            propsMap.put("ssl.endpoint.identification.algorithm", "");
            propsMap.put("sasl.jaas.config", buildJaasConfig(kafkaUser, kafkaPassword));
            log.info("sasl.jaas.config:{}", propsMap.get("sasl.jaas.config"));
        }

        return propsMap;
    }

    private String buildJaasConfig(String username, String password) {
        return String.format("org.apache.kafka.common.security.plain.PlainLoginModule required username=\"%s\" password=\"%s\";",
                username.replace("\"", "\\\""),
                password.replace("\"", "\\\"")
        );
    }
}
