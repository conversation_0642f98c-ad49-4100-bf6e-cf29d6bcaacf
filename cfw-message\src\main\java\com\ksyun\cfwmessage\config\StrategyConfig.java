package com.ksyun.cfwmessage.config;

import com.ksyun.cfwcore.enums.EtcdListenEnum;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwmessage.annotation.Handle;
import com.ksyun.cfwmessage.es.EsIndexService.IdexService;
import com.ksyun.cfwmessage.kafka.handle.DataHanlder;
import com.ksyun.cfwmessage.service.etcd.EtcdListenService;
import lombok.Getter;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Getter
public class StrategyConfig{
    @Autowired
    private List<DataHanlder> dataHanlderList;

    @Autowired
    private List<EtcdListenService> etcdListenServiceList;

    @Autowired
    private List<IdexService> idexServiceList;

    private Map<String, DataHanlder> handleDataMap = new HashMap<>();

    private Map<String, EtcdListenService> etcdListenServiceMap = new HashMap<>();

    private Map<String, IdexService> idexServiceMap = new HashMap<>();

    @PostConstruct
    public void postProcessBeanFactory() throws BeansException {
        for (DataHanlder dataHanlder : dataHanlderList) {
            Handle handle = dataHanlder.getClass().getAnnotation(Handle.class);
            if (handle != null) {
                handleDataMap.put(handle.name(), dataHanlder);
            }
        }

        for (EtcdListenService etcdListenService : etcdListenServiceList) {
            List<EtcdListenEnum> listenKeys = etcdListenService.getListenKey();
            for (EtcdListenEnum listenKey : listenKeys) {
                etcdListenServiceMap.put(listenKey.getKey(), etcdListenService);
            }
        }

        for (IdexService idexService : idexServiceList) {
            LogTopicEnum logTopicEnum = idexService.getIndexType();
            idexServiceMap.put(logTopicEnum.getTopic(), idexService);
        }
    }

    public DataHanlder getDataHanlder(String name) {
        return handleDataMap.get(name);
    }

    public IdexService getIdexService(String topic) {
        return idexServiceMap.get(topic);
    }

}
