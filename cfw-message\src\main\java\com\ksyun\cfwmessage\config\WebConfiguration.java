package com.ksyun.cfwmessage.config;

import com.ksyun.cfwmessage.interceptor.ProxyAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class WebConfiguration implements WebMvcConfigurer {
    private ProxyAuthInterceptor proxyAuthInterceptor;


    @Autowired
    public void setProxyAuthInterceptor(ProxyAuthInterceptor proxyAuthInterceptor) {
        this.proxyAuthInterceptor = proxyAuthInterceptor;
    }
    /**
     * 请求路径返回多种数据格式
     */
    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
        //是否开启后缀模式
        configurer.favorPathExtension(false)
                //默认是xml模式
                .defaultContentType(MediaType.APPLICATION_JSON)
                .mediaType("xml", MediaType.APPLICATION_XML)
                .mediaType("json", MediaType.APPLICATION_JSON);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(proxyAuthInterceptor).addPathPatterns("/**").excludePathPatterns("/heart/CheckHealth");
 }
}
