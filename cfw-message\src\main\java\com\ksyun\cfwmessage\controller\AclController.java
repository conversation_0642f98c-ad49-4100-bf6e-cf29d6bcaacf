package com.ksyun.cfwmessage.controller;

import com.ksyun.cfwmessage.dao.service.CfwAclService;
import com.ksyun.cfwmessage.domain.ListOrderAclIdParam;
import com.ksyun.cfwmessage.domain.ListOrderAclIdResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class AclController {
    @Autowired
    private CfwAclService cfwAclService;

    /**
     * 获取acl名称列表
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=ListOrderAclId"}, method = RequestMethod.POST)
    public ListOrderAclIdResponse listOrderAclId(@RequestBody @Valid ListOrderAclIdParam param) {
        return cfwAclService.listOrderAclId(param);
    }
}
