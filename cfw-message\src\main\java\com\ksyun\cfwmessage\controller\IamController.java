package com.ksyun.cfwmessage.controller;


import cn.hutool.json.JSONUtil;
import com.ksyun.cfwmessage.domain.iam.ProjectMqData;
import com.ksyun.cfwmessage.service.kfw.FwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"},produces = MediaType.APPLICATION_JSON_VALUE)
public class IamController {

    @Autowired
    private FwService fwService ;
    @RequestMapping(params = {"Action=ProjectChange"}, method = RequestMethod.POST,consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public void projectchange(@ModelAttribute ProjectMqData param) {
        log.info("项目制 projectchange param:{}", JSONUtil.toJsonStr(param));
        fwService.projectchange(param);
    }
}
