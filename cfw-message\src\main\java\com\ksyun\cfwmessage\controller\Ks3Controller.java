package com.ksyun.cfwmessage.controller;


import com.ksyun.cfwcore.domain.ks3.DownFileUrlResponse;
import com.ksyun.cfwcore.domain.ks3.FileParam;
import com.ksyun.cfwmessage.service.ks3.Ks3Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Validated
@RequestMapping(value = "/", params = {"Action"}, produces = {MediaType.APPLICATION_JSON_VALUE})
public class Ks3Controller {
    @Autowired
    private Ks3Service ks3Service;

    /**
     * 获取文件下载地址
     * @param param
     * @return
     * @throws Exception
     */
    @RequestMapping(params = {"Action=GetDownFileUrl"}, method = RequestMethod.POST)
    @ResponseBody
    public DownFileUrlResponse getDownFileUrl(@RequestBody @Valid FileParam param) {
        return ks3Service.getDownFileUrl(param);
    }

}
