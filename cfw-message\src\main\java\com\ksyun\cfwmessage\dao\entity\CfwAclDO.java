package com.ksyun.cfwmessage.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName cfw_acl
 */
@TableName(value ="cfw_acl")
@Data
public class CfwAclDO implements Serializable {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 实例ID
     */
    @TableField(value = "acl_id")
    private String aclId;

    /**
     *
     */
    @TableField(value = "acl_name")
    private String aclName;

    /**
     * 实例ID
     */
    @TableField(value = "fw_id")
    private String fwId;

    /**
     * 
     */
    @TableField(value = "account_id")
    private String accountId;

    /**
     * 出入向(in入向:源安全域untrust--目的安全域trust, out出向:源安全域trust--目的安全域untrust)
     */
    @TableField(value = "direction")
    private String direction;


    /**
     * 源地址类型(ip|addrbook|zone|any)
     */
    @TableField(value = "src_type")
    private String srcType;

    /**
     * 源IP
     */
    @TableField(value = "src_ip")
    private String srcIp;

    /**
     * 源地址簿ID
     */
    @TableField(value = "src_addrbook")
    private String srcAddrbook;

    /**
     * 地域
     */
    @TableField(value = "src_zone")
    private String srcZone;

    /**
     * 目的地址类型(ip|addrbook|any)
     */
    @TableField(value = "dest_type")
    private String destType;

    /**
     * 目的IP
     */
    @TableField(value = "dest_ip")
    private String destIp;

    /**
     * 目的地址簿
     */
    @TableField(value = "dest_addrbook")
    private String destAddrbook;


    /**
     * 服务类型(service|servicegroup|any)
     */
    @TableField(value = "service_type")
    private String serviceType;

    /**
     * 服务协议
     */
    @TableField(value = "service_info")
    private String serviceInfo;

    /**
     * 服务组
     */
    @TableField(value = "service_group")
    private String serviceGroup;

    /**
     * 应用类型(app|any)
     */
    @TableField(value = "app_type")
    private String appType;

    /**
     * 
     */
    @TableField(value = "app_value")
    private String appValue;

    /**
     * 动作(accept|deny)
     */
    @TableField(value = "policy")
    private String policy;

    /**
     * 状态(start|stop)
     */
    @TableField(value = "status")
    private String status;

    /**
     * 优先级
     */
    @TableField(value = "priority")
    private Integer priority;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 命中数
     */
    @TableField(value = "hit_count")
    private Long hitCount;

    /**
     * 
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     *
     */
    @TableField(value = "delete_status")
    private Integer deleteStatus;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}