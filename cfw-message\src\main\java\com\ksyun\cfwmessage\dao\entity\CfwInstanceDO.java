package com.ksyun.cfwmessage.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* 
* <AUTHOR>
 * @TableName cfw_instance
*/
@Data
@TableName("cfw_instance")
public class CfwInstanceDO implements Serializable {

    private static final long serialVersionUID = -3204425123049859610L;
    /**
    * 
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 实例ID
    */
    private String fwId;

    /**
     * （高级版Advanced | 企业版Enterprise）
     */
    private String instanceType;

    /**
     * 项目Id
     */
    private String projectId;

    /**
    *
    */
    private String accountId;
    /**
    * 
    */
    private String subnetId;
    /**
    * 
    */
    private String region;
    /**
    * 
    */
    private String name;
    /**
    * 套餐
    */
    private String chargeType;
    /**
    * 带宽值
    */
    private Integer bandwidth;
    /**
    * 1-创建中、2-运行中、3-更配中、4-关停、5-异常、6-退订中
    */
    private Integer status;
    /**
    * 可防护ip总数
    */
    private Integer totalEipNum;
    /**
    * 已防护ip总数
    */
    private Integer usedEipNum;
    /**
    * ips状态 0-停止，1-开启
    */
    private Integer ipsStatus;
    /**
    * av状态 0-停止，1-开启
    */
    private Integer avStatus;
    /**
    * fwLbId
    */
    private String fwLbId;
    /**
    * fwLbVni
    */
    private Integer fwLbVni;
    /**
    * 是否删除 0-未删，1-已删
    */
    private Integer deleteStatus;
    /**
    * 创建时间
    */
    private Date createTime;
    /**
    * 更新时间
    */
    private Date updateTime;

    /**
     * 订单Id
     */
    private String subOrderId;

}
