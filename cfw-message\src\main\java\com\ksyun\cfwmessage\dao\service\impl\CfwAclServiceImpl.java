package com.ksyun.cfwmessage.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import com.ksyun.cfwmessage.dao.entity.CfwAclDO;
import com.ksyun.cfwmessage.dao.mapper.CfwAclMapper;
import com.ksyun.cfwmessage.dao.service.CfwAclService;
import com.ksyun.cfwmessage.domain.ListOrderAclIdParam;
import com.ksyun.cfwmessage.domain.ListOrderAclIdResponse;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description 针对表【cfw_acl】的数据库操作Service实现
 * @createDate 2024-12-20 15:24:00
 */
@Service
public class CfwAclServiceImpl extends ServiceImpl<CfwAclMapper, CfwAclDO> implements CfwAclService {

    @Override
    public ListOrderAclIdResponse listOrderAclId(ListOrderAclIdParam param) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwAclDO::getAclId);
        queryWrapper.eq(CfwAclDO::getFwId, param.getWallId());
        queryWrapper.eq(CfwAclDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        queryWrapper.orderByAsc(CfwAclDO::getPriority);
        List<CfwAclDO> list = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(list)) {
            return new ListOrderAclIdResponse().setAclIds(list.stream().map(CfwAclDO::getAclId).collect(Collectors.toList()));
        }
        return new ListOrderAclIdResponse();
    }

    @Override
    public String getNameByAclId(String aclId) {
        LambdaQueryWrapper<CfwAclDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CfwAclDO::getAclName);
        queryWrapper.eq(CfwAclDO::getAclId, aclId);
        CfwAclDO cfwAclDO = this.getOne(queryWrapper);
        if(Objects.nonNull(cfwAclDO)){
            return cfwAclDO.getAclName();
        }else{
            return "";
        }
    }
}




