package com.ksyun.cfwmessage.dao.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwmessage.dao.entity.CfwDictionaryDO;
import com.ksyun.cfwmessage.dao.mapper.CfwDictionaryMapper;
import com.ksyun.cfwmessage.dao.service.CfwDictionaryService;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【cfw_dictionary】的数据库操作Service实现
 * @createDate 2025-01-13 15:30:35
 */
@Service
public class CfwDictionaryServiceImpl extends ServiceImpl<CfwDictionaryMapper, CfwDictionaryDO> implements CfwDictionaryService {
    @Autowired
    private JedisTemplate jedisTemplate;

    @Override
    public Map<String, String> queryDictionaryByType(String type) throws Exception {
        String dictionaryStr = jedisTemplate.getKey(RedisConstants.CFW_DICTIONARY);
        List<CfwDictionaryDO> list;
        if (StringUtils.isBlank(dictionaryStr)) {
            LambdaQueryWrapper<CfwDictionaryDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CfwDictionaryDO::getDicType, type);
            queryWrapper.orderByAsc(CfwDictionaryDO::getOrderNum);
            list = this.list(queryWrapper);
            jedisTemplate.setExpireKey(RedisConstants.CFW_DICTIONARY, JSONUtil.toJsonStr(list), 1800);
        } else {
            ObjectMapper objectMapper = new ObjectMapper();
            list = objectMapper.readValue(dictionaryStr, new TypeReference<List<CfwDictionaryDO>>() {});
        }
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(CfwDictionaryDO::getCode, CfwDictionaryDO::getDescription, (k1, k2) -> k1));
        }
        return new HashMap<>();
    }
}




