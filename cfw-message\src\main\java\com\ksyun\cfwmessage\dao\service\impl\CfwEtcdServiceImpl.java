package com.ksyun.cfwmessage.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ksyun.cfwcore.enums.ExecutedStatusEnum;
import com.ksyun.cfwmessage.dao.entity.CfwEtcdDO;
import com.ksyun.cfwmessage.dao.mapper.CfwEtcdMapper;
import com.ksyun.cfwmessage.dao.service.CfwEtcdService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cfw_etcd_cluster】的数据库操作Service实现
* @createDate 2024-12-25 23:09:01
*/
@Service
public class CfwEtcdServiceImpl extends ServiceImpl<CfwEtcdMapper, CfwEtcdDO>
    implements CfwEtcdService {

    @Override
    public List<CfwEtcdDO> queryChangeWallOperation(Date yesterday) {
        LambdaQueryWrapper<CfwEtcdDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.le(CfwEtcdDO::getCreateTime,yesterday);
        queryWrapper.eq(CfwEtcdDO::getStatus, ExecutedStatusEnum.UNEXECUTED.getCode());
        return null;
    }

    @Override
    public void updateStatusByIds(List<Long> ids) {
        LambdaUpdateWrapper<CfwEtcdDO> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.in(CfwEtcdDO::getId, ids);
        updateWrapper.set(CfwEtcdDO::getStatus,ExecutedStatusEnum.EXECUTED.getCode());
        this.update(updateWrapper);
    }
}




