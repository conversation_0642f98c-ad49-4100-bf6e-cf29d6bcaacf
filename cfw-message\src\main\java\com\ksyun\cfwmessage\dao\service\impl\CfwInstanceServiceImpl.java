package com.ksyun.cfwmessage.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import com.ksyun.cfwmessage.dao.entity.CfwInstanceDO;
import com.ksyun.cfwmessage.dao.entity.CfwProjectDO;
import com.ksyun.cfwmessage.dao.mapper.CfwInstanceMapper;
import com.ksyun.cfwmessage.dao.service.CfwInstanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CfwInstanceServiceImpl extends ServiceImpl<CfwInstanceMapper, CfwInstanceDO> implements CfwInstanceService {
    @Autowired
    private CfwInstanceMapper cfwInstanceMapper;

    @Override
    public CfwInstanceDO selectByFwId(String fwId) {
        LambdaQueryWrapper<CfwInstanceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CfwInstanceDO::getFwId, fwId);
        queryWrapper.eq(CfwInstanceDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateFwStatus(String fwId, Integer status) {
        LambdaUpdateWrapper<CfwInstanceDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwInstanceDO::getFwId, fwId);
        updateWrapper.set(CfwInstanceDO::getStatus, status);
        updateWrapper.set(CfwInstanceDO::getUpdateTime, new Date());
        this.update(updateWrapper);
    }

    @Override
    public void batchUpdateProjectId(List<CfwProjectDO> cfwProjectDOList) {
        cfwInstanceMapper.batchUpdateProjectId(cfwProjectDOList);
    }


}
