package com.ksyun.cfwmessage.dao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ksyun.cfwcore.enums.DeleteFlagEnum;
import com.ksyun.cfwmessage.dao.entity.CfwRsDO;
import com.ksyun.cfwmessage.dao.mapper.CfwRsMapper;
import com.ksyun.cfwmessage.dao.service.CfwRsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CfwRsServiceImpl  extends ServiceImpl<CfwRsMapper, CfwRsDO> implements CfwRsService {
    @Override
    public CfwRsDO selectByFwInstanceId(String instanceId) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CfwRsDO::getFwInstanceId,instanceId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.getOne(queryWrapper);
    }

    @Override
    public List<CfwRsDO> selectByFwId(String fwId) {
        LambdaQueryWrapper<CfwRsDO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CfwRsDO::getFwId,fwId);
        queryWrapper.eq(CfwRsDO::getDeleteStatus, DeleteFlagEnum.RESERVE.getStatus());
        return this.list(queryWrapper);
    }

    @Override
    public void updateRsStatus(String fwInstanceId, int rsStatus) {
        LambdaUpdateWrapper<CfwRsDO> updateWrapper =new LambdaUpdateWrapper<>();
        updateWrapper.eq(CfwRsDO::getFwInstanceId,fwInstanceId);
        updateWrapper.set(CfwRsDO::getRsStatus,rsStatus);
        updateWrapper.set(CfwRsDO::getUpdateTime,new Date());
        this.update(updateWrapper);
    }
}
