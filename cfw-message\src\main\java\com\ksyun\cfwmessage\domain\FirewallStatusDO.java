package com.ksyun.cfwmessage.domain;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Document(indexName = "FirewallStatus")
public class FirewallStatusDO implements Serializable {

    private static final long serialVersionUID = 1576268587150727865L;
    @Field(type = FieldType.Text)
    private String fwId;
    @Id
    private String fwInstanceId;
    @Field(type = FieldType.Keyword)
    private String timestamp;
    /**
     * 0-初始化中，1-运行中，2-异常，3-升级中
     */
    @Field(type = FieldType.Keyword)
    private Integer status;
    /**
     * CPU使用率
     */
    @Field(type = FieldType.Text)
    private String cpu;
    /**
     * 内存使用率
     */
    @Field(type = FieldType.Text)
    private String mem;
    /**
     * 磁盘使用率
     */
    @Field(type = FieldType.Text)
    private String disk;
    /**
     * 防火墙版本
     */
    @Field(type = FieldType.Text)
    private String vfw_version;
    /**
     * docker agent版本
     */
    @Field(type = FieldType.Text)
    private String agent_version;
    /**
     * ips版本
     */
    @Field(type = FieldType.Text)
    private String ips_version;
}
