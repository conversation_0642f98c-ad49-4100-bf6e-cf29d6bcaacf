package com.ksyun.cfwmessage.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class FirewallUploadStatusEtcd implements Serializable {
    private static final long serialVersionUID = -2981050116960732550L;
    private String etcdKey;
    private String fwId;
    private String fwInstanceId;
    private String traceId;
    private String operationId;
    private String timestamp;
    /**
     * 0-初始化中，1-运行中，2-异常，3-升级中
     */
    private Integer status;
    /**
     * CPU使用率
     */
    private String cpu;
    /**
     * 内存使用率
     */
    private String mem;
    /**
     * 磁盘使用率
     */
    private String disk;
    /**
     * 防火墙版本
     */
    private String vfw_version;
    /**
     * docker agent版本
     */
    private String agent_version;
    /**
     * ips版本
     */
    private String ips_version;
}
