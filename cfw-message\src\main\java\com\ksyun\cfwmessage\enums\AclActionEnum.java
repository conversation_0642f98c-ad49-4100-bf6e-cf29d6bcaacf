package com.ksyun.cfwmessage.enums;

public enum AclActionEnum {
    SESSION_START("session start", "permit"),
    POLICY_DENY("policy deny", "deny"),

    POLICY_DEFAULT_PERMIT("policy default(permit)", "permit"),
    POLICY_DEFAULT_DENY("policy default(deny)", "deny"),

    ;
    private String code;
    private String kcCode;

    AclActionEnum(String code, String kcCode) {
        this.code = code;
        this.kcCode = kcCode;
    }

    public static String getKcCodeByCode(String code) {
        for (AclActionEnum val : values()) {
            if (val.code.equals(code)) {
                return val.kcCode;
            }
        }
        return null;
    }

}
