package com.ksyun.cfwmessage.enums;

public enum RiskActionEnum {
    LOG_ONLY("log-only", "观察模式"),
    RESET("reset", "连接重试"),

    BLOCK_IP("block-ip, blocktime", "IP封禁"),
    BLOCK_SERVICE("block-service, blocktime", "服务阻断"),

    NO_ACTION("No action", "未处置"),

    ;
    private String code;
    private String kcMsg;

    RiskActionEnum(String code, String kcMsg) {
        this.code = code;
        this.kcMsg = kcMsg;
    }

    public static String getKcCodeByCode(String code) {
        for (RiskActionEnum val : values()) {
            if (val.code.contains(code)) {
                return val.code.replace(code, val.kcMsg);
            }
        }
        return null;
    }
}
