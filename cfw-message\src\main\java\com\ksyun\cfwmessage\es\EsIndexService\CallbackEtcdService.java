package com.ksyun.cfwmessage.es.EsIndexService;

import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.config.CommonMessageConfig;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class CallbackEtcdService  implements IdexService{

    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CommonMessageConfig commonMessageConfig;
    @Override
    public void createIndex(String indexName) {
        CreateIndexRequest request = new CreateIndexRequest(indexName);

        // 设置索引的设置和映射
        request.settings(Settings.builder()
                .put("index.number_of_shards", 3)
                .put("index.number_of_replicas", 1)
        );

        String jsonMapping = "{"
                + "\"properties\": {"
                + "\"etcdKey\": {\"type\": \"text\"},"
                + "\"fwId\": {\"type\": \"keyword\"},"
                + "\"fwInstanceId\": {\"type\": \"keyword\"},"
                + "\"traceId\": {\"type\": \"keyword\"},"
                + "\"operationId\": {\"type\": \"keyword\"},"
                + "\"result\": {\"type\": \"text\"},"
                + "\"reason\": {\"type\": \"text\"},"
                + "\"timestamp\": {\"type\": \"keyword\"}"
                + "}"
                + "}";
        request.mapping(jsonMapping, XContentType.JSON);
        try {
            AcknowledgedResponse createIndexResponse =cfwEsUtils.getRestHighLevelClient().indices().create(request, RequestOptions.DEFAULT);
            System.out.println("Index created: " + createIndexResponse.isAcknowledged());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public void deleteIndex() {
        LogTopicEnum logTopicEnum = getIndexType();
        Integer saveMonth = commonMessageConfig.getLogSaveDayMapCache().get(logTopicEnum.getTopic());
        if(Objects.isNull(saveMonth)){
            log.info("命令回调索引不删除");
            return ;
        }
        Date date = DateUtils.getMinusMonthStartTime(LocalDate.now(), saveMonth);
        String indexRisk = EsUtils.getIndexByMonth(LogTopicEnum.CFW_ETCD_CALLBACK.getTopic(), date);
        log.info("删除命令回调索引：{}",indexRisk);
        try {
            cfwEsUtils.deleteIndex(indexRisk);
        } catch (Exception e) {
            log.error("删除命令回调索引：" + e.getMessage());
        }
    }

    @Override
    public LogTopicEnum getIndexType() {
        return LogTopicEnum.CFW_ETCD_CALLBACK;
    }
}