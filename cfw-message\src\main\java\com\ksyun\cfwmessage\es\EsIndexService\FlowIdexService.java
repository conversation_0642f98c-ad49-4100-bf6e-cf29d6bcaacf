package com.ksyun.cfwmessage.es.EsIndexService;

import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwmessage.config.CommonMessageConfig;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwcore.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlowIdexService implements IdexService{
    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CommonMessageConfig commonMessageConfig;

    @Override
    public void createIndex(String indexName) {
        CreateIndexRequest request = new CreateIndexRequest(indexName);

        // 设置索引的设置和映射
        request.settings(Settings.builder()
                .put("index.number_of_shards", 8)
                .put("index.number_of_replicas", 1)
                .put("index.max_result_window", 2000000000)
        );

        String jsonMapping = "{"
                + "\"properties\": {"
                + "\"time\": {\"type\": \"keyword\"},"
                + "\"fw-instance-id\": {\"type\": \"keyword\"},"
                + "\"log-id\": {\"type\": \"keyword\"},"
                + "\"source-ip\": {\"type\": \"keyword\"},"
                + "\"source-port\": {\"type\": \"keyword\"},"
                + "\"src-region-id\": {\"type\": \"keyword\"},"
                + "\"src-region-name\": {\"type\": \"keyword\"},"
                + "\"destination-ip\": {\"type\": \"keyword\"},"
                + "\"destination-port\": {\"type\": \"keyword\"},"
                + "\"dst-region-id\": {\"type\": \"keyword\"},"
                + "\"dst-region-name\": {\"type\": \"keyword\"},"
                + "\"protocol\": {\"type\": \"keyword\"},"
                + "\"kc_protocol\": {\"type\": \"keyword\"},"
                + "\"send-bytes\": {\"type\": \"keyword\"},"
                + "\"send-packets\": {\"type\": \"keyword\"},"
                + "\"receive-bytes\": {\"type\": \"keyword\"},"
                + "\"receive-packets\": {\"type\": \"keyword\"},"
                + "\"start-time\": {\"type\": \"keyword\"},"
                + "\"end-time\": {\"type\": \"keyword\"},"
                + "\"direction\": {\"type\": \"keyword\"},"
                + "\"dst-host\": {\"type\": \"keyword\"},"
                + "\"app\": {\"type\": \"keyword\"},"
                + "\"kc_app\": {\"type\": \"keyword\"},"
                + "\"source-zone\": {\"type\": \"keyword\"},"
                + "\"destination-zone\": {\"type\": \"keyword\"}"
                + "}"
                + "}";
        request.mapping(jsonMapping, XContentType.JSON);
        try {
            AcknowledgedResponse createIndexResponse =cfwEsUtils.getRestHighLevelClient().indices().create(request, RequestOptions.DEFAULT);
            System.out.println("Index created: " + createIndexResponse.isAcknowledged());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deleteIndex() {
        LogTopicEnum logTopicEnum = getIndexType();
        Integer saveDay = commonMessageConfig.getLogSaveDayMapCache().get(logTopicEnum.getTopic());
        if(Objects.isNull(saveDay)){
            log.info("流量日志索引不删除");
            return ;
        }

        Date date = DateUtils.getMinusDayStartTime(LocalDate.now(),saveDay);
        String indexFlow = EsUtils.getIndexByDay(LogTopicEnum.CFW_FLOW.getTopic(), date);
        log.info("删除流量日志索引：{}",indexFlow);
        try{
            cfwEsUtils.deleteIndex(indexFlow);
        }catch (Exception e){
            log.error("删除Flow索引错误："+e.getMessage());
        }
    }

    @Override
    public LogTopicEnum getIndexType() {
        return LogTopicEnum.CFW_FLOW;
    }
}
