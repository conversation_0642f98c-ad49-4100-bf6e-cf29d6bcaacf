package com.ksyun.cfwmessage.es.EsIndexService;

import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class KfwStatusIdexService implements IdexService{
    @Autowired
    private CfwEsUtils cfwEsUtils;
    @Override
    public void createIndex(String indexName) {
        CreateIndexRequest request = new CreateIndexRequest(indexName);

        // 设置索引的设置和映射
        request.settings(Settings.builder()
                .put("index.number_of_shards", 3)
                .put("index.number_of_replicas", 1)
                .put("index.max_result_window", 2000000000)
        );

        String jsonMapping = "{"
                + "\"properties\": {"
                + "\"fwId\": {\"type\": \"keyword\"},"
                + "\"fwInstanceId\": {\"type\": \"keyword\"},"
                + "\"timestamp\": {\"type\": \"keyword\"},"
                + "\"status\": {\"type\": \"keyword\"},"
                + "\"cpu\": {\"type\": \"text\"},"
                + "\"mem\": {\"type\": \"text\"},"
                + "\"disk\": {\"type\": \"text\"},"
                + "\"vfw_version\": {\"type\": \"text\"},"
                + "\"agent_version\": {\"type\": \"text\"},"
                + "\"ips_version\": {\"type\": \"text\"}"
                + "}"
                + "}";
        request.mapping(jsonMapping, XContentType.JSON);
        try {
            AcknowledgedResponse createIndexResponse =cfwEsUtils.getRestHighLevelClient().indices().create(request, RequestOptions.DEFAULT);
            System.out.println("Index created: " + createIndexResponse.isAcknowledged());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deleteIndex() {
    }

    @Override
    public LogTopicEnum getIndexType() {
        return LogTopicEnum.CFW_STATUS;
    }
}
