package com.ksyun.cfwmessage.es.EsIndexService;

import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.config.CommonMessageConfig;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.Objects;

@Service
@Slf4j
public class UploadStatusIdexService implements IdexService{
    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CommonMessageConfig commonMessageConfig;

    @Override
    public void createIndex(String indexName) {
        CreateIndexRequest request = new CreateIndexRequest(indexName);

        // 设置索引的设置和映射
        request.settings(Settings.builder()
                .put("index.number_of_shards", 5)
                .put("index.number_of_replicas", 1)
        );

        String jsonMapping = "{"
                + "\"properties\": {"
                + "\"etcdKey\": {\"type\": \"text\"},"
                + "\"fwId\": {\"type\": \"keyword\"},"
                + "\"fwInstanceId\": {\"type\": \"keyword\"},"
                + "\"traceId\": {\"type\": \"keyword\"},"
                + "\"operationId\": {\"type\": \"keyword\"},"
                + "\"timestamp\": {\"type\": \"keyword\"},"
                + "\"status\": {\"type\": \"keyword\"},"
                + "\"cpu\": {\"type\": \"text\"},"
                + "\"mem\": {\"type\": \"text\"},"
                + "\"disk\": {\"type\": \"text\"},"
                + "\"vfw_version\": {\"type\": \"text\"},"
                + "\"agent_version\": {\"type\": \"text\"},"
                + "\"ips_version\": {\"type\": \"text\"}"
                + "}"
                + "}";
        request.mapping(jsonMapping, XContentType.JSON);
        try {
            AcknowledgedResponse createIndexResponse =cfwEsUtils.getRestHighLevelClient().indices().create(request, RequestOptions.DEFAULT);
            System.out.println("Index created: " + createIndexResponse.isAcknowledged());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void deleteIndex() {
        LogTopicEnum logTopicEnum = getIndexType();
        Integer saveDay = commonMessageConfig.getLogSaveDayMapCache().get(logTopicEnum.getTopic());
        if(Objects.isNull(saveDay)){
            log.info("状态上报详情索引不删除");
            return ;
        }
        Date date = DateUtils.getMinusDayStartTime(LocalDate.now(), saveDay);
        String indexRisk = EsUtils.getIndexByDay(LogTopicEnum.CFW_STATUS_UPLOAD.getTopic(), date);
        log.info("删除状态上报详情索引：{}",indexRisk);
        try {
            cfwEsUtils.deleteIndex(indexRisk);
        } catch (Exception e) {
            log.error("删除状态上报详情索引：" + e.getMessage());
        }
    }

    @Override
    public LogTopicEnum getIndexType() {
        return LogTopicEnum.CFW_STATUS_UPLOAD;
    }
}
