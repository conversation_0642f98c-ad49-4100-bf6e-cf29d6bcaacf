package com.ksyun.cfwmessage.es.log;

import com.ksyun.cfwcore.constants.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * es高级客户端配置
 */
@Slf4j
@Configuration
public class CfwEsConfig {

    @Value("${cfw.elasticsearch.host}")
    private String host;
    @Value("${cfw.elasticsearch.port}")
    private int port;
    @Value("${cfw.elasticsearch.userName}")
    private String username;
    @Value("${cfw.elasticsearch.password}")
    private String password;

    @Bean("restHighLevelClient")
    public RestHighLevelClient buildDeviceRestHighLevelClient() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        List<String> hostArr = Arrays.asList(StringUtils.split(host, CommonConstant.COMMA));
        RestClientBuilder builder = RestClient.builder(constructHttpHosts(hostArr, port, "http"))
                //鉴权 + keepalive
                .setHttpClientConfigCallback(httpConfig -> httpConfig.setDefaultCredentialsProvider(credentialsProvider)
                        .setDefaultIOReactorConfig(IOReactorConfig.custom().setIoThreadCount(20).setSoKeepAlive(true).build())
                        .setKeepAliveStrategy((httpResponse, httpContext) -> TimeUnit.MINUTES.toSeconds(1)))
                //超时时间
                .setRequestConfigCallback(requestConfig -> requestConfig.setConnectTimeout(30 * 1000).setSocketTimeout(300 * 1000));
        return new RestHighLevelClient(builder);
    }

    /**
     * constructHttpHosts函数转换host集群节点ip列表。
     */
    public static HttpHost[] constructHttpHosts(List<String> host, int port, String protocol) {
        return host.stream().map(p -> new HttpHost(p, port, protocol)).toArray(HttpHost[]::new);
    }
}