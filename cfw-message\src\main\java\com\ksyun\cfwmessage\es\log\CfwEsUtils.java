package com.ksyun.cfwmessage.es.log;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.ksyun.cfwcore.constants.CommonConstant;
import com.ksyun.cfwcore.constants.KafkaLogConstants;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.config.StrategyConfig;
import com.ksyun.cfwmessage.es.EsIndexService.IdexService;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BackoffPolicy;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.common.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

@Component
@Slf4j
public class CfwEsUtils {
    @Qualifier("restHighLevelClient")
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private Map<String,BulkProcessor> bulkProcessorMap=new HashMap<>();

    @Autowired
    private JedisTemplate jedisTemplate;
    
    @Autowired
    @Lazy
    private StrategyConfig strategyConfig;

    public RestHighLevelClient getRestHighLevelClient() {
        return restHighLevelClient;
    }

    @PostConstruct
    public void init() {
        LogTopicEnum[] values = LogTopicEnum.values();
        for (LogTopicEnum value : values) {
            BulkProcessor bulkProcessor = createBulkProcessor();
            bulkProcessorMap.put(value.getTopic(),bulkProcessor);
        }
    }

    private BulkProcessor createBulkProcessor() {
        BulkProcessor.Listener listener = new BulkProcessor.Listener() {
            @Override
            public void beforeBulk(long executionId, BulkRequest request) {
                int numberOfActions = request.numberOfActions();
                log.info("Executing bulk [{}] with {} requests", executionId, numberOfActions);
            }

            @Override
            public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
                if (response.hasFailures()) {
                    log.error("Bulk [{}] executed with failures,response = {}", executionId,
                            response.buildFailureMessage());
                } else {
                    log.info("Bulk [{}] completed in {} milliseconds", executionId, response.getTook().getMillis());
                }
              /*  BulkItemResponse[] responses = response.getItems();*/
            }

            @Override
            public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
                log.error("Failed to execute bulk", failure);
            }
        };

        BiConsumer<BulkRequest, ActionListener<BulkResponse>> bulkConsumer =
                (request, bulkListener) -> restHighLevelClient.bulkAsync(request, RequestOptions.DEFAULT, bulkListener);

        return BulkProcessor.builder(bulkConsumer, listener)
                // 1000条数据请求执行一次bulk
                .setBulkActions(10000)
                // 10mb的数据刷新一次bulk
                .setBulkSize(new ByteSizeValue(10L, ByteSizeUnit.MB))
                // 并发请求数量, 0不并发
                .setConcurrentRequests(50)
                // 固定1s必须刷新一次
                .setFlushInterval(TimeValue.timeValueSeconds(1L))
                // 重试5次，间隔1s
                .setBackoffPolicy(BackoffPolicy.constantBackoff(TimeValue.timeValueSeconds(1L), 3)).build();
    }

    @PreDestroy
    public void destroy() {
        try {
            if(MapUtils.isNotEmpty(bulkProcessorMap)){
                for (BulkProcessor bulkProcessor:bulkProcessorMap.values()){
                    bulkProcessor.awaitClose(30, TimeUnit.SECONDS);
                }
            }
            restHighLevelClient.close();
        } catch (Exception e) {
            log.error("Failed to close restHighLevelClient && bulkProcessor", e);
        }
        log.info("restHighLevelClient && bulkProcessor closed!");
    }

    public boolean isExistsIndex(String indexName) {
        List<String> indexsList= jedisTemplate.getListCache(RedisConstants.ES_ALL_INDEX);
        if (CollectionUtil.isEmpty(indexsList)) {
            try {
                indexsList = new ArrayList<>();
                GetIndexRequest allIndex = new GetIndexRequest("*");
                GetIndexResponse response = restHighLevelClient.indices().get(allIndex, RequestOptions.DEFAULT);
                String[] indexArr = response.getIndices();
                if (indexArr != null) {
                    indexsList.addAll(Arrays.asList(indexArr));
                    jedisTemplate.setListCache(RedisConstants.ES_ALL_INDEX,indexArr);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return CollectionUtil.isNotEmpty(indexsList) && indexsList.contains(indexName);
    }

    private List<String> getAllIndex() {
        List<String> indexsList= jedisTemplate.getListCache(RedisConstants.ES_ALL_INDEX);
        if (CollectionUtil.isEmpty(indexsList)) {
            try {
                indexsList = new ArrayList<>();
                GetIndexRequest allIndex = new GetIndexRequest("*");
                GetIndexResponse response = restHighLevelClient.indices().get(allIndex, RequestOptions.DEFAULT);
                String[] indexArr = response.getIndices();
                if (indexArr != null) {
                    indexsList.addAll(Arrays.asList(indexArr));
                    jedisTemplate.setListCache(RedisConstants.ES_ALL_INDEX, indexArr);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return indexsList;
    }



    /**
     * 创建索引
     */
    public void createIndex(String indexName,String topic) {
        try {
            IdexService idexService= strategyConfig.getIdexService(topic);
            if(Objects.isNull(idexService)){
                throw new Exception("未找到topic对应的IdexService");
            }
            idexService.createIndex(indexName);
            GetIndexRequest allIndex = new GetIndexRequest("*");
            GetIndexResponse response = restHighLevelClient.indices().get(allIndex, RequestOptions.DEFAULT);
            String[] indexs = response.getIndices();
            jedisTemplate.setListCache(RedisConstants.ES_ALL_INDEX, indexs);
        } catch (Exception e) {
            log.error("createESIndex error:" + e.getMessage());
        }
    }

    /**
     * 批量添加数据
     *
     * @return
     */
    public void batchSaveDataMap(Map<String, List<Map<String, Object>>> mapList,String topic){
        if (CollectionUtil.isEmpty(mapList)) {
            return;
        }
        BulkProcessor bulkProcessor = bulkProcessorMap.get(topic);
        for (Map.Entry<String, List<Map<String, Object>>> entry : mapList.entrySet()) {
            String index = entry.getKey();
            // 判断索引是否存在否则创建
            if (!isExistsIndex(index)) {
                createIndex(index,topic);
            }
            if (CollectionUtil.isEmpty(entry.getValue())) {
                continue;
            }
            for (Map<String, Object> map : entry.getValue()) {
                if (Objects.nonNull(map.get(KafkaLogConstants.ID))) {
                    bulkProcessor.add(new IndexRequest(index).id((String) map.get(KafkaLogConstants.ID)).source(map, XContentType.JSON));
                } else {
                    bulkProcessor.add(new IndexRequest(index).source(map, XContentType.JSON));
                }
            }
        }
    }

    /**
     * 批量添加数据
     *
     * @return
     */
    public <T> void batchSaveDataObject(Map<String, List<T>> mapList,String topic) throws Exception {
        if (CollectionUtil.isEmpty(mapList)) {
            return;
        }
        BulkProcessor bulkProcessor = bulkProcessorMap.get(topic);
        for (Map.Entry<String, List<T>> entry : mapList.entrySet()) {
            String index = entry.getKey();
            // 判断索引是否存在否则创建
            if (!isExistsIndex(index)) {
                createIndex(index,topic);
            }
            if (CollectionUtil.isEmpty(entry.getValue())) {
                continue;
            }
            for (T entity : entry.getValue()) {
                bulkProcessor.add(new IndexRequest(index).source(JSONUtil.toJsonStr(entity), XContentType.JSON));
            }
        }
    }

    /**
     * 单个添加数据
     *
     * @return
     */
    public <T> void saveDataObject(String index, T data, String topic,String id) throws Exception {
        BulkProcessor bulkProcessor = bulkProcessorMap.get(topic);
        // 判断索引是否存在否则创建
        if (!isExistsIndex(index)) {
            createIndex(index,topic);
        }
        if (Objects.isNull(data)) {
            return;
        }

        if (StringUtils.isNotBlank(id)) {
            bulkProcessor.add(new IndexRequest(index).id(id).source(JSONUtil.toJsonStr(data), XContentType.JSON));
        } else {
            bulkProcessor.add(new IndexRequest(index).source(JSONUtil.toJsonStr(data), XContentType.JSON));
        }

    }

    public List<String> fetchLogIndex(String logType, Date dateFrom, Date dateTo) {
        //查所有index
        List<String> allIndexList = getAllIndex();

        //保留日期，时分秒清零
        dateFrom = DateUtils.setHours(DateUtils.setMinutes(DateUtils.setSeconds(DateUtils.setMilliseconds(dateFrom,
                0), 0), 0), 0);
        dateTo = DateUtils.setHours(DateUtils.setMinutes(DateUtils.setSeconds(DateUtils.setMilliseconds(dateTo,
                0), 0), 0), 0);
        List<String> reslutList = Lists.newArrayList();

        while (dateFrom.compareTo(dateTo) <= 0) {
            String currentIndex = String.join(CommonConstant.UNDERLINE, logType,
                    DateUtils.formatDate(dateFrom, DateUtils.DATE_FORMAT));
            if (allIndexList.contains(currentIndex)) {
                reslutList.add(currentIndex);
            }
            dateFrom = DateUtils.addDays(dateFrom, 1);
        }

        return reslutList;
    }

    public void deleteIndex(String index) {
        try {
           if(!isExistsIndex(index)) {
               return;
           }
            DeleteIndexRequest request = new DeleteIndexRequest(index);
            AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            System.out.println("Index deleted: " + deleteIndexResponse.isAcknowledged());
        } catch (IOException e) {
            e.printStackTrace();
        }
        jedisTemplate.delKey(RedisConstants.ES_ALL_INDEX);
    }
}
