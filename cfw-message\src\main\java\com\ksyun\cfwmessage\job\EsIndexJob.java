package com.ksyun.cfwmessage.job;

import com.ksyun.cfwmessage.config.StrategyConfig;
import com.ksyun.cfwmessage.es.EsIndexService.IdexService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;

@Slf4j
@Component
public class EsIndexJob {
    @Autowired
    @Lazy
    private StrategyConfig strategyConfig;

    @XxlJob("EsIndexJob")
    public void esIndexJob() {
        log.info("EsIndexJob start 索引定时任务开始执行，当前时间：" + new Date());
        // 获取当前时间的 Date 对象
        Map<String, IdexService> idexServiceMap = strategyConfig.getIdexServiceMap();
        for(IdexService entity:idexServiceMap.values()) {
            entity.deleteIndex();
        }
        log.info("EsIndexJob end 索引定时任务执行结束，当前时间：" + new Date());
    }
}
