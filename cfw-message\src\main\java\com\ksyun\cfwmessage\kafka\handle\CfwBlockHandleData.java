package com.ksyun.cfwmessage.kafka.handle;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.constants.KafkaLogConstants;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.DictionaryTypeEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwmessage.dao.service.CfwAclService;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwmessage.annotation.Handle;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwmessage.constants.LogConstants;
import com.ksyun.cfwmessage.dao.entity.CfwRsDO;
import com.ksyun.cfwmessage.dao.service.CfwDictionaryService;
import com.ksyun.cfwmessage.dao.service.CfwRsService;
import com.ksyun.cfwcore.utils.JSON;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Component
@Handle(name = "cfw-block")
@Slf4j
public class CfwBlockHandleData implements DataHanlder {
    @Autowired
    private RedissonTemplate redissonTemplate;

    @Autowired
    private JedisTemplate jedisTemplate;

    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwDictionaryService cfwDictionaryService;

    @Autowired
    private CfwAclService cfwAclService;
    @Override
    public void handle(List<Map<String, Object>> dataList) throws Exception {
        Map<String, List<Map<String, Object>>> mapList = new HashMap<>();
        Map<String,String> dirMap = cfwDictionaryService.queryDictionaryByType(DictionaryTypeEnum.Log.getType());
        for (Map<String, Object> data : dataList) {
            Long createTime = (Long) data.get(KafkaLogConstants.TIME);
            String rsId = (String) data.get(KafkaLogConstants.FW_INSTANCE_ID);
            Object body = data.get(KafkaLogConstants.BODY);
            if (Objects.isNull(body)) {
                log.error("数据错误body:{}", JSONUtil.toJsonStr(body));
                continue;
            }
            Map<String, Object> bodyMap = JSON.stringParseMap(body.toString());

            String aclId = (String) bodyMap.get(KafkaLogConstants.RULE_NAME);
            if (StringUtils.isBlank(aclId) || StringUtils.isBlank(rsId)) {
                log.error("数据错误rsId:{},body:{}", JSONUtil.toJsonStr(body), rsId);
                return;
            }

            if ("-".equals(aclId)) {
                log.info("“-”的acl过滤，rsId:{},body:{}", JSONUtil.toJsonStr(body), rsId);
                return;
            }
            //名称
            String aclName = this.getAclName(aclId);
            bodyMap.put(LogConstants.ACL_NAME, aclName);

            //协议
            String protocol = bodyMap.get(LogConstants.PROTOCOL).toString();
            if (StringUtils.isNotBlank(protocol) && protocol.contains(":")) {
                String[] protocols = protocol.split(":");
                bodyMap.put(LogConstants.KC_PROTOCOL, protocols[0]);
            } else {
                bodyMap.put(LogConstants.KC_PROTOCOL, protocol);
            }

            //动作
            String action = bodyMap.get(LogConstants.ACTION).toString();
            if (StringUtils.isNotBlank(action) && StringUtils.isNotBlank(dirMap.get(action))) {
                bodyMap.put(LogConstants.KC_ACTION, dirMap.get(action));
            } else {
                bodyMap.put(LogConstants.KC_ACTION, action);
            }

            //方向
            String direction = bodyMap.get(LogConstants.SOURCE_ZONE).toString();
            if(StringUtils.isNotBlank(direction)&&StringUtils.isNotBlank(dirMap.get(direction))){
                bodyMap.put(LogConstants.DIRECTION, dirMap.get(direction));
            }else{
                bodyMap.put(LogConstants.DIRECTION, direction);
            }

            //app
            String app = bodyMap.get(LogConstants.APP).toString();
            if (StringUtils.isNotBlank(app) && app.contains("-")) {
                String[] apps = app.split("-");
                bodyMap.put(LogConstants.KC_APP, apps[0]);
            } else {
                bodyMap.put(LogConstants.KC_APP, app);
            }

            bodyMap.put(KafkaLogConstants.TIME,createTime);
            bodyMap.put(KafkaLogConstants.FW_INSTANCE_ID,rsId);

            String index = EsUtils.getIndexByDay(LogTopicEnum.CFW_BLOCK.getTopic(), new Date(createTime));
            mapList.putIfAbsent(index, new ArrayList<>());
            mapList.get(index).add(bodyMap);
            //更新acl命中次数
            try {
                updateAclHitCount(rsId, aclId);
            } catch (Exception e) {
                log.error("updateAclHitCount:error:{}", e.getMessage());
            }
        }
        cfwEsUtils.batchSaveDataMap(mapList, LogTopicEnum.CFW_BLOCK.getTopic());
    }

    /**
     * 更新acl命中次数
     *
     */
    private void updateAclHitCount(String rsId,String aclId) {
        String fwId = getFwIdByRsId(rsId);
        if(StringUtils.isBlank(fwId)){
            log.error("查询FwId错误：rsId:{},aclId:{}", rsId,aclId);
            return;
        }
        String keyRedis = String.format(RedisConstants.ACL_HIT_COUNT, fwId);
        String lockKey =  String.format(RedisConstants.CFW_HIT_LOCK, fwId,aclId);
        RLock lock = redissonTemplate.getRedissonClient().getLock(lockKey);
        lock.lock(60, TimeUnit.SECONDS);
        try {
            String hitCountStr = jedisTemplate.getMapValue(keyRedis, aclId);
            Integer hitCount = 0;
            if (Objects.isNull(hitCountStr) || NumberUtils.isCreatable(hitCountStr)) {
                hitCount = NumberUtils.toInt(hitCountStr) + 1;
            }
            jedisTemplate.addOrReplaceMapValue(keyRedis, aclId, String.valueOf(hitCount));
        } catch (Exception e) {
            log.error("updateAclHitCount redis error:{}", e.getMessage());
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    private String getFwIdByRsId(String rsId) {
        String keyRedis = String.format(RedisConstants.FW_ID, rsId);
        String fwId = jedisTemplate.getKey(keyRedis);
        if (StringUtils.isBlank(fwId)) {
            CfwRsDO cfwRsDO = cfwRsService.selectByFwInstanceId(rsId);
            if (Objects.nonNull(cfwRsDO)) {
                fwId = cfwRsDO.getFwId();
                jedisTemplate.setExpireKey(keyRedis, fwId, 300);
            } else {
                fwId = "";
                log.error("查询FwId错误：rsId:{}", rsId);
            }
        }
        return fwId;
    }


    private String getAclName(String aclId) {
        String keyRedis = String.format(RedisConstants.ACL_NAME, aclId);
        String aclName = jedisTemplate.getKey(keyRedis);
        if (StringUtils.isBlank(aclName)) {
            String name = cfwAclService.getNameByAclId(aclId);
            if (StringUtils.isNotBlank(name)) {
                aclName = name;
                jedisTemplate.setExpireKey(keyRedis, aclName, 300);
            } else {
                aclName = aclId;
                log.error("查询AclName错误：{}", aclId);
            }
        }
        return aclName;
    }
}
