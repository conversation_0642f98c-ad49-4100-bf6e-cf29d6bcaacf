package com.ksyun.cfwmessage.kafka.handle;

import com.ksyun.cfwcore.constants.KafkaLogConstants;
import com.ksyun.cfwcore.enums.DictionaryTypeEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwmessage.annotation.Handle;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.utils.JSON;
import com.ksyun.cfwmessage.constants.LogConstants;
import com.ksyun.cfwmessage.dao.service.CfwDictionaryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Handle(name = "cfw-flow")
public class CfwFlowHandleData implements DataHanlder {
    @Autowired
    private CfwEsUtils cfwEsUtils;
    @Autowired
    private CfwDictionaryService cfwDictionaryService;

    @Override
    public void handle(List<Map<String, Object>> dataList) throws Exception {
        Map<String, List<Map<String, Object>>> mapList = new HashMap<>();
        Map<String,String> dirMap = cfwDictionaryService.queryDictionaryByType(DictionaryTypeEnum.Log.getType());
        for (Map<String, Object> data : dataList) {
            Long createTime = (Long) data.get(KafkaLogConstants.TIME);
            String rsId = (String) data.get(KafkaLogConstants.FW_INSTANCE_ID);
            Object body = data.get(KafkaLogConstants.BODY);

            if (Objects.isNull(body)) {
                continue;
            }

            Map<String, Object> bodyMap = JSON.stringParseMap(body.toString());
            //协议
            String protocol = bodyMap.get(LogConstants.PROTOCOL).toString();
            if (StringUtils.isNotBlank(protocol) && protocol.contains(":")) {
                String[] protocols = protocol.split(":");
                bodyMap.put(LogConstants.KC_PROTOCOL, protocols[0]);
            } else {
                bodyMap.put(LogConstants.KC_PROTOCOL, bodyMap.get(LogConstants.PROTOCOL).toString());
            }


            //方向
            String direction = bodyMap.get(LogConstants.SOURCE_ZONE).toString();
            if(StringUtils.isNotBlank(direction)&&StringUtils.isNotBlank(dirMap.get(direction))){
                bodyMap.put(LogConstants.DIRECTION, dirMap.get(direction));
            }else{
                bodyMap.put(LogConstants.DIRECTION, direction);
            }

            //app
            String app = bodyMap.get(LogConstants.APP).toString();
            if (StringUtils.isNotBlank(app) && app.contains("-")) {
                String[] apps = app.split("-");
                bodyMap.put(LogConstants.KC_APP, apps[0]);
            } else {
                bodyMap.put(LogConstants.KC_APP, app);
            }

            bodyMap.put(KafkaLogConstants.TIME,createTime);
            bodyMap.put(KafkaLogConstants.FW_INSTANCE_ID,rsId);

            String index = EsUtils.getIndexByDay(LogTopicEnum.CFW_FLOW.getTopic(), new Date(createTime));
            mapList.putIfAbsent(index, new ArrayList<>());
            mapList.get(index).add(bodyMap);
        }
        cfwEsUtils.batchSaveDataMap(mapList, LogTopicEnum.CFW_FLOW.getTopic());
    }

}
