package com.ksyun.cfwmessage.kafka.listener;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.List;


/**
 * <AUTHOR>
 */
@Component
public class CfwFlowListener extends LogBaseListener {

    @Override
    @KafkaListener(id = "CfwFlowListener", topics = {"cfw-flow"}, containerFactory = "batchFactory")
    public void handleMsg(List<ConsumerRecord<Integer, String>> recordList, Acknowledgment ack) throws Exception {
        super.handleMsg(recordList, ack);
    }
}
