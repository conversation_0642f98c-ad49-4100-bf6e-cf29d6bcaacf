package com.ksyun.cfwmessage.kafka.listener;

import com.google.common.collect.Maps;
import com.ksyun.cfwmessage.config.StrategyConfig;
import com.ksyun.cfwmessage.kafka.handle.DataHanlder;
import com.ksyun.cfwcore.utils.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.support.Acknowledgment;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class LogBaseListener {

    @Autowired
    @Lazy
    private StrategyConfig strategyConfig;


    /**
     * 消息处理
     *
     * @param recordList
     * @param ack
     */
    public void handleMsg(List<ConsumerRecord<Integer, String>> recordList, Acknowledgment ack) throws Exception {
        //写入es前的处理逻辑
        beforeDispatchToEs(recordList);

        //写入es
        dispatchToEs(recordList, ack);
    }

    /**
     * 消息写es前的处理逻辑
     *
     * @param recordList
     */
    protected void beforeDispatchToEs(List<ConsumerRecord<Integer, String>> recordList) {

    }

    /**
     * 将消息内容写入es
     *
     * @param recordList 消息记录
     * @param ack        kafka消息回执操作对象
     */
    public void dispatchToEs(List<ConsumerRecord<Integer, String>> recordList, Acknowledgment ack) throws Exception {
        try {
            log.info("拉取到消息条数->{}", recordList.size());
            long startTime = System.currentTimeMillis();
            // 按topic分组
            Map<String, List<Map<String, Object>>> dataMap = Maps.newHashMap();
            for (ConsumerRecord<Integer, String> record : recordList) {
                try {
                    String topic = record.topic();
                    Map<String, Object> recordMap = JSON.parse(record.value(), Map.class);
                    dataMap.putIfAbsent(topic, new ArrayList<>());
                    dataMap.get(topic).add(recordMap);
                } catch (Exception e) {
                    log.error("消息格式转换异常", e);
                }
            }
            // 数据写入ES
            writeToEs(dataMap);
            // kafka手动提交
            ack.acknowledge();
            long endTime = System.currentTimeMillis();
            log.info("消息处理完成，耗时->{}ms", endTime - startTime);
        } catch (Exception e) {
            log.error("消息处理异常:{}", e.getMessage());
            throw e;
        }
    }

    /**
     * 数据写入ES
     *
     * @param dataMap
     */
    private void writeToEs(Map<String, List<Map<String, Object>>> dataMap) throws Exception {
        for (Map.Entry<String, List<Map<String, Object>>> entry : dataMap.entrySet()) {
            String topic = entry.getKey();
            List<Map<String, Object>> dataList = entry.getValue();
            DataHanlder dataHanlder = strategyConfig.getDataHanlder(topic);
            if (Objects.isNull(dataHanlder)) {
                log.error("未找到对应topic的处理类->{}", topic);
                continue;
            }
            dataHanlder.handle(dataList);
        }
    }
}
