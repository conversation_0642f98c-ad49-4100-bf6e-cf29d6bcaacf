package com.ksyun.cfwmessage.service.etcd;

import cn.hutool.core.collection.CollectionUtil;
import com.ksyun.cfwmessage.config.StrategyConfig;
import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.Watch;
import io.etcd.jetcd.options.WatchOption;
import io.etcd.jetcd.watch.WatchEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EtcdListenInit {
    @Autowired
    @Lazy
    private StrategyConfig strategyConfig;

    @Autowired
    private Client etcdClient;
    
    private List<Watch.Watcher> watchList = new ArrayList<>();

    @PostConstruct
    private void init() {
        // 创建Watcher
        Watch watchClient = etcdClient.getWatchClient();
        Map<String, EtcdListenService> etcdListenServiceMap =  strategyConfig.getEtcdListenServiceMap();
        etcdListenServiceMap.forEach((listenKey,service)->{
            // 添加监听路径
            ByteSequence keyPrefix = ByteSequence.from(listenKey, Charset.defaultCharset());
            WatchOption options = WatchOption.newBuilder().withPrefix(keyPrefix).build();
            // 监听器
            Watch.Listener listener = Watch.listener(watchResponse -> {
                for (WatchEvent event : watchResponse.getEvents()) {// 事件
                    String eventType = String.valueOf(event.getEventType());
                    String key = String.valueOf(event.getKeyValue().getKey().toString(StandardCharsets.UTF_8));
                    String value = String.valueOf(event.getKeyValue().getValue().toString(StandardCharsets.UTF_8));
                    log.info("key:{},valve:{},事件类型：{}", key, value, eventType);
                    if (WatchEvent.EventType.PUT.equals(event.getEventType())) {
                        try {
                            service.handle(key, value);
                        } catch (Exception ex) {
                            log.error("监听事件异常:", ex);
                        }
                    }
                }
            });
            // 监听key, 有变化时触发监听器
            Watch.Watcher watch= watchClient.watch(keyPrefix, options, listener);
            watchList.add(watch);
        });
    }

    @PreDestroy
    public void destroy() {
        if (CollectionUtil.isNotEmpty(watchList)) {
            for (Watch.Watcher watch : watchList) {
                watch.close();
            }
        }
        etcdClient.close();
        log.info("etcd&&watch close...");
    }
}
