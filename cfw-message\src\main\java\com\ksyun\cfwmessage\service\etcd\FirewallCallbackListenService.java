package com.ksyun.cfwmessage.service.etcd;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ksyun.cfwcore.alert.AlertTemplate;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmDeal;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmPriority;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.enums.EtcdListenEnum;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.domain.FirewallEtcdCallback;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FirewallCallbackListenService implements EtcdListenService{
    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private AlertTemplate alertTemplate;

    @Override
    public void handle(String key, String value) {
        log.info("处理开始,key:{},valve:{}", key, value);
        String[] keyArr = key.split("/");
        if (keyArr.length < 3) {
            log.error("key:{},valve:{},路径不正确", key, value);
        }
        String fwInstanceId = keyArr[keyArr.length - 1];
        String fwId = keyArr[keyArr.length - 2];
        FirewallEtcdCallback firewallEtcdCallbackData = JSON.parseObject(value, FirewallEtcdCallback.class);
        if (Objects.isNull(firewallEtcdCallbackData)) {
            log.error("解析失败,key:{},valve:{}", key, value);
        }
        firewallEtcdCallbackData.setEtcdKey(key);
        firewallEtcdCallbackData.setFwId(fwId);
        firewallEtcdCallbackData.setFwInstanceId(fwInstanceId);
        //保存ES
        try {
            String timestamp = firewallEtcdCallbackData.getTimestamp();
            if(StringUtils.isBlank(timestamp)){
                timestamp =  DateUtils.getDate(new Date(), DateUtils.DATETIME_FORMAT);
            }
            String index = EsUtils.getIndexByMonth(LogTopicEnum.CFW_ETCD_CALLBACK.getTopic(), timestamp);
            cfwEsUtils.saveDataObject(index,firewallEtcdCallbackData, LogTopicEnum.CFW_ETCD_CALLBACK.getTopic(),"");
            //失败操作告警
            if("fail".equals(firewallEtcdCallbackData.getResult())){
                OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
                onePieceAlarm.setName(Constants.OPEN_API_SERVICE_NAME);
                onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
                onePieceAlarm.setProduct(commonConfig.getAlertCode());
                onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
                onePieceAlarm.setContent("【镜像操作失败】，fwId：" + firewallEtcdCallbackData.getFwId());
                onePieceAlarm.setHtml_content("【镜像操作失败】，fwId：" + firewallEtcdCallbackData.getFwId() +"，FirewallEtcdCallback:" + "[" + JSONUtil.toJsonStr(firewallEtcdCallbackData) + "]");
                alertTemplate.send(onePieceAlarm);
            }
        } catch (Exception e) {
            log.error("监听事件保存es异常:{},key:{},valve:{}", e, key, value);
            return ;
        }
        log.info("处理结束,key:{},valve:{}", key, value);
    }

    @Override
    public List<EtcdListenEnum> getListenKey() {
        return Arrays.asList(EtcdListenEnum.FIREWALL_CLUSTER_CALLBACK,EtcdListenEnum.FIREWALL_COMMAND_CALLBACK);
    }
}
