package com.ksyun.cfwmessage.service.etcd;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.*;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwcore.monitor.api.domain.MonitorCreateParam;
import com.ksyun.cfwcore.monitor.enums.MonitorProductTypeEnum;
import com.ksyun.cfwcore.rabbitmq.SyncMessageSendService;
import com.ksyun.cfwcore.trade.wapper.FwNotifyService;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.dao.entity.CfwInstanceDO;
import com.ksyun.cfwmessage.dao.entity.CfwRsDO;
import com.ksyun.cfwmessage.dao.service.CfwInstanceService;
import com.ksyun.cfwmessage.dao.service.CfwRsService;
import com.ksyun.cfwmessage.domain.FirewallEtcdCallback;
import com.ksyun.cfwmessage.enums.ResultTypeEnum;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import com.ksyun.common.proxy.ProxyAuth;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FirewallStartListenService implements EtcdListenService {

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private RedissonTemplate redissonTemplate;

    @Autowired
    private SyncMessageSendService syncMessageSendService;

    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private FwNotifyService fwNotifyService;

    @Override
    public void handle(String key, String value) {
        log.info("key:{},valve:{},处理开始", key, value);
        String[] keyArr = key.split("/");
        if (keyArr.length < 3) {
            log.error("key:{},valve:{},路径不正确", key, value);
            return;
        }
        String fwInstanceId = keyArr[keyArr.length - 1];
        String fwId = keyArr[keyArr.length - 2];
        String lockkey = String.format(RedisConstants.CFW_FW_LOCK,fwId);
        RLock lock = redissonTemplate.getRedissonClient().getLock(lockkey);
        lock.lock(60, TimeUnit.SECONDS);
        FirewallEtcdCallback firewallEtcdCallbackData;
        try {
            firewallEtcdCallbackData = JSON.parseObject(value, FirewallEtcdCallback.class);
            if (Objects.isNull(firewallEtcdCallbackData)) {
                log.error("key:{},valve:{},解析失败", key, value);
                return;
            }

            firewallEtcdCallbackData.setEtcdKey(key);
            firewallEtcdCallbackData.setFwId(fwId);
            firewallEtcdCallbackData.setFwInstanceId(fwInstanceId);

            //保存ES
            try {
                String timestamp = firewallEtcdCallbackData.getTimestamp();
                if (StringUtils.isBlank(timestamp)) {
                    timestamp = DateUtils.getDate(new Date(), DateUtils.DATETIME_FORMAT);
                }
                String index = EsUtils.getIndexByMonth(LogTopicEnum.CFW_ETCD_CALLBACK.getTopic(), timestamp);
                cfwEsUtils.saveDataObject(index, firewallEtcdCallbackData, LogTopicEnum.CFW_ETCD_CALLBACK.getTopic(),"");
            } catch (Exception e) {
                log.error("监听事件保存es异常:", e);
            }

            //处理状态
            int rsStatus = ResultTypeEnum.SUCCESS.getCode().equals(firewallEtcdCallbackData.getResult()) ? FirewallRsStatusEnum.NORMAL.getStatus() : FirewallRsStatusEnum.ABNORMAL.getStatus();
            cfwRsService.updateRsStatus(fwInstanceId, rsStatus);
            List<CfwRsDO> cfwRsDOList = cfwRsService.selectByFwId(fwId);
            if(CollectionUtil.isEmpty(cfwRsDOList)){
                log.error("防火墙的查询节点不存在（或已删除）：instanceId：{},fwId：{}，key:{},valve:{}", fwInstanceId, fwId, key, value);
                return;
            }
            long countNormal = cfwRsDOList.stream().filter(cfwRs -> FirewallRsStatusEnum.NORMAL.getStatus().equals(cfwRs.getRsStatus())).count();
            long countAbnormal = cfwRsDOList.stream().filter(cfwRs -> FirewallRsStatusEnum.ABNORMAL.getStatus().equals(cfwRs.getRsStatus())).count();
            CfwInstanceDO fwInfo = cfwInstanceService.selectByFwId(fwId);
            if (Objects.isNull(fwInfo)) {
                log.error("防火墙不存在：instanceId：{},fwId：{}，key:{},valve:{}", fwInstanceId, fwId, key, value);
                return;
            }

            if (countAbnormal!=0L) {
                if(!FirewallStatusEnum.ERROR.getStatus().equals(fwInfo.getStatus())){
                    cfwInstanceService.updateFwStatus(fwId, FirewallStatusEnum.ERROR.getStatus());
                    log.info("fwId:{}修改成异常",fwId);
                }
                ProxyAuth auth = new ProxyAuth(firewallEtcdCallbackData.getTraceId(),fwInfo.getAccountId(),fwInfo.getRegion());
                fwNotifyService.notifySubOrder(fwInfo.getSubOrderId(),NotifySubOrderResult.FAIL.getValue(),fwInfo.getFwId(), auth,"实例启动失败");
            }
            if ((countNormal == cfwRsDOList.size())) {
                if(!FirewallStatusEnum.RUNNING.getStatus().equals(fwInfo.getStatus())){
                    cfwInstanceService.updateFwStatus(fwId, FirewallStatusEnum.RUNNING.getStatus());
                    log.info("fwId:{}修改成运行中",fwId);
                }
                ProxyAuth auth = new ProxyAuth(firewallEtcdCallbackData.getTraceId(),fwInfo.getAccountId(),fwInfo.getRegion());
                fwNotifyService.notifySubOrder(fwInfo.getSubOrderId(),NotifySubOrderResult.SUCCESS.getValue(),fwInfo.getFwId(), auth,"");

                //云监控注册实例
                MonitorCreateParam monitorCreateParam = MonitorCreateParam.builder()
                        .productType(MonitorProductTypeEnum.KFW.getType())
                        .regionKey(fwInfo.getRegion())
                        .hostName(fwInfo.getName())
                        .instanceId(fwInfo.getFwLbId())
                        .guestIp(MonitorProductTypeEnum.KFW.name())
                        .build();
                syncMessageSendService.notifyMonitorCreate(auth, monitorCreateParam);
            }
            log.info("处理完成,key:{},valve:{}", key, value);

        } catch (Exception ex) {
            log.error("Etcd callback 处理异常,key:{},valve:{}", fwInstanceId, value, ex);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    @Override
    public List<EtcdListenEnum> getListenKey() {
        return Collections.singletonList(EtcdListenEnum.FIREWALL_START_CALLBACK);
    }


}
