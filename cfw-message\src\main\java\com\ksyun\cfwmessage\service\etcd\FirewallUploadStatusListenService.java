package com.ksyun.cfwmessage.service.etcd;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.ksyun.cfwcore.alert.AlertTemplate;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmDeal;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmPriority;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwcore.enums.EtcdListenEnum;
import com.ksyun.cfwcore.enums.FirewallRsStatusEnum;
import com.ksyun.cfwcore.enums.FirewallStatusEnum;
import com.ksyun.cfwcore.enums.LogTopicEnum;
import com.ksyun.cfwcore.es.EsUtils;
import com.ksyun.cfwcore.etcd.EtcdService;
import com.ksyun.cfwmessage.domain.FirewallStatusDO;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwcore.utils.DateUtils;
import com.ksyun.cfwmessage.dao.entity.CfwInstanceDO;
import com.ksyun.cfwmessage.dao.entity.CfwRsDO;
import com.ksyun.cfwmessage.dao.service.CfwInstanceService;
import com.ksyun.cfwmessage.dao.service.CfwRsService;
import com.ksyun.cfwmessage.domain.FirewallUploadStatusEtcd;
import com.ksyun.cfwmessage.enums.InstanceStatusEnum;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.comm.cache.redisson.core.RedissonTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FirewallUploadStatusListenService implements EtcdListenService {

    @Autowired
    private RedissonTemplate redissonTemplate;

    @Autowired
    private CfwRsService cfwRsService;

    @Autowired
    private CfwInstanceService cfwInstanceService;

    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private JedisTemplate jedisTemplate;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private AlertTemplate alertTemplate;

    @Autowired
    private EtcdService etcdService;

    @Override
    public void handle(String key, String value) {
        log.info("key:{},valve:{},处理开始", key, value);
        String[] keyArr = key.split("/");
        if (keyArr.length < 5) {
            log.error("key:{},valve:{},路径不正确", key, value);
        }
        String fwInstanceId = keyArr[keyArr.length - 1];
        String fwId = keyArr[keyArr.length - 3];

        String lockkey = String.format(RedisConstants.CFW_FW_LOCK,fwId);
        RLock lock = redissonTemplate.getRedissonClient().getLock(lockkey);
        lock.lock(60, TimeUnit.SECONDS);
        FirewallUploadStatusEtcd firewallUploadStatusEtcd;
        try {
            firewallUploadStatusEtcd = JSON.parseObject(value, FirewallUploadStatusEtcd.class);
            if (Objects.isNull(firewallUploadStatusEtcd)) {
                log.error("key:{},valve:{},解析失败", key, value);
                return;
            }

            firewallUploadStatusEtcd.setEtcdKey(key);
            firewallUploadStatusEtcd.setFwId(fwId);
            firewallUploadStatusEtcd.setFwInstanceId(fwInstanceId);

            int rsStatus = InstanceStatusEnum.ABNORMAL.getCode().equals(firewallUploadStatusEtcd.getStatus()) ? FirewallRsStatusEnum.ABNORMAL.getStatus() : FirewallRsStatusEnum.NORMAL.getStatus();
            //保存ES
            try {
                String timestamp = firewallUploadStatusEtcd.getTimestamp();
                if (StringUtils.isBlank(timestamp)) {
                    timestamp = DateUtils.getDate(new Date(), DateUtils.DATETIME_FORMAT);
                    firewallUploadStatusEtcd.setTimestamp(timestamp);
                }
                String index = EsUtils.getIndexByDay(LogTopicEnum.CFW_STATUS_UPLOAD.getTopic(), new Date());
                cfwEsUtils.saveDataObject(index, firewallUploadStatusEtcd, LogTopicEnum.CFW_STATUS_UPLOAD.getTopic(),"");

                FirewallStatusDO firewallStatusDO = new FirewallStatusDO();
                BeanUtils.copyProperties(firewallUploadStatusEtcd,firewallStatusDO);
                firewallStatusDO.setStatus(rsStatus);
                cfwEsUtils.saveDataObject(LogTopicEnum.CFW_STATUS.getTopic(),firewallStatusDO,LogTopicEnum.CFW_STATUS.getTopic(),firewallStatusDO.getFwInstanceId());
            } catch (Exception e) {
                log.error("监听事件保存es异常,error:{}:,key:{},value:{}", e.getMessage(),key,value);
            }

            //五分钟上报一次
            String fwInstanceIdKey = String.format(RedisConstants.CFW_RS_UPLOAD, fwInstanceId);
            String statusRedis = jedisTemplate.getKey(fwInstanceIdKey);
            if (StringUtils.isNotBlank(statusRedis) && statusRedis.equals(String.valueOf(rsStatus))) {
                log.info("redis缓存五分钟，间隔五分钟再处理,key:{},valve:{}", key, value);
                return;
            }
            jedisTemplate.setExpireKey(fwInstanceIdKey,String.valueOf(rsStatus),5 * 60);
            cfwRsService.updateRsStatus(fwInstanceId, rsStatus);
            List<CfwRsDO> cfwRsDOList = cfwRsService.selectByFwId(fwId);
            if(CollectionUtil.isEmpty(cfwRsDOList)){
                log.error("防火墙的查询节点不存在（或已删除）：instanceId：{},fwId：{}，key:{},valve:{}", fwInstanceId, fwId, key, value);
                return;
            }
            long countAbnormal = cfwRsDOList.stream().filter(cfwRs -> FirewallRsStatusEnum.ABNORMAL.getStatus().equals(cfwRs.getRsStatus())).count();
            long countNormal = cfwRsDOList.stream().filter(cfwRs -> FirewallRsStatusEnum.NORMAL.getStatus().equals(cfwRs.getRsStatus())).count();
            CfwInstanceDO fwInfo = cfwInstanceService.selectByFwId(fwId);
            if (Objects.isNull(fwInfo)) {
                log.error("防火墙不存在：instanceId：{},fwId：{}", fwInstanceId, fwId);
            }
            if (countAbnormal != 0L && FirewallStatusEnum.RUNNING.getStatus().equals(fwInfo.getStatus())) {
                cfwInstanceService.updateFwStatus(fwId, FirewallStatusEnum.ERROR.getStatus());
                try{
                    OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
                    onePieceAlarm.setName(Constants.OPEN_API_SERVICE_NAME);
                    onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
                    onePieceAlarm.setProduct(commonConfig.getAlertCode());
                    onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
                    onePieceAlarm.setContent("【防火墙状态异常】，fwId：" + fwId);
                    onePieceAlarm.setHtml_content("【防火墙状态异常】，fwId：" + fwId + "，fwInstanceId:" + "[" + fwInstanceId + "]");
                    alertTemplate.send(onePieceAlarm);
                }catch (Exception e){
                    log.error("防火墙告警异常，有防火墙状态异常：fwId:{},fwInstanceId:{}",fwId,fwInstanceId);
                }
            }
            if ((countNormal == cfwRsDOList.size()) && FirewallStatusEnum.ERROR.getStatus().equals(fwInfo.getStatus())) {
                cfwInstanceService.updateFwStatus(fwId, FirewallStatusEnum.RUNNING.getStatus());
                try{
                    OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
                    onePieceAlarm.setName(Constants.OPEN_API_SERVICE_NAME);
                    onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
                    onePieceAlarm.setProduct(commonConfig.getAlertCode());
                    onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
                    onePieceAlarm.setContent("【防火墙状态由异常变成正常】，fwId：" + fwId);
                    onePieceAlarm.setHtml_content("【防火墙状态由异常变成正常】，fwId：" + fwId + "，fwInstanceId:" + "[" + fwInstanceId + "]");
                    alertTemplate.send(onePieceAlarm);
                }catch (Exception e){
                    log.error("防火墙告警异常，防火墙状态由异常变成正常：fwId:{},fwInstanceId:{}",fwId,fwInstanceId);
                }
            }
            log.info("处理完成,key:{},valve:{}", key, value);
            etcdService.deleteSinglePrefix(key);
            log.info("删除etcd成功,key:{}", key);
        } catch (Exception ex) {
            log.error("Etcd callback 处理异常,key:{},valve:{},{}", fwInstanceId, value, ex);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.forceUnlock();
            }
        }
    }

    @Override
    public List<EtcdListenEnum> getListenKey() {
        return Collections.singletonList(EtcdListenEnum.FIREWALL_UPLOAD_STATUS);
    }
}
