package com.ksyun.cfwmessage.service.kfw;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.ksyun.cfwcore.enums.ProductGroup;
import com.ksyun.cfwcore.enums.ProductType;
import com.ksyun.cfwmessage.dao.entity.CfwProjectDO;
import com.ksyun.cfwmessage.dao.service.CfwInstanceService;
import com.ksyun.cfwmessage.domain.iam.ProjectChangeMessage;
import com.ksyun.cfwmessage.domain.iam.ProjectMqData;
import com.ksyun.cfwmessage.domain.iam.TradeProjectMesssageParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class FwService {

    @Autowired
    private CfwInstanceService cfwInstanceService;

    public void projectchange(ProjectMqData param) {
        if (Objects.isNull(param)||StringUtils.isBlank(param.getData())) {
            return;
        }
        //字符串转换对象
        Gson gson = new Gson();
        Type typeProjectChangeMessage = new TypeToken<ProjectChangeMessage>(){}.getType();
        ProjectChangeMessage projectChangeMessage = gson.fromJson(param.getData(), typeProjectChangeMessage);
        if(Objects.isNull(projectChangeMessage)||StringUtils.isBlank(projectChangeMessage.getMsg())){
            return ;
        }


        Type typeTradeProjectMesssageParam = new TypeToken<TradeProjectMesssageParam>(){}.getType();
        TradeProjectMesssageParam tradeProjectMesssageParam = gson.fromJson( projectChangeMessage.getMsg(), typeTradeProjectMesssageParam);
        if(Objects.isNull(tradeProjectMesssageParam)){
            return ;
        }

        List<TradeProjectMesssageParam.Instance> instanceList = tradeProjectMesssageParam.getInstanceList();
        if (CollectionUtils.isEmpty(instanceList)) {
            return;
        }
        List<CfwProjectDO> cfwProjectDOList = new ArrayList<>();
        for(TradeProjectMesssageParam.Instance instance:instanceList){
            if(ProductType.KFW.getValue()==instance.getProductType()|| ProductGroup.KFW.getValue() == instance.getProductGroup()||"KFW".equals(instance.getProductLine())){
                CfwProjectDO cfwProjectDO = new CfwProjectDO().setProjectId(String.valueOf(instance.getIamProjectId())).setFwId(instance.getInstanceId());
                cfwProjectDOList.add(cfwProjectDO);
            }
        }
        if(CollectionUtils.isNotEmpty(cfwProjectDOList)){
            cfwInstanceService.batchUpdateProjectId(cfwProjectDOList);
        }
    }
}
