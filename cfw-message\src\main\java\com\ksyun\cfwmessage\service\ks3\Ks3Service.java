package com.ksyun.cfwmessage.service.ks3;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.config.Ks3Config;
import com.ksyun.cfwcore.domain.ks3.DownFileUrlResponse;
import com.ksyun.cfwcore.domain.ks3.FileParam;
import com.ksyun.cfwmessage.dao.entity.CfwRegionConfigDO;
import com.ksyun.cfwmessage.dao.entity.CfwRsDO;
import com.ksyun.cfwmessage.dao.service.CfwRegionConfigService;
import com.ksyun.cfwmessage.dao.service.CfwRsService;
import com.ksyun.ks3.http.HttpClientConfig;
import com.ksyun.ks3.service.Ks3;
import com.ksyun.ks3.service.Ks3Client;
import com.ksyun.ks3.service.Ks3ClientConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class Ks3Service {
    @Autowired
    private Ks3Config ks3Config;

    @Autowired
    private CfwRegionConfigService  regionConfigService;

    @Autowired
    private CfwRsService cfwRsService;

    public DownFileUrlResponse getDownFileUrl(FileParam param) {
        CfwRsDO cfwRsDO = cfwRsService.selectByFwInstanceId(param.getHostName());
        if(Objects.isNull(cfwRsDO)){
            throw new RuntimeException("主机名查询错误");
        }
        CfwRegionConfigDO regionConfigDO = regionConfigService.queryRegionConfig(cfwRsDO.getRegion());
        if(Objects.isNull(regionConfigDO)){
            throw new RuntimeException("region配置查询错误");
        }
        Ks3 client = getKs3(regionConfigDO.getKs3Endpoint());
        String bucketName = regionConfigDO.getKs3Bucket();
        String url = client.generatePresignedUrl(bucketName, param.getFileName(), 1800);
        log.info("参数：{}，文件下载地址：{}", JSONUtil.toJsonStr(param),url);
        return new DownFileUrlResponse().setUrl(url);
    }

    /**
     * 获取ks3客户端
     *
     * @return
     */
    private Ks3 getKs3(String endpoint) {
        String accessKeyId = ks3Config.getKs3Ak();
        String accessKeySecret = ks3Config.getKs3Sk();
        Ks3ClientConfig config = new Ks3ClientConfig();
        config.setEndpoint(endpoint);
        config.setPathStyleAccess(false);
        HttpClientConfig hconfig = new HttpClientConfig();
        config.setHttpClientConfig(hconfig);
        Ks3 client = new Ks3Client(accessKeyId, accessKeySecret, config);
        return client;
    }

}
