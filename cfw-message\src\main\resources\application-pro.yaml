net-commons:
  base:
    apollo:
      refresh:
        enable: true
        namespaces: pro
        packages: cfw-api
  cache:
    redis:
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      data-base: ${redis.database}
      jedis:
        enable: true
      redisson:
        enable: true
        retry-attempts: 10
        retry-interval: 100
        connection-pool-size: 128
        connection-minimum-idle-size: 54
  platform:
    trade:
      enable: true
      product-url: ${trade.product.url}
      instance-url: ${trade.instance.url}
      order-url: ${trade.order.url}

