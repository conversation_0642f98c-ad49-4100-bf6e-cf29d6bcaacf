<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ksyun.cfwmessage.dao.mapper.CfwInstanceMapper">
    <update id="batchUpdateProjectId" parameterType="list">
		update cfw_instance
		<trim prefix="set" suffixOverrides=",">
			<trim prefix="project_id =case" suffix="end,">
				<foreach collection="list" item="item" index="index">
					when fw_id = #{item.fwId,jdbcType=VARCHAR} then #{item.projectId,jdbcType=VARCHAR}
				</foreach>
			</trim>
		</trim>
		, update_time = NOW()
		where fw_id in
		<foreach collection="list" item="item" index="index" separator="," open="(" close=")">
			#{item.fwId,jdbcType=VARCHAR}
		</foreach>
	</update>
</mapper>