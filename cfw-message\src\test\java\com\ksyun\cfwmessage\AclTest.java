package com.ksyun.cfwmessage;

import com.ksyun.cfwcore.utils.JSON;
import com.ksyun.cfwmessage.kafka.handle.CfwBlockHandleData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Map;

@SpringBootTest(classes = CfwMessageApplication.class)
public class AclTest {

    @Autowired
    private CfwBlockHandleData cfwBlockHandleData;
    @Test
    void kafkatest() throws Exception {
        String cfwblock = "{\n" +"\"time\":1742886130070,\n" +"\"fw-instance-id\":\"9e325445-20b2-44b7-b815-0b68ea57c577\",\n" +"\"vsys\":\"\",\n" +"\"body\":\"log-id=4424364d, source-ip=**************, source-port=60000, src-region-id=DE, src-region-name=Germany, destination-ip=*************, destination-port=29065, dst-region-id=CN, dst-region-name=Chinese Mainland, dst-host=-, protocol=TCP, app=TCP-ANY, rule-name=测试, action=policy deny, source-zone=3001, destination-zone=3002\\nreason=start/policy rematch,\\nstart-time=1742800073795\"\n" + "}";
        Map<String, Object> recordMap = JSON.parse(cfwblock, Map.class);
        cfwBlockHandleData.handle(Arrays.asList(recordMap));
    }
}
