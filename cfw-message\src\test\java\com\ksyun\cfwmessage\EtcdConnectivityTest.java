package com.ksyun.cfwmessage;

import cn.hutool.json.JSONUtil;
import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.kv.GetResponse;
import io.etcd.jetcd.kv.PutResponse;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;


@Slf4j
public class EtcdConnectivityTest {
    public static void main(String[] args) throws Exception{
        /*String username = "root";
        String password = "123456";*/
        Client client = Client.builder().endpoints("http://10.100.42.237:81").build();
        CompletableFuture<PutResponse> result=putValue(client,"mykey", "value2322");
        CompletableFuture<String> value = getValue(client,"mykey");
        System.out.println("返回结果"+":"+ JSONUtil.toJsonStr(value));
    }

    public static CompletableFuture<PutResponse> putValue(Client etcdClient,String key, String value) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        ByteSequence valueByte = ByteSequence.from(value, StandardCharsets.UTF_8);
        return etcdClient.getKVClient().put(keyByte, valueByte);
    }

    public static CompletableFuture<String> getValue(Client etcdClient,String key) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        CompletableFuture<GetResponse> getFuture = etcdClient.getKVClient().get(keyByte);
        return getFuture.thenApply(getResponse -> {
            if (getResponse.getKvs().isEmpty()) {
                return null;
            }
            log.info("*****mykey******{}",getResponse.getKvs().get(0).getValue().toString(StandardCharsets.UTF_8));
            return getResponse.getKvs().get(0).getValue().toString(StandardCharsets.UTF_8);
        });
    }

    public static CompletableFuture<Void> deleteValue(Client etcdClient,String key) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        return etcdClient.getKVClient().delete(keyByte).thenAccept(deleteResponse -> {});
    }

}