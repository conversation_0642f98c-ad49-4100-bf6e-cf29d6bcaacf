package com.ksyun.cfwmessage;

import io.etcd.jetcd.ByteSequence;
import io.etcd.jetcd.Client;
import io.etcd.jetcd.Watch;
import io.etcd.jetcd.kv.PutResponse;
import io.etcd.jetcd.watch.WatchEvent;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class EtcdWatchExample {
    public static void main(String[] args) throws Exception {
        // 创建 etcd 客户端

        String username = "root";
        String password = "123456";
        Client client = Client.builder().endpoints("http://10.100.42.237:2379").user(ByteSequence.from(username, StandardCharsets.UTF_8))
                .password(ByteSequence.from(password, StandardCharsets.UTF_8)).build();

        // 创建Watcher
        Watch watchClient = client.getWatchClient();

        String newKey = "mykey";
        // 创建 Watcher
        ByteSequence keyPrefix = ByteSequence.from(newKey, Charset.defaultCharset());
       // 监听器
        Watch.Listener listener = Watch.listener(watchResponse -> {
            log.info("newKey,事件类型");
            System.out.println("newKey,事件类型");
            for (WatchEvent event : watchResponse.getEvents()) {// 事件
                String eventType = String.valueOf(event.getEventType());
                String key = String.valueOf(event.getKeyValue().getKey().toString(StandardCharsets.UTF_8));
                String value = String.valueOf(event.getKeyValue().getValue().toString(StandardCharsets.UTF_8));
                log.info("newKey,事件类型{},{},{}", key, value, eventType);
                if (WatchEvent.EventType.PUT.equals(event.getEventType())) {
                    log.info("newKey,事件类型{},{}", key, value);
                    //listenEtcd(key, value);
                }
            }
        });
        // 监听key, 有变化时触发监听器
        watchClient.watch(keyPrefix, listener);
        putValue(client,newKey,"55");
        Thread.sleep(100000);
        log.info("监听结束");
    }

    public static CompletableFuture<PutResponse> putValue(Client etcdClient, String key, String value) {
        ByteSequence keyByte = ByteSequence.from(key, StandardCharsets.UTF_8);
        ByteSequence valueByte = ByteSequence.from(value, StandardCharsets.UTF_8);
        return etcdClient.getKVClient().put(keyByte, valueByte);
    }

}