package com.ksyun.cfwmessage;

import com.ksyun.cfwcore.constants.RedisConstants;
import com.ksyun.cfwmessage.es.EsIndexService.FlowIdexService;
import com.ksyun.cfwmessage.es.log.CfwEsUtils;
import com.ksyun.cfwmessage.kafka.handle.CfwBlockHandleData;
import com.ksyun.cfwmessage.kafka.handle.CfwFlowHandleData;
import com.ksyun.cfwmessage.kafka.handle.CfwRiskHandleData;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@Slf4j
@SpringBootTest(classes = CfwMessageApplication.class)
public class FlowTest {
    @Autowired
    private CfwFlowHandleData flowHandleData;

    @Autowired
    private CfwBlockHandleData blockHandleData;

    @Autowired
    private CfwRiskHandleData riskHandleData;

    @Autowired
    private CfwEsUtils cfwEsUtils;

    @Autowired
    private JedisTemplate jedisTemplate;
    @Test
    public void flowTest() throws Exception {

        List<String> key = jedisTemplate.getListCache(RedisConstants.ES_ALL_INDEX);
        log.info("打印key:{}", key);
/*        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.from(0);
        sourceBuilder.size(170000);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.sort("time", SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);

        searchRequest.indices("cfw-flow_2025-04-09");
        SearchResponse searchResponse = null;
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            searchResponse = cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
        }
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        //long total = searchResponse.getHits().getTotalHits().value;
        List<Map<String, Object>> dataList=new ArrayList<>(170000);
        for (SearchHit searchHit : searchHits) {
            Map<String, Object> hitMap = searchHit.getSourceAsMap();
            dataList.add(hitMap);
        }
        flowHandleData.handle(dataList);*/
    }

    @Test
    public void blockTest() throws Exception {

        jedisTemplate.delKey(RedisConstants.ES_ALL_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.from(0);
        sourceBuilder.size(170000);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.sort("time", SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);

        searchRequest.indices("cfw-block_2025-04-09");
        SearchResponse searchResponse = null;
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            searchResponse = cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
        }
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        //long total = searchResponse.getHits().getTotalHits().value;
        List<Map<String, Object>> dataList=new ArrayList<>(170000);
        for (SearchHit searchHit : searchHits) {
            Map<String, Object> hitMap = searchHit.getSourceAsMap();
            dataList.add(hitMap);
        }
        blockHandleData.handle(dataList);
    }

    @Test
    public void riskTest() throws Exception {

        jedisTemplate.delKey(RedisConstants.ES_ALL_INDEX);
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.from(0);
        sourceBuilder.size(170000);
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        sourceBuilder.query(boolQueryBuilder);
        sourceBuilder.sort("time", SortOrder.DESC);
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.source(sourceBuilder);

        searchRequest.indices("cfw-risk_2025-04-08","cfw-risk_2025-04-10",
                "cfw-risk_2025-04-11","cfw-risk_2025-04-12","cfw-risk_2025-04-13","cfw-risk_2025-04-14");
        SearchResponse searchResponse = null;
        log.info("打印dsl语句:{}", sourceBuilder);
        try {
            searchResponse = cfwEsUtils.getRestHighLevelClient().search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
        }
        SearchHit[] searchHits = searchResponse.getHits().getHits();
        //long total = searchResponse.getHits().getTotalHits().value;
        List<Map<String, Object>> dataList=new ArrayList<>(170000);
        for (SearchHit searchHit : searchHits) {
            Map<String, Object> hitMap = searchHit.getSourceAsMap();
            dataList.add(hitMap);
        }
        riskHandleData.handle(dataList);
    }
}
