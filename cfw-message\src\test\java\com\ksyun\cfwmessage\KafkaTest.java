package com.ksyun.cfwmessage;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Properties;

@SpringBootTest
public class KafkaTest {
    public static void main(String[] args) {
      //  String cfwflow = "{\"time\":1748327062122,\"fw-instance-id\":\"1eb0ce8c-be5b-4937-bd79-5283e096f96a\",\"vsys\":\"\",\"body\":\"log-id=4424364f, source-ip=**********, source-port=12552, src-region-id=, src-region-name=, destination-ip=**********, destination-port=110, dst-region-id=, dst-region-name=, protocol=TCP, app=POP3, send-bytes=937, send-packets=15, receive-bytes=11323, receive-packets=20, start-time=2023-11-14 16:21:51, end-time=2023-11-14 16:21:53, source-zone=vsys1-trust, destination-zone=vsys1-trust\"}";
        String cfwrisk = "{\"time\":1748327062129,\"fw-instance-id\":\"1eb0ce8c-be5b-4937-bd79-5283e096f96a\",\"vsys\":\"\",\"body\":\"source-ip=*************, source-port=41762, src-region-id=, src-region-name=US, destination-ip=**************, destination-port=8899, dst-region-id=, dst-region-name=CN, protocol=TCP, app=HTTP, action=log-only, attack-type=Vulnerability Exploit Attack, attack-name=Oracle WebLogic Server Activator Insecure Deserialization Vulnerability (CVE-2018-2893) attack-rule-id=336205, severity=Medium, source=predefined, source-zone=3001, destination-zone=3002, payload=+hZUZMhT+hY/AaHMCABFSADeox5AACsGmD2nXoojfEZlrqMiIsMZfW95TPLgkYAYACrBGQAAAQEICv7eOuwTs6viR0VUIC9Mb2dpbiBIVFRQLzEuMQ0KSG9zdDogMTI0LjcwLjEwMS4xNzQ6ODg5OQ0KVXNlci1BZ2VudDogTW96aWxsYS81LjAgKGNvbXBhdGlibGU7IENlbnN5c0luc3BlY3QvMS4xOyAraHR0cHM6Ly9hYm91dC5jZW5zeXMuaW8vKQ0KQWNjZXB0OiAqLyoNCkFjY2VwdC1FbmNvZGluZzogZ3ppcA0KDQo=\"}";
        //String cfwblock = "{\n" +"\"time\":1748327062124,\n" +"\"fw-instance-id\":\"1eb0ce8c-be5b-4937-bd79-5283e096f96a\",\n" +"\"vsys\":\"\",\n" +"\"body\":\"log-id=4424364d, source-ip=**************, source-port=60000, src-region-id=DE, src-region-name=Germany, destination-ip=*************, destination-port=29065, dst-region-id=CN, dst-region-name=Chinese Mainland, dst-host=-, protocol=TCP, app=TCP-ANY, rule-name=50546d09-70a4-47af-9c43-a40170b2c6d3, action=policy deny, source-zone=3001, destination-zone=3002\\nreason=start/policy rematch,\\nstart-time=1698200769000\"\n" + "}";
        sendLogToKafka(cfwrisk);
    }

    private static void sendLogToKafka(String logData) {
        KafkaProducer<String, String> producer = initializeKafkaProducer();
        sendMessageToKafka(producer, "cfw-risk", logData);
        closeKafkaProducer(producer);
    }

    private static KafkaProducer<String, String> initializeKafkaProducer() {
        Properties props = new Properties();
        props.put("bootstrap.servers", "*************:9092");
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        // 直接配置 SASL 用户名和密码
        props.put("security.protocol", "SASL_PLAINTEXT");
        props.put("sasl.mechanism", "PLAIN");
        props.put("sasl.jaas.config", "org.apache.kafka.common.security.plain.PlainLoginModule required username=\""+"admin"+"\" password=\""+"Ksc1234*"+"\";");
        return new KafkaProducer<>(props);
    }

    private static void sendMessageToKafka(KafkaProducer<String, String> producer, String topic, String message) {
        producer.send(new ProducerRecord<>(topic, message));
    }

    private static void closeKafkaProducer(KafkaProducer<String, String> producer) {
        producer.close();
    }
}
