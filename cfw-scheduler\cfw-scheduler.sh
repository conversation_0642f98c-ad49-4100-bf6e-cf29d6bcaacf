#!/usr/bin/env bash

DEPLOY_DIR=$(cd "$(dirname "$0")"; pwd)
#echo ${DEPLOY_DIR}
#JVM_OPTS=$3
JVM_OPTS="-Xms4G -Xmx4G -XX:+UseG1GC"
PACKAGE_NAME="cfw-scheduler.jar"
APP_PATH="/data/ksyun/cfw-scheduler"
PORT=9700
Reg=$2

if [ $3 ]; then
  JVM_OPTS=${@:3}
fi

#GC_LOG=$DEPLOY_DIR/logs/gc
#if  [ ! -d $GC_LOG ];then
#  mkdir -p $GC_LOG
#fi

#source /etc/profile && nohup java -Dlog4j2.formatMsgNoLookups=true -Djava.library.path=/usr/local/apr/lib -Djava.net.preferIPv4Stack=true -Xms4G -Xmx8G -Xmn2g -Xss2m -XX:SurvivorRatio=8 -XX:+UseG1GC -jar cfw-scheduler.jar --server.tomcat.basedir=/data/ksyun/cfw-scheduler/ --server.port=9800 --spring.profiles.active=wq > /dev/null 2>&1 &

JAVA_OPTS="-Dlog4j2.formatMsgNoLookups=true -Djava.library.path=/usr/local/apr/lib -Djava.net.preferIPv4Stack=true ${JVM_OPTS} -jar ${PACKAGE_NAME} --server.tomcat.basedir=${APP_PATH} --server.port=${PORT} --spring.profiles.active=${Reg}"


#PID_FILE=$DEPLOY_DIR/net-elasticsearch-query.pid
case $1 in
start)
        echo "PROGRAM STARTING ..."
        source /etc/profile && nohup java ${JAVA_OPTS} > /dev/null 2>&1 &
        #echo $! > $PID_FILE
        disown
        echo "The Java Program Started (PID:$!)"
;;
stop)
        echo "PROGRAM STOPPING ..."
        pid1=`(ps -ef |grep "${PACKAGE_NAME}" |grep -v "grep") | awk '{print $2}'`
        kill ${pid1} > /dev/null 2>&1
        sleep 5
        if [ `ps -ef |grep "${PACKAGE_NAME}" |grep -v "grep" | awk '{print $2}'|wc -l` -ne 0 ];then
#        if [ ! -n "${pid1}" ];then
           kill -9 ${pid1} > /dev/null 2>&1
           echo "Kill Java Program !"
        else
           echo "The java program stopped successfully !"
        fi
;;
status)
        pid2=`ps -ef |grep "${PACKAGE_NAME}" |grep -v "grep"|awk '{print $2}'`
        if [ ! -n "${pid2}" ]; then
          echo "The java program is stopped !!!"
        else
          echo "The java program is running, PID: ${pid2}"
        fi
;;
*)
    echo "Usage: $0 {start|stop|status}"
;;
esac

exit 0