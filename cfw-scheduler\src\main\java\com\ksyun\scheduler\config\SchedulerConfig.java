package com.ksyun.scheduler.config;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.ksyun.comm.config.annotations.RefreshField;
import com.ksyun.comm.config.annotations.RefreshType;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Log4j2
@Component
@RefreshType
@Getter
@EnableApolloConfig({"cfw-scheduler-common"})
public class SchedulerConfig {

    @Value("${current.region}")
    @RefreshField("current.region")
    private String currentRegion;

/*    @Value("${error.log.send.onepiece:true}")
    @RefreshField("error.log.send.onepiece")
    public String isSendOnepiece;// 是否发送onepiece告警开关

    @Value("${send.onepiece.url:http://alarm.inner.sdns.ksyun.com/alarm/receptor}")
    @RefreshField("send.onepiece.url")
    public String sendOnepieceUrl;*/

}