package com.ksyun.scheduler.rabbitmq;

import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ksyun.comm.util.JsonBinder;
import com.ksyun.cfwcore.rabbitmq.RabbitMQConfiguration;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.scheduler.rabbitmq.processor.MessageProcessor;
import com.ksyun.scheduler.thread.SchedulerThreadPool;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;



@Component
@Slf4j
public class ApiToSchedulerMessageListener {

    @Autowired
    private SchedulerThreadPool schedulerThreadPool;

    private JsonBinder jsonBinder = JsonBinder.buildNormalBinder(false);

    @RabbitListener(queues = RabbitMQConfiguration.API_TO_SCHEDULER_NEW_QUEUE, containerFactory = "simpleRabbitListenerContainerFactory")
    public void receiveMessage(Message message, Channel channel) throws Exception {
        if (message == null) {
            log.warn("receive empty message from rabbitmq.");
            return;
        }
        byte[] body = message.getBody();
        String message_json = new String(body, StandardCharsets.UTF_8);
        log.debug("receive message from queue {}, {}", RabbitMQConfiguration.API_TO_SCHEDULER_NEW_QUEUE, message_json);
        MessageInfo<?> messageInfo = null;
        try {
            messageInfo = jsonBinder.fromJson(message_json, MessageInfo.class);

            if (messageInfo != null && messageInfo.getMessage_type() != null) {
                MessageProcessor messageProcessor = new MessageProcessor(messageInfo, message, channel);
                ListenableFuture<Object> future = schedulerThreadPool.getListeningExecutorService().submit(messageProcessor);
                Futures.addCallback(future, messageProcessor, schedulerThreadPool.getListeningExecutorService());
            }
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        }
    }
}