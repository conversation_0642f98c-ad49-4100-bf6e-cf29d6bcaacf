package com.ksyun.scheduler.rabbitmq.config;

import com.ksyun.scheduler.rabbitmq.MessageCommonHandler;
import com.ksyun.scheduler.rabbitmq.annotation.ApiToSchedulerCallBack;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
public class ApiToSchedulerCallbackConfig {

    @Autowired
    private ApplicationContext applicationContext;

    public static Map<Integer, MessageCommonHandler> commonHandlerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        Map<String, MessageCommonHandler> handlerMap = applicationContext.getBeansOfType(MessageCommonHandler.class);
        handlerMap.forEach((_key, _value) -> {
            MessageCommonHandler messageCommonHandler = handlerMap.get(_key);
            ApiToSchedulerCallBack apiToSchedulerCallBack = messageCommonHandler.getClass().getAnnotation(ApiToSchedulerCallBack.class);
            if (apiToSchedulerCallBack != null) {
                commonHandlerMap.put(apiToSchedulerCallBack.messageType(), messageCommonHandler);
            }
        });
    }
}
