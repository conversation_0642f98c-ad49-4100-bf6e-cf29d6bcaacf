package com.ksyun.scheduler.rabbitmq.handler.monitor;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.monitor.api.domain.MonitorResponse;
import com.ksyun.cfwcore.monitor.api.domain.MonitorUpdateParam;
import com.ksyun.cfwcore.monitor.wapper.MonitorWapper;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.proxy.ProxyAuth;
import com.ksyun.scheduler.rabbitmq.MessageCommonHandler;
import com.ksyun.scheduler.rabbitmq.annotation.ApiToSchedulerCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ApiToSchedulerCallBack(messageType = 3)
public class UpdateMonitorHandler implements MessageCommonHandler {

    @Autowired
    private MonitorWapper monitorWapper;

    @Override
    public void process(MessageInfo<?> messageInfo) {
        ProxyAuth auth = messageInfo.getAuth();
        MonitorUpdateParam monitorUpdateParam = jsonBinder.fromJson(jsonBinder.toJson(messageInfo.getMessage()), MonitorUpdateParam.class);
        log.info("[MonitorUpdate] get message from queue,param:[{}],auth:[{}]", monitorUpdateParam, JSONUtil.toJsonStr(auth));
        MonitorResponse response = monitorWapper.updateMonitor(auth, monitorUpdateParam);
        if (response.getStatus() != 0) {
            ScheduleWarnLog.getLog().error(new NetworkLogMsg().putMsg("通知监控出现错误," + response.getMessage()));
        }
        log.info("[MonitorUpdate] auth: [{}], param: [{}] response :[{}]", JSONUtil.toJsonStr(auth), monitorUpdateParam, response);
    }
}
