package com.ksyun.scheduler.rabbitmq.handler.trade;

import cn.hutool.json.JSONUtil;
import com.ksyun.cfwcore.enums.NotifySubOrderResult;
import com.ksyun.cfwcore.enums.SubOrderStatusEnum;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.comm.thirdpart.trade.api.domain.NotifySubOrderParam;
import com.ksyun.comm.thirdpart.trade.api.domain.NotifySubOrderResponse;
import com.ksyun.comm.thirdpart.trade.api.domain.SubOrder;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.scheduler.config.SchedulerConfig;
import com.ksyun.scheduler.rabbitmq.MessageCommonHandler;
import com.ksyun.scheduler.rabbitmq.annotation.ApiToSchedulerCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ApiToSchedulerCallBack(messageType = 1)
public class NotifySubOrderHandler implements MessageCommonHandler {

    private TradeUtils tradeUtils;

    private JedisTemplate jedisTemplate;

    private SchedulerConfig schedulerConfig;

    @Override
    public void process(MessageInfo<?> messageInfo) {
        NotifySubOrderParam notifySubOrderParam = jsonBinder.fromJson(jsonBinder.toJson(messageInfo.getMessage()), NotifySubOrderParam.class);

        if (checkAlreadyNotify(notifySubOrderParam.getInstanceId(), notifySubOrderParam.getSubOrderId())) {
            log.info("[NotifySubOrder] checkAlreadyNotify true. auth {} with param {}", messageInfo.getAuth(), notifySubOrderParam);
            return;
        }

        boolean isExist = checkExist(notifySubOrderParam);
        if (!isExist) {
            NotifySubOrderResponse result = tradeUtils.notifySubOrder(notifySubOrderParam);
            log.info("[NotifySubOrder] notifySubOrder result: {}", JSONUtil.toJsonStr(result));
        } else {
            log.info("5秒内通知订单实例{} 订单{} 状态{} ,本次通知跳过", notifySubOrderParam.getInstanceId(),
                    notifySubOrderParam.getSubOrderId(), notifySubOrderParam.getStatus());
        }
        log.info("[NotifySubOrder] notify suborder success. auth {} with param {}", JSONUtil.toJsonStr(messageInfo.getAuth()), notifySubOrderParam);
    }

    private Boolean checkExist(NotifySubOrderParam notifySubOrderParam) {
        boolean isExist = false;
        try {
            isExist = jedisTemplate.checkKeyExist(notifySubOrderParam.getInstanceId() + "_"
                    + notifySubOrderParam.getStatus() + "_" + notifySubOrderParam.getSubOrderId(), "1", 5);
        } catch (Exception e) {
            ScheduleWarnLog.getLog().error(
                    new NetworkLogMsg().putMsg(schedulerConfig.getCurrentRegion()
                            + "redis出现连接或者超时问题，" + "[" + e.getMessage() + "]"), e);
        }
        return isExist;
    }

    private boolean checkAlreadyNotify(String instanceId, String subOrderId) {
        boolean alreadySucc = jedisTemplate.checkMemberExists(instanceId + "_" + NotifySubOrderResult.SUCCESS.getValue() + "_" + subOrderId, "1");
        if (alreadySucc) {
            log.info("[NotifySubOrder] checkAlreadyNotify alreadySucc. instanceId {} ,subOrderId {}", instanceId, subOrderId);
            return alreadySucc;
        }
        boolean alreadyFail = jedisTemplate.checkMemberExists(instanceId + "_" + NotifySubOrderResult.FAIL.getValue() + "_" + subOrderId, "1");
        if (alreadyFail) {
            log.info("[NotifySubOrder] checkAlreadyNotify alreadyFail. instanceId {} ,subOrderId {}", instanceId, subOrderId);
            return alreadyFail;
        }

        SubOrder subOrder = tradeUtils.querySubOrderBySubOrderId(subOrderId);
        if (subOrder != null && subOrder.getStatus() != null) {
            if (subOrder.getStatus().equals(SubOrderStatusEnum.CREATING.getStatus())) {
                return false;
            } else {
                //完结状态
                return true;
            }
        }
        return false;
    }


    @Autowired
    public void setTradeUtils(TradeUtils tradeUtils) {
        this.tradeUtils = tradeUtils;
    }

    @Autowired
    public void setJedisTemplate(JedisTemplate jedisTemplate) {
        this.jedisTemplate = jedisTemplate;
    }

    @Autowired
    public void setSchedulerConfig(SchedulerConfig schedulerConfig) {
        this.schedulerConfig = schedulerConfig;
    }
}
