package com.ksyun.scheduler.rabbitmq.handler.trade;

import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.config.RegionConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.constants.ErrorCode;
import com.ksyun.cfwcore.enums.InstanceStatusEnum;
import com.ksyun.cfwcore.holder.InnerAPIHolder;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.cfwcore.trade.wapper.TradeWapper;
import com.ksyun.comm.cache.jedis.core.JedisTemplate;
import com.ksyun.comm.thirdpart.trade.api.domain.*;
import com.ksyun.comm.thirdpart.trade.util.TradeUtils;
import com.ksyun.common.http.OpenAPIException;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.network.log.plugin.OnePiecePlugin;
import com.ksyun.common.network.log.utils.NetWorkLogUtils;
import com.ksyun.common.proxy.ProxyAuth;
import com.ksyun.scheduler.config.SchedulerConfig;
import com.ksyun.scheduler.rabbitmq.MessageCommonHandler;
import com.ksyun.scheduler.rabbitmq.annotation.ApiToSchedulerCallBack;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ApiToSchedulerCallBack(messageType = 5)
public class OrderDeleteInstanceHandler implements MessageCommonHandler {

    private TradeUtils tradeUtils;

    private JedisTemplate jedisTemplate;

    @Autowired
    private SchedulerConfig schedulerConfig;

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private TradeWapper tradeWapper;

    @Override
    public void process(MessageInfo<?> messageInfo) {
        try {
            ProxyAuth proxyAuth = messageInfo.getAuth();
            RefundInstanceParam refundInstanceParam = jsonBinder.fromJson(jsonBinder.toJson(messageInfo.getMessage()), RefundInstanceParam.class);
            if (refundInstanceParam.getSource() == null) {
                refundInstanceParam.setSource(0);
            }
            boolean isExist = checkExist(refundInstanceParam.getInstanceId());
            if (!isExist) {
                log.info("[Order2RefundInstance] get message from queue,DeleteInstanceParam:[{}],proxy:[{}]", refundInstanceParam, proxyAuth);
                initLogConfig(refundInstanceParam);

                //如果是已经最终状态 则终止
                if (checkAlreadyUnsubscribe(refundInstanceParam.getInstanceId())) {
                    return;
                }
                RefundInstanceResponse response = tradeWapper.refundInstance(proxyAuth, refundInstanceParam);
                log.info("[Order2RefundInstance] auth: [{}], param: [{}] response :[{}]", proxyAuth, refundInstanceParam, response);
                if (commonConfig.getTradeRefundRetryCodeSet().contains(response.getMessage())) {
                    throw new OpenAPIException(ErrorCode.PaymentFailure, "实例退订出现问题且出现重试，需要抛出异常", HttpStatus.BAD_REQUEST);
                }
            } else {
                log.info("5秒内通知过实例{}退订,本次通知跳过", refundInstanceParam.getInstanceId());
                if (messageInfo.getRetry_times().intValue() > 1) {
                    throw new OpenAPIException(ErrorCode.PaymentFailure, "实例退订出现问题且出现重试，需要抛出异常", HttpStatus.BAD_REQUEST);
                }
            }
            log.info("[RefundInstance] refund instance success. auth {} with param {}", proxyAuth, refundInstanceParam);
        } catch (Exception e) {
            log.error("RefundRetryListener onMessage error,messageInfo:{}", messageInfo, e);
        } finally {
            NetWorkLogUtils.clearNetWorkLogCommonInfo();
        }
    }

    private void initLogConfig(RefundInstanceParam message) {
        ProxyAuth auth = new ProxyAuth(message.getInstanceId(), String.valueOf(message.getAdminId()), schedulerConfig.getCurrentRegion());

        OnePiecePlugin plugin = new OnePiecePlugin(commonConfig.getSendOnepieceUrl(),
                commonConfig.getSendOnepieceMark(), Constants.SCHEDULER_SERVICE_NAME, commonConfig.getAlertCode());
        try {
            InnerAPIHolder.setProxyAuth(auth, "RefundRetry", "RefundRetryListener", plugin);
        } catch (Exception e) {
            log.error("initLogConfig error", e);
        }
    }

    private Boolean checkExist(String instanceId) {
        boolean isExist = false;
        try {
            isExist = jedisTemplate.checkKeyExist("REFUND" + instanceId, "1", 5);
        } catch (Exception e) {
            ScheduleWarnLog.getLog().error(
                    new NetworkLogMsg().putMsg(schedulerConfig.getCurrentRegion()
                            + "redis出现连接或者超时问题，" + "[" + e.getMessage() + "]"), e);
        }
        return isExist;
    }

    private boolean checkAlreadyUnsubscribe(String instanceId) {
        try {
            InstanceSubOrder instanceInfoResponse = tradeUtils.querySubOrderInstanceId(instanceId);
            if (instanceInfoResponse != null && instanceInfoResponse.getStatus() != null) {
                if (InstanceStatusEnum.checkIsFinal(instanceInfoResponse.getStatus())) {
                    log.info("[RefundRetryListener] refundRetry, 实例已经是最终状态 instanceInfo:[{}]", instanceInfoResponse);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("checkAlreadyUnsubscribe", e);
        }
        return false;
    }


    @Autowired
    public void setTradeUtils(TradeUtils tradeUtils) {
        this.tradeUtils = tradeUtils;
    }

    @Autowired
    public void setJedisTemplate(JedisTemplate jedisTemplate) {
        this.jedisTemplate = jedisTemplate;
    }
}
