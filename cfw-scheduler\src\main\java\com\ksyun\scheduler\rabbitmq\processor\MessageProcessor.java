package com.ksyun.scheduler.rabbitmq.processor;

import com.google.common.util.concurrent.FutureCallback;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.ksyun.cfwcore.alert.AlertTemplate;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarm;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmDeal;
import com.ksyun.cfwcore.alert.domain.OnePieceAlarmPriority;
import com.ksyun.cfwcore.config.CommonConfig;
import com.ksyun.cfwcore.constants.Constants;
import com.ksyun.cfwcore.context.SpringContext;
import com.ksyun.cfwcore.log.ScheduleWarnLog;
import com.ksyun.cfwcore.rabbitmq.domain.MessageInfo;
import com.ksyun.cfwcore.rabbitmq.enums.SchedulerMessageTypeEnum;
import com.ksyun.comm.util.HostUtils;
import com.ksyun.common.network.log.message.handle.NetworkLogMsg;
import com.ksyun.common.network.log.plugin.OnePiecePlugin;
import com.ksyun.common.network.log.utils.NetWorkLogUtils;
import com.ksyun.scheduler.config.SchedulerConfig;
import com.ksyun.scheduler.rabbitmq.config.ApiToSchedulerCallbackConfig;
import com.ksyun.scheduler.thread.SchedulerThreadPool;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;

import java.net.SocketException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.concurrent.Callable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class MessageProcessor implements Callable<Object>, FutureCallback<Object> {

    private final int MAX_RETRY_TIMES = 3;// 该类型消息最大重试次数
    private MessageInfo<?> messageInfo;

    private Message message;

    private Channel channel;

    // ==============================================================================

    private SchedulerConfig schedulerConfig;

    private SchedulerThreadPool schedulerThreadPool;

    private CommonConfig commonConfig;

    private AlertTemplate alertTemplate;


    public MessageProcessor(MessageInfo<?> messageInfo, Message message, Channel channel) {
        this.messageInfo = messageInfo;
        this.message = message;
        this.channel = channel;
        this.schedulerConfig = SpringContext.getBean(SchedulerConfig.class);
        this.schedulerThreadPool = SpringContext.getBean(SchedulerThreadPool.class);
        this.commonConfig = SpringContext.getBean(CommonConfig.class);
        this.alertTemplate = SpringContext.getBean(AlertTemplate.class);
    }

    @Override
    public Object call() throws Exception {
        //清理当前线程的存储数据
        NetWorkLogUtils.clearNetWorkLogCommonInfo();
        OnePiecePlugin plugin = new OnePiecePlugin(commonConfig.getSendOnepieceUrl(),
                commonConfig.getSendOnepieceMark(),
                Constants.SCHEDULER_SERVICE_NAME, commonConfig.getAlertCode());
        NetWorkLogUtils.setNetWorkLogCommonInfo(messageInfo.getMessage_id(), new HashMap<String, String>() {
            {
                String hostName = null;
                try {
                    hostName = HostUtils.getLocalHosts().get(0);
                } catch (SocketException e) {
                    hostName = "";
                }
                put("Address", hostName);
                put("Action", "TO_SCHEDULER消息：" + SchedulerMessageTypeEnum.getMessageTypeStr(messageInfo.getMessage_type()));
                put("Region", schedulerConfig.getCurrentRegion());
                put("Service", "OpenAPI异步任务处理");
            }

        }, plugin);

        messageInfo.getRetry_times().incrementAndGet();
        // 处理器分发
        ApiToSchedulerCallbackConfig.commonHandlerMap.get(messageInfo.getMessage_type()).process(messageInfo);
        return null;
    }

    @Override
    public void onSuccess(Object o) {
        log.info("消息处理成功,message_id:{},失败重试次数[{}]", messageInfo.getMessage_id(), messageInfo.getRetry_times().get());
    }

    @Override
    public void onFailure(Throwable throwable) {
        byte[] body = message.getBody();
        String message_json = new String(body, StandardCharsets.UTF_8);
        int retryTimes = messageInfo.getRetry_times().incrementAndGet();
        boolean isRetry = false;
        if (retryTimes == 1) {
            isRetry = true;
            log.error("消息处理出现问题，开始首次进入重试流程，message_json={}", message_json);
            ScheduleWarnLog.getLog().error(new NetworkLogMsg().putMsg("消息处理出现问题，开始首次进入重试流程,message_json=" + message_json));
        } else if (retryTimes <= MAX_RETRY_TIMES) {
            isRetry = true;
            log.error("message handle error,retry it, retry times:[{}],error message:{}", retryTimes, throwable.getMessage());
            log.info("重新发送消息 message_json={},消息队列稍后重试", message_json);
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            log.error("消息处理出现问题，并且达到重试的最大次数:[{}]，已发送告警，需手动处理，message_json={}", retryTimes, message_json);
            ScheduleWarnLog.getLog().error(new NetworkLogMsg().putMsg("消息处理出现问题，并且达到重试的最大次数[" + MAX_RETRY_TIMES + "],message_json=" + message_json));

            OnePieceAlarm onePieceAlarm = new OnePieceAlarm();
            onePieceAlarm.setName(Constants.SCHEDULER_SERVICE_NAME);
            onePieceAlarm.setPriority(OnePieceAlarmPriority.P2.getPriority());
            onePieceAlarm.setProduct(commonConfig.getAlertCode());
            onePieceAlarm.setNo_deal(OnePieceAlarmDeal.ASK.getDeal());
            String type=SchedulerMessageTypeEnum.getMessageTypeStr(messageInfo.getMessage_type());
            onePieceAlarm.setContent("【"+type+"】重试超过预警次数，id：" + messageInfo.getInstanceId());
            onePieceAlarm.setHtml_content("【"+type+"】重试超过预警次数，id：" + messageInfo.getInstanceId()+"，messageInfo:" + "[" + messageInfo + "]");
            alertTemplate.send(onePieceAlarm);
        }

        if (isRetry) {
            ListenableFuture<Object> future = schedulerThreadPool.getListeningExecutorService().submit(this);
            Futures.addCallback(future, this, schedulerThreadPool.getListeningExecutorService());
        }
    }
}