server:
  tomcat:
    max-threads: 2000
    accept-count: 2000
    connection-timeout: 20000
management:
  endpoint:
    shutdown:
      enabled: true
  endpoints:
    web:
      exposure:
        include: shutdown
spring:
  profiles:
    active: @profiles.active@
logging:
  config: classpath:log4j2.xml
net-commons:
  base:
    tomcat:
      protocol: nio
      enable: true
    one-piece:
      enable: true
      url: http://alarm.inner.sdns.ksyun.com/alarm/receptor
    http:
      read-timeout: 60000
      connect-timeout: 2000
      http-client: HTTP-COMPONENT