2025-08-01 13:54:29,490 INFO   [] [] [] [] [] [CfwApiApplication:55] [main] : Starting CfwApiApplication using Java 1.8.0_462 on KC-20250721RUGV with PID 2820 (D:\FireWallCode\cfw-parent\cfw-api\target\classes started by <PERSON> in D:\FireWallCode\cfw-parent\cfw-api)
2025-08-01 13:54:29,494 DEBUG  [] [] [] [] [] [CfwApiApplication:56] [main] : Running with Spring Boot v2.7.10, Spring v5.3.26
2025-08-01 13:54:29,494 INFO   [] [] [] [] [] [CfwApiApplication:637] [main] : The following 1 profile is active: "dev"
2025-08-01 13:54:34,112 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties dns_message_en.properties loaded 
2025-08-01 13:54:34,113 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties dns_message_zh.properties loaded 
2025-08-01 13:54:34,113 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties message_en.properties loaded 
2025-08-01 13:54:34,114 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties message_zh.properties loaded 
2025-08-01 13:54:34,114 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties net-commons-trade-message.properties loaded 
2025-08-01 13:54:34,115 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties neutron_message_en.properties loaded 
2025-08-01 13:54:34,116 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties neutron_message_zh.properties loaded 
2025-08-01 13:54:34,116 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties order_display_en.properties loaded 
2025-08-01 13:54:34,117 INFO   [] [] [] [] [] [CommonUtils:80] [main] : Properties order_display_zh.properties loaded 
2025-08-01 13:54:46,336 INFO   [] [] [] [] [] [CommonConfig:66] [main] : BillType与ChargeType对应关系加载成功 [{1=ChargeComment(name=预付费(包年包月), code=PrePaidByMonth, bill=1), 2=ChargeComment(name=按小时配置付费(小时结), code=PrePaidByHour, bill=2), 3=ChargeComment(name=按月峰值付费(月结), code=PostPaidByPeak, bill=3), 5=ChargeComment(name=按日配置付费(月结), code=PostPaidByDay, bill=5), 6=ChargeComment(name=按日峰值付费(月结), code=PostPaidByDailyPeak, bill=6), 80=ChargeComment(name=按小时带宽实时付费, code=BandwidthHourly, bill=80), 83=ChargeComment(name=地域共享带宽, code=PostPaidByRegionPeak, bill=83), 84=ChargeComment(name=按小时配置付费(月结), code=PostPaidByHour, bill=84), 85=ChargeComment(name=按日峰值实时付费, code=PrePaidByDailyPeak, bill=85), 86=ChargeComment(name=按日用量实时付费, code=DailyPaidByTransfer, bill=86), 87=ChargeComment(name=按小时配置实时付费, code=HourlyInstantSettlement, bill=87), 105=ChargeComment(name=按月配置付费(月结), code=PostPaidByMonthlyConfig, bill=105), 704=ChargeComment(name=按月用量付费(月结), code=PostPaidByTransfer, bill=704), 801=ChargeComment(name=预付费(按次), code=PrepaidByTime, bill=801), 805=ChargeComment(name=后付费(按次), code=PostpaidByTime, bill=805), 807=ChargeComment(name=按月增强95峰值付费(月结), code=PostPaidByAdvanced95Peak, bill=807), 808=ChargeComment(name=按月第5峰均值付费(月结), code=PostPaidBy5thPeakAvg, bill=808), 809=ChargeComment(name=按小时用量实时付费(小时结), code=PrePaidByHourUsage, bill=809)}]
2025-08-01 13:54:46,344 INFO   [] [] [] [] [] [CommonConfig:78] [main] : 初始化tradeRefundRetryCodeSet
2025-08-01 13:54:46,346 INFO   [] [] [] [] [] [CommonConfig:91] [main] : tradeRefundRetryCodeSet size=1
2025-08-01 13:54:46,351 INFO   [] [] [] [] [] [RegionConfig:51] [main] : 机房缓存regionCodeCache配置加载成功 [{cn-qingyangtest-1=QYTestRegionOne, cn-guangzhou-1=GZVPCRegion, cn-beijing-6=TJWQRegion, cn-qingdaodev-1=QD01vpcdevRegionOne, cn-beijing-5=BJZJMVPCRegion, cn-beijing-1=BJYZRegionOne, ap-singapore-1=SGPRegionOne, cn-beijing-3=BJZJMRegionOne, cn-shanghai-1=SHRegionOne, cn-shanghai-2=SHPBSRegionOne, cn-beijing-1-new=BJYZVPCRegion, cn-qingyangdev-2=QYDevRegionTwo, cn-hongkong-2=HKVPCRegion, cn-shanghai-3=SHPBSVpctestRegionOne, cn-hongkong-1=HKSTRegionOne, us-west-1=USCARegionOne, cn-shanghai-7=SHPBSbasictestRegionOne}]
2025-08-01 13:54:46,351 INFO   [] [] [] [] [] [RegionConfig:52] [main] : 机房缓存keystoneRegionCache配置加载成功 [{GZVPCRegion=cn-guangzhou-1, SHPBSVpctestRegionOne=cn-shanghai-3, BJZJMRegionOne=cn-beijing-3, TJWQRegion=cn-beijing-6, HKSTRegionOne=cn-hongkong-1, BJYZVPCRegion=cn-beijing-1-new, QD01vpcdevRegionOne=cn-qingdaodev-1, USCARegionOne=us-west-1, SHPBSRegionOne=cn-shanghai-2, SGPRegionOne=ap-singapore-1, SHRegionOne=cn-shanghai-1, SHPBSbasictestRegionOne=cn-shanghai-7, QYDevRegionTwo=cn-qingyangdev-2, BJZJMVPCRegion=cn-beijing-5, QYTestRegionOne=cn-qingyangtest-1, HKVPCRegion=cn-hongkong-2, BJYZRegionOne=cn-beijing-1}]
2025-08-01 13:54:46,355 INFO   [] [] [] [] [] [RegionConfig:68] [main] : VPC机房中文名称配置加载成功 [{cn-qingyangtest-1=庆阳1区（测试）, cn-guangzhou-1=华南1（广州）, cn-taipei-1=台北, cn-north-1-gov=华北政务1（北京）, cn-shanghai-fin=华东金融1（上海）, cn-beijing-6=华北1（北京）, cn-qingdaodev-1=自用（青岛2）, cn-qingdao-1=自用（青岛）, cn-southwest-1=西南1（重庆）, ap-singapore-1=新加坡, cn-central-1=华中1（武汉）, eu-east-1=俄罗斯（莫斯科）, cn-shanghai-2=华东1（上海）, cn-beijing-1-new=北京1区(VPC), cn-qingyangdev-2=庆阳2区（研发自用）, cn-hongkong-2=香港, cn-shanghai-3=华东2（上海）, cn-beijing-fin=华北金融1（北京）}]
2025-08-01 13:54:46,356 INFO   [] [] [] [] [] [RegionConfig:69] [main] : 机房Code国际化名称列表 [{cn-qingyangtest-1=Qingyangtest1, cn-guangzhou-1=CN South 1(Guangzhou), cn-taipei-1=Taipei, cn-north-1-gov=CN North Government 1(Beijing), cn-shanghai-fin=CN East Finance 1(Shanghai), cn-beijing-6=CN North 1(Beijing), cn-qingdaodev-1=Self-use (Qingdao2), cn-qingdao-1=Self-use (QingDao), cn-southwest-1=CN Southwest 1(Chongqing), ap-singapore-1=Singapore, cn-central-1=CN Central 1(Wuhan), eu-east-1=Russia (Moscow), cn-shanghai-2=CN East 1(Shanghai), cn-beijing-1-new=cn_beijing_1(VPC), cn-qingyangdev-2=cn_qingyangdev_2, cn-hongkong-2=Hong Kong, cn-shanghai-3=CN East 2(Shanghai), cn-beijing-fin=CN North Finance 1(Beijing)}]
2025-08-01 13:54:46,480 INFO   [] [] [] [] [] [CommonUtils:119] [main] : 自定义配置部分HTTP_CLIENT参数
2025-08-01 13:54:46,770 INFO   [] [] [] [] [] [EtcdConfig:42] [main] : saslEnable:true,username:root
2025-08-01 13:54:47,157 INFO   [] [] [] [] [] [JedisTemplate:72] [main] : 传统模式jedisPool启动
2025-08-01 13:54:47,236 INFO   [] [] [] [] [] [RedissonTemplate:93] [main] : 单机模式Redisson启动
2025-08-01 13:54:53,853 INFO   [] [] [] [] [] [ThreadPool:35] [main] : 初始化线程池开始
2025-08-01 13:54:53,854 INFO   [] [] [] [] [] [ThreadPool:37] [main] : 初始化线程池开始结束
2025-08-01 13:54:54,533 INFO   [] [] [] [] [] [ApiThreadPool:26] [main] : 消费者消息队列线程池初始化开始
2025-08-01 13:54:54,534 INFO   [] [] [] [] [] [ApiThreadPool:35] [main] : 消费者消息队列线程池初始化结束
2025-08-01 13:54:54,562 INFO   [] [] [] [] [] [XxlJobConfig:53] [main] : >>>>>>>>>>> xxl-job config init.
2025-08-01 13:54:56,286 INFO   [] [] [] [] [] [ApiThreadPool:40] [main] : 消费者消息队列线程池shutdown开始
2025-08-01 13:54:56,286 INFO   [] [] [] [] [] [ApiThreadPool:47] [main] : 消费者消息队列线程池shutdown结束
2025-08-01 13:54:56,602 INFO   [] [] [] [] [] [ThreadPool:26] [main] : 结束线程池开始
2025-08-01 13:54:56,603 INFO   [] [] [] [] [] [ThreadPool:30] [main] : 结束线程池结束
